# FixMyCal Environment Configuration
# Copy this file to .env and fill in the appropriate values

# Frontend Configuration
VITE_API_URL=http://localhost:8000
VITE_EVOLUTION_API_URL=http://localhost:8080
VITE_EVOLUTION_API_KEY=your_evolution_api_key
VITE_GOOGLE_CLIENT_ID=your_google_client_id
VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# Backend Configuration
DATABASE_URL=********************************************/fixmycal
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=http://localhost:5173/dashboard/settings/google-callback

# AI Configuration
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
OPENAI_API_KEY_DOCKER=${OPENAI_API_KEY}
GEMINI_API_KEY_DOCKER=${GEMINI_API_KEY}

# Stripe Configuration
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# WhatsApp Integration (Evolution API)
# These are used by the Evolution API container
SERVER_TYPE=http
SERVER_PORT=8080
AUTHENTICATION_API_KEY=your_evolution_api_key
DATABASE_ENABLED=true
DATABASE_PROVIDER=postgresql
DATABASE_CONNECTION_URI=**********************************************************************/evolution_db
CACHE_REDIS_ENABLED=true
CACHE_REDIS_URI=redis://evolution-redis:6379
CACHE_REDIS_PREFIX_KEY=evolution
CACHE_REDIS_SAVE_INSTANCES=false
POSTGRES_USER=evolution_user
POSTGRES_PASSWORD=evolution_password
POSTGRES_DB=evolution_db
LOG_LEVEL=ERROR,WARN,DEBUG,INFO,LOG,VERBOSE,DARK,WEBHOOKS
LOG_COLOR=true
LOG_BAILEYS=error
DEL_INSTANCE=false
QRCODE_LIMIT=30
QRCODE_COLOR=#175197
CONFIG_SESSION_PHONE_CLIENT=FixMyCal
