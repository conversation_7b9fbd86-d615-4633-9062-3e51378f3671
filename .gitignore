# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment variables
.env
.env.*
!.env.example

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Personal documentation
TECHNICAL_DOCS.md
NOTES.md
PRESENTATION_NOTES.md

# Presentation materials
Technical_Presentation_Outline.md
FixMyCal_Technical_Presentation.md
Technical_Presentation_PowerPoint_Template.md
Commercial_Presentation_Outline.md
FixMyCal_Commercial_Presentation.md
Commercial_Presentation_PowerPoint_Template.md
PostgreSQL_Database_Exploration_Commands.md
*_Presentation_*.md
*_presentation_*.md
