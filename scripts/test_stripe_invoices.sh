#!/bin/bash
# Script to create test data in Stripe for invoice download testing

# Check if Stripe CLI is installed
if ! command -v stripe &> /dev/null; then
    echo "Stripe CLI is not installed. Please install it first."
    echo "See https://stripe.com/docs/stripe-cli for installation instructions."
    exit 1
fi

# Check if logged in to Stripe
if ! stripe whoami &> /dev/null; then
    echo "You are not logged in to Stripe. Please run 'stripe login' first."
    exit 1
fi

echo "Creating test data in Stripe..."

# Create a test customer
echo "Creating test customer..."
CUSTOMER_RESPONSE=$(stripe customers create --name="FixMyCal Test User" --email="<EMAIL>")
CUSTOMER_ID=$(echo "$CUSTOMER_RESPONSE" | grep -o "cus_[a-zA-Z0-9]*")
echo "Created customer with ID: $CUSTOMER_ID"

# Create a test product
echo "Creating test product..."
PRODUCT_RESPONSE=$(stripe products create --name="Professional Plan")
PRODUCT_ID=$(echo "$PRODUCT_RESPONSE" | grep -o "prod_[a-zA-Z0-9]*")
echo "Created product with ID: $PRODUCT_ID"

# Create a price for the product
echo "Creating price for the product..."
PRICE_RESPONSE=$(stripe prices create --product="$PRODUCT_ID" --unit-amount=5000 --currency=eur --recurring[interval]=month)
PRICE_ID=$(echo "$PRICE_RESPONSE" | grep -o "price_[a-zA-Z0-9]*")
echo "Created price with ID: $PRICE_ID"

# Create a subscription
echo "Creating subscription..."
SUBSCRIPTION_RESPONSE=$(stripe subscriptions create --customer="$CUSTOMER_ID" --items[0][price]="$PRICE_ID")
SUBSCRIPTION_ID=$(echo "$SUBSCRIPTION_RESPONSE" | grep -o "sub_[a-zA-Z0-9]*")
echo "Created subscription with ID: $SUBSCRIPTION_ID"

# Create an invoice
echo "Creating invoice..."
INVOICE_RESPONSE=$(stripe invoices create --customer="$CUSTOMER_ID" --collection_method=send_invoice --days_until_due=30)
INVOICE_ID=$(echo "$INVOICE_RESPONSE" | grep -o "in_[a-zA-Z0-9]*")
echo "Created invoice with ID: $INVOICE_ID"

# Finalize the invoice
echo "Finalizing invoice..."
stripe invoices finalize "$INVOICE_ID"

# Pay the invoice
echo "Paying invoice..."
stripe invoices pay "$INVOICE_ID"

echo "Test data creation complete!"
echo ""
echo "Customer ID: $CUSTOMER_ID"
echo "Product ID: $PRODUCT_ID"
echo "Price ID: $PRICE_ID"
echo "Subscription ID: $SUBSCRIPTION_ID"
echo "Invoice ID: $INVOICE_ID"
echo ""
echo "You can now test the invoice download functionality in your application."
echo "To forward webhook events to your local application, run:"
echo "stripe listen --forward-to http://localhost:8000/dashboard/billing/webhook"
