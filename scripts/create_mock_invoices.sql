-- Create mock data for testing invoice download functionality

-- First, check if we have any users
SELECT id FROM users LIMIT 1;

-- If no users exist, create a test user
DO $$
DECLARE
    user_id uuid;
    plan_id uuid;
BEGIN
    -- Check if we have any users
    SELECT id INTO user_id FROM users LIMIT 1;
    
    -- If no users exist, create a test user
    IF user_id IS NULL THEN
        INSERT INTO users (id, email, password_hash, business_name)
        VALUES (
            gen_random_uuid(),
            '<EMAIL>',
            '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', -- 'password'
            'Test Business'
        )
        RETURNING id INTO user_id;
    END IF;
    
    -- Check if we have any pricing plans
    SELECT id INTO plan_id FROM pricing_plans LIMIT 1;
    
    -- If no pricing plans exist, create test plans
    IF plan_id IS NULL THEN
        INSERT INTO pricing_plans (id, name, monthly_fee, features)
        VALUES (
            gen_random_uuid(),
            'Professional',
            50.0,
            '["Unlimited appointments", "5,000 AI responses/month", "Priority support"]'
        )
        RETURNING id INTO plan_id;
    END IF;
    
    -- Create mock invoices
    INSERT INTO invoices (
        id, 
        user_id, 
        plan_id, 
        period_start, 
        period_end, 
        total, 
        stripe_invoice_id, 
        status,
        invoice_pdf_url,
        hosted_invoice_url
    )
    VALUES
    (
        gen_random_uuid(),
        user_id,
        plan_id,
        CURRENT_DATE - INTERVAL '30 days',
        CURRENT_DATE,
        50.0,
        'inv_mock_1',
        'paid',
        NULL,
        NULL
    ),
    (
        gen_random_uuid(),
        user_id,
        plan_id,
        CURRENT_DATE - INTERVAL '60 days',
        CURRENT_DATE - INTERVAL '30 days',
        50.0,
        'inv_mock_2',
        'paid',
        NULL,
        NULL
    ),
    (
        gen_random_uuid(),
        user_id,
        plan_id,
        CURRENT_DATE - INTERVAL '90 days',
        CURRENT_DATE - INTERVAL '60 days',
        50.0,
        'inv_mock_3',
        'paid',
        NULL,
        NULL
    );
    
    -- Create a mock invoice with "real" Stripe URLs for testing
    INSERT INTO invoices (
        id, 
        user_id, 
        plan_id, 
        period_start, 
        period_end, 
        total, 
        stripe_invoice_id, 
        status,
        invoice_pdf_url,
        hosted_invoice_url
    )
    VALUES
    (
        gen_random_uuid(),
        user_id,
        plan_id,
        CURRENT_DATE - INTERVAL '15 days',
        CURRENT_DATE + INTERVAL '15 days',
        50.0,
        'in_mock_with_urls',
        'paid',
        'https://files.stripe.com/v1/files/file_mock_pdf/pdf',
        'https://dashboard.stripe.com/test/invoices/in_mock_with_urls'
    );
    
END $$;
