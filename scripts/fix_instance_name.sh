#!/bin/bash

# Find all TypeScript files with instanceName
echo "Files containing instanceName:"
grep -r "instanceName" --include="*.ts" --include="*.tsx" src/

# Count total occurrences
TOTAL=$(grep -r "instanceName" --include="*.ts" --include="*.tsx" src/ | wc -l)
echo "Total occurrences: $TOTAL"

# Replace all occurrences of instanceName with getInstanceName()
# This handles the case where instanceName is used in a string template
find src/ -type f -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/\${instanceName}/\${getInstanceName()}/g'

# This handles the case where instanceName is used as a variable
find src/ -type f -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/instanceName\([^(]\)/getInstanceName()\1/g'

# This handles the case where instanceName is imported
find src/ -type f -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/import { instanceName/import { getInstanceName/g'
find src/ -type f -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/, instanceName/, getInstanceName/g'

echo "Replacement complete. Please check the files for any remaining issues."
