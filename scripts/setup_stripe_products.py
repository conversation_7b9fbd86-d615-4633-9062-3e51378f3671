#!/usr/bin/env python3
"""
Script to set up Stripe products and prices for FixMyCal subscription plans.
This script creates products and prices in Stripe and updates the pricing_plans table
with the Stripe price IDs.

Usage:
    python setup_stripe_products.py

Environment variables:
    STRIPE_SECRET_KEY: Your Stripe secret key
    DATABASE_URL: PostgreSQL connection string
"""

import os
import sys
import stripe
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from uuid import UUID

# Check if running in Docker
if os.path.exists('/.dockerenv'):
    # Use the Docker database URL
    DATABASE_URL = os.environ.get('DATABASE_URL', '********************************************/fixmycal')
else:
    # Use the local database URL
    DATABASE_URL = os.environ.get('DATABASE_URL', 'postgresql://postgres:postgres@localhost:5432/fixmycal')

# Get Stripe API key from environment
STRIPE_SECRET_KEY = os.environ.get('STRIPE_SECRET_KEY')

if not STRIPE_SECRET_KEY:
    print("Error: STRIPE_SECRET_KEY environment variable is not set.")
    sys.exit(1)

# Initialize Stripe
stripe.api_key = STRIPE_SECRET_KEY

# Connect to the database
engine = create_engine(DATABASE_URL)
Session = sessionmaker(bind=engine)
session = Session()

def create_stripe_product(name, description, monthly_fee, features):
    """Create a Stripe product and price"""
    print(f"Creating Stripe product for {name} plan...")
    
    # Create the product
    product = stripe.Product.create(
        name=f"{name} Plan",
        description=description
    )
    
    # Create the price (monthly subscription)
    price = stripe.Price.create(
        product=product.id,
        unit_amount=int(monthly_fee * 100),  # Convert to cents
        currency="eur",
        recurring={"interval": "month"}
    )
    
    # Create the annual price (with 20% discount)
    annual_fee = monthly_fee * 12 * 0.8  # 20% discount
    annual_price = stripe.Price.create(
        product=product.id,
        unit_amount=int(annual_fee * 100),  # Convert to cents
        currency="eur",
        recurring={"interval": "year"}
    )
    
    print(f"Created product {product.id} with price {price.id} (monthly) and {annual_price.id} (annual)")
    
    return {
        "product_id": product.id,
        "price_id": price.id,
        "annual_price_id": annual_price.id
    }

def update_pricing_plan(plan_id, stripe_product_id, stripe_price_id, stripe_annual_price_id):
    """Update the pricing_plan table with Stripe IDs"""
    print(f"Updating pricing plan {plan_id} with Stripe IDs...")
    
    # Update the pricing plan
    session.execute(
        text("""
        UPDATE pricing_plans 
        SET stripe_product_id = :product_id, 
            stripe_price_id = :price_id,
            stripe_annual_price_id = :annual_price_id
        WHERE id = :plan_id
        """),
        {
            "product_id": stripe_product_id,
            "price_id": stripe_price_id,
            "annual_price_id": stripe_annual_price_id,
            "plan_id": plan_id
        }
    )
    session.commit()

def main():
    """Main function"""
    print("Setting up Stripe products and prices for FixMyCal subscription plans...")
    
    # Get all pricing plans
    plans = session.execute(text("SELECT id, name, monthly_fee, features FROM pricing_plans")).fetchall()
    
    for plan in plans:
        plan_id = plan[0]
        name = plan[1]
        monthly_fee = plan[2]
        features = plan[3]
        
        # Skip Enterprise plan (custom pricing)
        if name == "Enterprise":
            print(f"Skipping {name} plan (custom pricing)")
            continue
        
        # Create Stripe product and price
        stripe_ids = create_stripe_product(
            name=name,
            description=f"{name} subscription plan for FixMyCal",
            monthly_fee=monthly_fee,
            features=features
        )
        
        # Update the pricing plan with Stripe IDs
        update_pricing_plan(
            plan_id=plan_id,
            stripe_product_id=stripe_ids["product_id"],
            stripe_price_id=stripe_ids["price_id"],
            stripe_annual_price_id=stripe_ids["annual_price_id"]
        )
    
    print("Done!")

if __name__ == "__main__":
    main()
