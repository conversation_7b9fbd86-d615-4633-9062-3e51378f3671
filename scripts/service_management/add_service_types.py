#!/usr/bin/env python3
"""
<PERSON>ript to add service types to the database.
"""

import requests
import json

# Define the service types to add
service_types = [
    "Haircut",
    "Hair Coloring",
    "Hair Styling",
    "Massage",
    "Facial",
    "Manicure",
    "Pedicure",
    "Consultation",
    "Physiotherapy",
    "Other"
]

# Get the JWT token (you'll need to replace this with a valid token)
def get_token():
    try:
        # Try to log in and get a token
        login_data = {
            "username": "<EMAIL>",  # Replace with your admin email
            "password": "password123"         # Replace with your admin password
        }
        
        response = requests.post("http://localhost:8000/auth/login", json=login_data)
        response.raise_for_status()
        
        token_data = response.json()
        return token_data.get("access_token")
    except Exception as e:
        print(f"Error getting token: {str(e)}")
        return None

# Add service types
def add_service_types(token):
    if not token:
        print("No token available. Cannot add service types.")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    for type_name in service_types:
        try:
            # Try to create the service type
            data = {"name": type_name}
            response = requests.post(
                "http://localhost:8000/dashboard/service-types",
                headers=headers,
                json=data
            )
            
            if response.status_code == 200 or response.status_code == 201:
                print(f"Successfully added service type: {type_name}")
            elif response.status_code == 400 and "already exists" in response.text:
                print(f"Service type already exists: {type_name}")
            else:
                print(f"Failed to add service type {type_name}: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"Error adding service type {type_name}: {str(e)}")

if __name__ == "__main__":
    print("Getting authentication token...")
    token = get_token()
    
    if token:
        print("Token obtained successfully.")
        print("Adding service types...")
        add_service_types(token)
    else:
        print("Failed to get authentication token. Please check your credentials.")
