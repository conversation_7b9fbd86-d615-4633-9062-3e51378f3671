#!/usr/bin/env python3
"""
<PERSON>ript to add services to the database.
"""

import requests
import json

# Define the services to add (you'll need to replace the type_id with actual IDs from your database)
services = [
    {
        "name": "Basic Haircut",
        "description": "A simple haircut with scissors and clippers",
        "price": 25.0,
        "duration_minutes": 30,
        "active": True,
        "type_id": ""  # Will be filled dynamically
    },
    {
        "name": "Premium Haircut",
        "description": "A premium haircut with wash and styling",
        "price": 45.0,
        "duration_minutes": 60,
        "active": True,
        "type_id": ""  # Will be filled dynamically
    },
    {
        "name": "Full Body Massage",
        "description": "A relaxing full body massage",
        "price": 80.0,
        "duration_minutes": 90,
        "active": True,
        "type_id": ""  # Will be filled dynamically
    },
    {
        "name": "Facial Treatment",
        "description": "A rejuvenating facial treatment",
        "price": 60.0,
        "duration_minutes": 60,
        "active": True,
        "type_id": ""  # Will be filled dynamically
    }
]

# Get the JWT token (you'll need to replace this with a valid token)
def get_token():
    try:
        # Try to log in and get a token
        login_data = {
            "username": "<EMAIL>",  # Replace with your admin email
            "password": "password123"         # Replace with your admin password
        }
        
        response = requests.post("http://localhost:8000/auth/login", json=login_data)
        response.raise_for_status()
        
        token_data = response.json()
        return token_data.get("access_token")
    except Exception as e:
        print(f"Error getting token: {str(e)}")
        return None

# Get service types
def get_service_types(token):
    if not token:
        print("No token available. Cannot get service types.")
        return {}
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(
            "http://localhost:8000/dashboard/service-types",
            headers=headers
        )
        
        if response.status_code == 200:
            service_types = response.json()
            return {st["name"]: st["id"] for st in service_types}
        else:
            print(f"Failed to get service types: {response.status_code} - {response.text}")
            return {}
    except Exception as e:
        print(f"Error getting service types: {str(e)}")
        return {}

# Add services
def add_services(token, service_types):
    if not token:
        print("No token available. Cannot add services.")
        return
    
    if not service_types:
        print("No service types available. Cannot add services.")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Map service names to type IDs
    services[0]["type_id"] = service_types.get("Haircut", "")
    services[1]["type_id"] = service_types.get("Haircut", "")
    services[2]["type_id"] = service_types.get("Massage", "")
    services[3]["type_id"] = service_types.get("Facial", "")
    
    for service in services:
        if not service["type_id"]:
            print(f"Skipping service {service['name']} because no matching type ID was found.")
            continue
        
        try:
            response = requests.post(
                "http://localhost:8000/dashboard/services",
                headers=headers,
                json=service
            )
            
            if response.status_code == 200 or response.status_code == 201:
                print(f"Successfully added service: {service['name']}")
            else:
                print(f"Failed to add service {service['name']}: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"Error adding service {service['name']}: {str(e)}")

if __name__ == "__main__":
    print("Getting authentication token...")
    token = get_token()
    
    if token:
        print("Token obtained successfully.")
        print("Getting service types...")
        service_types = get_service_types(token)
        
        if service_types:
            print(f"Found {len(service_types)} service types.")
            print("Adding services...")
            add_services(token, service_types)
        else:
            print("No service types found. Please add service types first.")
    else:
        print("Failed to get authentication token. Please check your credentials.")
