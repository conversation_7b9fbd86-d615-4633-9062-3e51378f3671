#!/usr/bin/env python3
"""
Script to update pricing plans with Stripe product and price IDs.

Usage:
    python update_stripe_ids.py

Environment variables:
    DATABASE_URL: PostgreSQL connection string
"""

import os
import sys
import json
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from uuid import UUID

# Check if running in Docker
if os.path.exists('/.dockerenv'):
    # Use the Docker database URL
    DATABASE_URL = os.environ.get('DATABASE_URL', '********************************************/fixmycal')
else:
    # Use the local database URL
    DATABASE_URL = os.environ.get('DATABASE_URL', 'postgresql://postgres:postgres@localhost:5432/fixmycal')

# Connect to the database
engine = create_engine(DATABASE_URL)
Session = sessionmaker(bind=engine)
session = Session()

# Stripe product and price IDs for each plan
# Replace these with your actual Stripe IDs
STRIPE_IDS = {
    'Starter': {
        'product_id': 'prod_YOUR_STARTER_PRODUCT_ID',
        'price_id': 'price_YOUR_STARTER_MONTHLY_PRICE_ID',
        'annual_price_id': 'price_YOUR_STARTER_ANNUAL_PRICE_ID'
    },
    'Professional': {
        'product_id': 'prod_YOUR_PROFESSIONAL_PRODUCT_ID',
        'price_id': 'price_YOUR_PROFESSIONAL_MONTHLY_PRICE_ID',
        'annual_price_id': 'price_YOUR_PROFESSIONAL_ANNUAL_PRICE_ID'
    }
}

def update_pricing_plan(plan_name, stripe_product_id, stripe_price_id, stripe_annual_price_id):
    """Update the pricing_plan table with Stripe IDs"""
    print(f"Updating pricing plan '{plan_name}' with Stripe IDs...")
    
    # Update the pricing plan
    result = session.execute(
        text("""
        UPDATE pricing_plans 
        SET stripe_product_id = :product_id, 
            stripe_price_id = :price_id,
            stripe_annual_price_id = :annual_price_id
        WHERE name = :plan_name
        RETURNING id
        """),
        {
            "product_id": stripe_product_id,
            "price_id": stripe_price_id,
            "annual_price_id": stripe_annual_price_id,
            "plan_name": plan_name
        }
    )
    
    # Check if any rows were updated
    plan_id = result.fetchone()
    if plan_id:
        print(f"Successfully updated pricing plan '{plan_name}' (ID: {plan_id[0]})")
    else:
        print(f"No pricing plan found with name '{plan_name}'")
    
    session.commit()

def main():
    """Main function"""
    print("Updating pricing plans with Stripe IDs...")
    
    # Get all pricing plans
    plans = session.execute(text("SELECT id, name FROM pricing_plans")).fetchall()
    
    if not plans:
        print("No pricing plans found in the database.")
        return
    
    print(f"Found {len(plans)} pricing plans:")
    for plan in plans:
        print(f"  - {plan[1]} (ID: {plan[0]})")
    
    print("\nEnter Stripe IDs for each plan:")
    
    for plan_name, ids in STRIPE_IDS.items():
        print(f"\n{plan_name} Plan:")
        product_id = input(f"  Product ID [{ids['product_id']}]: ") or ids['product_id']
        price_id = input(f"  Monthly Price ID [{ids['price_id']}]: ") or ids['price_id']
        annual_price_id = input(f"  Annual Price ID [{ids['annual_price_id']}]: ") or ids['annual_price_id']
        
        update_pricing_plan(plan_name, product_id, price_id, annual_price_id)
    
    print("\nDone!")

if __name__ == "__main__":
    main()
