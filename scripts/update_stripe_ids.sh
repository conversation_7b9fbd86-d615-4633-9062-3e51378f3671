#!/bin/bash

# <PERSON>ript to update Stripe product and price IDs in the database
# This script will prompt for Stripe IDs and update the pricing_plans table

# Always use Docker since we're running PostgreSQL in a container
PSQL_CMD="docker-compose exec -T postgres psql -U postgres -d fixmycal"

echo "Updating Stripe IDs for pricing plans..."

# Get all pricing plans
echo "Fetching pricing plans..."
$PSQL_CMD -c "SELECT id, name, monthly_fee FROM pricing_plans ORDER BY name"

# Update Starter plan
echo ""
echo "Enter Stripe IDs for Starter plan:"
read -p "Product ID: " starter_product_id
read -p "Monthly Price ID: " starter_price_id
read -p "Annual Price ID: " starter_annual_price_id

if [ -n "$starter_product_id" ] && [ -n "$starter_price_id" ]; then
  echo "Updating Starter plan..."
  $PSQL_CMD -c "UPDATE pricing_plans SET stripe_product_id = '$starter_product_id', stripe_price_id = '$starter_price_id', stripe_annual_price_id = '$starter_annual_price_id' WHERE name = 'Starter'"
fi

# Update Professional plan
echo ""
echo "Enter Stripe IDs for Professional plan:"
read -p "Product ID: " pro_product_id
read -p "Monthly Price ID: " pro_price_id
read -p "Annual Price ID: " pro_annual_price_id

if [ -n "$pro_product_id" ] && [ -n "$pro_price_id" ]; then
  echo "Updating Professional plan..."
  $PSQL_CMD -c "UPDATE pricing_plans SET stripe_product_id = '$pro_product_id', stripe_price_id = '$pro_price_id', stripe_annual_price_id = '$pro_annual_price_id' WHERE name = 'Professional'"
fi

echo ""
echo "Done! Verifying updates..."
$PSQL_CMD -c "SELECT id, name, monthly_fee, stripe_product_id, stripe_price_id, stripe_annual_price_id FROM pricing_plans ORDER BY name"
