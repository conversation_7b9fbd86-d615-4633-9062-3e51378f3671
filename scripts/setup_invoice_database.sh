#!/bin/bash
# <PERSON><PERSON>t to set up the database for invoice download testing

# Check if <PERSON><PERSON> is running
if ! docker info &> /dev/null; then
    echo "Docker is not running. Please start Docker first."
    exit 1
fi

# Check if the application is running
if ! docker-compose ps | grep -q "backend.*Up"; then
    echo "The application is not running. Please start it with 'docker-compose up -d' first."
    exit 1
fi

echo "Setting up the database for invoice download testing..."

# Copy the SQL script to the postgres container
echo "Copying SQL script to postgres container..."
docker-compose exec -T postgres mkdir -p /app/scripts
docker-compose cp backend/scripts/add_invoice_urls.sql postgres:/app/scripts/

# Run the SQL script
echo "Running SQL script to add new columns..."
docker-compose exec postgres psql -U postgres -d fixmycal -f /app/scripts/add_invoice_urls.sql

# Copy the Python script to the backend container
echo "Copying Python script to backend container..."
docker-compose exec -T backend mkdir -p /app/scripts
docker-compose cp backend/scripts/update_invoice_urls.py backend:/app/scripts/

# Run the Python script
echo "Running Python script to update existing invoices..."
docker-compose exec backend python /app/scripts/update_invoice_urls.py

echo "Database setup complete!"
echo ""
echo "You can now test the invoice download functionality in your application."
