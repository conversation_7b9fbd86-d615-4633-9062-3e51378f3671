--
-- PostgreSQL database dump
--

-- Dumped from database version 15.4 (Debian 15.4-2.pgdg120+1)
-- Dumped by pg_dump version 15.4 (Debian 15.4-2.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: vector; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS vector WITH SCHEMA public;


--
-- Name: EXTENSION vector; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION vector IS 'vector data type and ivfflat and hnsw access methods';


--
-- Name: appointmentstatus; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.appointmentstatus AS ENUM (
    'CONFIRMED',
    'CANCELLED',
    'PENDING'
);


ALTER TYPE public.appointmentstatus OWNER TO postgres;

--
-- Name: therapytype; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.therapytype AS ENUM (
    'POSTURAL',
    'TRAUMATOLOGY'
);


ALTER TYPE public.therapytype OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: ai_conversation_contexts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ai_conversation_contexts (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    client_id uuid NOT NULL,
    context_data json NOT NULL,
    updated_at timestamp with time zone
);


ALTER TABLE public.ai_conversation_contexts OWNER TO postgres;

--
-- Name: alembic_version; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.alembic_version (
    version_num character varying(32) NOT NULL
);


ALTER TABLE public.alembic_version OWNER TO postgres;

--
-- Name: appointments; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.appointments (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    client_id uuid NOT NULL,
    service_id uuid NOT NULL,
    start_time timestamp with time zone NOT NULL,
    end_time timestamp with time zone NOT NULL,
    status public.appointmentstatus,
    notes character varying,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone
);


ALTER TABLE public.appointments OWNER TO postgres;

--
-- Name: clients; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.clients (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    phone character varying,
    name character varying,
    email character varying,
    preferences json,
    created_at timestamp with time zone DEFAULT now(),
    last_appointment timestamp with time zone,
    next_appointment timestamp with time zone,
    tags jsonb,
    notes text
);


ALTER TABLE public.clients OWNER TO postgres;

--
-- Name: external_integrations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.external_integrations (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    integration_type character varying NOT NULL,
    credentials json NOT NULL,
    active boolean,
    last_synced timestamp with time zone
);


ALTER TABLE public.external_integrations OWNER TO postgres;

--
-- Name: invoices; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.invoices (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    plan_id uuid NOT NULL,
    period_start date NOT NULL,
    period_end date NOT NULL,
    total double precision NOT NULL,
    stripe_invoice_id character varying,
    status character varying NOT NULL
);


ALTER TABLE public.invoices OWNER TO postgres;

--
-- Name: messages; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.messages (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    client_id uuid NOT NULL,
    parent_id uuid,
    content text NOT NULL,
    is_from_business boolean,
    sent_at timestamp with time zone DEFAULT now(),
    embedding public.vector(384),
    intent_data json
);


ALTER TABLE public.messages OWNER TO postgres;

--
-- Name: notification_preferences; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notification_preferences (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    event_type character varying NOT NULL,
    email_enabled boolean,
    sms_enabled boolean,
    push_enabled boolean,
    reminder_minutes integer
);


ALTER TABLE public.notification_preferences OWNER TO postgres;

--
-- Name: pricing_plans; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.pricing_plans (
    id uuid NOT NULL,
    name character varying NOT NULL,
    cost_per_message double precision,
    cost_per_appointment double precision,
    monthly_fee double precision NOT NULL,
    features json
);


ALTER TABLE public.pricing_plans OWNER TO postgres;

--
-- Name: service_fisioterapeuta; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.service_fisioterapeuta (
    service_id uuid NOT NULL,
    tipus_terapia public.therapytype NOT NULL,
    a_domicili boolean
);


ALTER TABLE public.service_fisioterapeuta OWNER TO postgres;

--
-- Name: service_perruqueria; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.service_perruqueria (
    service_id uuid NOT NULL,
    usa_maquina boolean,
    tipus_tall character varying
);


ALTER TABLE public.service_perruqueria OWNER TO postgres;

--
-- Name: service_types; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.service_types (
    id uuid NOT NULL,
    name character varying NOT NULL
);


ALTER TABLE public.service_types OWNER TO postgres;

--
-- Name: services; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.services (
    id uuid NOT NULL,
    type_id uuid,
    user_id uuid,
    name character varying NOT NULL,
    description character varying,
    price double precision NOT NULL,
    duration_minutes integer NOT NULL,
    active boolean
);


ALTER TABLE public.services OWNER TO postgres;

--
-- Name: user_auth; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_auth (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    refresh_token character varying NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    device_info character varying,
    last_login timestamp with time zone DEFAULT now()
);


ALTER TABLE public.user_auth OWNER TO postgres;

--
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id uuid NOT NULL,
    email character varying NOT NULL,
    password_hash character varying NOT NULL,
    business_name character varying,
    currency character varying,
    registered_at timestamp with time zone DEFAULT now(),
    email_verified boolean,
    settings json
);


ALTER TABLE public.users OWNER TO postgres;

--
-- Data for Name: ai_conversation_contexts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.ai_conversation_contexts (id, user_id, client_id, context_data, updated_at) FROM stdin;
\.


--
-- Data for Name: alembic_version; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.alembic_version (version_num) FROM stdin;
38d603ece296
\.


--
-- Data for Name: appointments; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.appointments (id, user_id, client_id, service_id, start_time, end_time, status, notes, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: clients; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.clients (id, user_id, phone, name, email, preferences, created_at, last_appointment, next_appointment, tags, notes) FROM stdin;
7205dc56-cbc0-4861-ba2e-3041a9f6fb36	b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7	123456789	John Doe	<EMAIL>	\N	2025-04-04 08:03:16.440916+00	2024-01-15 10:00:00+00	2024-02-15 10:00:00+00	["Inactive"]	\N
a205dc56-cbc0-4861-ba2e-3041a9f6fb39	b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7	5552345678	Sarah Lee	<EMAIL>	\N	2025-04-04 08:03:16.440916+00	2024-01-12 11:00:00+00	2024-02-12 11:00:00+00	["Referral"]	"Referred by John Doe."
b305dc56-cbc0-4861-ba2e-3041a9f6fb40	b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7	5553456789	David Wilson	<EMAIL>	\N	2025-04-04 08:03:16.440916+00	2024-01-18 14:00:00+00	2024-02-18 14:00:00+00	["Corporate"]	"CEO of Local Corp. Schedule flexibility is important."
c405dc56-cbc0-4861-ba2e-3041a9f6fb41	b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7	5554567890	Jessica Taylor	<EMAIL>	\N	2025-04-04 08:03:16.440916+00	\N	2024-02-22 10:00:00+00	["Family"]	"First appointment scheduled. Found us through Google search."
d505dc56-cbc0-4861-ba2e-3041a9f6fb42	b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7	5555678901	Emily Johnson	<EMAIL>	\N	2025-04-04 08:03:16.440916+00	2024-01-05 10:00:00+00	2024-02-19 10:00:00+00	["Special Needs"]	"Referred by Jane Smith. First-time client discount applied."
8205dc56-cbc0-4861-ba2e-3041a9f6fb37	b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7	987654321	Jane Smith	<EMAIL>	\N	2025-04-04 08:03:16.440916+00	2024-01-20 10:00:00+00	2024-02-20 10:00:00+00	["VIP"]	"Prefers morning appointments."
9205dc56-cbc0-4861-ba2e-3041a9f6fb38	b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7	5551234567	Michael Brown	<EMAIL>	\N	2025-04-04 08:03:16.440916+00	2024-01-10 09:00:00+00	2024-02-10 09:00:00+00	["New Client"]	"Prefers afternoon appointments."
\.


--
-- Data for Name: external_integrations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.external_integrations (id, user_id, integration_type, credentials, active, last_synced) FROM stdin;
\.


--
-- Data for Name: invoices; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.invoices (id, user_id, plan_id, period_start, period_end, total, stripe_invoice_id, status) FROM stdin;
\.


--
-- Data for Name: messages; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.messages (id, user_id, client_id, parent_id, content, is_from_business, sent_at, embedding, intent_data) FROM stdin;
\.


--
-- Data for Name: notification_preferences; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.notification_preferences (id, user_id, event_type, email_enabled, sms_enabled, push_enabled, reminder_minutes) FROM stdin;
\.


--
-- Data for Name: pricing_plans; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.pricing_plans (id, name, cost_per_message, cost_per_appointment, monthly_fee, features) FROM stdin;
\.


--
-- Data for Name: service_fisioterapeuta; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.service_fisioterapeuta (service_id, tipus_terapia, a_domicili) FROM stdin;
\.


--
-- Data for Name: service_perruqueria; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.service_perruqueria (service_id, usa_maquina, tipus_tall) FROM stdin;
\.


--
-- Data for Name: service_types; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.service_types (id, name) FROM stdin;
\.


--
-- Data for Name: services; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.services (id, type_id, user_id, name, description, price, duration_minutes, active) FROM stdin;
\.


--
-- Data for Name: user_auth; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_auth (id, user_id, refresh_token, expires_at, device_info, last_login) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, email, password_hash, business_name, currency, registered_at, email_verified, settings) FROM stdin;
b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7	<EMAIL>	$2b$12$gkOlAW0RcceUTMFdkop96.BmHrleW4WbQyiTmPeZ2lViGdlAj.PC.	FixMyCal	EUR	2025-04-04 06:02:02.155271+00	f	{}
\.


--
-- Name: ai_conversation_contexts ai_conversation_contexts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ai_conversation_contexts
    ADD CONSTRAINT ai_conversation_contexts_pkey PRIMARY KEY (id);


--
-- Name: alembic_version alembic_version_pkc; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);


--
-- Name: appointments appointments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.appointments
    ADD CONSTRAINT appointments_pkey PRIMARY KEY (id);


--
-- Name: clients clients_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_pkey PRIMARY KEY (id);


--
-- Name: external_integrations external_integrations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.external_integrations
    ADD CONSTRAINT external_integrations_pkey PRIMARY KEY (id);


--
-- Name: invoices invoices_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_pkey PRIMARY KEY (id);


--
-- Name: messages messages_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_pkey PRIMARY KEY (id);


--
-- Name: notification_preferences notification_preferences_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notification_preferences
    ADD CONSTRAINT notification_preferences_pkey PRIMARY KEY (id);


--
-- Name: pricing_plans pricing_plans_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.pricing_plans
    ADD CONSTRAINT pricing_plans_pkey PRIMARY KEY (id);


--
-- Name: service_fisioterapeuta service_fisioterapeuta_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_fisioterapeuta
    ADD CONSTRAINT service_fisioterapeuta_pkey PRIMARY KEY (service_id);


--
-- Name: service_perruqueria service_perruqueria_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_perruqueria
    ADD CONSTRAINT service_perruqueria_pkey PRIMARY KEY (service_id);


--
-- Name: service_types service_types_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_types
    ADD CONSTRAINT service_types_pkey PRIMARY KEY (id);


--
-- Name: services services_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.services
    ADD CONSTRAINT services_pkey PRIMARY KEY (id);


--
-- Name: user_auth user_auth_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_auth
    ADD CONSTRAINT user_auth_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: ix_users_email; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_users_email ON public.users USING btree (email);


--
-- Name: ai_conversation_contexts ai_conversation_contexts_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ai_conversation_contexts
    ADD CONSTRAINT ai_conversation_contexts_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- Name: ai_conversation_contexts ai_conversation_contexts_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ai_conversation_contexts
    ADD CONSTRAINT ai_conversation_contexts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: appointments appointments_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.appointments
    ADD CONSTRAINT appointments_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- Name: appointments appointments_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.appointments
    ADD CONSTRAINT appointments_service_id_fkey FOREIGN KEY (service_id) REFERENCES public.services(id);


--
-- Name: appointments appointments_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.appointments
    ADD CONSTRAINT appointments_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: clients clients_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: external_integrations external_integrations_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.external_integrations
    ADD CONSTRAINT external_integrations_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: invoices invoices_plan_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_plan_id_fkey FOREIGN KEY (plan_id) REFERENCES public.pricing_plans(id);


--
-- Name: invoices invoices_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: messages messages_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- Name: messages messages_parent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.messages(id);


--
-- Name: messages messages_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: notification_preferences notification_preferences_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notification_preferences
    ADD CONSTRAINT notification_preferences_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: service_fisioterapeuta service_fisioterapeuta_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_fisioterapeuta
    ADD CONSTRAINT service_fisioterapeuta_service_id_fkey FOREIGN KEY (service_id) REFERENCES public.services(id);


--
-- Name: service_perruqueria service_perruqueria_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_perruqueria
    ADD CONSTRAINT service_perruqueria_service_id_fkey FOREIGN KEY (service_id) REFERENCES public.services(id);


--
-- Name: services services_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.services
    ADD CONSTRAINT services_type_id_fkey FOREIGN KEY (type_id) REFERENCES public.service_types(id);


--
-- Name: services services_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.services
    ADD CONSTRAINT services_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- PostgreSQL database dump complete
--

