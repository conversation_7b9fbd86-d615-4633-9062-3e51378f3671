#!/bin/bash

# Database backup and restore script for FixMyCalSaaS
# This script handles database backups and restoration with schema migration

# Configuration
DB_NAME="fixmycal"
DB_USER="postgres"
BACKUP_DIR="./backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="${BACKUP_DIR}/${DB_NAME}_${TIMESTAMP}.sql"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Function to display usage information
show_usage() {
  echo "Usage: $0 [backup|restore|auto-backup] [backup_file]"
  echo ""
  echo "Commands:"
  echo "  backup        Create a new database backup"
  echo "  restore       Restore database from a backup file"
  echo "  auto-backup   Set up automatic backups (requires crontab)"
  echo ""
  echo "Examples:"
  echo "  $0 backup                     # Create a new backup"
  echo "  $0 restore backups/fixmycal_20250413_181500.sql  # Restore from a specific backup"
  echo "  $0 auto-backup                # Set up automatic daily backups"
}

# Function to create a database backup
create_backup() {
  echo "Creating backup of $DB_NAME database..."

  # Create a full database dump
  docker-compose exec -T postgres pg_dump -U $DB_USER $DB_NAME > $BACKUP_FILE

  if [ $? -eq 0 ]; then
    echo "Backup created successfully: $BACKUP_FILE"
    echo "Backup size: $(du -h $BACKUP_FILE | cut -f1)"

    # Create a compressed version
    gzip -c $BACKUP_FILE > "${BACKUP_FILE}.gz"
    echo "Compressed backup created: ${BACKUP_FILE}.gz"
    echo "Compressed size: $(du -h ${BACKUP_FILE}.gz | cut -f1)"

    # Keep only the 10 most recent backups
    echo "Cleaning up old backups..."
    OLD_SQL_BACKUPS=$(ls -t ${BACKUP_DIR}/${DB_NAME}_*.sql 2>/dev/null | tail -n +11)
    if [ -n "$OLD_SQL_BACKUPS" ]; then
      echo "$OLD_SQL_BACKUPS" | xargs rm
    fi

    OLD_GZ_BACKUPS=$(ls -t ${BACKUP_DIR}/${DB_NAME}_*.sql.gz 2>/dev/null | tail -n +11)
    if [ -n "$OLD_GZ_BACKUPS" ]; then
      echo "$OLD_GZ_BACKUPS" | xargs rm
    fi

    return 0
  else
    echo "Error: Backup failed!"
    return 1
  fi
}

# Function to restore a database from backup
restore_database() {
  local backup_file=$1

  if [ ! -f "$backup_file" ]; then
    echo "Error: Backup file not found: $backup_file"
    return 1
  fi

  echo "Restoring database from backup: $backup_file"
  echo "WARNING: This will overwrite the current database. All current data will be lost."
  echo "Press ENTER to continue or CTRL+C to cancel..."
  read

  # Drop and recreate the database
  echo "Dropping existing database..."
  docker-compose exec -T postgres psql -U $DB_USER -c "DROP DATABASE IF EXISTS ${DB_NAME}_temp;"
  docker-compose exec -T postgres psql -U $DB_USER -c "CREATE DATABASE ${DB_NAME}_temp;"

  # Restore the backup to the temporary database
  echo "Restoring backup to temporary database..."
  if [[ "$backup_file" == *.gz ]]; then
    # If the backup is compressed, decompress it on the fly
    gunzip -c "$backup_file" | docker-compose exec -T postgres psql -U $DB_USER -d ${DB_NAME}_temp
  else
    # Otherwise, restore directly
    cat "$backup_file" | docker-compose exec -T postgres psql -U $DB_USER -d ${DB_NAME}_temp
  fi

  if [ $? -ne 0 ]; then
    echo "Error: Failed to restore backup!"
    docker-compose exec -T postgres psql -U $DB_USER -c "DROP DATABASE IF EXISTS ${DB_NAME}_temp;"
    return 1
  fi

  # Apply migrations to the temporary database
  echo "Applying database migrations..."
  DB_NAME=${DB_NAME}_temp docker-compose exec backend alembic upgrade head

  if [ $? -ne 0 ]; then
    echo "Error: Failed to apply migrations to the temporary database!"
    docker-compose exec -T postgres psql -U $DB_USER -c "DROP DATABASE IF EXISTS ${DB_NAME}_temp;"
    return 1
  fi

  # Swap the databases
  echo "Swapping databases..."
  docker-compose exec -T postgres psql -U $DB_USER -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '$DB_NAME';"
  docker-compose exec -T postgres psql -U $DB_USER -c "DROP DATABASE IF EXISTS ${DB_NAME}_old;"
  docker-compose exec -T postgres psql -U $DB_USER -c "ALTER DATABASE $DB_NAME RENAME TO ${DB_NAME}_old;"
  docker-compose exec -T postgres psql -U $DB_USER -c "ALTER DATABASE ${DB_NAME}_temp RENAME TO $DB_NAME;"

  echo "Database restored successfully!"
  echo "The old database is available as ${DB_NAME}_old if needed."

  # Restart the backend to ensure it connects to the new database
  echo "Restarting backend service..."
  docker-compose restart backend

  return 0
}

# Function to set up automatic backups
setup_auto_backup() {
  echo "Setting up automatic daily backups..."

  # Get the absolute path to this script
  SCRIPT_PATH=$(realpath "$0")

  # Create a cron job for daily backups at 2 AM
  (crontab -l 2>/dev/null || echo "") | grep -v "$SCRIPT_PATH backup" | \
    { cat; echo "0 2 * * * $SCRIPT_PATH backup > /tmp/db_backup.log 2>&1"; } | \
    crontab -

  if [ $? -eq 0 ]; then
    echo "Automatic daily backups scheduled at 2 AM."
    echo "Backup logs will be written to /tmp/db_backup.log"
    return 0
  else
    echo "Error: Failed to set up automatic backups!"
    return 1
  fi
}

# Main script logic
case "$1" in
  backup)
    create_backup
    ;;
  restore)
    if [ -z "$2" ]; then
      echo "Error: No backup file specified for restore."
      show_usage
      exit 1
    fi
    restore_database "$2"
    ;;
  auto-backup)
    setup_auto_backup
    ;;
  *)
    show_usage
    exit 1
    ;;
esac

exit $?
