#!/bin/bash

# <PERSON>ript to update Stripe product and price IDs in the database
# This script uses the provided IDs to update the pricing_plans table

# Starter plan IDs
STARTER_PRODUCT_ID="prod_Rj03th4Q7Ymn5I"
STARTER_MONTHLY_PRICE_ID="price_1RQp6MB2Zxp0l7taNXiFYFnb"
STARTER_ANNUAL_PRICE_ID="price_1RQp1AB2Zxp0l7taYsUIR3WT"

# Professional plan IDs
PROFESSIONAL_PRODUCT_ID="prod_SLVfH2BtcC7msI"
PROFESSIONAL_MONTHLY_PRICE_ID="price_1RQoeTB2Zxp0l7taK9bRnbRx"
PROFESSIONAL_ANNUAL_PRICE_ID="price_1RQoeTB2Zxp0l7ta9zZxKDTO"

echo "Updating Stripe IDs for pricing plans..."

# Update Starter plan
echo "Updating Starter plan..."
docker-compose exec -T postgres psql -U postgres -d fixmycal -c "UPDATE pricing_plans SET stripe_product_id = '$STARTER_PRODUCT_ID', stripe_price_id = '$STARTER_MONTHLY_PRICE_ID', stripe_annual_price_id = '$STARTER_ANNUAL_PRICE_ID' WHERE name = 'Starter'"

# Update Professional plan
echo "Updating Professional plan..."
docker-compose exec -T postgres psql -U postgres -d fixmycal -c "UPDATE pricing_plans SET stripe_product_id = '$PROFESSIONAL_PRODUCT_ID', stripe_price_id = '$PROFESSIONAL_MONTHLY_PRICE_ID', stripe_annual_price_id = '$PROFESSIONAL_ANNUAL_PRICE_ID' WHERE name = 'Professional'"

echo "Done! Verifying updates..."
docker-compose exec -T postgres psql -U postgres -d fixmycal -c "SELECT id, name, monthly_fee, stripe_product_id, stripe_price_id, stripe_annual_price_id FROM pricing_plans ORDER BY name"
