# WhatsApp Integration in FixMyCalSaaS

This document explains how WhatsApp integration works in the FixMyCalSaaS application.

## Overview

The WhatsApp integration uses Evolution API to connect to WhatsApp and handle messages. The integration allows users to:

1. Connect their WhatsApp account to the application
2. Send and receive messages
3. Use AI to automatically respond to messages
4. Book appointments through WhatsApp

## Key Components

### Frontend

- **WhatsApp Service**: `src/services/whatsappServiceSimplified.ts`
  - Provides methods for connecting to WhatsApp, checking status, and sending messages
  - Combines the functionality of the original whatsappService.ts and evolutionApiService.ts

- **WhatsApp Module**: `src/services/whatsapp/`
  - Contains the core functionality for WhatsApp integration
  - Handles connections, messages, contacts, and media

- **WebSocket Integration**: `src/services/whatsapp/socketio.ts`
  - Provides real-time updates for WhatsApp messages
  - Uses Socket.io to connect to Evolution API

- **Messages Page**: `src/pages/dashboard/MessagesPage.tsx`
  - Displays WhatsApp conversations and messages
  - Allows sending messages and managing conversations

### Backend

- **WhatsApp Routes**: `backend/app/routes/whatsapp.py`
  - API endpoints for WhatsApp integration
  - Handles webhook events from Evolution API

- **WhatsApp Service**: `backend/app/services/whatsapp_service.py`
  - Backend service for WhatsApp integration
  - Handles message processing and AI integration

## Flow of Messages

1. **Incoming Messages**:
   - A client sends a message to your WhatsApp number
   - Evolution API receives the message and sends a webhook event to your backend
   - The backend processes the message, saves it to the database, and uses the AI assistant to generate a response
   - The AI assistant analyzes the message, generates a response, and extracts any actions (like booking appointments)
   - The backend sends the response back to the client via Evolution API
   - The frontend receives a real-time update via WebSocket or polling and updates the UI

2. **Outgoing Messages**:
   - You send a message from the Messages page
   - The frontend sends the message to the backend
   - The backend sends the message to the client via Evolution API
   - The frontend updates the UI with the sent message

## AI Integration

The AI integration works as follows:

1. When a message is received from a client, it's processed by the AI assistant
2. The AI assistant analyzes the message and generates a response
3. If the message contains an intent to book an appointment, the AI extracts the details and creates an appointment
4. The AI response is sent back to the client via WhatsApp

## Troubleshooting

If WhatsApp integration is not working:

1. Check if the WhatsApp connection is active in the Evolution API dashboard
2. Check if the webhook is properly configured in Evolution API
3. Check if auto-responses are enabled in the AI configuration
4. Check the logs for any errors related to WhatsApp integration
