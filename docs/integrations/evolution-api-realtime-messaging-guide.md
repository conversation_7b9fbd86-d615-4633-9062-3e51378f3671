# Real-time Messaging with Evolution API

This guide focuses on implementing real-time messaging functionality in your application using Evolution API v2, with a particular emphasis on the MessagesPage implementation in your project.

## Table of Contents

1. [Introduction](#introduction)
2. [Architecture Overview](#architecture-overview)
3. [Setting Up Real-time Messaging](#setting-up-real-time-messaging)
4. [Implementing the Backend](#implementing-the-backend)
5. [Implementing the Frontend](#implementing-the-frontend)
6. [Advanced Features](#advanced-features)
7. [Performance Optimization](#performance-optimization)
8. [Troubleshooting](#troubleshooting)

## Introduction

Real-time messaging is a critical feature for modern applications, allowing users to send and receive messages instantly. The Evolution API provides powerful capabilities for implementing real-time messaging through webhooks and WebSockets.

### Benefits of Real-time Messaging

- **Instant Communication**: Users can send and receive messages without refreshing the page
- **Better User Experience**: Real-time updates create a more engaging and responsive application
- **Reduced Server Load**: Eliminates the need for frequent polling
- **Scalability**: Efficiently handles high message volumes

## Architecture Overview

A typical real-time messaging architecture with Evolution API consists of:

1. **WhatsApp Connection**: Evolution API connects to WhatsApp and manages the connection
2. **Webhook Receiver**: Your backend receives webhook events from Evolution API
3. **WebSocket Server**: Your backend forwards events to connected clients via WebSockets
4. **Frontend Client**: Your React application displays messages and handles user interactions

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   WhatsApp  │◄───►│  Evolution  │────►│  Your       │◄───►│  React      │
│   Servers   │     │  API        │     │  Backend    │     │  Frontend   │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
                          │                    ▲
                          │                    │
                          ▼                    │
                    ┌─────────────┐           │
                    │  Webhook    │───────────┘
                    │  Events     │
                    └─────────────┘
```

## Setting Up Real-time Messaging

### Prerequisites

- Evolution API server running
- Backend server with webhook endpoint
- Frontend application with WebSocket support

### Step 1: Configure Webhook in Evolution API

```bash
curl --request POST \
  --url http://localhost:8080/webhook/set/{instance} \
  --header 'Content-Type: application/json' \
  --header 'apikey: your-api-key' \
  --data '{
  "enabled": true,
  "url": "https://your-backend.com/whatsapp/webhook/{user_id}",
  "webhookByEvents": true,
  "webhookBase64": true,
  "events": [
    "MESSAGES_UPSERT",
    "MESSAGES_UPDATE",
    "CONNECTION_UPDATE"
  ]
}'
```

### Step 2: Set Up WebSocket Server in Your Backend

Your backend needs to:
1. Receive webhook events from Evolution API
2. Process these events
3. Forward relevant events to connected clients via WebSockets

## Implementing the Backend

### Webhook Handler

```python
# In your FastAPI backend
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from typing import Dict, List
import json

app = FastAPI()

# Store active WebSocket connections
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        if user_id not in self.active_connections:
            self.active_connections[user_id] = []
        self.active_connections[user_id].append(websocket)

    def disconnect(self, websocket: WebSocket, user_id: str):
        if user_id in self.active_connections:
            self.active_connections[user_id].remove(websocket)

    async def send_message(self, message: dict, user_id: str):
        if user_id in self.active_connections:
            for connection in self.active_connections[user_id]:
                await connection.send_json(message)

manager = ConnectionManager()

# WebSocket endpoint
@app.websocket("/whatsapp/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    await manager.connect(websocket, user_id)
    try:
        while True:
            # Keep the connection alive
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)

# Webhook endpoint
@app.post("/whatsapp/webhook/{user_id}")
async def whatsapp_webhook(user_id: str, request: Request):
    # Get the webhook data
    webhook_data = await request.json()
    event_type = webhook_data.get('event')
    
    print(f"Received webhook for user {user_id}, event: {event_type}")
    
    # Process based on event type
    if event_type == "MESSAGES_UPSERT":
        # Process new messages
        await process_new_messages(user_id, webhook_data)
    elif event_type == "MESSAGES_UPDATE":
        # Process message updates
        await process_message_updates(user_id, webhook_data)
    elif event_type == "CONNECTION_UPDATE":
        # Process connection updates
        await process_connection_update(user_id, webhook_data)
        
    return {"success": True}

async def process_new_messages(user_id: str, webhook_data: dict):
    # Extract message data
    messages = webhook_data.get('data', {}).get('messages', [])
    
    for message_data in messages:
        # Skip messages sent by the business
        if message_data.get('key', {}).get('fromMe', False):
            continue
            
        # Extract the phone number
        remote_jid = message_data.get('key', {}).get('remoteJid', '')
        if not remote_jid or '@' not in remote_jid:
            continue
            
        phone = remote_jid.split('@')[0]
        
        # Extract the message content
        message_content = ''
        if 'conversation' in message_data.get('message', {}):
            message_content = message_data['message']['conversation']
        elif 'extendedTextMessage' in message_data.get('message', {}):
            message_content = message_data['message']['extendedTextMessage'].get('text', '')
            
        if not phone or not message_content:
            continue
            
        # Send a notification via WebSocket
        await manager.send_message({
            "type": "new_message",
            "data": {
                "contact_id": f"whatsapp-{phone}",
                "message": {
                    "id": message_data.get('key', {}).get('id', ''),
                    "text": message_content,
                    "timestamp": message_data.get('messageTimestamp', ''),
                    "sender": "them"
                }
            }
        }, user_id)
```

### Database Integration

To persist messages, you'll need to save them to your database:

```python
async def process_new_messages(user_id: str, webhook_data: dict, db: Session):
    # ... (previous code)
    
    # Save the message to the database
    new_message = Message(
        user_id=user_id,
        client_id=client_id,  # You need to find or create the client
        content=message_content,
        is_from_business=False,
        sent_at=datetime.fromtimestamp(message_data.get('messageTimestamp', 0)),
        source="whatsapp"
    )
    db.add(new_message)
    db.commit()
    
    # ... (rest of the code)
```

## Implementing the Frontend

### WebSocket Connection in React

```tsx
// In your MessagesPage.tsx
import React, { useEffect, useState, useCallback } from 'react';

const MessagesPage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [usingWebSocket, setUsingWebSocket] = useState(false);
  
  // Set up WebSocket connection
  useEffect(() => {
    // Get user ID from token
    const token = localStorage.getItem('accessToken');
    if (!token) {
      console.warn('No authentication token found');
      return;
    }

    try {
      // Extract user ID from JWT token
      const tokenData = JSON.parse(atob(token.split('.')[1]));
      const userId = tokenData.sub;

      // Create WebSocket connection
      const wsUrl = `${import.meta.env.VITE_WS_URL || 'ws://localhost:8000'}/whatsapp/ws/${userId}`;
      console.log(`Connecting to WebSocket at ${wsUrl}`);

      const socket = new WebSocket(wsUrl);

      socket.onopen = () => {
        console.log('WebSocket connection established');
        setUsingWebSocket(true);
      };

      socket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        console.log('WebSocket message received:', data);

        if (data.type === 'new_message') {
          handleWebSocketMessage(data.data);
        } else if (data.type === 'contact_update') {
          refreshContacts();
        }
      };

      socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        setUsingWebSocket(false);
        setupPolling(); // Fall back to polling
      };

      socket.onclose = () => {
        console.log('WebSocket connection closed');
        setUsingWebSocket(false);
        setupPolling(); // Fall back to polling
      };

      // Clean up on unmount
      return () => {
        if (socket && socket.readyState === WebSocket.OPEN) {
          socket.close();
        }
      };
    } catch (error) {
      console.error('Error setting up WebSocket:', error);
      setupPolling(); // Fall back to polling
    }
  }, []);
  
  // Function to handle messages from WebSocket
  const handleWebSocketMessage = useCallback((data: any) => {
    const { contact_id, message } = data;

    // If this is for the currently selected contact, add it to the messages
    if (selectedContact?.id === contact_id) {
      setMessages(prev => {
        // Check if we already have this message (avoid duplicates)
        const exists = prev.some(m => m.id === message.id);
        if (exists) return prev;
        return [...prev, message];
      });
    } else {
      // If it's for a different contact, refresh contacts to update the list
      refreshContacts();
    }
  }, [selectedContact, refreshContacts]);
  
  // Set up polling as fallback
  const setupPolling = useCallback(() => {
    // Polling implementation
    // ...
  }, []);
  
  // Rest of your component
  // ...
};

export default MessagesPage;
```

### Sending Messages

```tsx
const sendMessage = async (text: string) => {
  if (!selectedContact || !text.trim()) return;
  
  // Extract phone number from contact ID
  let phone = selectedContact.phone;
  if (selectedContact.id.startsWith('whatsapp-')) {
    phone = selectedContact.id.replace('whatsapp-', '');
  }
  
  try {
    // Create a temporary message
    const tempMessage: Message = {
      id: `temp-${Date.now()}`,
      text,
      timestamp: new Date().toISOString(),
      sender: 'me',
      status: 'sending'
    };
    
    // Add to messages immediately for UI responsiveness
    setMessages(prev => [...prev, tempMessage]);
    
    // Send the message via API
    const response = await fetch(`${import.meta.env.VITE_API_URL}/whatsapp/send-message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
      },
      body: JSON.stringify({
        phone,
        text
      })
    });
    
    const data = await response.json();
    
    if (data.success) {
      // Update the temporary message with the real message ID
      setMessages(prev => prev.map(m => 
        m.id === tempMessage.id 
          ? { ...m, id: data.messageId, status: 'sent' } 
          : m
      ));
    } else {
      // Mark as failed
      setMessages(prev => prev.map(m => 
        m.id === tempMessage.id 
          ? { ...m, status: 'failed' } 
          : m
      ));
    }
  } catch (error) {
    console.error('Error sending message:', error);
    // Mark as failed
    setMessages(prev => prev.map(m => 
      m.id === `temp-${Date.now()}` 
        ? { ...m, status: 'failed' } 
        : m
    ));
  }
};
```

## Advanced Features

### Message Status Updates

To display message status (sent, delivered, read), process the `MESSAGES_UPDATE` events:

```python
async def process_message_updates(user_id: str, webhook_data: dict):
    # Extract message data
    updates = webhook_data.get('data', [])
    
    for update in updates:
        message_key = update.get('key', {})
        update_data = update.get('update', {})
        
        # Only process updates for messages sent by the business
        if not message_key.get('fromMe', False):
            continue
            
        # Extract the phone number
        remote_jid = message_key.get('remoteJid', '')
        if not remote_jid or '@' not in remote_jid:
            continue
            
        phone = remote_jid.split('@')[0]
        message_id = message_key.get('id', '')
        status = update_data.get('status', '')
        
        # Map WhatsApp status to your application status
        status_mapping = {
            'ERROR': 'failed',
            'PENDING': 'sent',
            'SERVER_ACK': 'sent',
            'DELIVERY_ACK': 'delivered',
            'READ': 'read',
            'PLAYED': 'read'
        }
        
        mapped_status = status_mapping.get(status, 'sent')
        
        # Send a notification via WebSocket
        await manager.send_message({
            "type": "message_update",
            "data": {
                "contact_id": f"whatsapp-{phone}",
                "message": {
                    "id": message_id,
                    "status": mapped_status
                }
            }
        }, user_id)
```

### Typing Indicators

To show typing indicators, use the `PRESENCE_UPDATE` events:

```python
async def process_presence_update(user_id: str, webhook_data: dict):
    # Extract presence data
    presence = webhook_data.get('data', {})
    
    remote_jid = presence.get('id', '')
    if not remote_jid or '@' not in remote_jid:
        return
        
    phone = remote_jid.split('@')[0]
    presence_type = presence.get('type', '')
    
    # Send a notification via WebSocket
    await manager.send_message({
        "type": "presence_update",
        "data": {
            "contact_id": f"whatsapp-{phone}",
            "presence": presence_type  # 'composing', 'available', etc.
        }
    }, user_id)
```

### Media Messages

To handle media messages (images, videos, documents), process them in the webhook handler:

```python
async def process_new_messages(user_id: str, webhook_data: dict, db: Session):
    # ... (previous code)
    
    # Extract media content if available
    media_url = None
    media_type = None
    
    if 'imageMessage' in message_data.get('message', {}):
        media_type = 'image'
        # If webhookBase64 is enabled, you'll get the image in base64
        if 'jpegThumbnail' in message_data['message']['imageMessage']:
            base64_data = message_data['message']['imageMessage']['jpegThumbnail']
            media_url = await save_base64_media(base64_data, f"image_{message_id}.jpg")
    elif 'videoMessage' in message_data.get('message', {}):
        media_type = 'video'
        # Handle video similarly
    elif 'documentMessage' in message_data.get('message', {}):
        media_type = 'document'
        # Handle document similarly
    
    # Include media information in the WebSocket message
    if media_url and media_type:
        await manager.send_message({
            "type": "new_message",
            "data": {
                "contact_id": f"whatsapp-{phone}",
                "message": {
                    "id": message_id,
                    "text": message_content,
                    "timestamp": message_data.get('messageTimestamp', ''),
                    "sender": "them",
                    "media": {
                        "type": media_type,
                        "url": media_url
                    }
                }
            }
        }, user_id)
    else:
        # Send text message as before
        # ...
```

## Performance Optimization

### Efficient Message Loading

When loading message history, use pagination to avoid loading too many messages at once:

```tsx
const loadMessages = async (contactId: string, limit = 50, before?: string) => {
  try {
    const response = await fetch(
      `${import.meta.env.VITE_API_URL}/messages?contactId=${contactId}&limit=${limit}${before ? `&before=${before}` : ''}`,
      {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      }
    );
    
    const data = await response.json();
    
    if (before) {
      // Prepend older messages
      setMessages(prev => [...data.messages, ...prev]);
    } else {
      // Set initial messages
      setMessages(data.messages);
    }
    
    setHasMoreMessages(data.messages.length === limit);
  } catch (error) {
    console.error('Error loading messages:', error);
  }
};
```

### Message Deduplication

To avoid duplicate messages when using both WebSockets and polling:

```tsx
const addMessage = (newMessage: Message) => {
  setMessages(prev => {
    // Check if the message already exists
    const exists = prev.some(m => m.id === newMessage.id);
    if (exists) return prev;
    
    // Add the new message
    return [...prev, newMessage];
  });
};
```

### Connection Management

Handle WebSocket reconnection gracefully:

```tsx
const [reconnectAttempts, setReconnectAttempts] = useState(0);
const maxReconnectAttempts = 5;
const reconnectInterval = 5000; // 5 seconds

const connectWebSocket = useCallback(() => {
  // ... (WebSocket connection code)
  
  socket.onclose = () => {
    console.log('WebSocket connection closed');
    setUsingWebSocket(false);
    
    // Try to reconnect if not at max attempts
    if (reconnectAttempts < maxReconnectAttempts) {
      setTimeout(() => {
        setReconnectAttempts(prev => prev + 1);
        connectWebSocket();
      }, reconnectInterval);
    } else {
      // Fall back to polling
      setupPolling();
    }
  };
}, [reconnectAttempts]);
```

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failures**
   - Check that your backend WebSocket server is running
   - Verify that the WebSocket URL is correct
   - Ensure that your server supports WebSocket connections

2. **Missing Messages**
   - Verify that your webhook is configured correctly
   - Check that your webhook handler is processing all message types
   - Ensure that your database is storing messages correctly

3. **Duplicate Messages**
   - Implement message deduplication in your frontend
   - Check that your webhook handler is not processing the same message multiple times

### Debugging Tips

1. **Enable Verbose Logging**
   ```tsx
   // In your frontend
   const DEBUG = true;
   
   const logDebug = (message: string, data?: any) => {
     if (DEBUG) {
       console.log(`[DEBUG] ${message}`, data);
     }
   };
   
   // Use throughout your code
   logDebug('WebSocket message received', data);
   ```

2. **Monitor WebSocket Status**
   ```tsx
   // Add a status indicator to your UI
   <div className="connection-status">
     {usingWebSocket ? 'Real-time: Connected' : 'Real-time: Disconnected (using polling)'}
   </div>
   ```

3. **Implement Fallback Mechanisms**
   - Always have a polling fallback for when WebSockets fail
   - Implement retry logic for failed API calls

## Conclusion

Implementing real-time messaging with Evolution API provides a powerful and responsive user experience. By combining webhooks, WebSockets, and efficient frontend code, you can create a messaging application that feels instantaneous and reliable.

Remember to:
- Configure webhooks to receive real-time updates
- Set up a WebSocket server to forward events to clients
- Implement efficient message handling in your frontend
- Add fallback mechanisms for when real-time connections fail
- Optimize for performance and reliability

With these components in place, your application will provide a seamless messaging experience that rivals dedicated messaging platforms.
