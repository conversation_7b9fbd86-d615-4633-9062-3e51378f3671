# AI Messaging System

This document explains the AI messaging system implemented in FixMyCalSaaS, which allows clients to interact with an AI assistant for answering questions and booking appointments.

## Overview

The AI messaging system consists of several components:

1. **Vector Database**: Uses PostgreSQL with pgvector extension to store message embeddings for semantic search
2. **AI Assistant**: Processes client messages and generates responses using Google's Gemini API
3. **Message Storage**: Stores all messages with vector embeddings for retrieval
4. **Conversation Interface**: Provides a UI for viewing and interacting with client conversations

## Setup Requirements

To use the AI messaging system, you need:

1. PostgreSQL with pgvector extension installed
2. A Google Gemini API key (add to your .env file)
3. The backend and frontend services running
4. The httpx Python package for making API requests

### Testing the Gemini Integration

Before using the AI messaging system, you can test your Gemini API key with the provided test script:

```bash
./scripts/test_gemini_docker.sh
```

This script tests both the text generation and embeddings APIs to ensure your API key is working correctly.

## How It Works

### Message Processing Flow

1. Client sends a message through WhatsApp or the app
2. Message is stored in the database and embedded as a vector
3. AI assistant processes the message and generates a response
4. Response is stored in the database with its vector embedding
5. Response is sent back to the client

### Vector Search

The system uses vector embeddings to enable semantic search across all messages:

1. Each message is embedded using OpenAI's text-embedding model
2. Embeddings are stored in a vector column in the database
3. When searching, the search query is embedded and compared to stored embeddings
4. Results are ranked by similarity and returned to the user

### Appointment Booking

The AI assistant can detect appointment booking intent and help clients book appointments:

1. When a booking intent is detected, available time slots are retrieved
2. The AI guides the client through selecting a service and time
3. Once confirmed, the appointment is created in the system
4. The appointment is also added to Google Calendar if integrated

## Configuration

### Gemini API Key

Add your Google Gemini API key to the `.env` file:

```
GEMINI_API_KEY=your-api-key-here
```

### Vector Database Setup

The vector database is automatically set up when the application starts. It:

1. Creates the pgvector extension if not already installed
2. Creates a vector index on the messages table for efficient similarity search

## Usage

### Viewing Conversations

1. Navigate to the Messages page in the dashboard
2. Select a client from the left sidebar
3. View the conversation history and send new messages

### Searching Messages

1. On the Messages page, use the search box in the conversation header
2. Enter your search query and press Enter
3. View semantically similar messages ranked by relevance

### Monitoring AI Conversations

As a business owner, you can:

1. View all AI conversations with clients
2. Intervene in conversations when needed by sending your own messages
3. See when appointments are booked through the AI assistant

## Customization

### AI Assistant Behavior

You can customize the AI assistant's behavior by modifying:

1. The system prompt in `backend/app/services/ai_assistant.py`
2. The embedding model in `backend/app/services/embedding_service.py`
3. The message templates for appointment reminders and confirmations

## Troubleshooting

### Common Issues

1. **AI not responding**: Check your Gemini API key and quota
2. **Vector search not working**: Ensure pgvector extension is properly installed
3. **Missing embeddings**: Check that the embedding service is working correctly

### Logs

Check the application logs for errors related to the AI messaging system:

```bash
docker-compose logs backend
```

## Security Considerations

The AI messaging system includes several security measures:

1. Messages are associated with specific users and clients
2. Authentication is required for all API endpoints
3. Rate limiting prevents abuse of the Gemini API
4. Input validation prevents injection attacks

## Future Enhancements

Planned enhancements for the AI messaging system:

1. Fine-tuning the AI model on domain-specific data
2. Adding support for more languages
3. Implementing more sophisticated intent detection
4. Adding analytics for conversation insights
