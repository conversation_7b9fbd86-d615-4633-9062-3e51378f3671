# Integrations Documentation

This section contains documentation for the external integrations used in FixMyCalSaaS.

## Contents

- [AI Messaging](./ai_messaging.md) - Documentation for AI messaging integration
- [Evolution API Integration Architecture](./evolution-api-integration-architecture.md) - Overview of the Evolution API integration
- [Evolution API Quick Reference](./evolution-api-quick-reference.md) - Quick reference for Evolution API
- [Evolution API Realtime Messaging Guide](./evolution-api-realtime-messaging-guide.md) - Guide for realtime messaging with Evolution API
- [Evolution API v2 Comprehensive Reference](./evolution-api-v2-comprehensive-reference.md) - Comprehensive reference for Evolution API v2
- [Evolution API v2 Reference](./evolution-api-v2-reference.md) - Reference for Evolution API v2
- [Evolution API Webhook Guide](./evolution-api-webhook-guide.md) - Guide for Evolution API webhooks
- [Evolution API Webhook Implementation Guide](./evolution-api-webhook-implementation-guide.md) - Guide for implementing Evolution API webhooks
- [Evolution API Webhook Implementation](./evolution-api-webhook-implementation.md) - Implementation details for Evolution API webhooks
- [WhatsApp README](./README_WHATSAPP.md) - General information about WhatsApp integration

## Integration Overview

FixMyCalSaaS integrates with several external services:

1. **WhatsApp (via Evolution API)**: For client messaging and notifications
2. **Google Calendar**: For appointment synchronization
3. **Stripe**: For payment processing and subscription management
4. **Google Gemini AI**: For natural language processing and automated responses

These integrations enable the core functionality of the application, allowing for seamless communication with clients, calendar management, and automated responses.
