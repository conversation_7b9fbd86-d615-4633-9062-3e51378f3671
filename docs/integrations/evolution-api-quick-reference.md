# Evolution API v2 Quick Reference Guide

This quick reference guide provides the most commonly used Evolution API v2 endpoints for your application, with examples and code snippets.

## Table of Contents

1. [Authentication](#authentication)
2. [Instance Management](#instance-management)
3. [Sending Messages](#sending-messages)
4. [Receiving Messages](#receiving-messages)
5. [Contact Management](#contact-management)
6. [Webhook Configuration](#webhook-configuration)
7. [Common Patterns](#common-patterns)

## Authentication

All API requests require an API key in the header:

```
apikey: your_api_key_here
```

## Instance Management

### Create Instance

Create a new WhatsApp instance:

```bash
curl --request POST \
  --url http://localhost:8080/instance/create \
  --header 'Content-Type: application/json' \
  --header 'apikey: your_api_key_here' \
  --data '{
  "instanceName": "user-123",
  "token": "user-123",
  "qrcode": true,
  "webhookUrl": "https://your-backend.com/whatsapp/webhook/user-123",
  "webhookByEvents": true,
  "webhookBase64": true,
  "events": [
    "MESSAGES_UPSERT",
    "MESSAGES_UPDATE",
    "CONNECTION_UPDATE"
  ]
}'
```

**Python Example:**

```python
import requests

def create_whatsapp_instance(user_id):
    response = requests.post(
        f"{EVOLUTION_API_URL}/instance/create",
        headers={
            "Content-Type": "application/json",
            "apikey": EVOLUTION_API_KEY
        },
        json={
            "instanceName": user_id,
            "token": user_id,
            "qrcode": True,
            "webhookUrl": f"{BACKEND_URL}/whatsapp/webhook/{user_id}",
            "webhookByEvents": True,
            "webhookBase64": True,
            "events": [
                "MESSAGES_UPSERT",
                "MESSAGES_UPDATE",
                "CONNECTION_UPDATE"
            ]
        }
    )
    
    return response.json()
```

### Connect Instance

Connect to an existing instance and get QR code:

```bash
curl --request GET \
  --url http://localhost:8080/instance/connect/user-123 \
  --header 'apikey: your_api_key_here'
```

**Python Example:**

```python
def connect_whatsapp_instance(user_id):
    response = requests.get(
        f"{EVOLUTION_API_URL}/instance/connect/{user_id}",
        headers={"apikey": EVOLUTION_API_KEY}
    )
    
    return response.json()
```

### Check Connection State

Check the connection state of an instance:

```bash
curl --request GET \
  --url http://localhost:8080/instance/connectionState/user-123 \
  --header 'apikey: your_api_key_here'
```

**Python Example:**

```python
def check_connection_state(user_id):
    response = requests.get(
        f"{EVOLUTION_API_URL}/instance/connectionState/{user_id}",
        headers={"apikey": EVOLUTION_API_KEY}
    )
    
    return response.json()
```

### Delete Instance

Delete an instance:

```bash
curl --request DELETE \
  --url http://localhost:8080/instance/delete/user-123 \
  --header 'apikey: your_api_key_here'
```

**Python Example:**

```python
def delete_whatsapp_instance(user_id):
    response = requests.delete(
        f"{EVOLUTION_API_URL}/instance/delete/{user_id}",
        headers={"apikey": EVOLUTION_API_KEY}
    )
    
    return response.json()
```

## Sending Messages

### Send Text Message

Send a plain text message:

```bash
curl --request POST \
  --url http://localhost:8080/message/sendText/user-123 \
  --header 'Content-Type: application/json' \
  --header 'apikey: your_api_key_here' \
  --data '{
  "number": "1234567890",
  "text": "Hello, this is a test message",
  "delay": 1200
}'
```

**Python Example:**

```python
def send_text_message(user_id, phone, text):
    response = requests.post(
        f"{EVOLUTION_API_URL}/message/sendText/{user_id}",
        headers={
            "Content-Type": "application/json",
            "apikey": EVOLUTION_API_KEY
        },
        json={
            "number": phone,
            "text": text,
            "delay": 1200
        }
    )
    
    return response.json()
```

### Send Image Message

Send an image message:

```bash
curl --request POST \
  --url http://localhost:8080/message/sendMedia/user-123 \
  --header 'Content-Type: application/json' \
  --header 'apikey: your_api_key_here' \
  --data '{
  "number": "1234567890",
  "mediatype": "image",
  "media": "https://example.com/image.jpg",
  "caption": "Check out this image!"
}'
```

**Python Example:**

```python
def send_image_message(user_id, phone, image_url, caption=None):
    response = requests.post(
        f"{EVOLUTION_API_URL}/message/sendMedia/{user_id}",
        headers={
            "Content-Type": "application/json",
            "apikey": EVOLUTION_API_KEY
        },
        json={
            "number": phone,
            "mediatype": "image",
            "media": image_url,
            "caption": caption
        }
    )
    
    return response.json()
```

### Send Document

Send a document:

```bash
curl --request POST \
  --url http://localhost:8080/message/sendMedia/user-123 \
  --header 'Content-Type: application/json' \
  --header 'apikey: your_api_key_here' \
  --data '{
  "number": "1234567890",
  "mediatype": "document",
  "media": "https://example.com/document.pdf",
  "fileName": "document.pdf"
}'
```

**Python Example:**

```python
def send_document(user_id, phone, document_url, filename):
    response = requests.post(
        f"{EVOLUTION_API_URL}/message/sendMedia/{user_id}",
        headers={
            "Content-Type": "application/json",
            "apikey": EVOLUTION_API_KEY
        },
        json={
            "number": phone,
            "mediatype": "document",
            "media": document_url,
            "fileName": filename
        }
    )
    
    return response.json()
```

## Receiving Messages

### Find Messages

Find messages from a specific contact:

```bash
curl --request POST \
  --url http://localhost:8080/chat/findMessages/user-123 \
  --header 'Content-Type: application/json' \
  --header 'apikey: your_api_key_here' \
  --data '{
  "where": {
    "key": {
      "remoteJid": "<EMAIL>"
    }
  }
}'
```

**Python Example:**

```python
def find_messages(user_id, phone):
    response = requests.post(
        f"{EVOLUTION_API_URL}/chat/findMessages/{user_id}",
        headers={
            "Content-Type": "application/json",
            "apikey": EVOLUTION_API_KEY
        },
        json={
            "where": {
                "key": {
                    "remoteJid": f"{phone}@s.whatsapp.net"
                }
            }
        }
    )
    
    return response.json()
```

### Mark Message as Read

Mark a message as read:

```bash
curl --request POST \
  --url http://localhost:8080/chat/markMessageAsRead/user-123 \
  --header 'Content-Type: application/json' \
  --header 'apikey: your_api_key_here' \
  --data '{
  "key": {
    "remoteJid": "<EMAIL>",
    "fromMe": false,
    "id": "message-id-123"
  }
}'
```

**Python Example:**

```python
def mark_message_as_read(user_id, phone, message_id, from_me=False):
    response = requests.post(
        f"{EVOLUTION_API_URL}/chat/markMessageAsRead/{user_id}",
        headers={
            "Content-Type": "application/json",
            "apikey": EVOLUTION_API_KEY
        },
        json={
            "key": {
                "remoteJid": f"{phone}@s.whatsapp.net",
                "fromMe": from_me,
                "id": message_id
            }
        }
    )
    
    return response.json()
```

## Contact Management

### Find Contacts

Find all contacts:

```bash
curl --request POST \
  --url http://localhost:8080/chat/findContacts/user-123 \
  --header 'Content-Type: application/json' \
  --header 'apikey: your_api_key_here' \
  --data '{
  "where": {}
}'
```

**Python Example:**

```python
def find_contacts(user_id):
    response = requests.post(
        f"{EVOLUTION_API_URL}/chat/findContacts/{user_id}",
        headers={
            "Content-Type": "application/json",
            "apikey": EVOLUTION_API_KEY
        },
        json={
            "where": {}
        }
    )
    
    return response.json()
```

### Check if Number is on WhatsApp

Check if a phone number is registered on WhatsApp:

```bash
curl --request POST \
  --url http://localhost:8080/chat/check-is-whatsapp/user-123 \
  --header 'Content-Type: application/json' \
  --header 'apikey: your_api_key_here' \
  --data '{
  "numbers": ["1234567890"]
}'
```

**Python Example:**

```python
def check_is_whatsapp(user_id, phone_numbers):
    response = requests.post(
        f"{EVOLUTION_API_URL}/chat/check-is-whatsapp/{user_id}",
        headers={
            "Content-Type": "application/json",
            "apikey": EVOLUTION_API_KEY
        },
        json={
            "numbers": phone_numbers
        }
    )
    
    return response.json()
```

## Webhook Configuration

### Set Webhook

Configure a webhook for an instance:

```bash
curl --request POST \
  --url http://localhost:8080/webhook/set/user-123 \
  --header 'Content-Type: application/json' \
  --header 'apikey: your_api_key_here' \
  --data '{
  "enabled": true,
  "url": "https://your-backend.com/whatsapp/webhook/user-123",
  "webhookByEvents": true,
  "webhookBase64": true,
  "events": [
    "MESSAGES_UPSERT",
    "MESSAGES_UPDATE",
    "CONNECTION_UPDATE"
  ]
}'
```

**Python Example:**

```python
def set_webhook(user_id, webhook_url):
    response = requests.post(
        f"{EVOLUTION_API_URL}/webhook/set/{user_id}",
        headers={
            "Content-Type": "application/json",
            "apikey": EVOLUTION_API_KEY
        },
        json={
            "enabled": True,
            "url": webhook_url,
            "webhookByEvents": True,
            "webhookBase64": True,
            "events": [
                "MESSAGES_UPSERT",
                "MESSAGES_UPDATE",
                "CONNECTION_UPDATE"
            ]
        }
    )
    
    return response.json()
```

### Find Webhook

Get the current webhook configuration:

```bash
curl --request GET \
  --url http://localhost:8080/webhook/find/user-123 \
  --header 'apikey: your_api_key_here'
```

**Python Example:**

```python
def find_webhook(user_id):
    response = requests.get(
        f"{EVOLUTION_API_URL}/webhook/find/{user_id}",
        headers={"apikey": EVOLUTION_API_KEY}
    )
    
    return response.json()
```

## Common Patterns

### Complete WhatsApp Connection Flow

```python
def connect_whatsapp(user_id):
    # Step 1: Check if instance exists
    instances = requests.get(
        f"{EVOLUTION_API_URL}/instance/fetchInstances",
        headers={"apikey": EVOLUTION_API_KEY}
    ).json()
    
    instance_exists = any(instance.get('instanceName') == user_id for instance in instances)
    
    if not instance_exists:
        # Step 2: Create a new instance
        create_response = requests.post(
            f"{EVOLUTION_API_URL}/instance/create",
            headers={
                "Content-Type": "application/json",
                "apikey": EVOLUTION_API_KEY
            },
            json={
                "instanceName": user_id,
                "token": user_id,
                "qrcode": True,
                "webhookUrl": f"{BACKEND_URL}/whatsapp/webhook/{user_id}",
                "webhookByEvents": True,
                "webhookBase64": True,
                "events": [
                    "MESSAGES_UPSERT",
                    "MESSAGES_UPDATE",
                    "CONNECTION_UPDATE"
                ]
            }
        ).json()
    
    # Step 3: Connect to the instance
    connect_response = requests.get(
        f"{EVOLUTION_API_URL}/instance/connect/{user_id}",
        headers={"apikey": EVOLUTION_API_KEY}
    ).json()
    
    # Step 4: Return QR code if available
    if 'qrcode' in connect_response:
        return {
            'success': True,
            'qrcode': connect_response['qrcode']
        }
    
    # Step 5: Check connection state
    state_response = requests.get(
        f"{EVOLUTION_API_URL}/instance/connectionState/{user_id}",
        headers={"apikey": EVOLUTION_API_KEY}
    ).json()
    
    return {
        'success': True,
        'state': state_response.get('state', 'DISCONNECTED')
    }
```

### Webhook Handler for New Messages

```python
from fastapi import FastAPI, Request, WebSocket, WebSocketDisconnect
from typing import Dict, List
import json

app = FastAPI()

# Store active WebSocket connections
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        if user_id not in self.active_connections:
            self.active_connections[user_id] = []
        self.active_connections[user_id].append(websocket)

    def disconnect(self, websocket: WebSocket, user_id: str):
        if user_id in self.active_connections:
            self.active_connections[user_id].remove(websocket)

    async def send_message(self, message: dict, user_id: str):
        if user_id in self.active_connections:
            for connection in self.active_connections[user_id]:
                await connection.send_json(message)

manager = ConnectionManager()

# WebSocket endpoint
@app.websocket("/whatsapp/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    await manager.connect(websocket, user_id)
    try:
        while True:
            # Keep the connection alive
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)

# Webhook endpoint
@app.post("/whatsapp/webhook/{user_id}")
async def whatsapp_webhook(user_id: str, request: Request):
    # Get the webhook data
    webhook_data = await request.json()
    event_type = webhook_data.get('event')
    
    print(f"Received webhook for user {user_id}, event: {event_type}")
    
    # Process based on event type
    if event_type == "MESSAGES_UPSERT":
        # Process new messages
        await process_new_messages(user_id, webhook_data)
    elif event_type == "MESSAGES_UPDATE":
        # Process message updates
        await process_message_updates(user_id, webhook_data)
    elif event_type == "CONNECTION_UPDATE":
        # Process connection updates
        await process_connection_update(user_id, webhook_data)
        
    return {"success": True}

async def process_new_messages(user_id: str, webhook_data: dict):
    # Extract message data
    messages = webhook_data.get('data', {}).get('messages', [])
    
    for message_data in messages:
        # Skip messages sent by the business
        if message_data.get('key', {}).get('fromMe', False):
            continue
            
        # Extract the phone number
        remote_jid = message_data.get('key', {}).get('remoteJid', '')
        if not remote_jid or '@' not in remote_jid:
            continue
            
        phone = remote_jid.split('@')[0]
        
        # Extract the message content
        message_content = ''
        if 'conversation' in message_data.get('message', {}):
            message_content = message_data['message']['conversation']
        elif 'extendedTextMessage' in message_data.get('message', {}):
            message_content = message_data['message']['extendedTextMessage'].get('text', '')
            
        if not phone or not message_content:
            continue
            
        # Send a notification via WebSocket
        await manager.send_message({
            "type": "new_message",
            "data": {
                "contact_id": f"whatsapp-{phone}",
                "message": {
                    "id": message_data.get('key', {}).get('id', ''),
                    "text": message_content,
                    "timestamp": message_data.get('messageTimestamp', ''),
                    "sender": "them"
                }
            }
        }, user_id)
```

### React Component for WhatsApp Connection

```tsx
import React, { useState, useEffect } from 'react';
import QRCode from 'qrcode.react';

const WhatsAppConnection: React.FC = () => {
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [connectionState, setConnectionState] = useState<string>('DISCONNECTED');
  const [loading, setLoading] = useState<boolean>(false);
  
  const connectWhatsApp = async () => {
    setLoading(true);
    
    try {
      const response = await fetch('/api/whatsapp/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
      
      const data = await response.json();
      
      if (data.qrcode) {
        setQrCode(data.qrcode);
      }
      
      if (data.state) {
        setConnectionState(data.state);
      }
    } catch (error) {
      console.error('Error connecting WhatsApp:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Check connection state periodically
  useEffect(() => {
    if (connectionState !== 'CONNECTED') {
      const interval = setInterval(async () => {
        try {
          const response = await fetch('/api/whatsapp/status', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
            }
          });
          
          const data = await response.json();
          
          if (data.state) {
            setConnectionState(data.state);
            
            if (data.state === 'CONNECTED') {
              setQrCode(null);
              clearInterval(interval);
            }
          }
        } catch (error) {
          console.error('Error checking WhatsApp status:', error);
        }
      }, 5000);
      
      return () => clearInterval(interval);
    }
  }, [connectionState]);
  
  return (
    <div className="whatsapp-connection">
      <h2>WhatsApp Connection</h2>
      
      <div className="connection-status">
        Status: {connectionState}
      </div>
      
      {qrCode && (
        <div className="qr-code-container">
          <p>Scan this QR code with your WhatsApp:</p>
          <QRCode value={qrCode} size={256} />
        </div>
      )}
      
      {connectionState !== 'CONNECTED' && (
        <button 
          onClick={connectWhatsApp} 
          disabled={loading}
        >
          {loading ? 'Connecting...' : 'Connect WhatsApp'}
        </button>
      )}
      
      {connectionState === 'CONNECTED' && (
        <p>Your WhatsApp is connected!</p>
      )}
    </div>
  );
};

export default WhatsAppConnection;
```

### React Component for WhatsApp Chat

```tsx
import React, { useState, useEffect, useRef } from 'react';

interface Message {
  id: string;
  text: string;
  timestamp: string;
  sender: 'me' | 'them';
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
}

interface Contact {
  id: string;
  name: string;
  phone: string;
  lastMessage?: string;
  timestamp?: string;
}

const WhatsAppChat: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [messageText, setMessageText] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Load contacts
  useEffect(() => {
    const fetchContacts = async () => {
      setLoading(true);
      
      try {
        const response = await fetch('/api/whatsapp/contacts', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
          }
        });
        
        const data = await response.json();
        setContacts(data);
      } catch (error) {
        console.error('Error fetching contacts:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchContacts();
  }, []);
  
  // Load messages when contact is selected
  useEffect(() => {
    if (selectedContact) {
      const fetchMessages = async () => {
        setLoading(true);
        
        try {
          const response = await fetch(`/api/whatsapp/messages?contactId=${selectedContact.id}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
            }
          });
          
          const data = await response.json();
          setMessages(data);
        } catch (error) {
          console.error('Error fetching messages:', error);
        } finally {
          setLoading(false);
        }
      };
      
      fetchMessages();
    }
  }, [selectedContact]);
  
  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);
  
  // Send message
  const sendMessage = async () => {
    if (!selectedContact || !messageText.trim()) return;
    
    // Create temporary message
    const tempMessage: Message = {
      id: `temp-${Date.now()}`,
      text: messageText,
      timestamp: new Date().toISOString(),
      sender: 'me',
      status: 'sending'
    };
    
    // Add to messages immediately
    setMessages(prev => [...prev, tempMessage]);
    
    // Clear input
    setMessageText('');
    
    try {
      const response = await fetch('/api/whatsapp/send-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify({
          contactId: selectedContact.id,
          text: tempMessage.text
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        // Update message status
        setMessages(prev => prev.map(m => 
          m.id === tempMessage.id 
            ? { ...m, id: data.messageId, status: 'sent' } 
            : m
        ));
      } else {
        // Mark as failed
        setMessages(prev => prev.map(m => 
          m.id === tempMessage.id 
            ? { ...m, status: 'failed' } 
            : m
        ));
      }
    } catch (error) {
      console.error('Error sending message:', error);
      
      // Mark as failed
      setMessages(prev => prev.map(m => 
        m.id === tempMessage.id 
          ? { ...m, status: 'failed' } 
          : m
      ));
    }
  };
  
  return (
    <div className="whatsapp-chat">
      <div className="contacts-list">
        <h3>Contacts</h3>
        
        {loading && !selectedContact && <p>Loading contacts...</p>}
        
        <ul>
          {contacts.map(contact => (
            <li 
              key={contact.id}
              className={selectedContact?.id === contact.id ? 'selected' : ''}
              onClick={() => setSelectedContact(contact)}
            >
              <div className="contact-name">{contact.name}</div>
              <div className="contact-last-message">{contact.lastMessage}</div>
              <div className="contact-timestamp">{contact.timestamp}</div>
            </li>
          ))}
        </ul>
      </div>
      
      <div className="chat-area">
        {selectedContact ? (
          <>
            <div className="chat-header">
              <h3>{selectedContact.name}</h3>
              <div className="contact-phone">{selectedContact.phone}</div>
            </div>
            
            <div className="messages-container">
              {loading && <p>Loading messages...</p>}
              
              {messages.map(message => (
                <div 
                  key={message.id}
                  className={`message ${message.sender === 'me' ? 'sent' : 'received'}`}
                >
                  <div className="message-text">{message.text}</div>
                  <div className="message-meta">
                    <span className="message-time">
                      {new Date(message.timestamp).toLocaleTimeString()}
                    </span>
                    {message.sender === 'me' && (
                      <span className={`message-status ${message.status}`}>
                        {message.status}
                      </span>
                    )}
                  </div>
                </div>
              ))}
              
              <div ref={messagesEndRef} />
            </div>
            
            <div className="message-input">
              <input
                type="text"
                value={messageText}
                onChange={e => setMessageText(e.target.value)}
                placeholder="Type a message"
                onKeyPress={e => e.key === 'Enter' && sendMessage()}
              />
              <button onClick={sendMessage}>Send</button>
            </div>
          </>
        ) : (
          <div className="no-chat-selected">
            <p>Select a contact to start chatting</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default WhatsAppChat;
```

## Conclusion

This quick reference guide provides the most commonly used Evolution API v2 endpoints and patterns for your application. Use these examples as a starting point for implementing WhatsApp integration in your application.

Remember to:
- Secure your API key
- Handle errors gracefully
- Implement proper validation
- Test thoroughly before deploying to production

For more detailed information, refer to the comprehensive Evolution API v2 documentation.
