# Evolution API Webhook Implementation Guide

This guide provides a comprehensive overview of the webhook functionality in Evolution API v2, including setup, event types, payload structures, and implementation best practices.

## Table of Contents

1. [Introduction to Webhooks](#introduction-to-webhooks)
2. [Setting Up Webhooks](#setting-up-webhooks)
3. [Webhook Events](#webhook-events)
4. [Webhook Payload Structure](#webhook-payload-structure)
5. [Handling Webhook Events](#handling-webhook-events)
6. [Security Considerations](#security-considerations)
7. [Implementation Examples](#implementation-examples)
8. [Troubleshooting](#troubleshooting)
9. [Best Practices](#best-practices)

## Introduction to Webhooks

Webhooks are a way for the Evolution API to send real-time notifications to your application when certain events occur. Instead of your application constantly polling the API for updates, the API can push updates to your application as they happen, making your application more efficient and responsive.

### Benefits of Using Webhooks

- **Real-time Updates**: Receive instant notifications when events occur
- **Reduced API Calls**: Eliminate the need for frequent polling
- **Efficiency**: Process only the events that matter to your application
- **Scalability**: Handle high volumes of messages more efficiently

## Setting Up Webhooks

### Configuring a Webhook

To set up a webhook, you need to make a POST request to the `/webhook/set/{instance}` endpoint:

```bash
curl --request POST \
  --url https://{server-url}/webhook/set/{instance} \
  --header 'Content-Type: application/json' \
  --header 'apikey: <api-key>' \
  --data '{
  "enabled": true,
  "url": "https://your-webhook-url.com/webhook",
  "webhookByEvents": true,
  "webhookBase64": true,
  "events": [
    "MESSAGES_UPSERT",
    "MESSAGES_UPDATE",
    "CONNECTION_UPDATE"
  ]
}'
```

### Parameters

- `enabled` (boolean, required): Enables the webhook for this instance
- `url` (string, required): Your webhook URL that will receive the events
- `webhookByEvents` (boolean): Enables webhook by events (recommended)
- `webhookBase64` (boolean): Sends media files in base64 format when available
- `events` (array, required): List of events you want to receive

### Retrieving Webhook Configuration

To retrieve the current webhook configuration for an instance, use the GET request to `/webhook/find/{instance}`:

```bash
curl --request GET \
  --url https://{server-url}/webhook/find/{instance} \
  --header 'apikey: <api-key>'
```

## Webhook Events

The Evolution API supports a wide range of events that can trigger webhook notifications:

### Message Events

- `MESSAGES_UPSERT`: Triggered when new messages are received
- `MESSAGES_UPDATE`: Triggered when messages are updated (e.g., read status)
- `MESSAGES_DELETE`: Triggered when messages are deleted
- `MESSAGES_SET`: Triggered when messages are set (usually during initial sync)
- `SEND_MESSAGE`: Triggered when a message is sent

### Contact Events

- `CONTACTS_SET`: Triggered when contacts are set (usually during initial sync)
- `CONTACTS_UPSERT`: Triggered when new contacts are added
- `CONTACTS_UPDATE`: Triggered when contacts are updated

### Chat Events

- `CHATS_SET`: Triggered when chats are set (usually during initial sync)
- `CHATS_UPSERT`: Triggered when new chats are created
- `CHATS_UPDATE`: Triggered when chats are updated
- `CHATS_DELETE`: Triggered when chats are deleted

### Group Events

- `GROUPS_UPSERT`: Triggered when new groups are created
- `GROUP_UPDATE`: Triggered when group information is updated
- `GROUP_PARTICIPANTS_UPDATE`: Triggered when group participants change

### Status Events

- `PRESENCE_UPDATE`: Triggered when a contact's presence status changes
- `CONNECTION_UPDATE`: Triggered when the connection status changes

### Other Events

- `APPLICATION_STARTUP`: Triggered when the application starts
- `QRCODE_UPDATED`: Triggered when the QR code is updated
- `CALL`: Triggered when a call is received
- `LABELS_EDIT`: Triggered when labels are edited
- `LABELS_ASSOCIATION`: Triggered when labels are associated with chats
- `TYPEBOT_START`: Triggered when a typebot starts
- `TYPEBOT_CHANGE_STATUS`: Triggered when a typebot status changes

## Webhook Payload Structure

The webhook payload structure varies depending on the event type, but generally follows this format:

```json
{
  "event": "EVENT_NAME",
  "instance": "instance-name",
  "data": {
    // Event-specific data
  }
}
```

### Example Payloads

#### New Message (MESSAGES_UPSERT)

```json
{
  "event": "MESSAGES_UPSERT",
  "instance": "instance-name",
  "data": {
    "key": {
      "remoteJid": "<EMAIL>",
      "fromMe": false,
      "id": "message-id-123"
    },
    "message": {
      "conversation": "Hello, this is a test message"
    },
    "messageTimestamp": 1620000000
  }
}
```

#### Message Update (MESSAGES_UPDATE)

```json
{
  "event": "MESSAGES_UPDATE",
  "instance": "instance-name",
  "data": {
    "key": {
      "remoteJid": "<EMAIL>",
      "fromMe": true,
      "id": "message-id-123"
    },
    "update": {
      "status": "READ"
    }
  }
}
```

#### Connection Update (CONNECTION_UPDATE)

```json
{
  "event": "CONNECTION_UPDATE",
  "instance": "instance-name",
  "data": {
    "connection": "open",
    "lastDisconnect": {
      "date": "2023-01-01T00:00:00.000Z",
      "error": null
    }
  }
}
```

## Handling Webhook Events

### Setting Up a Webhook Receiver

To receive webhook events, you need to set up an endpoint on your server that can accept POST requests. Here's an example using Node.js and Express:

```javascript
const express = require('express');
const app = express();
app.use(express.json());

// Webhook endpoint
app.post('/webhook', (req, res) => {
  const { event, instance, data } = req.body;
  
  console.log(`Received ${event} event from instance ${instance}`);
  
  switch (event) {
    case 'MESSAGES_UPSERT':
      handleNewMessage(data);
      break;
    case 'MESSAGES_UPDATE':
      handleMessageUpdate(data);
      break;
    case 'CONNECTION_UPDATE':
      handleConnectionUpdate(data);
      break;
    // Handle other events...
  }
  
  // Always respond with 200 OK to acknowledge receipt
  res.status(200).send('OK');
});

function handleNewMessage(data) {
  const { key, message, messageTimestamp } = data;
  const { remoteJid, fromMe, id } = key;
  
  // Extract phone number from remoteJid
  const phone = remoteJid.split('@')[0];
  
  // Extract message content
  let messageContent = '';
  if (message.conversation) {
    messageContent = message.conversation;
  } else if (message.extendedTextMessage) {
    messageContent = message.extendedTextMessage.text;
  } else if (message.imageMessage) {
    messageContent = message.imageMessage.caption || 'Image';
  }
  
  console.log(`New message from ${phone}: ${messageContent}`);
  
  // Process the message (e.g., save to database, notify users)
}

// Start the server
app.listen(3000, () => {
  console.log('Webhook server listening on port 3000');
});
```

### Processing Media Messages

If you've enabled `webhookBase64`, media files will be included in the webhook payload as base64-encoded strings:

```javascript
function handleNewMessage(data) {
  const { key, message, messageTimestamp } = data;
  
  if (message.imageMessage) {
    const imageData = message.imageMessage.jpegThumbnail; // Base64 encoded
    // Process the image (e.g., save to disk, upload to cloud storage)
    saveBase64Image(imageData, `image_${key.id}.jpg`);
  }
  
  // Process other message types...
}

function saveBase64Image(base64Data, filename) {
  const buffer = Buffer.from(base64Data, 'base64');
  require('fs').writeFileSync(filename, buffer);
}
```

## Security Considerations

### Validating Webhook Requests

To ensure that webhook requests are coming from your Evolution API server and not from a malicious source, you should implement validation:

1. **Use HTTPS**: Always use HTTPS for your webhook endpoint
2. **Implement Authentication**: Add a secret token to your webhook URL or use a header-based authentication mechanism
3. **Validate Request Origin**: Check the IP address of the incoming request against your Evolution API server's IP

### Example: Using a Secret Token

```javascript
app.post('/webhook/:token', (req, res) => {
  const { token } = req.params;
  
  // Validate the token
  if (token !== 'your-secret-token') {
    return res.status(401).send('Unauthorized');
  }
  
  // Process the webhook...
  
  res.status(200).send('OK');
});
```

## Implementation Examples

### Real-time Chat Application

Here's how you might implement a real-time chat application using webhooks:

1. **Set up the webhook** to receive `MESSAGES_UPSERT` and `MESSAGES_UPDATE` events
2. **Process incoming messages** and store them in your database
3. **Notify connected clients** using WebSockets when new messages arrive
4. **Update message status** (delivered, read) based on webhook events

```javascript
// WebSocket server for real-time client updates
const WebSocket = require('ws');
const wss = new WebSocket.Server({ port: 8080 });

// Store connected clients
const clients = new Map();

// WebSocket connection handler
wss.on('connection', (ws, req) => {
  const userId = extractUserIdFromRequest(req);
  clients.set(userId, ws);
  
  ws.on('close', () => {
    clients.delete(userId);
  });
});

// Webhook handler for new messages
app.post('/webhook', (req, res) => {
  const { event, data } = req.body;
  
  if (event === 'MESSAGES_UPSERT') {
    // Extract recipient user ID from the message
    const userId = extractUserIdFromMessage(data);
    
    // Notify the client if they're connected
    if (clients.has(userId)) {
      clients.get(userId).send(JSON.stringify({
        type: 'new_message',
        message: formatMessage(data)
      }));
    }
  }
  
  res.status(200).send('OK');
});
```

### Automated Response System

You can use webhooks to build an automated response system:

1. **Set up the webhook** to receive `MESSAGES_UPSERT` events
2. **Process incoming messages** and determine if they require a response
3. **Generate and send responses** using the Evolution API

```javascript
app.post('/webhook', async (req, res) => {
  const { event, instance, data } = req.body;
  
  if (event === 'MESSAGES_UPSERT' && !data.key.fromMe) {
    const phone = data.key.remoteJid.split('@')[0];
    let messageContent = '';
    
    if (data.message.conversation) {
      messageContent = data.message.conversation;
    } else if (data.message.extendedTextMessage) {
      messageContent = data.message.extendedTextMessage.text;
    }
    
    // Generate a response
    const response = await generateResponse(messageContent);
    
    // Send the response
    await sendResponse(instance, phone, response);
  }
  
  res.status(200).send('OK');
});

async function sendResponse(instance, phone, text) {
  const response = await fetch(`https://{server-url}/message/sendText/${instance}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'apikey': 'your-api-key'
    },
    body: JSON.stringify({
      number: phone,
      text: text
    })
  });
  
  return response.json();
}
```

## Troubleshooting

### Common Issues

1. **Webhook Not Receiving Events**
   - Verify that the webhook URL is accessible from the internet
   - Check that the webhook is properly configured in the Evolution API
   - Ensure that the events you want to receive are included in the configuration

2. **Error Handling**
   - Always respond with a 200 status code to acknowledge receipt
   - Log webhook payloads for debugging
   - Implement retry logic for failed processing

3. **Performance Issues**
   - Use a queue system for processing webhook events
   - Implement asynchronous processing for time-consuming tasks
   - Scale your webhook receiver horizontally if needed

### Debugging Tips

1. **Log All Incoming Webhooks**
   ```javascript
   app.post('/webhook', (req, res) => {
     console.log('Webhook received:', JSON.stringify(req.body, null, 2));
     // Process the webhook...
     res.status(200).send('OK');
   });
   ```

2. **Test Your Webhook Endpoint**
   - Use tools like [Webhook.site](https://webhook.site/) to test your webhook endpoint
   - Send test events to your webhook endpoint to verify that it's working correctly

3. **Monitor Webhook Performance**
   - Track the time it takes to process webhook events
   - Set up alerts for webhook processing failures

## Best Practices

1. **Respond Quickly**
   - Webhook handlers should respond as quickly as possible
   - Process events asynchronously if they require time-consuming operations

2. **Implement Idempotency**
   - Design your webhook handler to be idempotent (processing the same event multiple times should have the same result)
   - Use message IDs to deduplicate events

3. **Handle Retries**
   - Implement retry logic for failed webhook processing
   - Use exponential backoff for retries

4. **Scale Appropriately**
   - Design your webhook handler to scale horizontally
   - Use a queue system for high-volume webhook processing

5. **Monitor and Alert**
   - Set up monitoring for webhook processing
   - Create alerts for webhook failures

6. **Secure Your Webhook**
   - Use HTTPS for your webhook endpoint
   - Implement authentication for webhook requests
   - Validate the source of webhook requests

7. **Graceful Degradation**
   - Implement fallback mechanisms for when webhooks fail
   - Consider using polling as a backup for critical functionality

## Conclusion

Webhooks are a powerful way to build real-time applications with the Evolution API. By following the guidelines in this document, you can implement a robust webhook system that efficiently processes WhatsApp events and provides a responsive user experience.

Remember to prioritize security, performance, and reliability in your webhook implementation, and to monitor your webhook system to ensure it's functioning correctly.
