# Evolution API Integration Architecture

This document outlines the architecture for integrating Evolution API v2 with your application, focusing on system design, data flow, and implementation considerations.

## Table of Contents

1. [System Overview](#system-overview)
2. [Architecture Components](#architecture-components)
3. [Data Flow](#data-flow)
4. [Integration Patterns](#integration-patterns)
5. [Database Schema](#database-schema)
6. [Security Considerations](#security-considerations)
7. [Scaling Considerations](#scaling-considerations)
8. [Implementation Roadmap](#implementation-roadmap)

## System Overview

The integration architecture connects your application with WhatsApp through Evolution API, enabling real-time messaging, contact management, and automated responses.

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Your Application                          │
│                                                                  │
│  ┌──────────────┐    ┌──────────────┐    ┌──────────────────┐   │
│  │              │    │              │    │                  │   │
│  │  Frontend    │◄──►│  Backend     │◄──►│  Database        │   │
│  │  (React)     │    │  (FastAPI)   │    │  (PostgreSQL)    │   │
│  │              │    │              │    │                  │   │
│  └──────────────┘    └──────┬───────┘    └──────────────────┘   │
│                             │                                    │
└─────────────────────────────┼────────────────────────────────────┘
                              │
                              ▼
                     ┌──────────────────┐
                     │                  │
                     │  Evolution API   │
                     │                  │
                     └────────┬─────────┘
                              │
                              ▼
                     ┌──────────────────┐
                     │                  │
                     │    WhatsApp      │
                     │                  │
                     └──────────────────┘
```

## Architecture Components

### 1. Frontend (React)

The frontend provides the user interface for messaging and contact management:

- **MessagesPage**: Displays conversations and allows sending messages
- **ContactsPage**: Manages WhatsApp contacts
- **SettingsPage**: Configures WhatsApp connection settings
- **WebSocket Client**: Maintains real-time connection with the backend

### 2. Backend (FastAPI)

The backend handles business logic and communication with Evolution API:

- **API Routes**: Expose endpoints for the frontend
- **Webhook Handler**: Processes events from Evolution API
- **WebSocket Server**: Pushes real-time updates to the frontend
- **Authentication**: Secures API endpoints and WebSocket connections
- **Business Logic**: Implements application-specific logic

### 3. Evolution API

Evolution API manages the WhatsApp connection:

- **Instance Management**: Creates and manages WhatsApp instances
- **Message Handling**: Sends and receives messages
- **Webhook Notifications**: Sends events to your backend
- **Media Processing**: Handles images, videos, and documents

### 4. Database (PostgreSQL)

The database stores application data:

- **Users**: Application users
- **Clients**: WhatsApp contacts
- **Messages**: Message history
- **Settings**: Application and WhatsApp settings

## Data Flow

### 1. Sending a Message

```
┌──────────┐     ┌──────────┐     ┌──────────────┐     ┌──────────┐
│          │     │          │     │              │     │          │
│ Frontend │────►│ Backend  │────►│ Evolution API│────►│ WhatsApp │
│          │     │          │     │              │     │          │
└──────────┘     └──────────┘     └──────────────┘     └──────────┘
                      │
                      ▼
                ┌──────────┐
                │          │
                │ Database │
                │          │
                └──────────┘
```

1. User sends a message through the frontend
2. Frontend sends a request to the backend
3. Backend saves the message to the database
4. Backend forwards the message to Evolution API
5. Evolution API sends the message to WhatsApp

### 2. Receiving a Message

```
┌──────────┐     ┌──────────┐     ┌──────────────┐     ┌──────────┐
│          │     │          │     │              │     │          │
│ Frontend │◄────│ Backend  │◄────│ Evolution API│◄────│ WhatsApp │
│          │     │          │     │              │     │          │
└──────────┘     └──────────┘     └──────────────┘     └──────────┘
                      │
                      ▼
                ┌──────────┐
                │          │
                │ Database │
                │          │
                └──────────┘
```

1. WhatsApp receives a message
2. Evolution API detects the message
3. Evolution API sends a webhook event to your backend
4. Backend processes the message and saves it to the database
5. Backend sends the message to connected frontend clients via WebSocket

### 3. Setting Up a WhatsApp Connection

```
┌──────────┐     ┌──────────┐     ┌──────────────┐     ┌──────────┐
│          │     │          │     │              │     │          │
│ Frontend │────►│ Backend  │────►│ Evolution API│────►│ WhatsApp │
│          │◄────│          │◄────│              │◄────│          │
└──────────┘     └──────────┘     └──────────────┘     └──────────┘
                      │
                      ▼
                ┌──────────┐
                │          │
                │ Database │
                │          │
                └──────────┘
```

1. User initiates WhatsApp connection from the frontend
2. Frontend sends a request to the backend
3. Backend creates an instance in Evolution API
4. Evolution API generates a QR code
5. Backend returns the QR code to the frontend
6. User scans the QR code with their phone
7. WhatsApp establishes the connection
8. Evolution API notifies the backend via webhook
9. Backend updates the connection status in the database
10. Backend notifies the frontend via WebSocket

## Integration Patterns

### 1. Webhook-Based Integration

The primary integration pattern uses webhooks for real-time event notification:

```python
# Backend webhook handler
@app.post("/whatsapp/webhook/{user_id}")
async def whatsapp_webhook(user_id: str, request: Request):
    webhook_data = await request.json()
    event_type = webhook_data.get('event')
    
    # Process the event based on its type
    if event_type == "MESSAGES_UPSERT":
        await process_new_messages(user_id, webhook_data)
    elif event_type == "CONNECTION_UPDATE":
        await process_connection_update(user_id, webhook_data)
    
    return {"success": True}
```

### 2. REST API Integration

For operations initiated by your application, use the REST API:

```python
# Backend function to send a message
async def send_whatsapp_message(user_id: str, phone: str, text: str):
    try:
        response = await httpx.post(
            f"{settings.EVOLUTION_API_URL}/message/sendText/{user_id}",
            headers={
                "Content-Type": "application/json",
                "apikey": settings.EVOLUTION_API_KEY
            },
            json={
                "number": phone,
                "text": text
            }
        )
        
        return response.json()
    except Exception as e:
        logger.error(f"Error sending WhatsApp message: {str(e)}")
        raise
```

### 3. WebSocket Integration

For real-time updates to the frontend, use WebSockets:

```python
# Backend WebSocket handler
@app.websocket("/whatsapp/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    await manager.connect(websocket, user_id)
    try:
        while True:
            # Keep the connection alive
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
```

## Database Schema

### Users Table

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### Clients Table

```sql
CREATE TABLE clients (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    email VARCHAR(255),
    tags TEXT[] DEFAULT '{}',
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE (user_id, phone)
);
```

### Messages Table

```sql
CREATE TABLE messages (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id),
    client_id UUID NOT NULL REFERENCES clients(id),
    content TEXT NOT NULL,
    is_from_business BOOLEAN NOT NULL,
    sent_at TIMESTAMP NOT NULL,
    delivered_at TIMESTAMP,
    read_at TIMESTAMP,
    parent_id UUID REFERENCES messages(id),
    source VARCHAR(50) NOT NULL DEFAULT 'app',
    media_type VARCHAR(50),
    media_url VARCHAR(255),
    embedding VECTOR(1536),
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### WhatsApp Connections Table

```sql
CREATE TABLE whatsapp_connections (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) UNIQUE,
    instance_name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'disconnected',
    connected_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

## Security Considerations

### API Key Protection

The Evolution API key should be securely stored and never exposed to the frontend:

```python
# In your backend settings
class Settings(BaseSettings):
    EVOLUTION_API_URL: str
    EVOLUTION_API_KEY: str
    
    class Config:
        env_file = ".env"

settings = Settings()
```

### User Authentication

Ensure that webhook endpoints validate the user ID:

```python
@app.post("/whatsapp/webhook/{user_id}")
async def whatsapp_webhook(user_id: str, request: Request, db: Session = Depends(get_db)):
    # Validate that the user exists
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        return JSONResponse(status_code=404, content={"error": "User not found"})
    
    # Process the webhook
    # ...
```

### WebSocket Authentication

Secure WebSocket connections with authentication:

```python
@app.websocket("/whatsapp/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    # Get the token from the query parameters
    token = websocket.query_params.get("token")
    if not token or not validate_token(token, user_id):
        await websocket.close(code=1008)  # Policy violation
        return
    
    await manager.connect(websocket, user_id)
    # ...
```

### Data Isolation

Ensure that users can only access their own data:

```python
# In your API routes
@app.get("/messages")
async def get_messages(
    client_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify that the client belongs to the user
    client = db.query(Client).filter(
        Client.id == client_id,
        Client.user_id == current_user.id
    ).first()
    
    if not client:
        raise HTTPException(status_code=404, detail="Client not found")
    
    # Fetch messages
    messages = db.query(Message).filter(
        Message.client_id == client_id,
        Message.user_id == current_user.id
    ).order_by(Message.sent_at.desc()).limit(50).all()
    
    return messages
```

## Scaling Considerations

### Multiple WhatsApp Instances

For applications with many users, each user will have their own WhatsApp instance:

```python
# Create a WhatsApp instance for a user
async def create_whatsapp_instance(user_id: str):
    try:
        response = await httpx.post(
            f"{settings.EVOLUTION_API_URL}/instance/create",
            headers={
                "Content-Type": "application/json",
                "apikey": settings.EVOLUTION_API_KEY
            },
            json={
                "instanceName": user_id,
                "token": user_id,
                "qrcode": True,
                "webhookUrl": f"{settings.BACKEND_URL}/whatsapp/webhook/{user_id}",
                "webhookByEvents": True,
                "webhookBase64": True,
                "events": [
                    "MESSAGES_UPSERT",
                    "MESSAGES_UPDATE",
                    "CONNECTION_UPDATE"
                ]
            }
        )
        
        return response.json()
    except Exception as e:
        logger.error(f"Error creating WhatsApp instance: {str(e)}")
        raise
```

### Database Indexing

Optimize database queries with appropriate indexes:

```sql
-- Index for message queries by client
CREATE INDEX idx_messages_client_id ON messages(client_id);

-- Index for message queries by user
CREATE INDEX idx_messages_user_id ON messages(user_id);

-- Index for client queries by user
CREATE INDEX idx_clients_user_id ON clients(user_id);

-- Index for searching clients by phone
CREATE INDEX idx_clients_phone ON clients(phone);
```

### Caching

Implement caching for frequently accessed data:

```python
# In your backend
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
from fastapi_cache.decorator import cache

@app.on_event("startup")
async def startup():
    redis = aioredis.from_url(settings.REDIS_URL, encoding="utf8", decode_responses=True)
    FastAPICache.init(RedisBackend(redis), prefix="fastapi-cache")

@app.get("/clients")
@cache(expire=60)  # Cache for 60 seconds
async def get_clients(current_user: User = Depends(get_current_user)):
    # Fetch clients
    # ...
```

### Message Queue

For high-volume applications, use a message queue for processing webhook events:

```python
# In your backend
from fastapi import BackgroundTasks

@app.post("/whatsapp/webhook/{user_id}")
async def whatsapp_webhook(
    user_id: str,
    request: Request,
    background_tasks: BackgroundTasks
):
    webhook_data = await request.json()
    
    # Process the webhook asynchronously
    background_tasks.add_task(process_webhook, user_id, webhook_data)
    
    return {"success": True}

async def process_webhook(user_id: str, webhook_data: dict):
    # Process the webhook data
    # ...
```

## Implementation Roadmap

### Phase 1: Basic Integration

1. Set up Evolution API server
2. Implement webhook receiver in the backend
3. Create WhatsApp instance management
4. Implement basic message sending and receiving

### Phase 2: Real-time Messaging

1. Set up WebSocket server in the backend
2. Implement WebSocket client in the frontend
3. Add real-time message updates
4. Implement message status updates

### Phase 3: Advanced Features

1. Add media message support
2. Implement contact synchronization
3. Add typing indicators
4. Implement message search

### Phase 4: Optimization and Scaling

1. Optimize database queries
2. Implement caching
3. Add message queue for webhook processing
4. Set up monitoring and alerting

## Conclusion

This architecture provides a robust foundation for integrating Evolution API with your application. By following these patterns and considerations, you can build a scalable, secure, and efficient WhatsApp integration that provides a seamless user experience.

Remember to:
- Secure your API keys and user data
- Implement proper error handling and logging
- Test thoroughly, especially webhook handling
- Monitor performance and scale as needed

With this architecture in place, your application will be able to leverage the full power of WhatsApp messaging through Evolution API.
