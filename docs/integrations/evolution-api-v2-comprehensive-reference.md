# Evolution API v2 Comprehensive Reference

This document provides a complete reference for the Evolution API v2, organizing all endpoints by category with their key parameters and usage examples.

## Table of Contents

1. [Introduction](#introduction)
2. [Authentication](#authentication)
3. [Instance Management](#instance-management)
4. [Webhook Management](#webhook-management)
5. [Settings Management](#settings-management)
6. [Message Management](#message-management)
7. [Chat Management](#chat-management)
8. [Profile Management](#profile-management)
9. [Group Management](#group-management)
10. [Integration Options](#integration-options)
11. [Implementation Recommendations](#implementation-recommendations)

## Introduction

Evolution API is a powerful REST API for WhatsApp integration, allowing developers to create, manage, and interact with WhatsApp instances programmatically. This API supports various features including sending and receiving messages, managing contacts, handling groups, and integrating with other services.

**Base URL**: `http://localhost:8080` (default) or your custom server URL

## Authentication

All API requests require an API key for authentication, which should be included in the request headers.

```
apikey: your_api_key_here
```

## Instance Management

### Create Instance
- **Endpoint**: `POST /instance/create`
- **Description**: Creates a new WhatsApp instance
- **Key Parameters**:
  - `instanceName` (required): Unique name for the instance
  - `token`: API key (leave empty to create dynamically)
  - `qrcode`: Boolean to generate QR code automatically
  - `webhookUrl`: URL for webhook notifications
  - `webhookEvents`: Array of events to trigger webhook

### Fetch Instances
- **Endpoint**: `GET /instance/fetchInstances`
- **Description**: Returns all created instances

### Instance Connect
- **Endpoint**: `GET /instance/connect/{instance}`
- **Description**: Connects to an instance and returns QR code if needed
- **Path Parameters**:
  - `instance`: ID of the instance to connect

### Restart Instance
- **Endpoint**: `PUT /instance/restart/{instance}`
- **Description**: Restarts an instance
- **Path Parameters**:
  - `instance`: ID of the instance to restart

### Connection State
- **Endpoint**: `GET /instance/connectionState/{instance}`
- **Description**: Returns the connection state of an instance
- **Path Parameters**:
  - `instance`: ID of the instance

### Logout Instance
- **Endpoint**: `POST /instance/logout/{instance}`
- **Description**: Logs out from an instance
- **Path Parameters**:
  - `instance`: ID of the instance

### Delete Instance
- **Endpoint**: `DELETE /instance/delete/{instance}`
- **Description**: Deletes an instance
- **Path Parameters**:
  - `instance`: ID of the instance
- **Query Parameters**:
  - `force`: Boolean to force deletion

### Set Presence
- **Endpoint**: `POST /instance/setPresence/{instance}`
- **Description**: Sets the presence status for an instance
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `presence`: Presence status (available, unavailable, composing, recording, paused)
  - `pushname`: Display name

## Webhook Management

### Set Webhook
- **Endpoint**: `POST /webhook/set/{instance}`
- **Description**: Configures a webhook for an instance
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `enabled`: Boolean to enable/disable webhook
  - `url`: Webhook URL
  - `webhookByEvents`: Boolean to enable webhook by events
  - `webhookBase64`: Boolean to send files in base64
  - `events`: Array of events to trigger webhook

### Find Webhook
- **Endpoint**: `GET /webhook/find/{instance}`
- **Description**: Returns the webhook configuration for an instance
- **Path Parameters**:
  - `instance`: ID of the instance

## Settings Management

### Set Settings
- **Endpoint**: `POST /settings/set/{instance}`
- **Description**: Configures settings for an instance
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `reject_call`: Boolean to reject calls automatically
  - `msg_call`: Message to send when rejecting calls
  - `groups_ignore`: Boolean to ignore group messages
  - `always_online`: Boolean to keep WhatsApp always online
  - `read_messages`: Boolean to mark messages as read
  - `read_status`: Boolean to mark status as read

### Find Settings
- **Endpoint**: `GET /settings/find/{instance}`
- **Description**: Returns the settings for an instance
- **Path Parameters**:
  - `instance`: ID of the instance

## Message Management

### Send Plain Text
- **Endpoint**: `POST /message/sendText/{instance}`
- **Description**: Sends a text message
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `number`: Recipient's phone number with country code
  - `text`: Message content
  - `delay`: Delay in milliseconds before sending
  - `quoted`: Object containing quoted message details
  - `linkPreview`: Boolean to enable link previews

### Send Media
- **Endpoint**: `POST /message/sendMedia/{instance}`
- **Description**: Sends a media message (image, video, document)
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `number`: Recipient's phone number with country code
  - `mediatype`: Type of media (image, video, document)
  - `media`: URL or base64 of the media
  - `fileName`: Name of the file
  - `caption`: Caption for the media

### Send Audio
- **Endpoint**: `POST /message/sendWhatsAppAudio/{instance}`
- **Description**: Sends an audio message
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `number`: Recipient's phone number with country code
  - `audio`: URL or base64 of the audio
  - `ptt`: Boolean to send as voice note

### Send Sticker
- **Endpoint**: `POST /message/sendSticker/{instance}`
- **Description**: Sends a sticker
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `number`: Recipient's phone number with country code
  - `sticker`: URL or base64 of the sticker

### Send Location
- **Endpoint**: `POST /message/sendLocation/{instance}`
- **Description**: Sends a location
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `number`: Recipient's phone number with country code
  - `latitude`: Latitude coordinate
  - `longitude`: Longitude coordinate
  - `name`: Name of the location
  - `address`: Address of the location

### Send Contact
- **Endpoint**: `POST /message/sendContact/{instance}`
- **Description**: Sends a contact
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `number`: Recipient's phone number with country code
  - `contact`: Contact information object

### Send Reaction
- **Endpoint**: `POST /message/sendReaction/{instance}`
- **Description**: Sends a reaction to a message
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `reactionMessage`: Object containing reaction details
  - `key`: Object containing message key details

### Send Poll
- **Endpoint**: `POST /message/sendPoll/{instance}`
- **Description**: Sends a poll
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `number`: Recipient's phone number with country code
  - `name`: Poll question
  - `options`: Array of poll options
  - `selectableCount`: Number of options that can be selected

### Send List
- **Endpoint**: `POST /message/sendList/{instance}`
- **Description**: Sends a list message
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `number`: Recipient's phone number with country code
  - `buttonText`: Text for the button
  - `description`: Description of the list
  - `sections`: Array of list sections

### Send Buttons
- **Endpoint**: `POST /message/sendButton/{instance}`
- **Description**: Sends a button message
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `number`: Recipient's phone number with country code
  - `buttonText`: Array of button texts
  - `description`: Description of the buttons
  - `title`: Title of the message

## Chat Management

### Check is WhatsApp
- **Endpoint**: `POST /chat/check-is-whatsapp/{instance}`
- **Description**: Checks if phone numbers are registered on WhatsApp
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `numbers`: Array of phone numbers to check

### Mark Message As Read
- **Endpoint**: `POST /chat/markMessageAsRead/{instance}`
- **Description**: Marks a message as read
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `key`: Object containing message key details

### Mark Message As Unread
- **Endpoint**: `POST /chat/markMessageAsUnread/{instance}`
- **Description**: Marks a message as unread
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `key`: Object containing message key details

### Archive Chat
- **Endpoint**: `POST /chat/archiveChat/{instance}`
- **Description**: Archives a chat
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `number`: Phone number with country code
  - `archive`: Boolean to archive/unarchive

### Delete Message for Everyone
- **Endpoint**: `DELETE /chat/deleteMessageForEveryone/{instance}`
- **Description**: Deletes a message for everyone
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `key`: Object containing message key details

### Update Message
- **Endpoint**: `POST /chat/updateMessage/{instance}`
- **Description**: Updates a message
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `key`: Object containing message key details
  - `text`: New message text

### Send Presence
- **Endpoint**: `POST /chat/sendPresence/{instance}`
- **Description**: Sends presence status to a chat
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `number`: Phone number with country code
  - `presence`: Presence status

### Update Block Status
- **Endpoint**: `POST /chat/updateBlockStatus/{instance}`
- **Description**: Blocks or unblocks a contact
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `number`: Phone number with country code
  - `action`: Block action (block/unblock)

### Fetch Profile Picture URL
- **Endpoint**: `POST /chat/fetchProfilePictureUrl/{instance}`
- **Description**: Fetches the profile picture URL of a contact
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `number`: Phone number with country code

### Get Base64
- **Endpoint**: `POST /chat/getBase64/{instance}`
- **Description**: Gets the base64 of a media message
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `key`: Object containing message key details

### Find Contacts
- **Endpoint**: `POST /chat/findContacts/{instance}`
- **Description**: Finds contacts
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `where`: Object containing search criteria

### Find Messages
- **Endpoint**: `POST /chat/findMessages/{instance}`
- **Description**: Finds messages in a chat
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `where`: Object containing search criteria with `key.remoteJid` for the contact

### Find Status Message
- **Endpoint**: `POST /chat/findStatusMessage/{instance}`
- **Description**: Finds status messages
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `where`: Object containing search criteria

### Find Chats
- **Endpoint**: `POST /chat/findChats/{instance}`
- **Description**: Finds chats
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `where`: Object containing search criteria

## Profile Management

### Fetch Business Profile
- **Endpoint**: `POST /profile/fetchBusinessProfile/{instance}`
- **Description**: Fetches the business profile
- **Path Parameters**:
  - `instance`: ID of the instance

### Fetch Profile
- **Endpoint**: `POST /profile/fetchProfile/{instance}`
- **Description**: Fetches the profile
- **Path Parameters**:
  - `instance`: ID of the instance

### Update Profile Name
- **Endpoint**: `POST /profile/updateProfileName/{instance}`
- **Description**: Updates the profile name
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `name`: New profile name

### Update Profile Status
- **Endpoint**: `POST /profile/updateProfileStatus/{instance}`
- **Description**: Updates the profile status
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `status`: New profile status

### Update Profile Picture
- **Endpoint**: `POST /profile/updateProfilePicture/{instance}`
- **Description**: Updates the profile picture
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `picture`: URL or base64 of the picture

### Remove Profile Picture
- **Endpoint**: `DELETE /profile/removeProfilePicture/{instance}`
- **Description**: Removes the profile picture
- **Path Parameters**:
  - `instance`: ID of the instance

### Fetch Privacy Settings
- **Endpoint**: `GET /profile/fetchPrivacySettings/{instance}`
- **Description**: Fetches privacy settings
- **Path Parameters**:
  - `instance`: ID of the instance

### Update Privacy Settings
- **Endpoint**: `POST /profile/updatePrivacySettings/{instance}`
- **Description**: Updates privacy settings
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `readreceipts`: Read receipts setting
  - `profile`: Profile visibility setting
  - `status`: Status visibility setting
  - `online`: Online status visibility setting
  - `last`: Last seen visibility setting
  - `groupadd`: Group add setting

## Group Management

### Create Group
- **Endpoint**: `POST /group/create/{instance}`
- **Description**: Creates a new group
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `subject`: Group name
  - `participants`: Array of participant phone numbers
  - `description`: Group description

### Update Group Picture
- **Endpoint**: `POST /group/updateGroupPicture/{instance}`
- **Description**: Updates a group's picture
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `groupId`: Group ID
  - `picture`: URL or base64 of the picture

### Update Group Subject
- **Endpoint**: `POST /group/updateGroupSubject/{instance}`
- **Description**: Updates a group's subject
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `groupId`: Group ID
  - `subject`: New group subject

### Update Group Description
- **Endpoint**: `POST /group/updateGroupDescription/{instance}`
- **Description**: Updates a group's description
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `groupId`: Group ID
  - `description`: New group description

### Fetch Invite Code
- **Endpoint**: `GET /group/fetchInviteCode/{instance}/{groupId}`
- **Description**: Fetches a group's invite code
- **Path Parameters**:
  - `instance`: ID of the instance
  - `groupId`: Group ID

### Revoke Invite Code
- **Endpoint**: `POST /group/revokeInviteCode/{instance}`
- **Description**: Revokes a group's invite code
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `groupId`: Group ID

### Send Group Invite
- **Endpoint**: `POST /group/sendInviteV2/{instance}`
- **Description**: Sends a group invite
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `groupId`: Group ID
  - `number`: Phone number to invite

### Find Group by Invite Code
- **Endpoint**: `GET /group/findGroupByInviteCode/{instance}/{inviteCode}`
- **Description**: Finds a group by invite code
- **Path Parameters**:
  - `instance`: ID of the instance
  - `inviteCode`: Group invite code

### Find Group by JID
- **Endpoint**: `GET /group/findGroupByJid/{instance}/{groupId}`
- **Description**: Finds a group by JID
- **Path Parameters**:
  - `instance`: ID of the instance
  - `groupId`: Group ID

### Fetch All Groups
- **Endpoint**: `GET /group/fetchAllGroups/{instance}`
- **Description**: Fetches all groups
- **Path Parameters**:
  - `instance`: ID of the instance

### Find Group Members
- **Endpoint**: `GET /group/findParticipants/{instance}/{groupId}`
- **Description**: Finds group members
- **Path Parameters**:
  - `instance`: ID of the instance
  - `groupId`: Group ID

### Update Group Members
- **Endpoint**: `POST /group/updateParticipant/{instance}`
- **Description**: Updates group members
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `groupId`: Group ID
  - `action`: Action to perform (add, remove, promote, demote)
  - `participants`: Array of participant phone numbers

### Update Group Setting
- **Endpoint**: `POST /group/updateSetting/{instance}`
- **Description**: Updates group settings
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `groupId`: Group ID
  - `action`: Setting to update (announcement, not_announcement, locked, unlocked)

### Toggle Ephemeral
- **Endpoint**: `POST /group/toggleEphemeral/{instance}`
- **Description**: Toggles ephemeral messages
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `groupId`: Group ID
  - `expiration`: Expiration time in seconds

### Leave Group
- **Endpoint**: `DELETE /group/leaveGroup/{instance}`
- **Description**: Leaves a group
- **Path Parameters**:
  - `instance`: ID of the instance
- **Body Parameters**:
  - `groupId`: Group ID

## Integration Options

The Evolution API supports integration with various services:

### Webhook Integration
- Configure webhooks to receive real-time updates for events like new messages, status changes, etc.
- Events can be filtered by type (e.g., MESSAGES_UPSERT, CONNECTION_UPDATE)
- Webhook payloads include detailed information about the event

### AI Integration
- OpenAI: Create and manage OpenAI bots for automated responses
- Evolution Bot: Use the built-in Evolution Bot for automated interactions
- Typebot: Create interactive flows with Typebot
- Dify: Integrate with Dify for AI-powered conversations
- Flowise: Use Flowise for visual AI workflow creation

### Other Integrations
- Chatwoot: Integrate with Chatwoot for customer support
- Websocket: Use WebSockets for real-time communication
- SQS: Integrate with Amazon SQS for message queuing
- RabbitMQ: Use RabbitMQ for message queuing

## Implementation Recommendations

### For Your Application

Based on your application's needs, here are some recommended implementations:

1. **Real-time Message Handling**:
   - Set up webhooks to receive real-time notifications of new messages
   - Use the `/chat/findMessages/{instance}` endpoint to retrieve message history
   - Implement WebSocket connections for real-time updates in the frontend

2. **Contact Management**:
   - Use `/chat/findContacts/{instance}` to retrieve contacts
   - Implement `/chat/check-is-whatsapp/{instance}` to verify if numbers are on WhatsApp
   - Sync WhatsApp contacts with your application's user database

3. **Automated Responses**:
   - Implement webhook handlers to process incoming messages
   - Use AI integrations for automated responses
   - Set up message templates for common responses

4. **Media Handling**:
   - Use the media endpoints to send and receive images, videos, and documents
   - Implement base64 encoding/decoding for media files
   - Store media files securely and efficiently

5. **Group Management**:
   - Create and manage groups programmatically
   - Implement group invite functionality
   - Manage group settings and members

6. **Security Considerations**:
   - Secure your API key
   - Validate webhook requests
   - Implement rate limiting
   - Use HTTPS for all API calls

### Best Practices

1. **Error Handling**:
   - Implement robust error handling for all API calls
   - Log errors and implement retry mechanisms
   - Provide meaningful error messages to users

2. **Performance Optimization**:
   - Cache frequently accessed data
   - Implement pagination for large data sets
   - Use WebSockets for real-time updates instead of polling

3. **User Experience**:
   - Provide feedback on message status (sent, delivered, read)
   - Implement typing indicators
   - Show online/offline status

4. **Compliance**:
   - Ensure compliance with WhatsApp's terms of service
   - Implement proper user consent mechanisms
   - Handle user data according to privacy regulations

5. **Monitoring and Analytics**:
   - Monitor API usage and performance
   - Track message delivery rates
   - Analyze user engagement metrics
