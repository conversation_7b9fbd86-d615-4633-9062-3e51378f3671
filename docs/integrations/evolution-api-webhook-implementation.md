# Evolution API Webhook Implementation for FixMyCal

This document outlines the specific implementation plan for integrating Evolution API webhooks into the FixMyCal application to enable real-time WhatsApp messaging.

## Current Architecture

The application currently uses:
- A FastAPI backend (`backend/app/main.py`)
- A React frontend with a dedicated MessagesPage (`src/pages/dashboard/MessagesPage.tsx`)
- Polling mechanism for checking new messages (every 10 seconds)
- WhatsApp integration via Evolution API

## Implementation Plan

### 1. Backend: Configure Webhook Endpoint

The backend already has a webhook endpoint at `/whatsapp/webhook/{user_id}` in `backend/app/routes/whatsapp.py`. We need to enhance this to:

1. Register this endpoint with the Evolution API when a user connects WhatsApp
2. Process incoming webhook events for real-time updates

### 2. Backend: Update WhatsApp Connection Process

Modify the WhatsApp connection process to register the webhook:

```python
# In backend/app/routes/whatsapp.py

@router.post("/connect")
async def connect_whatsapp(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Create a WhatsApp instance and get QR code for connection"""
    try:
        user_id = current_user["id"]
        
        # ... existing connection code ...
        
        # After successful instance creation, set up the webhook
        webhook_url = f"{settings.BACKEND_URL}/whatsapp/webhook/{user_id}"
        
        webhook_response = requests.post(
            f"{settings.EVOLUTION_API_URL}/webhook/set/{user_id}",
            headers=EVOLUTION_HEADERS,
            json={
                "enabled": True,
                "url": webhook_url,
                "webhookByEvents": True,
                "webhookBase64": True,
                "events": [
                    "MESSAGES_UPSERT",
                    "MESSAGES_UPDATE",
                    "SEND_MESSAGE",
                    "CONNECTION_UPDATE"
                ]
            }
        )
        
        print(f"Webhook setup response: {webhook_response.status_code} - {webhook_response.text}")
        
        # ... rest of the connection code ...
    
    except Exception as e:
        print(f"Error connecting WhatsApp: {str(e)}")
        return {"success": False, "message": f"Error: {str(e)}"}
```

### 3. Backend: Enhance Webhook Processing

Improve the existing webhook endpoint to handle different event types:

```python
@router.post("/webhook/{user_id}")
async def whatsapp_webhook(
    user_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """Webhook for WhatsApp messages and events"""
    try:
        # Get the webhook data
        webhook_data = await request.json()
        event_type = webhook_data.get('event')
        
        print(f"Received webhook for user {user_id}, event: {event_type}")
        
        # Process based on event type
        if event_type == "MESSAGES_UPSERT":
            # Handle new messages
            await process_new_messages(user_id, webhook_data, db)
        elif event_type == "MESSAGES_UPDATE":
            # Handle message updates (read status, etc.)
            await process_message_updates(user_id, webhook_data, db)
        elif event_type == "SEND_MESSAGE":
            # Handle sent messages confirmation
            await process_sent_message(user_id, webhook_data, db)
        elif event_type == "CONNECTION_UPDATE":
            # Handle connection status changes
            await process_connection_update(user_id, webhook_data, db)
            
        return {"success": True}
    
    except Exception as e:
        print(f"Error processing webhook: {str(e)}")
        return {"success": False, "message": f"Error: {str(e)}"}
```

### 4. Backend: Create WebSocket for Real-time Frontend Updates

Add a WebSocket endpoint to push updates to the frontend:

```python
# In backend/app/main.py
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
# ... other imports ...

# Create a connection manager for WebSockets
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        if user_id not in self.active_connections:
            self.active_connections[user_id] = []
        self.active_connections[user_id].append(websocket)

    def disconnect(self, websocket: WebSocket, user_id: str):
        if user_id in self.active_connections:
            self.active_connections[user_id].remove(websocket)

    async def send_message(self, message: dict, user_id: str):
        if user_id in self.active_connections:
            for connection in self.active_connections[user_id]:
                await connection.send_json(message)

manager = ConnectionManager()

# Add WebSocket endpoint
@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    await manager.connect(websocket, user_id)
    try:
        while True:
            # Keep the connection alive
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
```

### 5. Backend: Update Webhook Handler to Use WebSockets

Modify the webhook handler to push updates via WebSocket:

```python
async def process_new_messages(user_id, webhook_data, db):
    # Process the message as before
    # ...
    
    # Then send a notification via WebSocket
    await manager.send_message({
        "type": "new_message",
        "data": {
            "contact_id": f"whatsapp-{phone}",
            "message": {
                "id": message_id,
                "text": message_content,
                "timestamp": timestamp,
                "sender": "them"
            }
        }
    }, user_id)
```

### 6. Frontend: Add WebSocket Connection in MessagesPage

Update the MessagesPage to use WebSockets instead of polling:

```typescript
// In src/pages/dashboard/MessagesPage.tsx

// Add WebSocket connection
const [socket, setSocket] = useState<WebSocket | null>(null);

// Connect to WebSocket when component mounts
useEffect(() => {
  const token = localStorage.getItem('accessToken');
  if (!token) {
    console.warn('No authentication token found');
    navigate('/auth');
    return;
  }

  const userId = JSON.parse(atob(token.split('.')[1])).sub;
  const wsUrl = `${import.meta.env.VITE_WS_URL || 'ws://localhost:8000'}/ws/${userId}`;
  
  console.log(`Connecting to WebSocket at ${wsUrl}`);
  const newSocket = new WebSocket(wsUrl);
  
  newSocket.onopen = () => {
    console.log('WebSocket connection established');
    setSocket(newSocket);
  };
  
  newSocket.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log('WebSocket message received:', data);
    
    if (data.type === 'new_message') {
      handleNewMessage(data.data);
    } else if (data.type === 'message_update') {
      handleMessageUpdate(data.data);
    } else if (data.type === 'contact_update') {
      refreshContacts();
    }
  };
  
  newSocket.onerror = (error) => {
    console.error('WebSocket error:', error);
    addToast('Error connecting to real-time updates', 'error');
  };
  
  newSocket.onclose = () => {
    console.log('WebSocket connection closed');
    setSocket(null);
  };
  
  // Clean up on unmount
  return () => {
    if (newSocket) {
      newSocket.close();
    }
  };
}, [navigate, addToast, refreshContacts]);

// Function to handle new messages from WebSocket
const handleNewMessage = useCallback((data: any) => {
  const { contact_id, message } = data;
  
  // If this is for the currently selected contact, add it to the messages
  if (selectedContact?.id === contact_id) {
    setMessages(prev => [...prev, message]);
    
    // Update the last message timestamp
    setLastMessageTimestamp(prev => ({
      ...prev,
      [contact_id]: message.timestamp
    }));
    
    // Play a notification sound for incoming messages
    if (message.sender === 'them') {
      addToast(`New message from ${selectedContact.name}`, 'info');
    }
  } else {
    // If it's for a different contact, refresh contacts to update the list
    refreshContacts();
  }
}, [selectedContact, addToast, refreshContacts]);
```

### 7. Frontend: Update WhatsApp Service

Add WebSocket support to the WhatsApp service:

```typescript
// In src/services/whatsappService.ts

export interface WebSocketMessage {
  type: string;
  data: any;
}

export const whatsappService = {
  // ... existing methods ...
  
  /**
   * Create a WebSocket connection for real-time updates
   */
  createWebSocket(userId: string, onMessage: (data: WebSocketMessage) => void): WebSocket {
    const wsUrl = `${import.meta.env.VITE_WS_URL || 'ws://localhost:8000'}/ws/${userId}`;
    const socket = new WebSocket(wsUrl);
    
    socket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      onMessage(data);
    };
    
    return socket;
  }
};
```

## Testing the Implementation

1. Start the backend server
2. Connect a WhatsApp account through the settings page
3. Open the Messages page
4. Send a test message to the connected WhatsApp number from another phone
5. Verify that the message appears in real-time without polling

## Fallback Mechanism

To ensure reliability, we'll maintain the polling mechanism as a fallback:

1. If the WebSocket connection fails, fall back to polling
2. If a message is received via WebSocket but fails to process, the next poll will catch it
3. Implement reconnection logic for WebSocket to handle temporary disconnections

## Security Considerations

1. Validate the user_id in the webhook endpoint to ensure it matches a valid user
2. Use HTTPS for all API calls and WSS for WebSocket connections
3. Implement rate limiting on the webhook endpoint to prevent abuse
4. Add authentication to the WebSocket connection to prevent unauthorized access
