# Evolution API Webhook Integration Guide

This guide focuses specifically on implementing webhooks with the Evolution API v2 to enable real-time updates for WhatsApp messages and events.

## What are Webhooks?

Webhooks are user-defined HTTP callbacks that are triggered by specific events. When an event occurs in the Evolution API (like receiving a new message), the API makes an HTTP request to the URL configured for the webhook, sending data about the event.

## Benefits of Using Webhooks

1. **Real-time Updates**: Receive instant notifications when events occur
2. **Reduced Polling**: Eliminate the need for frequent API polling
3. **Efficiency**: Process only the events that matter to your application
4. **Scalability**: Handle high volumes of messages more efficiently

## Webhook Configuration

### Setting Up a Webhook

**Endpoint**: `POST /webhook/set/{instance}`

**Headers**:
- `Content-Type: application/json`
- `apikey: <your-api-key>`

**Path Parameters**:
- `instance`: Your WhatsApp instance ID

**Request Body**:
```json
{
  "enabled": true,
  "url": "https://your-server.com/webhook",
  "webhookByEvents": true,
  "webhookBase64": true,
  "events": [
    "MESSAGES_UPSERT",
    "MESSAGES_UPDATE",
    "MESSAGES_DELETE",
    "SEND_MESSAGE",
    "CONNECTION_UPDATE"
  ]
}
```

### Retrieving Webhook Configuration

**Endpoint**: `GET /webhook/find/{instance}`

**Headers**:
- `apikey: <your-api-key>`

**Path Parameters**:
- `instance`: Your WhatsApp instance ID

## Webhook Events

Here are the most relevant events for a messaging application:

| Event | Description |
|-------|-------------|
| `MESSAGES_UPSERT` | Triggered when new messages are received |
| `MESSAGES_UPDATE` | Triggered when messages are updated (e.g., read status) |
| `MESSAGES_DELETE` | Triggered when messages are deleted |
| `SEND_MESSAGE` | Triggered when a message is sent |
| `CONTACTS_UPSERT` | Triggered when new contacts are added |
| `CONTACTS_UPDATE` | Triggered when contacts are updated |
| `CHATS_UPSERT` | Triggered when new chats are created |
| `CHATS_UPDATE` | Triggered when chats are updated |
| `CONNECTION_UPDATE` | Triggered when connection status changes |

## Webhook Payload Examples

### New Message Received (MESSAGES_UPSERT)

```json
{
  "event": "MESSAGES_UPSERT",
  "instance": "your-instance-id",
  "data": {
    "key": {
      "remoteJid": "<EMAIL>",
      "fromMe": false,
      "id": "message-id-123"
    },
    "message": {
      "conversation": "Hello, this is a test message"
    },
    "messageTimestamp": 1620000000
  }
}
```

### Message Status Update (MESSAGES_UPDATE)

```json
{
  "event": "MESSAGES_UPDATE",
  "instance": "your-instance-id",
  "data": {
    "key": {
      "remoteJid": "<EMAIL>",
      "fromMe": true,
      "id": "message-id-123"
    },
    "update": {
      "status": "READ"
    }
  }
}
```

### Connection Status Change (CONNECTION_UPDATE)

```json
{
  "event": "CONNECTION_UPDATE",
  "instance": "your-instance-id",
  "data": {
    "connection": "open",
    "lastDisconnect": {
      "date": "2023-01-01T00:00:00.000Z",
      "error": null
    }
  }
}
```

## Implementing a Webhook Receiver

### Backend Implementation (Node.js Example)

```javascript
const express = require('express');
const app = express();
app.use(express.json());

// Webhook endpoint
app.post('/webhook', (req, res) => {
  const { event, instance, data } = req.body;
  
  console.log(`Received ${event} event from instance ${instance}`);
  
  switch (event) {
    case 'MESSAGES_UPSERT':
      handleNewMessage(data);
      break;
    case 'MESSAGES_UPDATE':
      handleMessageUpdate(data);
      break;
    case 'CONNECTION_UPDATE':
      handleConnectionUpdate(data);
      break;
    // Handle other events...
  }
  
  // Always respond with 200 OK to acknowledge receipt
  res.status(200).send('OK');
});

function handleNewMessage(data) {
  const { key, message, messageTimestamp } = data;
  const { remoteJid, fromMe, id } = key;
  
  console.log(`New message from ${remoteJid}: ${message.conversation || message.extendedTextMessage?.text}`);
  
  // Process the message (e.g., save to database, notify users)
}

// Start the server
app.listen(3000, () => {
  console.log('Webhook server listening on port 3000');
});
```

## Security Considerations

1. **Validate Webhook Source**: Implement authentication to ensure requests come from your Evolution API server
2. **Use HTTPS**: Always use HTTPS for your webhook endpoint
3. **Implement Rate Limiting**: Protect against potential abuse
4. **Add Timeout Handling**: Set appropriate timeouts for webhook processing

## Troubleshooting

1. **Webhook Not Receiving Events**:
   - Verify the webhook URL is accessible from the internet
   - Check that the webhook is properly configured in the Evolution API
   - Ensure the events you want to receive are included in the configuration

2. **Error Handling**:
   - Always respond with a 200 status code to acknowledge receipt
   - Log webhook payloads for debugging
   - Implement retry logic for failed processing

## Integration with Frontend Applications

For real-time updates in a React application:

1. **WebSocket Connection**: Set up a WebSocket connection between your frontend and backend
2. **Event Forwarding**: Forward webhook events from your backend to connected clients
3. **State Updates**: Update the application state based on received events

This approach allows your application to receive and display new messages in real-time without polling the API.
