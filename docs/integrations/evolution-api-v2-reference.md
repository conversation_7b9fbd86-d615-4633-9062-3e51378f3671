# Evolution API v2 Reference

This document provides a comprehensive reference for the Evolution API v2, focusing on key endpoints and functionality relevant to our WhatsApp integration.

## Base Information

- **Base URL**: `http://localhost:8080` (default) or environment variable `VITE_EVOLUTION_API_URL`
- **API Key**: Required for authentication, sent in the `apikey` header
- **Instance Name**: Required for most endpoints, identifies the WhatsApp instance

## Core Endpoints

### Instance Management

#### Create Instance
- **Endpoint**: `POST /instance/create`
- **Description**: Creates a new WhatsApp instance
- **Body**:
  ```json
  {
    "instanceName": "string",
    "token": "string",
    "qrcode": true,
    "webhook": "string",
    "webhookByEvents": true,
    "webhookBase64": true,
    "events": ["APPLICATION_STARTUP", "QRCODE_UPDATED", "MESSAGES_SET", "MESSAGES_UPSERT", "MESSAGES_UPDATE", "MESSAGES_DELETE", "SEND_MESSAGE", "CONTACTS_SET", "CONTACTS_UPSERT", "CONTACTS_UPDATE", "PRESENCE_UPDATE", "CHATS_SET", "CHATS_UPSERT", "CHATS_UPDATE", "CHATS_DELETE", "GROUPS_UPSERT", "GROUP_UPDATE", "GROUP_PARTICIPANTS_UPDATE", "CONNECTION_UPDATE", "CALL", "TYPEBOT_START", "TYPEBOT_CHANGE_STATUS"]
  }
  ```

#### Fetch Instances
- **Endpoint**: `GET /instance/fetchInstances`
- **Description**: Returns all created instances

#### Instance Connect
- **Endpoint**: `GET /instance/connect/{instance}`
- **Description**: Connects to an instance and returns QR code if needed

#### Connection State
- **Endpoint**: `GET /instance/connectionState/{instance}`
- **Description**: Returns the connection state of an instance

#### Delete Instance
- **Endpoint**: `DELETE /instance/delete/{instance}`
- **Description**: Deletes an instance

### Webhook Management

#### Set Webhook
- **Endpoint**: `POST /webhook/set/{instance}`
- **Description**: Configures a webhook for an instance
- **Body**:
  ```json
  {
    "enabled": true,
    "url": "string",
    "webhookByEvents": true,
    "webhookBase64": true,
    "events": ["APPLICATION_STARTUP", "QRCODE_UPDATED", "MESSAGES_SET", "MESSAGES_UPSERT", "MESSAGES_UPDATE", "MESSAGES_DELETE", "SEND_MESSAGE", "CONTACTS_SET", "CONTACTS_UPSERT", "CONTACTS_UPDATE", "PRESENCE_UPDATE", "CHATS_SET", "CHATS_UPSERT", "CHATS_UPDATE", "CHATS_DELETE", "GROUPS_UPSERT", "GROUP_UPDATE", "GROUP_PARTICIPANTS_UPDATE", "CONNECTION_UPDATE", "CALL", "TYPEBOT_START", "TYPEBOT_CHANGE_STATUS"]
  }
  ```

#### Find Webhook
- **Endpoint**: `GET /webhook/find/{instance}`
- **Description**: Returns the webhook configuration for an instance

### Message Management

#### Send Plain Text
- **Endpoint**: `POST /message/text/{instance}`
- **Description**: Sends a text message
- **Body**:
  ```json
  {
    "number": "string",
    "options": {
      "delay": 1200,
      "presence": "composing"
    },
    "textMessage": {
      "text": "string"
    }
  }
  ```

#### Find Messages
- **Endpoint**: `POST /chat/findMessages/{instance}`
- **Description**: Retrieves messages from a specific chat
- **Body**:
  ```json
  {
    "where": {
      "key": {
        "remoteJid": "string"
      }
    }
  }
  ```

### Contact Management

#### Find Contacts
- **Endpoint**: `POST /chat/findContacts/{instance}`
- **Description**: Retrieves contacts
- **Body**:
  ```json
  {
    "where": {}
  }
  ```

#### Check is WhatsApp
- **Endpoint**: `POST /chat/check-is-whatsapp/{instance}`
- **Description**: Checks if phone numbers are registered on WhatsApp
- **Body**:
  ```json
  {
    "numbers": ["string"]
  }
  ```

## Webhook Events

Webhooks can be configured to receive notifications for various events:

- `APPLICATION_STARTUP`: When the application starts
- `QRCODE_UPDATED`: When the QR code is updated
- `MESSAGES_SET`: When messages are set
- `MESSAGES_UPSERT`: When new messages are received
- `MESSAGES_UPDATE`: When messages are updated
- `MESSAGES_DELETE`: When messages are deleted
- `SEND_MESSAGE`: When a message is sent
- `CONTACTS_SET`: When contacts are set
- `CONTACTS_UPSERT`: When contacts are added
- `CONTACTS_UPDATE`: When contacts are updated
- `PRESENCE_UPDATE`: When presence status changes
- `CHATS_SET`: When chats are set
- `CHATS_UPSERT`: When chats are added
- `CHATS_UPDATE`: When chats are updated
- `CHATS_DELETE`: When chats are deleted
- `GROUPS_UPSERT`: When groups are added
- `GROUP_UPDATE`: When groups are updated
- `GROUP_PARTICIPANTS_UPDATE`: When group participants change
- `CONNECTION_UPDATE`: When connection status changes
- `CALL`: When a call is received
- `TYPEBOT_START`: When a typebot starts
- `TYPEBOT_CHANGE_STATUS`: When a typebot status changes

## Webhook Implementation

To implement webhooks in your application:

1. Create a server endpoint to receive webhook events
2. Configure the webhook in the Evolution API using the `/webhook/set/{instance}` endpoint
3. Process incoming webhook events based on their type

Example webhook payload for a new message:
```json
{
  "event": "MESSAGES_UPSERT",
  "instance": "instance-name",
  "data": {
    "key": {
      "remoteJid": "<EMAIL>",
      "fromMe": false,
      "id": "message-id"
    },
    "message": {
      "conversation": "Hello, this is a test message"
    },
    "messageTimestamp": 1620000000
  }
}
```

## Best Practices

1. Always validate webhook requests to ensure they come from your Evolution API server
2. Implement error handling for webhook processing
3. Consider using a queue system for processing webhook events to handle high volumes
4. Set up monitoring for webhook failures
5. Implement retry logic for failed webhook deliveries
