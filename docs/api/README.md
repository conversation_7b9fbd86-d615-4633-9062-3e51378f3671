# API Documentation

This section contains documentation for the FixMyCalSaaS API.

## Contents

- [API Documentation Guide](./api_documentation_guide.md) - Overview of the API structure and how to use the interactive documentation

## API Overview

The FixMyCalSaaS API is organized into the following functional groups:

### Authentication and User Management
- User registration and login
- Two-factor authentication
- User profile management

### Client Management
- Client CRUD operations
- Client filtering and search
- Client export

### Appointment Management
- Appointment scheduling
- Conflict detection
- Google Calendar synchronization
- Appointment reminders

### Service Management
- Service types
- Service configuration

### WhatsApp Integration
- WhatsApp connection
- Message sending and receiving
- Media handling

### AI and Conversations
- AI configuration
- Knowledge document management
- Conversation processing

### Billing and Invoices
- Subscription plans
- Payment processing
- Invoice management

## Using the API

The API is accessible at `http://localhost:8000` when running locally. Interactive documentation is available at:

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

Most endpoints require authentication using JWT tokens. See the [API Documentation Guide](./api_documentation_guide.md) for details on authentication and using the API.
