# FixMyCalSaaS API Documentation Guide

This guide provides an overview of the FixMyCalSaaS API structure and how to use the interactive documentation.

## Accessing the API Documentation

The API documentation is available at:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## API Structure

The API is organized into logical functional groups:

### Authentication and User Management
- **auth**: User registration, login, token refresh
- **2fa**: Two-factor authentication setup and verification
- **profile**: User profile management

### Client Management
- **clients**: Client CRUD operations, filtering, and export

### Appointment Management
- **appointments**: Appointment scheduling, updates, and conflict checking
- **reminders**: Appointment reminder settings and notifications

### Service Management
- **services**: Service types and services CRUD operations

### WhatsApp Integration
- **whatsapp**: WhatsApp connection, status, and messaging
- **messages**: Message management and conversation history
- **client-lookup**: Client lookup by phone number for WhatsApp integration

### AI and Conversations
- **ai-configuration**: AI assistant configuration and knowledge management
- **ai-response**: AI response generation
- **ai-messaging**: AI message processing for clients
- **conversations**: Conversation management

### Google Calendar Integration
- **integrations**: Google Calendar authentication and synchronization
- **google-calendar**: Google Calendar event caching and management

### Billing and Invoices
- **billing**: Subscription plans, checkout, and payment management
- **invoices**: Invoice management and downloads

### Media Handling
- **media**: Media proxy for WhatsApp images and voice messages

## Key Workflows

### Client Management Workflow
1. Create a client (`POST /dashboard/clients`)
2. View clients (`GET /dashboard/clients`)
3. Update client information (`PUT /dashboard/clients/{client_id}`)
4. Filter clients by tags (`GET /dashboard/clients/filter-tag/{tag}`)

### Appointment Scheduling Workflow
1. Get available services (`GET /dashboard/services`)
2. Create an appointment (`POST /dashboard/appointments`)
3. Check for conflicts (`GET /dashboard/appointments/check-conflicts`)
4. Sync with Google Calendar (`POST /dashboard/appointments/sync-with-google`)
5. Set up reminders (`PUT /dashboard/reminder-settings`)

### WhatsApp Integration Workflow
1. Connect to WhatsApp (`POST /whatsapp/connect`)
2. Check WhatsApp status (`GET /whatsapp/status`)
3. Configure AI responses (`POST /ai/ai-configuration`)
4. View incoming messages (`GET /dashboard/messages/contacts`)
5. Send messages (`POST /dashboard/messages/{client_id}/send`)

### AI Configuration Workflow
1. Set up AI configuration (`POST /ai/ai-configuration`)
2. Upload knowledge documents (`POST /ai/knowledge-documents/upload`)
3. Generate system prompt (`GET /ai/generate-system-prompt`)
4. Test AI responses (`POST /ai/generate-response`)

## Using the Interactive Documentation

1. **Authentication**: Click the "Authorize" button at the top of the Swagger UI and enter your JWT token
2. **Exploring Endpoints**: Expand a tag section to see all endpoints in that category
3. **Testing Endpoints**: Click "Try it out" on any endpoint to make a real API call
4. **Request/Response Models**: View the expected request body structure and response formats

## API Response Codes

- **200**: Success
- **201**: Created
- **400**: Bad Request (invalid input)
- **401**: Unauthorized (authentication required)
- **403**: Forbidden (insufficient permissions)
- **404**: Not Found
- **422**: Validation Error (request data doesn't match expected format)
- **500**: Server Error

## API Authentication

Most endpoints require authentication using a JWT token. To authenticate:

1. Register a user (`POST /auth/register`) or login (`POST /auth/login`)
2. Use the returned token in the Authorization header: `Bearer {token}`
3. For testing in Swagger UI, click the "Authorize" button and enter: `Bearer {token}`

## Pagination

Many list endpoints support pagination with the following query parameters:
- `page`: Page number (default: 1)
- `page_size`: Number of items per page (default varies by endpoint)

## Filtering and Sorting

Many list endpoints support filtering and sorting with query parameters:
- `sort_by`: Field to sort by
- `order`: Sort order (`asc` or `desc`)
- `search`: Text search term
- Additional endpoint-specific filters
