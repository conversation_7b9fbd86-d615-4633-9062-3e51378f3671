# Architecture Documentation

This section contains architecture diagrams and technical design documents for the FixMyCalSaaS application.

## Contents

### Diagrams

- [Database Structure](./diagrams/database_structure.md) - Entity-Relationship Diagram (ERD) showing database tables and their relationships
- [Application Components](./diagrams/application_components.md) - Diagram showing how frontend components connect to backend services
- [WhatsApp Integration Flow](./diagrams/whatsapp_integration_flow.md) - Sequence diagram for WhatsApp integration
- [Google Calendar Integration Flow](./diagrams/google_calendar_integration_flow.md) - Sequence diagram for Google Calendar integration
- [AI Processing Flow](./diagrams/ai_processing_flow.md) - Flowchart for AI message processing

## System Architecture Overview

FixMyCalSaaS follows a modern web application architecture with:

- **Frontend**: React/TypeScript single-page application
- **Backend**: FastAPI Python backend
- **Database**: PostgreSQL with pgvector for vector embeddings
- **External Integrations**: Google Calendar, WhatsApp (via Evolution API), Stripe
- **AI Services**: Google Gemini AI for natural language processing

## Key Components

### Frontend Components
- Landing page for marketing and sign-up
- Authentication pages for login/registration
- Dashboard for overview and analytics
- Calendar for appointment management
- Messages interface for WhatsApp communication
- Client management interface
- AI configuration interface
- Settings and billing management

### Backend Services
- Authentication and authorization service
- Client management service
- Appointment scheduling service
- WhatsApp integration service
- Google Calendar integration service
- AI processing service
- Billing and subscription service

### Database Design
The database uses a relational model with PostgreSQL, with vector extensions for AI embeddings. See the [Database Structure](./diagrams/database_structure.md) diagram for details on tables and relationships.

## Integration Points

The application integrates with several external services:

1. **Google Calendar API**: For syncing appointments
2. **WhatsApp (via Evolution API)**: For client messaging
3. **Stripe API**: For payment processing
4. **Google Gemini AI**: For natural language processing

See the integration flow diagrams for detailed information on how these integrations work.
