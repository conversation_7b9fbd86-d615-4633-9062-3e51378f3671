# FixMyCalSaaS Database Structure

```mermaid
erDiagram
    %% Database Tables and Relationships
    USERS ||--o{ CLIENTS : manages
    USERS ||--o{ SERVICES : offers
    USERS ||--o{ APPOINTMENTS : schedules
    USERS ||--o{ EXTERNAL_INTEGRATIONS : configures
    USERS ||--o{ AI_CONFIGURATIONS : customizes
    USERS ||--o{ KNOWLEDGE_DOCUMENTS : creates
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ INVOICES : receives
    USERS ||--o{ GOOGLE_CALENDAR_CACHE : stores
    USERS ||--o{ NOTIFICATION_PREFERENCES : configures
    USERS ||--o{ USER_AUTH : authenticates

    CLIENTS ||--o{ APPOINTMENTS : books
    CLIENTS ||--o{ MESSAGES : receives
    CLIENTS ||--o{ AI_CONVERSATION_CONTEXTS : participates_in

    SERVICE_TYPES ||--o{ SERVICES : categorizes
    SERVICES ||--o{ APPOINTMENTS : used_in

    APPOINTMENTS }|--|| SERVICES : requires
    APPOINTMENTS }|--|| CLIENTS : booked_for

    PRICING_PLANS ||--o{ USERS : subscribes
    PRICING_PLANS ||--o{ INVOICES : generates

    AI_CONFIGURATIONS ||--o{ AI_CONVERSATION_CONTEXTS : configures
    AI_CONVERSATION_CONTEXTS ||--o{ CONVERSATION_MESSAGES : contains

    MESSAGES }o--|| MESSAGES : replies_to

    %% Database Table Attributes
    USERS {
        UUID id PK
        string email UK
        string password_hash
        string business_name
        string currency
        string address
        string phone
        string website
        string logo_url
        datetime registered_at
        boolean email_verified
        UUID plan_id FK
        string stripe_customer_id
        string twofa_secret
        boolean twofa_enabled
        JSON backup_codes
    }

    USER_AUTH {
        UUID id PK
        UUID user_id FK
        string refresh_token
        datetime expires_at
        string device_info
        datetime last_login
    }

    CLIENTS {
        UUID id PK
        UUID user_id FK
        string phone
        string name
        string email
        datetime created_at
        datetime last_appointment
        datetime next_appointment
        JSON tags
        string notes
        string profile_image
        string stripe_customer_id
    }

    SERVICES {
        UUID id PK
        UUID type_id FK
        UUID user_id FK
        string name
        string description
        float price
        int duration_minutes
        boolean active
    }

    SERVICE_TYPES {
        UUID id PK
        string name
    }



    APPOINTMENTS {
        UUID id PK
        UUID user_id FK
        UUID client_id FK
        UUID service_id FK
        datetime start_time
        datetime end_time
        enum status
        string notes
        string google_event_id
        boolean synced_with_google
        datetime last_synced
        boolean is_recurring
        enum recurrence_frequency
        int recurrence_interval
        datetime recurrence_end_date
        int recurrence_count
        JSON recurrence_days
        UUID recurrence_parent_id FK
        boolean has_conflict
        string conflict_notes
        datetime created_at
        datetime updated_at
    }

    MESSAGES {
        UUID id PK
        UUID user_id FK
        UUID client_id FK
        UUID parent_id FK
        text content
        boolean is_from_business
        datetime sent_at
        vector embedding
        JSON intent_data
        text source
    }

    EXTERNAL_INTEGRATIONS {
        UUID id PK
        UUID user_id FK
        string integration_type
        JSON credentials
        boolean active
        datetime last_synced
    }

    NOTIFICATION_PREFERENCES {
        UUID id PK
        UUID user_id FK
        string event_type
        boolean email_enabled
        boolean sms_enabled
        boolean push_enabled
        int reminder_minutes
        JSON settings
    }

    AI_CONFIGURATIONS {
        UUID id PK
        UUID user_id FK
        text system_prompt
        string tone
        string primary_language
        JSON supported_languages
        JSON business_rules
        JSON response_templates
        JSON whatsapp_settings
        datetime created_at
        datetime updated_at
    }

    KNOWLEDGE_DOCUMENTS {
        UUID id PK
        UUID user_id FK
        string title
        text content
        text embedding
        JSON document_metadata
        datetime created_at
    }

    AI_CONVERSATION_CONTEXTS {
        UUID id PK
        UUID user_id FK
        UUID client_id FK
        JSON context_data
        string conversation_language
        int message_count
        string last_intent
        boolean has_booking_intent
        string booking_stage
        JSON booking_data
        datetime created_at
        datetime updated_at
        datetime last_message_at
    }

    CONVERSATION_MESSAGES {
        UUID id PK
        UUID conversation_id FK
        UUID user_id FK
        UUID client_id FK
        text content
        string role
        string detected_language
        string detected_intent
        JSON message_metadata
        datetime created_at
    }

    PRICING_PLANS {
        UUID id PK
        string name
        float cost_per_message
        float cost_per_appointment
        float monthly_fee
        JSON features
        string stripe_price_id
        string stripe_product_id
        string stripe_annual_price_id
    }

    INVOICES {
        UUID id PK
        UUID user_id FK
        UUID plan_id FK
        date period_start
        date period_end
        float total
        string stripe_invoice_id
        string status
        string invoice_pdf_url
        string hosted_invoice_url
    }

    GOOGLE_CALENDAR_CACHE {
        UUID id PK
        UUID user_id FK
        string event_id
        JSON event_data
        datetime start_time
        datetime end_time
        datetime created_at
        datetime updated_at
    }
```
