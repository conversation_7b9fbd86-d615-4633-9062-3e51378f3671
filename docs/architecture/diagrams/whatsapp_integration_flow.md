# FixMyCalSaaS WhatsApp Integration Flow

```mermaid
sequenceDiagram
    participant Client as WhatsApp Client
    participant WhatsApp as WhatsApp
    participant Evolution as Evolution API
    participant Backend as FixMyCal Backend
    participant AI as AI Assistant
    participant DB as Database
    participant Calendar as Google Calendar
    
    Client->>WhatsApp: Send message
    WhatsApp->>Evolution: Forward message
    Evolution->>Backend: Webhook notification
    Backend->>DB: Save incoming message
    Backend->>AI: Process message
    AI->>DB: Retrieve conversation history
    AI->>DB: Retrieve knowledge documents
    AI->>AI: Generate response
    
    alt Appointment booking intent
        AI->>Calendar: Check availability
        Calendar->>AI: Return available slots
        AI->>Backend: Book appointment
        Backend->>Calendar: Create calendar event
        Backend->>DB: Save appointment
    end
    
    AI->>Backend: Return response
    Backend->>DB: Save AI response
    Backend->>Evolution: Send response
    Evolution->>WhatsApp: Forward response
    WhatsApp->>Client: Deliver message
```
