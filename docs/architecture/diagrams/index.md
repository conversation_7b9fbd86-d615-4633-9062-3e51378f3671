# FixMyCalSaaS Application Structure Documentation

This directory contains diagrams that illustrate the structure and flow of the FixMyCalSaaS application.

## Available Diagrams

1. [Database Structure](database_structure.md) - Entity-Relationship Diagram (ERD) showing all database tables and their relationships.

2. [Application Components](application_components.md) - Diagram showing how frontend components connect to backend services and external integrations.

3. [WhatsApp Integration Flow](whatsapp_integration_flow.md) - Sequence diagram showing the flow of messages from WhatsApp clients through the system.

4. [Google Calendar Integration Flow](google_calendar_integration_flow.md) - Sequence diagram illustrating how appointments sync with Google Calendar.

5. [AI Processing Flow](ai_processing_flow.md) - Flowchart detailing how the AI assistant processes messages and takes actions.

## How to View These Diagrams

These diagrams are written in Mermaid syntax. You can view them in several ways:

1. **GitHub**: If you're viewing these files on GitHub, the diagrams will render automatically.

2. **Mermaid Live Editor**: Visit [Mermaid Live Editor](https://mermaid.live/) and paste the diagram code to visualize and edit.

3. **VS Code**: Install the "Markdown Preview Mermaid Support" extension to view diagrams directly in VS Code.

4. **Browser Extensions**: Various browser extensions can render Mermaid diagrams in Markdown files.

## Diagram Descriptions

### Database Structure
Shows all database tables, their attributes, and relationships between tables. This provides a comprehensive view of the data model.

### Application Components
Illustrates how different parts of the application connect to each other, from frontend pages to backend APIs and external services.

### WhatsApp Integration Flow
Details the sequence of events when a client sends a WhatsApp message, including how the message is processed by the AI assistant and how appointments can be booked.

### Google Calendar Integration Flow
Shows how appointments are synchronized with Google Calendar, both when creating/updating appointments and when viewing the calendar.

### AI Processing Flow
Provides a detailed flowchart of how the AI assistant processes incoming messages, detects intents, and takes actions like booking appointments.
