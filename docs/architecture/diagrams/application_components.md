# FixMyCalSaaS Application Components Architecture

## Overview
This diagram shows the complete application architecture including frontend components, backend services, external integrations, and data flow between all systems.

```mermaid
flowchart TB
    %% ===========================================
    %% FRONTEND LAYER
    %% ===========================================

    subgraph "🎨 Frontend - React/TypeScript"
        direction TB

        subgraph "📱 Main Pages"
            Landing["🌟 Landing Page"]
            Auth["🔐 Authentication"]
            Dashboard["📊 Dashboard"]
            Calendar["📅 Calendar"]
            Messages["💬 Messages"]
            Clients["👥 Clients"]
            Settings["⚙️ Settings"]
        end

        subgraph "🔧 Frontend Services"
            WhatsAppSvc["💬 WhatsApp Service"]
            AISvc["🤖 AI Service"]
            CalendarSvc["📅 Calendar Service"]
            AuthSvc["🔐 Auth Service"]
        end
    end

    %% ===========================================
    %% BACKEND LAYER
    %% ===========================================

    subgraph "🔧 Backend - FastAPI/Python"
        direction TB

        subgraph "🛣️ API Layer"
            AuthAPI["🔐 Auth API"]
            WhatsAppAPI["💬 WhatsApp API"]
            CalendarAPI["📅 Calendar API"]
            ClientAPI["👥 Client API"]
            AIAPI["🤖 AI Config API"]
        end

        subgraph "🧠 Core Services"
            AIAssistant["🤖 AI Assistant<br/>(Main Brain)"]
            WhatsAppService["💬 WhatsApp Service"]
            GoogleCalendar["📅 Google Calendar"]
            Database["🗄️ Database Models"]
        end
    end

    %% ===========================================
    %% EXTERNAL SERVICES
    %% ===========================================

    subgraph "🌐 External Services"
        direction LR

        WhatsApp["💬 WhatsApp<br/>(Evolution API)"]
        GeminiAI["🧠 Gemini AI"]
        GoogleCal["📅 Google Calendar"]
        Stripe["💳 Stripe"]
    end

    %% ===========================================
    %% DATABASE
    %% ===========================================

    subgraph "🗄️ Data Storage"
        PostgreSQL[("🗄️ PostgreSQL<br/>+ pgvector")]
        Redis[("📦 Redis Cache")]
    end

    %% ===========================================
    %% INFRASTRUCTURE
    %% ===========================================

    subgraph "🐳 Infrastructure"
        Docker["🐳 Docker<br/>5 Containers"]
    end

    %% ===========================================
    %% KEY CONNECTIONS
    %% ===========================================

    %% Frontend to Backend
    Messages --> WhatsAppSvc
    Calendar --> CalendarSvc
    Auth --> AuthSvc

    WhatsAppSvc --> WhatsAppAPI
    CalendarSvc --> CalendarAPI
    AuthSvc --> AuthAPI

    %% Backend Internal
    WhatsAppAPI --> AIAssistant
    CalendarAPI --> GoogleCalendar

    %% Backend to External
    AIAssistant --> GeminiAI
    WhatsAppService --> WhatsApp
    GoogleCalendar --> GoogleCal

    %% Backend to Database
    AIAssistant --> PostgreSQL
    Database --> PostgreSQL

    %% Infrastructure
    Docker --> PostgreSQL
    Docker --> Redis

    %% ===========================================
    %% STYLING
    %% ===========================================

    classDef frontend fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000
    classDef backend fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef external fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    classDef database fill:#e8f5e9,stroke:#388e3c,stroke-width:2px,color:#000
    classDef infrastructure fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000
    classDef ai fill:#f1f8e9,stroke:#689f38,stroke-width:2px,color:#000

    %% Apply styles
    class Landing,Auth,Dashboard,Calendar,Messages,Clients,Settings,WhatsAppSvc,AISvc,CalendarSvc,AuthSvc frontend
    class AuthAPI,WhatsAppAPI,CalendarAPI,ClientAPI,AIAPI,WhatsAppService,GoogleCalendar,Database backend
    class AIAssistant ai
    class WhatsApp,GeminiAI,GoogleCal,Stripe external
    class PostgreSQL,Redis database
    class Docker infrastructure
```

```mermaid
flowchart LR
    subgraph "🎨 Frontend - React/TypeScript"
        Landing["🌟 Landing"]
        Dashboard["📊 Dashboard"]
        Messages["💬 Messages"]
        Calendar["📅 Calendar"]
        Clients["👥 Clients"]
        Settings["⚙️ Settings"]

        AuthService["🔐 Auth Service"]
        WhatsAppService["💬 WhatsApp Service"]
        CalendarService["📅 Calendar Service"]
        AIService["🤖 AI Service"]
        BillingService["💳 Billing Service"]

        APIClient["🌐 API Client"]
        WebSocket["🔄 WebSocket"]
    end

    subgraph "🔧 Backend - FastAPI"
        AuthAPI["🔐 Auth API"]
        ClientAPI["👥 Client API"]
        AppointmentAPI["📅 Appointment API"]
        MessageAPI["💬 Message API"]
        AIConfigAPI["🤖 AI API"]
        BillingAPI["💳 Billing API"]

        AIAssistant["🤖 AI Assistant"]
        WhatsAppIntegration["💬 WhatsApp Integration"]
        GoogleCalService["📅 Google Calendar Sync"]
        BookingService["📅 Booking Logic"]
        WebSocketManager["🔄 WebSocket Manager"]
    end

    subgraph "🌐 External Services"
        GoogleCalendar["📅 Google Calendar"]
        WhatsApp["💬 WhatsApp via Evolution API"]
        Gemini["🧠 Gemini AI"]
        Stripe["💳 Stripe"]
    end

    subgraph "🗄️ Data Layer"
        PostgreSQL["🗄️ PostgreSQL + pgvector"]
        Redis["📦 Redis"]
    end

    subgraph "🐳 Infrastructure"
        Docker["🐳 Docker Compose"]
    end

    %% Connections
    Messages --> WhatsAppService --> WhatsAppIntegration --> WhatsApp
    Calendar --> CalendarService --> AppointmentAPI --> BookingService --> PostgreSQL
    AIService --> AIConfigAPI --> AIAssistant --> Gemini
    BillingService --> BillingAPI --> Stripe

    APIClient --> AuthService --> AuthAPI
    Clients --> ClientAPI
    Dashboard --> APIClient
    WebSocket --> WebSocketManager

    AIAssistant --> PostgreSQL
    AIAssistant --> Redis
    WebSocketManager --> Redis
    BookingService --> GoogleCalService --> GoogleCalendar

    Docker --> PostgreSQL
    Docker --> Redis
```

## Architecture Overview

### 🎨 **Frontend Layer**
- **React/TypeScript** application with 7 main pages
- **Component-based architecture** with reusable UI elements
- **Service layer** for API communication and business logic
- **Real-time updates** through WebSocket connections

### 🔧 **Backend Layer**
- **FastAPI** with RESTful API endpoints
- **AI Assistant** as the core intelligence engine
- **Service classes** for business logic and external integrations
- **Database models** for data persistence

### 🌐 **External Integrations**
- **WhatsApp** via Evolution API for messaging
- **Gemini AI** for natural language processing
- **Google Calendar** for appointment synchronization
- **Stripe** for payment processing

### �️ **Data Storage**
- **PostgreSQL** with pgvector for AI embeddings
- **Redis** for caching and session management

### 🐳 **Infrastructure**
- **Docker containers** for scalable deployment
- **Microservices architecture** for maintainability

## Key Data Flows

### � **WhatsApp Automation**
```
WhatsApp Message → AI Assistant → Gemini AI → Automated Response
```

### 📅 **Appointment Booking**
```
Client Request → AI Detection → Calendar Check → Booking Confirmation
```

### � **Real-time Updates**
```
Backend Events → WebSocket → Frontend Updates
```

This simplified architecture focuses on the **core components** and their **essential relationships**, making it easier to understand the system's structure and data flow.
