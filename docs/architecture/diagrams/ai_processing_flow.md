# FixMyCalSaaS AI Processing Flow

```mermaid
flowchart TD
    Start([Client message received]) --> SaveMessage[Save message to database]
    SaveMessage --> CheckAIConfig[Check AI configuration]
    
    CheckAIConfig --> AutoResponsesEnabled{Auto-responses enabled?}
    AutoResponsesEnabled -- No --> End([End process])
    AutoResponsesEnabled -- Yes --> GetContext[Get conversation context]
    
    GetContext --> GetHistory[Retrieve conversation history]
    GetHistory --> DetectIntent[Detect intent]
    DetectIntent --> BookingIntent{Booking intent?}
    
    BookingIntent -- Yes --> GetSlots[Get available time slots]
    BookingIntent -- No --> SkipSlots[Skip slot retrieval]
    
    GetSlots --> RetrieveKnowledge[Retrieve relevant knowledge]
    SkipSlots --> RetrieveKnowledge
    
    RetrieveKnowledge --> PreparePrompt[Prepare AI prompt]
    PreparePrompt --> CallAI[Call AI service]
    CallAI --> ExtractAction[Extract actions from response]
    
    ExtractAction --> HasAction{Contains action?}
    HasAction -- No --> SaveResponse[Save response]
    HasAction -- Yes --> ProcessAction[Process action]
    
    ProcessAction --> BookAppointment{Book appointment?}
    BookAppointment -- Yes --> CreateAppointment[Create appointment]
    BookAppointment -- No --> OtherAction[Process other action]
    
    CreateAppointment --> GoogleCalendar[Sync with Google Calendar]
    GoogleCalendar --> AppendConfirmation[Append confirmation to response]
    OtherAction --> SaveResponse
    
    AppendConfirmation --> SaveResponse
    SaveResponse --> SendResponse[Send response to client]
    SendResponse --> End
```
