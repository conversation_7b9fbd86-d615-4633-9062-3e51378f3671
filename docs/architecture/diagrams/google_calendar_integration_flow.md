# FixMyCalSaaS Google Calendar Integration Flow

```mermaid
sequenceDiagram
    participant User as User
    participant Frontend as FixMyCal Frontend
    participant Backend as FixMyCal Backend
    participant DB as Database
    participant Google as Google Calendar API
    
    User->>Frontend: Create/Update Appointment
    Frontend->>Backend: Send appointment data
    Backend->>DB: Save appointment
    
    alt Google Calendar Connected
        Backend->>Google: Create/Update event
        Google->>Backend: Return event ID
        Backend->>DB: Update with Google event ID
    end
    
    Backend->>Frontend: Return success
    Frontend->>User: Show confirmation
    
    User->>Frontend: View Calendar
    Frontend->>Backend: Request appointments
    Backend->>DB: Check cache expiry
    
    alt Cache expired
        Backend->>Google: Fetch updated events
        Google->>Backend: Return events
        Backend->>DB: Update cache
    end
    
    Backend->>Frontend: Return appointments
    Frontend->>User: Display calendar
```
