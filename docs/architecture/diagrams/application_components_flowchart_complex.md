# FixMyCalSaaS Application Components Architecture

## Overview
This diagram shows the complete application architecture including frontend components, backend services, external integrations, and data flow between all systems.

```mermaid
flowchart TB
    %% ===========================================
    %% FRONTEND LAYER - React/TypeScript
    %% ===========================================

    subgraph "🎨 Frontend Layer - React/TypeScript"
        direction TB

        subgraph "📄 Core Pages"
            LandingPage["🌟 Landing Page<br/>Marketing & Sign-up"]
            AuthPage["🔐 Auth Page<br/>Login/Register/2FA"]
            NotFoundPage["❌ 404 Page<br/>Error Handling"]
        end

        subgraph "📊 Dashboard Pages"
            DashboardPage["📈 Dashboard<br/>Analytics & Widgets"]
            CalendarPage["📅 Calendar<br/>Appointment Scheduling"]
            MessagesPage["💬 Messages<br/>WhatsApp Communication"]
            ClientsPage["👥 Clients<br/>Customer Management"]
            ClientChatPage["💬 Client Chat<br/>Individual Conversations"]
            AIConfigPage["🤖 AI Config<br/>Assistant Settings"]
            SettingsPage["⚙️ Settings<br/>Business Configuration"]
        end

        subgraph "🧩 Component Library"
            UIComponents["🎨 UI Components<br/>Button, Modal, Toast, etc."]
            DashboardComponents["📊 Dashboard Components<br/>Charts, Widgets, Stats"]
            MessageComponents["💬 Message Components<br/>Chat Interface, Contacts"]
            CalendarComponents["📅 Calendar Components<br/>Month/Week/Day Views"]
            ClientComponents["👥 Client Components<br/>Forms, Lists, Details"]
            SettingsComponents["⚙️ Settings Components<br/>Tabs, Forms, Integrations"]
        end

        subgraph "🔧 Services Layer"
            WhatsAppService["💬 WhatsApp Service<br/>Message Handling"]
            AIService["🤖 AI Service<br/>Assistant Communication"]
            ClientService["👥 Client Service<br/>Customer Management"]
            AppointmentService["📅 Appointment Service<br/>Scheduling Logic"]
            AuthService["🔐 Auth Service<br/>JWT Management"]
            BillingService["💳 Billing Service<br/>Stripe Integration"]
        end

        subgraph "📱 State Management"
            ZustandStore["🗄️ Zustand Store<br/>Global State"]
            APIClient["🌐 API Client<br/>HTTP Wrapper"]
            WebSocketClient["🔄 WebSocket Client<br/>Real-time Updates"]
        end
    end

    %% ===========================================
    %% BACKEND LAYER - FastAPI/Python
    %% ===========================================

    subgraph "🔧 Backend Layer - FastAPI/Python"
        direction TB

        subgraph "🛣️ API Routes"
            AuthAPI["🔐 Auth API<br/>JWT, 2FA, Tokens"]
            ClientAPI["👥 Client API<br/>CRUD Operations"]
            AppointmentAPI["📅 Appointment API<br/>Scheduling & Management"]
            ServiceAPI["🛠️ Service API<br/>Business Services"]
            WhatsAppAPI["💬 WhatsApp API<br/>Webhooks & Messaging"]
            MessagesAPI["💬 Messages API<br/>Chat History"]
            IntegrationsAPI["🔗 Integrations API<br/>External Services"]
            AIConfigAPI["🤖 AI Config API<br/>Assistant Settings"]
            BillingAPI["💳 Billing API<br/>Stripe Integration"]
            ProfileAPI["👤 Profile API<br/>User Management"]
        end

        subgraph "🧠 Core Services"
            AIAssistant["🤖 AI Assistant<br/>Message Processing & Booking"]
            WhatsAppServiceBackend["💬 WhatsApp Service<br/>Evolution API Integration"]
            GoogleCalendarService["📅 Google Calendar Service<br/>Two-way Sync"]
            GeminiService["🧠 Gemini Service<br/>AI Text Generation"]
            ConversationService["💬 Conversation Service<br/>Context Management"]
            EmbeddingService["🔍 Embedding Service<br/>Vector Search"]
            AppointmentServiceBackend["📅 Appointment Service<br/>Booking Logic"]
        end

        subgraph "🗄️ Database Models"
            UserModel["👤 User Model<br/>Business Owners"]
            ClientModel["👥 Client Model<br/>Customer Profiles"]
            AppointmentModel["📅 Appointment Model<br/>Scheduled Services"]
            ServiceModel["🛠️ Service Model<br/>Business Offerings"]
            MessageModel["💬 Message Model<br/>Chat History"]
            AIConfigModel["🤖 AI Config Model<br/>Assistant Settings"]
            AIContextModel["🧠 AI Context Model<br/>Conversation Memory"]
        end

        subgraph "⚙️ Infrastructure"
            FastAPIApp["🚀 FastAPI App<br/>Main Application"]
            WebSocketManager["🔄 WebSocket Manager<br/>Real-time Events"]
            TaskScheduler["⏰ Task Scheduler<br/>Background Jobs"]
            AuthMiddleware["🔐 Auth Middleware<br/>JWT Validation"]
        end
    end

    %% ===========================================
    %% EXTERNAL SERVICES
    %% ===========================================

    subgraph "🌐 External Services"
        direction TB

        subgraph "💬 WhatsApp Integration"
            EvolutionAPI["📱 Evolution API<br/>WhatsApp Gateway"]
            WhatsApp["💬 WhatsApp<br/>Messaging Platform"]
            EvolutionPostgres["🗄️ Evolution PostgreSQL<br/>WhatsApp Data"]
            EvolutionRedis["📦 Evolution Redis<br/>Message Cache"]
        end

        subgraph "🤖 AI Services"
            GeminiAI["🧠 Google Gemini AI<br/>Natural Language Processing"]
            OpenAI["🤖 OpenAI GPT<br/>Fallback AI Service"]
        end

        subgraph "📅 Calendar Integration"
            GoogleCalendarAPI["📅 Google Calendar API<br/>Event Synchronization"]
            GoogleOAuth["🔐 Google OAuth<br/>Authentication"]
        end

        subgraph "💳 Payment Processing"
            StripeAPI["💳 Stripe API<br/>Payment Processing"]
            StripeWebhooks["🔔 Stripe Webhooks<br/>Event Notifications"]
        end
    end

    %% ===========================================
    %% DATABASE LAYER
    %% ===========================================

    subgraph "🗄️ Database Layer"
        direction TB
        PostgreSQL[("🗄️ PostgreSQL<br/>Main Database<br/>+ pgvector")]
        Redis[("📦 Redis<br/>Cache & Sessions")]
    end

    %% ===========================================
    %% INFRASTRUCTURE
    %% ===========================================

    subgraph "🐳 Infrastructure - Docker"
        direction TB
        DockerCompose["🐳 Docker Compose<br/>5 Container Architecture"]
        BackendContainer["🔧 Backend Container<br/>FastAPI Application"]
        PostgresContainer["🗄️ Postgres Container<br/>Main Database"]
        EvolutionContainer["💬 Evolution Container<br/>WhatsApp Service"]
        EvolutionPostgresContainer["🗄️ Evolution Postgres<br/>WhatsApp Database"]
        RedisContainer["📦 Redis Container<br/>Cache Service"]
    end

    %% ===========================================
    %% CONNECTIONS - Frontend to Backend
    %% ===========================================

    %% Page to Service connections
    LandingPage --> AuthService
    AuthPage --> AuthService
    DashboardPage --> APIClient
    CalendarPage --> AppointmentService
    MessagesPage --> WhatsAppService
    ClientsPage --> ClientService
    AIConfigPage --> AIService
    SettingsPage --> BillingService

    %% Service to API connections
    WhatsAppService --> WhatsAppAPI
    AIService --> AIConfigAPI
    ClientService --> ClientAPI
    AppointmentService --> AppointmentAPI
    AuthService --> AuthAPI
    BillingService --> BillingAPI

    %% State management connections
    APIClient --> ZustandStore
    WebSocketClient --> WebSocketManager

    %% ===========================================
    %% CONNECTIONS - Backend Internal
    %% ===========================================

    %% API to Service connections
    WhatsAppAPI --> AIAssistant
    WhatsAppAPI --> WhatsAppServiceBackend
    AIConfigAPI --> AIAssistant
    AppointmentAPI --> AppointmentServiceBackend
    IntegrationsAPI --> GoogleCalendarService
    BillingAPI --> StripeAPI

    %% Service to Model connections
    AIAssistant --> AIConfigModel
    AIAssistant --> AIContextModel
    WhatsAppServiceBackend --> MessageModel
    AppointmentServiceBackend --> AppointmentModel
    GoogleCalendarService --> AppointmentModel

    %% Core service interactions
    AIAssistant --> GeminiService
    AIAssistant --> ConversationService
    AIAssistant --> EmbeddingService
    ConversationService --> AIContextModel

    %% ===========================================
    %% CONNECTIONS - External Services
    %% ===========================================

    %% WhatsApp flow
    WhatsAppServiceBackend --> EvolutionAPI
    EvolutionAPI --> WhatsApp
    EvolutionAPI --> EvolutionPostgres
    EvolutionAPI --> EvolutionRedis

    %% AI services
    GeminiService --> GeminiAI
    AIAssistant -.-> OpenAI

    %% Google services
    GoogleCalendarService --> GoogleCalendarAPI
    GoogleCalendarService --> GoogleOAuth

    %% Stripe services
    BillingAPI --> StripeAPI
    BillingAPI --> StripeWebhooks

    %% ===========================================
    %% CONNECTIONS - Database
    %% ===========================================

    %% Backend to Database
    UserModel --> PostgreSQL
    ClientModel --> PostgreSQL
    AppointmentModel --> PostgreSQL
    ServiceModel --> PostgreSQL
    MessageModel --> PostgreSQL
    AIConfigModel --> PostgreSQL
    AIContextModel --> PostgreSQL

    %% Cache connections
    WebSocketManager --> Redis
    AuthMiddleware --> Redis

    %% ===========================================
    %% CONNECTIONS - Infrastructure
    %% ===========================================

    %% Docker container relationships
    DockerCompose --> BackendContainer
    DockerCompose --> PostgresContainer
    DockerCompose --> EvolutionContainer
    DockerCompose --> EvolutionPostgresContainer
    DockerCompose --> RedisContainer

    %% Container to service mapping
    BackendContainer --> FastAPIApp
    PostgresContainer --> PostgreSQL
    EvolutionContainer --> EvolutionAPI
    EvolutionPostgresContainer --> EvolutionPostgres
    RedisContainer --> Redis

    %% ===========================================
    %% REAL-TIME CONNECTIONS
    %% ===========================================

    %% WebSocket connections (dotted lines for real-time)
    MessagesPage <-.-> WebSocketManager
    DashboardPage <-.-> WebSocketManager
    CalendarPage <-.-> WebSocketManager

    %% Webhook connections (dotted lines for events)
    WhatsApp -.-> WhatsAppAPI
    StripeWebhooks -.-> BillingAPI

    %% ===========================================
    %% STYLING
    %% ===========================================

    classDef frontend fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000
    classDef backend fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef external fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    classDef database fill:#e8f5e9,stroke:#388e3c,stroke-width:2px,color:#000
    classDef infrastructure fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000
    classDef ai fill:#f1f8e9,stroke:#689f38,stroke-width:2px,color:#000

    %% Frontend components
    class LandingPage,AuthPage,NotFoundPage,DashboardPage,CalendarPage,MessagesPage,ClientsPage,ClientChatPage,AIConfigPage,SettingsPage frontend
    class UIComponents,DashboardComponents,MessageComponents,CalendarComponents,ClientComponents,SettingsComponents frontend
    class WhatsAppService,AIService,ClientService,AppointmentService,AuthService,BillingService frontend
    class ZustandStore,APIClient,WebSocketClient frontend

    %% Backend components
    class AuthAPI,ClientAPI,AppointmentAPI,ServiceAPI,WhatsAppAPI,MessagesAPI,IntegrationsAPI,AIConfigAPI,BillingAPI,ProfileAPI backend
    class WhatsAppServiceBackend,GoogleCalendarService,ConversationService,EmbeddingService,AppointmentServiceBackend backend
    class UserModel,ClientModel,AppointmentModel,ServiceModel,MessageModel,AIConfigModel,AIContextModel backend
    class FastAPIApp,WebSocketManager,TaskScheduler,AuthMiddleware backend

    %% AI components
    class AIAssistant,GeminiService,GeminiAI,OpenAI ai

    %% External services
    class EvolutionAPI,WhatsApp,EvolutionPostgres,EvolutionRedis,GoogleCalendarAPI,GoogleOAuth,StripeAPI,StripeWebhooks external

    %% Database
    class PostgreSQL,Redis database

    %% Infrastructure
    class DockerCompose,BackendContainer,PostgresContainer,EvolutionContainer,EvolutionPostgresContainer,RedisContainer infrastructure
```
