# FixMyCalSaaS - Complete Project Structure

## 🏗️ **Project Architecture Overview**

```
FixMyCalSaaS/
├── 🎨 Frontend (React/TypeScript)
├── 🔧 Backend (FastAPI/Python)
├── 🐳 Infrastructure (Docker)
├── 📱 WhatsApp Integration (Evolution API)
└── 📊 Database (PostgreSQL + pgvector)
```

---

## 🎨 **Frontend Structure** (`src/`)

### **📄 Core Pages**
```
src/pages/
├── LandingPage.tsx                 # Marketing homepage
├── AuthPage.tsx                    # Login/Register
├── NotFoundPage.tsx               # 404 error page
└── dashboard/
    ├── DashboardPage.tsx          # Main analytics dashboard
    ├── CalendarPage.tsx           # Appointment scheduling
    ├── MessagesPage.tsx           # 💬 WhatsApp communication hub
    ├── ClientsPage.tsx            # Customer management
    ├── ClientChatPage.tsx         # Individual client chat
    ├── AIConfigPage.tsx           # AI assistant configuration
    ├── SettingsPage.tsx           # Business settings
    └── GoogleCallbackPage.tsx     # OAuth callback handler
```

### **🧩 Components Library**
```
src/components/
├── ui/                            # 🎨 Base UI Components
│   ├── Button.tsx                 # Animated buttons
│   ├── AnimatedModal.tsx          # Modal dialogs
│   ├── Toast.tsx                  # Notifications
│   ├── ThemeToggle.tsx           # Dark/light mode
│   ├── FileUpload.tsx            # File handling
│   └── FormField.tsx             # Form inputs
│
├── dashboard/                     # 📊 Dashboard Components
│   ├── DashboardLayout.tsx       # Main layout wrapper
│   ├── StatsCard.tsx             # Metric displays
│   ├── DashboardWidget.tsx       # Customizable widgets
│   └── charts/                   # 📈 Data visualization
│       ├── AppointmentStatusChart.tsx
│       ├── AppointmentTrendChart.tsx
│       ├── ClientGrowthChart.tsx
│       └── MessageActivityChart.tsx
│
├── messages/                      # 💬 WhatsApp Interface
│   ├── ContactList.tsx           # WhatsApp contacts
│   ├── MessageList.tsx           # Chat messages
│   ├── MessageInput.tsx          # Message composer
│   ├── MessageItem.tsx           # Individual message
│   └── ContactItem.tsx           # Contact display
│
├── calendar/                      # 📅 Calendar Components
│   ├── MonthView.tsx             # Monthly calendar
│   ├── WeekView.tsx              # Weekly view
│   ├── DayView.tsx               # Daily schedule
│   └── AppointmentDetailsModal.tsx
│
├── clients/                       # 👥 Client Management
│   ├── ClientList.tsx            # Client directory
│   ├── ClientForm.tsx            # Add/edit clients
│   ├── ClientDetail.tsx          # Client profile
│   └── ClientFilter.tsx          # Search/filter
│
├── settings/                      # ⚙️ Settings Panels
│   ├── BusinessProfileTab.tsx    # Business info
│   ├── IntegrationsTab.tsx       # External services
│   ├── AISettingsTab.tsx         # AI configuration
│   ├── BillingTab.tsx            # Stripe billing
│   ├── ServicesTab.tsx           # Service management
│   └── SecurityTab.tsx           # 2FA & security
│
├── landing/                       # 🌟 Marketing Components
│   ├── Hero.tsx                  # Homepage hero
│   ├── Features.tsx              # Feature showcase
│   ├── Pricing.tsx               # Subscription plans
│   ├── Navigation.tsx            # Site navigation
│   └── SavingsCalculator.tsx     # ROI calculator
│
└── auth/                         # 🔐 Authentication
    ├── TwoFactorAuthForm.tsx     # 2FA setup
    └── PasswordStrengthMeter.tsx # Security validation
```

### **🔧 Services Layer**
```
src/services/
├── whatsappService.ts            # 💬 WhatsApp API integration
├── aiService.ts                  # 🤖 AI assistant communication
├── clientService.ts              # 👥 Client management
├── appointmentService.ts         # 📅 Calendar operations
├── authService.ts                # 🔐 Authentication
├── billingService.ts             # 💳 Stripe integration
├── googleCalendarService.ts      # 📅 Google Calendar sync
└── whatsapp/                     # WhatsApp sub-services
    ├── connection.ts             # Connection management
    ├── messages.ts               # Message handling
    ├── contacts.ts               # Contact management
    └── socketio.ts               # Real-time events
```

### **📱 State Management**
```
src/lib/
├── auth.ts                       # 🔐 Zustand auth store
├── apiClient.ts                  # 🌐 HTTP client wrapper
└── utils.ts                      # 🛠️ Utility functions

src/hooks/
├── useClients.ts                 # Client data hooks
└── useToast.ts                   # Notification hooks

src/types/
├── client.ts                     # Client interfaces
├── whatsapp.ts                   # WhatsApp types
├── dashboard.ts                  # Dashboard types
└── api.ts                        # API response types
```

---

## 🔧 **Backend Structure** (`backend/app/`)

### **🛣️ API Routes**
```
backend/app/routes/
├── auth.py                       # 🔐 Authentication endpoints
├── client.py                     # 👥 Client CRUD operations
├── appointment.py                # 📅 Appointment management
├── service.py                    # 🛠️ Service definitions
├── whatsapp.py                   # 💬 WhatsApp webhook & API
├── ai_messaging.py               # 🤖 AI chat endpoints
├── ai_configuration.py           # ⚙️ AI settings
├── messages.py                   # 💬 Message history
├── integrations.py               # 🔗 External services
├── billing.py                    # 💳 Stripe billing
├── profile.py                    # 👤 User profile
└── twofa.py                      # 🔒 Two-factor auth
```

### **🗄️ Database Models**
```
backend/app/models/
├── user.py                       # 👤 Business users
├── client.py                     # 👥 Customer profiles
├── appointment.py                # 📅 Scheduled appointments
├── service.py                    # 🛠️ Business services
├── message.py                    # 💬 Chat messages
├── ai_configuration.py           # 🤖 AI settings
├── ai_context.py                 # 🧠 Conversation memory
├── integration.py                # 🔗 External connections
├── invoice.py                    # 💳 Billing records
└── google_calendar_cache.py      # 📅 Calendar cache
```

### **🧠 Core Services**
```
backend/app/services/
├── ai_assistant.py               # 🤖 AI brain (main component)
├── whatsapp_service.py           # 💬 WhatsApp Evolution API
├── google_calendar_service.py    # 📅 Google Calendar sync
├── gemini_service.py             # 🤖 Google Gemini AI
├── conversation_service.py       # 💬 Chat context management
├── embedding_service.py          # 🔍 Vector search
├── appointment_service.py        # 📅 Booking logic
└── language_detection.py         # 🌍 Multi-language support
```

### **⚙️ Infrastructure**
```
backend/app/
├── main.py                       # 🚀 FastAPI application
├── config.py                     # ⚙️ Environment settings
├── database.py                   # 🗄️ Database connection
├── auth.py                       # 🔐 JWT authentication
├── websocket.py                  # 🔄 Real-time connections
└── tasks/                        # ⏰ Background jobs
    ├── reminder_scheduler.py     # 📧 Appointment reminders
    └── whatsapp_sync.py          # 🔄 WhatsApp synchronization
```

---

## 🐳 **Infrastructure** 

### **Docker Architecture**
```
docker-compose.yml
├── 🗄️ postgres (PostgreSQL + pgvector)
├── 🔧 backend (FastAPI application)
├── 💬 evolution-api (WhatsApp integration)
├── 🗄️ evolution-postgres (WhatsApp data)
└── 📦 evolution-redis (WhatsApp cache)
```

### **WhatsApp Integration**
```
whatsapp-integration/
└── evolution-api/
    ├── Dockerfile                # WhatsApp service container
    ├── docker-compose.yml        # Standalone WhatsApp setup
    └── .env                      # WhatsApp configuration
```

---

## 📊 **Key Data Flow**

### **WhatsApp Message Processing**
```
1. 📱 WhatsApp Message → Evolution API
2. 🔗 Webhook → backend/routes/whatsapp.py
3. 🤖 AI Assistant → ai_assistant.py
4. 🧠 Gemini AI → Response Generation
5. 💬 Response → WhatsApp via Evolution API
6. 🗄️ Database → Message Storage
7. 🔄 WebSocket → Frontend Update
```

### **Appointment Booking Flow**
```
1. 👤 Client Request → WhatsApp/Frontend
2. 🤖 AI Detection → Booking Intent
3. 📅 Calendar Check → Available Slots
4. ✅ Confirmation → Appointment Creation
5. 📅 Google Sync → Calendar Update
6. 📧 Notification → Client Confirmation
```

---

## 🔑 **Key Integration Points**

### **Frontend ↔ Backend**
- **API Client**: `src/lib/apiClient.ts` → FastAPI endpoints
- **WebSocket**: Real-time updates for messages and appointments
- **Authentication**: JWT tokens with Zustand state management

### **Backend ↔ External Services**
- **WhatsApp**: Evolution API integration for messaging
- **Google Calendar**: OAuth2 + Calendar API for scheduling
- **Stripe**: Payment processing and subscription management
- **Gemini AI**: Natural language processing for chat responses

### **Database Architecture**
- **PostgreSQL**: Primary data storage
- **pgvector**: Vector embeddings for AI context
- **Redis**: WhatsApp message caching
- **Alembic**: Database migrations

---

## 🚀 **Development Workflow**

### **Frontend Development**
```bash
npm run dev                       # Start React development server
```

### **Backend Development**
```bash
docker-compose up -d              # Start all backend services
docker-compose exec backend alembic upgrade head  # Run migrations
```

### **Full Stack Testing**
```bash
# Frontend: http://localhost:5173
# Backend API: http://localhost:8000
# API Docs: http://localhost:8000/docs
# WhatsApp API: http://localhost:8080
```

This structure creates a **complete SaaS platform** that seamlessly integrates WhatsApp communication, AI-powered automation, appointment scheduling, and business management into one cohesive application.
