# Database Documentation

This section contains documentation related to the database structure and management in FixMyCalSaaS.

## Contents

- [PostgreSQL Database Exploration Commands](./PostgreSQL_Database_Exploration_Commands.md) - Useful commands for exploring the PostgreSQL database

## Database Overview

FixMyCalSaaS uses PostgreSQL as its primary database, with the following key features:

1. **Vector Database Capabilities**:
   - Uses pgvector extension for storing and querying vector embeddings
   - Enables semantic search for AI functionality

2. **Database Schema**:
   - Users and authentication
   - Clients and their metadata
   - Services and service types
   - Appointments and scheduling
   - Messages and conversations
   - AI configurations and knowledge documents
   - Billing and invoices

3. **Database Management**:
   - Migrations using Alembic
   - Automated backups
   - Performance optimization

## Database Diagram

For a visual representation of the database structure, see the [Database Structure Diagram](../architecture/diagrams/database_structure.md) in the Architecture section.

## Database Best Practices

When working with the FixMyCalSaaS database:

1. Always use migrations for schema changes
2. Implement proper indexing for performance
3. Use transactions for data integrity
4. Follow naming conventions for consistency
5. Regularly backup the database
