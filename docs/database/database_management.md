# Database Management Guide

This document explains how to manage database backups, migrations, and restoration for the FixMyCalSaaS application.

## Database Migrations

We use Alembic for database migrations. All schema changes should be tracked through Alembic migrations to ensure consistency between environments and when restoring from backups.

### Creating a New Migration

When you need to make changes to the database schema:

1. Make the necessary changes to the SQLAlchemy models in `backend/app/models/`
2. Generate a migration script:

```bash
docker-compose exec backend alembic revision --autogenerate -m "description_of_changes"
```

3. Review the generated migration script in `backend/migrations/versions/`
4. Apply the migration:

```bash
docker-compose exec backend alembic upgrade head
```

### Checking Migration Status

To see the current migration version:

```bash
docker-compose exec backend alembic current
```

To see migration history:

```bash
docker-compose exec backend alembic history
```

## Database Backups and Restoration

We've created a script to handle database backups and restoration. The script ensures that schema migrations are properly applied when restoring from a backup.

### Creating a Backup

To create a manual backup:

```bash
./scripts/db_backup_restore.sh backup
```

This will create a backup file in the `backups/` directory with a timestamp.

### Restoring from a Backup

To restore from a backup:

```bash
./scripts/db_backup_restore.sh restore backups/fixmycal_20250413_181500.sql
```

The restoration process:
1. Creates a temporary database
2. Restores the backup to the temporary database
3. Applies all pending migrations to bring the schema up to date
4. Swaps the temporary database with the current one
5. Restarts the backend service

### Automatic Backups

To set up automatic daily backups:

```bash
./scripts/db_backup_restore.sh auto-backup
```

This will create a cron job that runs daily at 2 AM to create a backup. The backup logs are written to `/tmp/db_backup.log`.

## Important Notes

- The backup script keeps only the 10 most recent backups to save disk space
- When restoring, the old database is temporarily kept as `fixmycal_old` in case you need to recover data
- Always test the restoration process in a non-production environment before using it in production
- Consider storing backups in a secure, off-site location for disaster recovery

## Troubleshooting

### Migration Conflicts

If you encounter migration conflicts, you may need to manually resolve them:

1. Check the current migration version:
   ```bash
   docker-compose exec backend alembic current
   ```

2. If needed, stamp the database with the correct version:
   ```bash
   docker-compose exec backend alembic stamp <revision_id>
   ```

3. Then try upgrading again:
   ```bash
   docker-compose exec backend alembic upgrade head
   ```

### Database Connection Issues

If the backend can't connect to the database after restoration:

1. Check the database logs:
   ```bash
   docker-compose logs postgres
   ```

2. Restart the backend service:
   ```bash
   docker-compose restart backend
   ```

3. If needed, restart all services:
   ```bash
   docker-compose down && docker-compose up -d
   ```
