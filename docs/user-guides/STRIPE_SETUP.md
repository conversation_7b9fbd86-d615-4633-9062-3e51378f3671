# Stripe Integration Setup Guide

This guide will walk you through setting up Stripe integration for FixMyCal to enable subscription billing and invoice downloads.

## Prerequisites

1. A Stripe account (you can sign up at [stripe.com](https://stripe.com))
2. Access to your FixMyCal application's environment variables

## Step 1: Get Your Stripe API Keys

1. Log in to your [Stripe Dashboard](https://dashboard.stripe.com/dashboard)
2. Go to Developers > API keys
3. You'll need two keys:
   - **Publishable Key**: Starts with `pk_live_` or `pk_test_`
   - **Secret Key**: Starts with `sk_live_` or `sk_test_`

**Note**: Use test keys for development and live keys for production.

## Step 2: Set Up Webhook Endpoint

Webhooks allow <PERSON><PERSON> to notify your application when events happen in your account, such as successful payments.

1. Go to Developers > Webhooks in your Stripe Dashboard
2. Click "Add endpoint"
3. Enter your webhook URL: `https://your-domain.com/dashboard/billing/webhook`
4. Select events to listen for:
   - `checkout.session.completed`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
5. Click "Add endpoint"
6. <PERSON><PERSON> the "Signing secret" - you'll need this for the next step

## Step 3: Configure Environment Variables

Add the following variables to your `.env` file:

```
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_signing_secret
```

For the frontend:

```
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
```

## Step 4: Create Products and Prices in Stripe

You need to create products and prices in Stripe that match your subscription plans:

1. Go to Products > Add Product in your Stripe Dashboard
2. Create the following products:
   - **Starter Plan**:
     - Name: Starter
     - Price: €25/month (recurring)
   - **Professional Plan**:
     - Name: Professional
     - Price: €50/month (recurring)
   - **Enterprise Plan**:
     - Name: Enterprise
     - Price: €100/month (recurring)

3. Note the Price IDs for each product (they start with `price_`)

## Step 5: Update Your Database

If you want to link your Stripe products to your database, you'll need to update the `pricing_plans` table with the Stripe Price IDs:

```sql
UPDATE pricing_plans SET stripe_price_id = 'price_your_starter_price_id' WHERE name = 'Starter';
UPDATE pricing_plans SET stripe_price_id = 'price_your_professional_price_id' WHERE name = 'Professional';
UPDATE pricing_plans SET stripe_price_id = 'price_your_enterprise_price_id' WHERE name = 'Enterprise';
```

## Step 6: Restart Your Application

After configuring all the above, restart your application:

```bash
docker-compose restart backend
```

## Testing the Integration

1. **Test Subscription**: Try upgrading to a different plan in the BillingTab
2. **Test Invoice Download**: After completing a subscription, try downloading an invoice

## Troubleshooting

### Common Issues

1. **"Stripe is not configured" error**:
   - Check that your environment variables are set correctly
   - Restart the backend after changing environment variables

2. **"Invalid API key" error**:
   - Verify your Stripe API keys are correct
   - Make sure you're not mixing test and live keys

3. **Webhook errors**:
   - Check that your webhook URL is accessible from the internet
   - Verify the webhook signing secret is correct

4. **Invoice download not working**:
   - This feature requires real Stripe invoices, not mock data
   - Complete a real subscription flow to generate a downloadable invoice

### Stripe Logs

You can view webhook delivery attempts and API request logs in the Stripe Dashboard under Developers > Webhooks and Developers > Logs.

## Going to Production

When you're ready to go to production:

1. Switch to live API keys
2. Update your webhook endpoint to your production URL
3. Make sure your products and prices are set up in your live Stripe account

## Additional Resources

- [Stripe API Documentation](https://stripe.com/docs/api)
- [Stripe Checkout Documentation](https://stripe.com/docs/payments/checkout)
- [Stripe Billing Documentation](https://stripe.com/docs/billing)
- [Stripe Webhooks Documentation](https://stripe.com/docs/webhooks)
