# Invoice Download Implementation Guide

This document explains how the invoice download functionality works in the FixMyCal application and how to set it up properly.

## Overview

The invoice download feature allows users to:
1. View their billing history
2. Download PDF invoices for their payments
3. View invoices online in the Stripe customer portal

## Implementation Details

### Frontend Components

The invoice download functionality is implemented in the following files:

- `src/components/settings/BillingTab.tsx`: Contains the UI for displaying invoices and the download button
- `src/services/billingService.ts`: Contains the logic for downloading invoices

### Backend Components

- `backend/app/routes/billing.py`: Contains the API endpoints for invoices
- `backend/app/models/invoice.py`: Contains the Invoice database model
- `backend/scripts/add_invoice_urls.sql`: SQL script to add the new columns to the database
- `backend/scripts/update_invoice_urls.py`: Python script to update existing invoices with URLs from Stripe

### How It Works

1. When a user clicks the "Download" button for an invoice:
   - The application first checks if the invoice object already has the URLs (hosted_invoice_url or invoice_pdf)
   - If the URLs are available, it opens the invoice in a new tab
   - If not, it calls `billingService.downloadInvoice(invoice.id)` to fetch the URLs from the backend
   - For mock invoices (IDs starting with 'inv_'), a message is displayed explaining that it's a mock invoice
   - For real Stripe invoices, the application fetches the invoice URLs from the backend
   - The invoice is opened in a new tab, either as a PDF or as a hosted Stripe invoice page

2. The backend handles the invoice download request by:
   - Checking if the invoice ID is valid
   - If it's a UUID, it looks up the invoice in the database
   - If the invoice has a Stripe invoice ID, it retrieves the invoice from Stripe
   - It stores the URLs in the database for future use
   - It returns the invoice PDF URL and hosted invoice URL

3. When a new invoice is created via the Stripe webhook:
   - The backend retrieves the invoice from Stripe
   - It stores the invoice PDF URL and hosted invoice URL in the database
   - This allows for faster access to the URLs in the future

## Setup Instructions

### 1. Configure Stripe

To enable real invoice downloads, you need to configure Stripe:

1. Add your Stripe API keys to the `.env` file:
   ```
   STRIPE_SECRET_KEY=sk_test_your_secret_key
   STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
   VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
   ```

2. Make sure your Stripe account is set up to generate invoices for payments:
   - Enable invoicing in your Stripe Dashboard
   - Configure invoice templates and branding

### 2. Update the Database Schema

To add the new columns to the database:

1. Run the SQL script to add the new columns:
   ```bash
   docker-compose exec postgres psql -U postgres -d fixmycal -f /app/scripts/add_invoice_urls.sql
   ```

2. Run the Python script to update existing invoices with URLs from Stripe:
   ```bash
   docker-compose exec backend python /app/scripts/update_invoice_urls.py
   ```

### 3. Testing with Mock Data

During development, you can test the UI with mock data:

1. The application automatically uses mock invoices if Stripe is not configured
2. Mock invoices have IDs starting with 'inv_' (e.g., 'inv_1', 'inv_2')
3. When trying to download a mock invoice, a message will be displayed explaining that it's a mock invoice

### 4. Testing with Real Stripe Invoices

To test with real Stripe invoices:

1. Configure Stripe as described above
2. Create a test customer and subscription in Stripe
3. Generate test invoices for the customer
4. The invoices will appear in the Billing History section
5. Click "Download" to view or download the invoice

## Troubleshooting

### Common Issues

1. **"Stripe is not configured" error**:
   - Check that your environment variables are set correctly
   - Restart the application after changing environment variables

2. **"No invoice download URL available" error**:
   - Check that the invoice exists in Stripe
   - Verify that the invoice has a PDF URL or hosted invoice URL

3. **Mock invoices not showing download options**:
   - This is expected behavior - mock invoices cannot be downloaded
   - Configure Stripe to enable real invoice downloads

## Future Improvements

Planned improvements for the invoice download feature:

1. Add support for downloading multiple invoices at once
2. Implement invoice filtering by date range
3. Add invoice search functionality
4. Improve invoice display with more details (line items, taxes, etc.)
5. Add support for downloading invoices in different formats (CSV, JSON, etc.)
