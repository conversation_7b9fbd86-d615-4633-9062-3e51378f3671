# Testing Invoice Download Functionality

This guide explains how to test the invoice download functionality using <PERSON><PERSON>'s test mode and the Stripe CLI.

## Prerequisites

1. **Stripe Account**: You need a Stripe account with API keys in test mode
2. **Stripe CLI**: Install the [Stripe CLI](https://stripe.com/docs/stripe-cli) for local testing
3. **API Keys**: Configure your application with Stripe test API keys

## Setting Up Stripe CLI

1. Install the Stripe CLI:
   - macOS: `brew install stripe/stripe-cli/stripe`
   - Windows: Download from [Stripe CLI releases](https://github.com/stripe/stripe-cli/releases/latest)
   - Linux: See [Stripe CLI installation guide](https://stripe.com/docs/stripe-cli#install)

2. Log in to your Stripe account:
   ```bash
   stripe login
   ```

3. Verify the installation:
   ```bash
   stripe --version
   ```

## Testing with Stripe Test Mode

### 1. Create Test Customers and Products

First, let's create test customers and products in Stripe:

```bash
# Create a test customer
stripe customers create --name="Test User" --email="<EMAIL>"

# The output will include a customer ID like cus_1234567890
# Save this ID for later use
```

### 2. Create Test Products and Prices

```bash
# Create a test product
stripe products create --name="Professional Plan"

# Create a price for the product (replace prod_123 with your product ID)
stripe prices create --product=prod_123 --unit-amount=5000 --currency=eur --recurring[interval]=month
```

### 3. Create Test Subscriptions

```bash
# Create a subscription for the customer (replace cus_123 and price_123 with your IDs)
stripe subscriptions create --customer=cus_123 --items[0][price]=price_123
```

### 4. Create Test Invoices

```bash
# Create a finalized invoice for the customer
stripe invoices create --customer=cus_123 --collection_method=send_invoice --days_until_due=30

# Finalize the invoice (replace in_123 with your invoice ID)
stripe invoices finalize in_123

# Pay the invoice
stripe invoices pay in_123
```

### 5. Test Webhook Events

You can simulate webhook events to test your application's handling of Stripe events:

```bash
# Trigger a checkout.session.completed event
stripe trigger checkout.session.completed

# Trigger an invoice.payment_succeeded event
stripe trigger invoice.payment_succeeded
```

## Testing in Your Application

### 1. Configure Your Application with Test Keys

Update your `.env` file with Stripe test API keys:

```
STRIPE_SECRET_KEY=sk_test_your_test_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_test_key
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_test_key
```

### 2. Forward Webhook Events to Your Local Environment

Use the Stripe CLI to forward webhook events to your local application:

```bash
stripe listen --forward-to http://localhost:8000/dashboard/billing/webhook
```

This will provide you with a webhook signing secret. Add this to your `.env` file:

```
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_signing_secret
```

### 3. Test the Invoice Download Flow

1. Start your application:
   ```bash
   docker-compose up -d
   ```

2. Run the database scripts to add the new columns:
   ```bash
   docker-compose exec postgres psql -U postgres -d fixmycal -f /app/scripts/add_invoice_urls.sql
   docker-compose exec backend python /app/scripts/update_invoice_urls.py
   ```

3. Log in to your application and navigate to the Billing tab

4. You should see the test invoices you created in Stripe

5. Click the "Download" button for an invoice to test the download functionality

## Troubleshooting

### Common Issues

1. **"Stripe is not configured" error**:
   - Check that your environment variables are set correctly
   - Restart the application after changing environment variables

2. **Webhook events not being received**:
   - Make sure the Stripe CLI is running and forwarding events
   - Check that the webhook URL is correct
   - Verify that the webhook signing secret is set correctly

3. **Invoice download not working**:
   - Check the browser console for errors
   - Verify that the invoice has a valid Stripe invoice ID
   - Make sure the invoice has been finalized in Stripe

### Stripe CLI Commands for Debugging

```bash
# List all customers
stripe customers list

# List all invoices
stripe invoices list

# View a specific invoice
stripe invoices retrieve in_123

# List all webhook endpoints
stripe webhook endpoints list

# View recent webhook events
stripe events list
```

## Using Stripe Dashboard for Testing

You can also use the [Stripe Dashboard](https://dashboard.stripe.com/test/dashboard) to:

1. Create and manage test customers
2. Create and manage test products and prices
3. Create and manage test subscriptions
4. View and download test invoices
5. View webhook events and delivery attempts

This can be easier than using the CLI for some operations.

## Additional Resources

- [Stripe Testing Documentation](https://stripe.com/docs/testing)
- [Stripe CLI Documentation](https://stripe.com/docs/stripe-cli)
- [Stripe API Reference](https://stripe.com/docs/api)
- [Stripe Webhook Documentation](https://stripe.com/docs/webhooks)
- [Stripe Invoice Documentation](https://stripe.com/docs/billing/invoices)
