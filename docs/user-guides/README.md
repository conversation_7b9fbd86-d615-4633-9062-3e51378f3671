# User Guides

This section contains user guides and tutorials for the FixMyCalSaaS application.

## Contents

*Note: This section will be expanded with more user guides as they are created.*

## Getting Started

### Installation and Setup

FixMyCalSaaS uses Docker for backend development and npm for frontend development:

#### Backend Setup
```bash
# Start the backend services
docker-compose up -d

# Rebuild if needed
docker-compose up -d --build
```

#### Frontend Setup
```bash
# Install dependencies
cd frontend
npm install

# Start development server
npm run dev
```

### Initial Configuration

After setting up the application, you'll need to:

1. Create an account or log in
2. Configure your business profile
3. Set up service types and services
4. Connect external integrations (Google Calendar, WhatsApp)
5. Configure AI assistant settings

## Feature Guides

### Client Management

The client management system allows you to:
- Add new clients
- View and edit client information
- Tag clients for easy filtering
- View appointment history
- Export client data

### Appointment Scheduling

The appointment scheduling system allows you to:
- Create new appointments
- Check for scheduling conflicts
- Set up recurring appointments
- Sync with Google Calendar
- Configure appointment reminders

### WhatsApp Integration

The WhatsApp integration allows you to:
- Connect your WhatsApp account
- Send and receive messages
- View conversation history
- Configure automated responses
- Handle media messages (images, voice)

### AI Configuration

The AI configuration system allows you to:
- Set up your AI assistant's personality and tone
- Configure business rules and hours
- Upload knowledge documents
- Set up response templates
- Configure multi-language support

### Billing and Subscription

The billing system allows you to:
- View available subscription plans
- Manage your current subscription
- Update payment methods
- View and download invoices
