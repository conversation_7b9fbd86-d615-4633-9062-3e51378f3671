version: '3.8'

services:
  postgres:
    image: ankane/pgvector:latest
    container_name: project-postgres-1
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: fixmycal
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./backend/scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - app-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    volumes:
      - ./backend:/app
      - ./.env:/app/.env
    ports:
      - "8000:8000"
    depends_on:
      - postgres
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=********************************************/fixmycal
      - SECRET_KEY=your-secret-key-here
      - ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=1440
      - REFRESH_TOKEN_EXPIRE_DAYS=30
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - STRIPE_SECRET_KEY=sk_test_51QpXpXB2Zxp0l7ta9CBVzDQQTBS9guhwLbmC1TXx3KSs21vs2NfCHVGGZLPpu7FU7junUpSglDc7U4OpjchVK6z500QIo0MKTY
      - STRIPE_PUBLISHABLE_KEY=pk_test_51QpXpXB2Zxp0l7taWTnS35gyUbIEqBbN2ppSM0XEsB99r6EoV1rRDlq251pmu4EMZYVlx3tH16d31wzxILrGPe3n00o3oLkd11
      - STRIPE_WEBHOOK_SECRET=whsec_test_stripe_webhook_secret_key_123456
      - BACKEND_URL=http://backend:8000
    networks:
      - app-network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Evolution API services
  evolution-api:
    build:
      context: ./whatsapp-integration/evolution-api
      dockerfile: Dockerfile
    container_name: evolution-api
    restart: always
    ports:
      - "8080:8080"
    env_file:
      - ./whatsapp-integration/evolution-api/.env
    volumes:
      - evolution-instances:/evolution/instances
      - evolution-store:/evolution/store
    depends_on:
      - evolution-postgres
      - evolution-redis
    networks:
      - app-network

  evolution-postgres:
    image: postgres:14-alpine
    container_name: evolution-postgres
    restart: always
    environment:
      - POSTGRES_USER=evolution_user
      - POSTGRES_PASSWORD=evolution_password
      - POSTGRES_DB=evolution_db
    volumes:
      - evolution-postgres-data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - app-network

  evolution-redis:
    image: redis:alpine
    container_name: evolution-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - evolution-redis-data:/data
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  postgres-data:
  evolution-instances:
  evolution-store:
  evolution-postgres-data:
  evolution-redis-data: