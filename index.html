<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="FixMyCal is an AI-powered calendar assistant that helps you manage your schedule effortlessly. Stay organized with smart automation." />
    <meta name="keywords" content="calendar assistant, AI scheduling, FixMyCal, appointment automation, smart calendar" />
    <meta name="author" content="FixMyCal Team" />

    <!-- Open Graph for Social Sharing -->
    <meta property="og:title" content="FixMyCal | AI Calendar Assistant" />
    <meta property="og:description" content="FixMyCal helps you automate and manage your appointments with AI-powered scheduling." />
    <meta property="og:image" content="https://fixmycal.com/images/Logo.png" />
    <meta property="og:url" content="https://fixmycal.com" />
    <meta property="og:type" content="website" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://fixmycal.com" />

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/images/Logo.png" />

    <!-- We don't need to preload the main script as it's loaded by the module script tag -->

    <title>FixMyCal | Calendar Assistant</title>

    <script type="text/javascript">
      (function(c,l,a,r,i,t,y){
          c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
          t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
          y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
      })(window, document, "clarity", "script", "qv7goaq77y");
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
