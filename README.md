# FixMyCalSaaS - AI-Powered Appointment Management System

## Project Overview

FixMyCalSaaS is a comprehensive SaaS (Software as a Service) platform designed for service providers to manage appointments, clients, and business operations efficiently. The system leverages AI to enhance user experience and automate routine tasks.

### Key Features

- **Client Management**: Store and manage client information with detailed profiles
- **Appointment Scheduling**: Create, edit, and manage appointments with calendar integration
- **Business Profile Management**: Customize your business details, including logo, contact information, and currency
- **Google Calendar Integration**: Sync appointments with Google Calendar
- **WhatsApp Integration**: Connect with clients via WhatsApp and manage conversations
- **AI-Powered Assistance**: Gemini AI integration for automated responses and appointment booking
- **User Authentication**: Secure login and registration system with JWT authentication
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Technology Stack

### Frontend
- **Framework**: React with TypeScript
- **State Management**: Zustand for global state
- **Styling**: Tailwind CSS with DaisyUI components
- **Routing**: React Router
- **Form Handling**: React Hook Form with Zod validation
- **HTTP Client**: Custom fetch wrapper with authentication

### Backend
- **Framework**: FastAPI (Python)
- **Database**: PostgreSQL with pgvector for vector embeddings
- **Authentication**: JWT with refresh tokens
- **ORM**: SQLAlchemy
- **Migrations**: Alembic
- **AI Integration**: Google Gemini API for natural language processing
- **API Documentation**: Swagger UI (OpenAPI)

### Infrastructure
- **Containerization**: Docker and Docker Compose
- **Version Control**: Git with GitHub
- **CI/CD**: GitHub Actions

## Project Architecture

### Database Schema

The application uses a PostgreSQL database with the following main tables:

- **users**: Stores user account information and business profile data
- **clients**: Stores client information associated with users
- **appointments**: Manages appointment data with references to clients and services
- **services**: Defines the services offered by the business
- **external_integrations**: Stores integration data (e.g., Google Calendar tokens)

### API Structure

The backend API follows RESTful principles and is organized into the following main routes:

- **/auth**: Authentication endpoints (login, register, refresh token)
- **/clients**: Client management endpoints
- **/appointments**: Appointment scheduling and management
- **/services**: Service definition and management
- **/integrations**: External service integrations (Google Calendar)

## Getting Started

### Using Docker (Recommended)

1. Clone the repository:
```bash
git clone https://github.com/yourusername/FixMyCalSaaS.git
cd FixMyCalSaaS
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit the .env file with your configuration values
```

3. Build and start the backend containers:
```bash
docker-compose up -d
```

4. Apply database migrations:
```bash
docker-compose exec backend alembic upgrade head
```

### Testing the Gemini AI Integration

1. Make sure you have a valid Gemini API key in your `.env` file:
```
GEMINI_API_KEY=your_gemini_api_key
```

2. Test the Gemini API connection:
```bash
./scripts/test_gemini_docker.sh
```

3. Test the WhatsApp AI integration:
```bash
./scripts/test_whatsapp_ai.sh
```
This will simulate a WhatsApp message and test the AI response, including appointment booking functionality.

### Testing the Google Calendar Integration

1. Make sure you have valid Google OAuth credentials in your `.env` file:
```
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
```

2. Connect your Google Calendar in the app's Settings > Integrations tab

3. Test the Google Calendar sync:
```bash
./scripts/test_google_calendar_sync.sh
```
This will find appointments that need syncing and sync them with Google Calendar.

### Setting Up and Testing the Billing System

1. Make sure you have valid Stripe API keys in your `.env` file:
```
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
```

2. Create products and prices in Stripe:
   - Create a product for each plan (Starter, Professional)
   - For each product, create two prices:
     - Monthly subscription price
     - Annual subscription price (with 20% discount)

3. Update the pricing plans in the database with Stripe IDs:
```bash
./scripts/update_stripe_ids.sh
```

4. Test the Stripe integration:
```bash
docker-compose exec backend python -m app.tests.test_stripe
```

5. To view and test the billing functionality:
   - Log in to the application
   - Navigate to Settings > Billing
   - View your current plan, usage statistics, and invoices
   - Test downloading an invoice
   - Test changing plans (upgrades/downgrades)
   - When upgrading, you'll be prompted to choose between monthly and annual billing
   - Annual billing offers a 20% discount compared to monthly billing

4. Start the frontend development server:
```bash
npm run dev
```

6. Access the application:
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs
   - WhatsApp API: http://localhost:8080

7. The database will be accessible at `localhost:5432` with:
   - Username: postgres
   - Password: postgres
   - Database: fixmycal

### Manual Development Setup

#### Prerequisites
- Node.js (v18 or later)
- Python 3.11+
- PostgreSQL 15+

#### Frontend Setup
1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

#### Backend Setup
1. Set up environment variables (if you haven't already):
```bash
cp .env.example .env
# Edit the .env file with your configuration values
```

2. Navigate to the backend directory:
```bash
cd backend
```

3. Create a virtual environment and activate it:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

4. Install dependencies:
```bash
pip install -r requirements.txt
```

5. Set up the database:
```bash
alembic upgrade head
```

6. Run the FastAPI server:
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## Database Management

### Migrations

The project uses Alembic for database migrations. When setting up a new installation, you need to run all migrations to create the database schema.

```bash
# Apply all migrations (required for new installations)
docker-compose exec backend alembic upgrade head

# Create a new migration (for development)
docker-compose exec backend alembic revision --autogenerate -m "description"

# Rollback migration
docker-compose exec backend alembic downgrade -1
```

#### Understanding Alembic Migrations

Alembic tracks database changes through migration files in `backend/alembic/versions/`. Each file represents a specific change to the database schema and includes both "upgrade" and "downgrade" functions.

When setting up a new installation, running `alembic upgrade head` applies all migrations in the correct order to create the complete database schema. This is the recommended approach rather than trying to create the schema manually.

### Backup and Restore

```bash
# Backup the database
docker exec -it project-postgres-1 pg_dump -U postgres -d fixmycal > backup.sql

# Restore the database
docker cp backup.sql project-postgres-1:/backup.sql
docker exec -it project-postgres-1 psql -U postgres -d fixmycal -f /backup.sql
```

## Environment Variables

Create a `.env` file in the project root by copying the provided `.env.example` file:

```bash
cp .env.example .env
```

Then edit the `.env` file to set your specific configuration values.

### Key Environment Variables

#### Backend Variables
- `DATABASE_URL`: PostgreSQL connection string
- `SECRET_KEY`: Secret key for JWT encoding
- `ALGORITHM`: Algorithm for JWT encoding (default: HS256)
- `ACCESS_TOKEN_EXPIRE_MINUTES`: Expiration time for access tokens
- `REFRESH_TOKEN_EXPIRE_DAYS`: Expiration time for refresh tokens
- `GOOGLE_CLIENT_ID`: Google OAuth client ID for Calendar integration
- `GOOGLE_CLIENT_SECRET`: Google OAuth client secret
- `GOOGLE_REDIRECT_URI`: Redirect URI for Google OAuth
- `OPENAI_API_KEY`: API key for OpenAI integration (optional, as fallback)
- `GEMINI_API_KEY`: API key for Google Gemini AI integration
- `STRIPE_SECRET_KEY`: Secret key for Stripe payments
- `STRIPE_PUBLISHABLE_KEY`: Publishable key for Stripe payments
- `STRIPE_WEBHOOK_SECRET`: Secret for Stripe webhooks

#### Frontend Variables
- `VITE_API_URL`: URL of the backend API (default: http://localhost:8000)
- `VITE_GOOGLE_CLIENT_ID`: Google OAuth client ID for frontend authentication
- `VITE_EVOLUTION_API_URL`: URL for the WhatsApp Evolution API
- `VITE_EVOLUTION_API_KEY`: API key for WhatsApp Evolution API

#### WhatsApp Integration Variables
The `.env` file also includes configuration for the WhatsApp Evolution API. See the `.env.example` file for details.

## Security Features

- JWT-based authentication with refresh tokens
- Password hashing using bcrypt
- CORS protection
- Input validation using Pydantic schemas
- SQL injection protection via SQLAlchemy ORM
- Regular security updates for dependencies

## Application Features

### User Authentication

The application provides a secure authentication system with:
- User registration with email verification
- Login with JWT tokens
- Password reset functionality
- Session management with refresh tokens

### Client Management

- Create, view, edit, and delete client profiles
- Search clients by name, email, or phone number
- View client appointment history
- Store client preferences and notes

### Appointment Scheduling

- Create and manage appointments with date, time, and duration
- Associate appointments with clients and services
- View appointments in calendar or list view
- Send appointment reminders
- Handle appointment cancellations and rescheduling

### Business Settings

- Customize business profile (name, logo, contact information)
- Set working hours and availability
- Define services and pricing
- Configure notification preferences

### Google Calendar Integration

- Connect your Google Calendar account
- Sync appointments between FixMyCalSaaS and Google Calendar
- Avoid double-booking by checking Google Calendar availability

### Billing System

- Subscription-based pricing model with multiple plans:
  - Starter Plan: €50/month (500 AI responses/month, up to 50 appointments/month)
  - Professional Plan: €100/month (5,000 AI responses/month, unlimited appointments)
  - Enterprise Plan: Custom pricing (unlimited AI responses, unlimited appointments)
- Secure payment processing through Stripe
- Invoice management with downloadable invoices
- Usage tracking for AI responses
- Annual billing option with 20% discount

## Future Enhancements

- SMS notifications for appointments
- Online payment processing
- Client portal for self-scheduling
- Advanced reporting and analytics
- Mobile application

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [FastAPI](https://fastapi.tiangolo.com/) for the backend framework
- [React](https://reactjs.org/) for the frontend framework
- [Tailwind CSS](https://tailwindcss.com/) for styling
- [DaisyUI](https://daisyui.com/) for UI components
- [PostgreSQL](https://www.postgresql.org/) for the database
- All other open-source libraries used in this project