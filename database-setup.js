const { exec } = require('child_process');
const path = require('path');

console.log('Setting up the database...');

// Update database URL in .env file
const setupProcess = exec('cd backend && python -c "from app.models import Base; from app.database import engine; Base.metadata.create_all(bind=engine); print(\'Database tables created successfully!\')"');

setupProcess.stdout.on('data', (data) => {
  console.log(data);
});

setupProcess.stderr.on('data', (data) => {
  console.error(`Database setup error: ${data}`);
});

setupProcess.on('close', (code) => {
  if (code === 0) {
    console.log('Database setup completed successfully.');
  } else {
    console.error(`Database setup failed with code ${code}`);
  }
});