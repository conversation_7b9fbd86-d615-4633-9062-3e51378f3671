{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/uuid": "^10.0.0", "esbuild": "^0.25.2", "framer-motion": "^12.6.3", "googleapis": "^131.0.0", "i18next": "^23.10.1", "i18next-browser-languagedetector": "^7.2.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.344.0", "puppeteer": "^24.5.0", "puppeteer-core": "^24.5.0", "qrcode.react": "^3.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-grid-layout": "^1.5.1", "react-i18next": "^14.1.0", "react-router-dom": "^7.5.2", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "tar-fs": "^3.0.7", "uuid": "^11.1.0", "ws": "^8.17.1", "zustand": "^4.5.2"}, "resolutions": {"tar-fs": "^3.0.7", "ws": "^8.17.1", "react-router": "^7.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.2.7"}}