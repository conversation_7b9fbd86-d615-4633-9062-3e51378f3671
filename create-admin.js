const fetch = require('node-fetch');

async function createAdminUser() {
  try {
    const response = await fetch('http://localhost:8000/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'adminpassword123',
        business_name: 'FixMyCal Admin'
      }),
    });

    const data = await response.json();
    console.log('Admin user created successfully!');
    console.log('Access token:', data.access_token);
    console.log('Refresh token:', data.refresh_token);
    console.log('You can now log in with email: <EMAIL> and password: adminpassword123');
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
}

createAdminUser();