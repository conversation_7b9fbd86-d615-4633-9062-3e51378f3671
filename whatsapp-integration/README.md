# WhatsApp Integration for FixMyCal

This directory contains the configuration files for integrating WhatsApp messaging into the FixMyCal application using Evolution API.

## Overview

The WhatsApp integration allows users to:
- Connect their WhatsApp account to the application
- Send appointment reminders and notifications via WhatsApp
- Communicate with clients directly through the application

## Components

1. **Evolution API**: A WhatsApp API based on Baileys that allows programmatic access to WhatsApp
2. **Backend Integration**: FastAPI endpoints to communicate with Evolution API
3. **Frontend Interface**: React components for managing WhatsApp connections and sending messages

## Setup Instructions

### Prerequisites

- Docker and Docker Compose installed
- FixMyCal backend and frontend running

### Installation

1. The WhatsApp integration is included in the main docker-compose.yml file
2. Start the services with:
   ```
   docker-compose up -d
   ```

3. The Evolution API will be available at http://localhost:8080
4. The WhatsApp integration can be managed through the Settings page in the FixMyCal application

### Connecting WhatsApp

1. Navigate to the Settings page in the FixMyCal application
2. Select the "WhatsApp" tab
3. Click "Connect WhatsApp"
4. Scan the QR code with your WhatsApp mobile app
5. Once connected, you can send test messages and manage your WhatsApp connection

## API Endpoints

The following endpoints are available for WhatsApp integration:

- `GET /whatsapp/status`: Check if WhatsApp is connected
- `POST /whatsapp/connect`: Create a WhatsApp instance and get QR code for connection
- `DELETE /whatsapp/disconnect`: Disconnect WhatsApp instance
- `POST /whatsapp/send-message`: Send a WhatsApp message

## Security Considerations

- The WhatsApp integration uses a separate database and Redis instance to isolate it from the main application
- Authentication is required for all WhatsApp API endpoints
- Each user has their own WhatsApp instance, ensuring data isolation

## Troubleshooting

If you encounter issues with the WhatsApp integration:

1. Check the Evolution API logs:
   ```
   docker-compose logs evolution-api
   ```

2. Ensure the Evolution API is running:
   ```
   docker-compose ps evolution-api
   ```

3. If the QR code expires, click "Connect WhatsApp" again to generate a new one

4. If messages are not being sent, check that your WhatsApp account is still connected by refreshing the status

## References

- [Evolution API Documentation](https://github.com/EvolutionAPI/evolution-api)
- [WhatsApp Business API Documentation](https://developers.facebook.com/docs/whatsapp/api/)
