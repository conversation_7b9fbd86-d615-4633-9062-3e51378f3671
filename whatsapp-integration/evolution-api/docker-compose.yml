version: '3.8'

services:
  evolution-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: evolution-api
    restart: always
    ports:
      - "8080:8080"
    environment:
      - AUTHENTICATION_API_KEY=${AUTHENTICATION_API_KEY}
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - REDIS_URI=redis://redis:6379
    volumes:
      - evolution-instances:/evolution/instances
      - evolution-store:/evolution/store
    depends_on:
      - postgres
      - redis
    networks:
      - evolution-network

  postgres:
    image: postgres:14-alpine
    container_name: evolution-postgres
    restart: always
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - evolution-postgres-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - evolution-network

  redis:
    image: redis:alpine
    container_name: evolution-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - evolution-redis-data:/data
    networks:
      - evolution-network

volumes:
  evolution-instances:
  evolution-store:
  evolution-postgres-data:
  evolution-redis-data:

networks:
  evolution-network:
    driver: bridge
