<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Download Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ccc;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0,0,0,.3);
            border-radius: 50%;
            border-top-color: #000;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>Invoice Download Test</h1>
    
    <div class="container">
        <h2>1. Get Invoices</h2>
        <p>This will fetch all invoices from the database.</p>
        <button id="getInvoicesBtn">Get Invoices</button>
        <span id="getInvoicesLoading" class="loading" style="display: none;"></span>
        <div id="getInvoicesResult"></div>
    </div>
    
    <div class="container">
        <h2>2. Download Invoice</h2>
        <p>Enter the invoice ID to download:</p>
        <input type="text" id="invoiceIdInput" placeholder="Invoice ID" style="padding: 8px; width: 300px;">
        <button id="downloadInvoiceBtn">Download Invoice</button>
        <span id="downloadInvoiceLoading" class="loading" style="display: none;"></span>
        <div id="downloadInvoiceResult"></div>
    </div>
    
    <div class="container">
        <h2>3. Open Invoice URL</h2>
        <p>Enter the invoice URL to open:</p>
        <input type="text" id="invoiceUrlInput" placeholder="Invoice URL" style="padding: 8px; width: 300px;">
        <button id="openInvoiceUrlBtn">Open URL</button>
    </div>

    <script>
        // Helper function to display JSON data
        function displayJson(elementId, data) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }

        // Helper function to show/hide loading indicator
        function setLoading(elementId, isLoading) {
            document.getElementById(elementId).style.display = isLoading ? 'inline-block' : 'none';
        }

        // Get invoices from the database
        document.getElementById('getInvoicesBtn').addEventListener('click', async () => {
            try {
                setLoading('getInvoicesLoading', true);
                
                // Get all invoices from the database
                const response = await fetch('http://localhost:8000/dashboard/billing/invoices', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        // Add a mock authorization header
                        'Authorization': 'Bearer mock_token'
                    }
                });
                
                const data = await response.json();
                displayJson('getInvoicesResult', data);
            } catch (error) {
                displayJson('getInvoicesResult', { error: error.message });
            } finally {
                setLoading('getInvoicesLoading', false);
            }
        });

        // Download invoice
        document.getElementById('downloadInvoiceBtn').addEventListener('click', async () => {
            try {
                const invoiceId = document.getElementById('invoiceIdInput').value.trim();
                if (!invoiceId) {
                    alert('Please enter an invoice ID');
                    return;
                }
                
                setLoading('downloadInvoiceLoading', true);
                
                // Download the invoice
                const response = await fetch(`http://localhost:8000/dashboard/billing/invoices/${invoiceId}/download`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        // Add a mock authorization header
                        'Authorization': 'Bearer mock_token'
                    }
                });
                
                const data = await response.json();
                displayJson('downloadInvoiceResult', data);
                
                // If the invoice has a hosted_invoice_url, add a button to open it
                if (data.hosted_invoice_url) {
                    const openUrlBtn = document.createElement('button');
                    openUrlBtn.textContent = 'Open Hosted Invoice URL';
                    openUrlBtn.onclick = () => window.open(data.hosted_invoice_url, '_blank');
                    document.getElementById('downloadInvoiceResult').appendChild(openUrlBtn);
                }
                
                // If the invoice has an invoice_pdf, add a button to open it
                if (data.invoice_pdf) {
                    const openPdfBtn = document.createElement('button');
                    openPdfBtn.textContent = 'Open Invoice PDF';
                    openPdfBtn.onclick = () => window.open(data.invoice_pdf, '_blank');
                    document.getElementById('downloadInvoiceResult').appendChild(openPdfBtn);
                }
            } catch (error) {
                displayJson('downloadInvoiceResult', { error: error.message });
            } finally {
                setLoading('downloadInvoiceLoading', false);
            }
        });

        // Open invoice URL
        document.getElementById('openInvoiceUrlBtn').addEventListener('click', () => {
            const url = document.getElementById('invoiceUrlInput').value.trim();
            if (!url) {
                alert('Please enter an invoice URL');
                return;
            }
            
            window.open(url, '_blank');
        });
    </script>
</body>
</html>
