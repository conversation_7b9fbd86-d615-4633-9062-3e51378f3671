import stripe

# Use the test key you provided
stripe.api_key = "sk_test_51QpXpXB2Zxp0l7ta9CBVzDQQTBS9guhwLbmC1TXx3KSs21vs2NfCHVGGZLPpu7FU7junUpSglDc7U4OpjchVK6z500QIo0MKTY"

try:
    # Try to create a customer
    customer = stripe.Customer.create(email="<EMAIL>")
    print("Customer created successfully: %s" % customer.id)

    # Try to retrieve the customer
    retrieved_customer = stripe.Customer.retrieve(customer.id)
    print("Customer retrieved successfully: %s" % retrieved_customer.id)

    # Try to create a portal session
    portal_session = stripe.billing_portal.Session.create(
        customer=customer.id,
        return_url="http://localhost:5173/dashboard/settings"
    )
    print("Portal session created successfully: %s" % portal_session.url)

except stripe.error.StripeError as e:
    print("Error: %s" % e)
