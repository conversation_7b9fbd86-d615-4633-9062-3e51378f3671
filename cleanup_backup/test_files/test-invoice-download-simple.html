<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Download Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ccc;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .invoice-item {
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
        }
        .invoice-item h3 {
            margin-top: 0;
        }
        .invoice-item p {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>Invoice Download Test</h1>
    
    <div class="container">
        <h2>Test User Login</h2>
        <p>Email: <span id="userEmail">Loading...</span></p>
        <p>Password: password</p>
        <button id="loginBtn">Login</button>
        <div id="loginResult"></div>
    </div>
    
    <div class="container">
        <h2>Invoices</h2>
        <button id="getInvoicesBtn">Get Invoices</button>
        <div id="invoicesContainer"></div>
    </div>
    
    <script>
        // API URL
        const API_URL = 'http://localhost:8000';
        
        // Store the access token
        let accessToken = '';
        
        // Get the latest test user
        async function getLatestTestUser() {
            try {
                const response = await fetch(`${API_URL}/test-users`, {
                    method: 'GET'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('userEmail').textContent = data.email;
                    return data.email;
                } else {
                    document.getElementById('userEmail').textContent = '<EMAIL>';
                    return '<EMAIL>';
                }
            } catch (error) {
                console.error('Error getting test user:', error);
                document.getElementById('userEmail').textContent = '<EMAIL>';
                return '<EMAIL>';
            }
        }
        
        // Login
        document.getElementById('loginBtn').addEventListener('click', async () => {
            try {
                const email = document.getElementById('userEmail').textContent;
                
                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: 'password'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    accessToken = data.access_token;
                    document.getElementById('loginResult').innerHTML = `<p style="color: green;">Login successful!</p>`;
                } else {
                    document.getElementById('loginResult').innerHTML = `<p style="color: red;">Login failed: ${data.detail}</p>`;
                }
            } catch (error) {
                console.error('Error logging in:', error);
                document.getElementById('loginResult').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        });
        
        // Get invoices
        document.getElementById('getInvoicesBtn').addEventListener('click', async () => {
            try {
                if (!accessToken) {
                    alert('Please login first');
                    return;
                }
                
                const response = await fetch(`${API_URL}/dashboard/billing/invoices`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`
                    }
                });
                
                const invoices = await response.json();
                
                const container = document.getElementById('invoicesContainer');
                container.innerHTML = '';
                
                if (invoices.length === 0) {
                    container.innerHTML = '<p>No invoices found</p>';
                    return;
                }
                
                invoices.forEach(invoice => {
                    const invoiceElement = document.createElement('div');
                    invoiceElement.className = 'invoice-item';
                    
                    invoiceElement.innerHTML = `
                        <h3>Invoice #${invoice.id}</h3>
                        <p><strong>Date:</strong> ${new Date(invoice.date).toLocaleDateString()}</p>
                        <p><strong>Amount:</strong> €${invoice.amount.toFixed(2)}</p>
                        <p><strong>Status:</strong> ${invoice.status}</p>
                        <p><strong>Type:</strong> ${invoice.type}</p>
                        <button class="download-btn" data-id="${invoice.id}">Download</button>
                    `;
                    
                    container.appendChild(invoiceElement);
                });
                
                // Add event listeners to download buttons
                document.querySelectorAll('.download-btn').forEach(button => {
                    button.addEventListener('click', async (event) => {
                        const invoiceId = event.target.getAttribute('data-id');
                        await downloadInvoice(invoiceId);
                    });
                });
            } catch (error) {
                console.error('Error getting invoices:', error);
                document.getElementById('invoicesContainer').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        });
        
        // Download invoice
        async function downloadInvoice(invoiceId) {
            try {
                const response = await fetch(`${API_URL}/dashboard/billing/invoices/${invoiceId}/download`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`
                    }
                });
                
                const data = await response.json();
                
                // Create a modal to display the result
                const modal = document.createElement('div');
                modal.style.position = 'fixed';
                modal.style.top = '0';
                modal.style.left = '0';
                modal.style.width = '100%';
                modal.style.height = '100%';
                modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                modal.style.display = 'flex';
                modal.style.justifyContent = 'center';
                modal.style.alignItems = 'center';
                
                const modalContent = document.createElement('div');
                modalContent.style.backgroundColor = 'white';
                modalContent.style.padding = '20px';
                modalContent.style.borderRadius = '5px';
                modalContent.style.maxWidth = '80%';
                modalContent.style.maxHeight = '80%';
                modalContent.style.overflow = 'auto';
                
                // Add close button
                const closeButton = document.createElement('button');
                closeButton.textContent = 'Close';
                closeButton.style.marginTop = '20px';
                closeButton.addEventListener('click', () => {
                    document.body.removeChild(modal);
                });
                
                // Display the result
                if (data.message) {
                    modalContent.innerHTML = `<h3>Invoice Download Result</h3><p>${data.message}</p>`;
                    modalContent.appendChild(closeButton);
                } else if (data.hosted_invoice_url) {
                    modalContent.innerHTML = `
                        <h3>Invoice Download Result</h3>
                        <p>Invoice URL available!</p>
                        <button id="openHostedUrl">Open Hosted Invoice</button>
                    `;
                    modalContent.appendChild(closeButton);
                    
                    // Add event listener to open hosted invoice URL
                    setTimeout(() => {
                        document.getElementById('openHostedUrl').addEventListener('click', () => {
                            window.open(data.hosted_invoice_url, '_blank');
                        });
                    }, 100);
                } else if (data.invoice_pdf) {
                    modalContent.innerHTML = `
                        <h3>Invoice Download Result</h3>
                        <p>Invoice PDF available!</p>
                        <button id="openPdfUrl">Open PDF</button>
                    `;
                    modalContent.appendChild(closeButton);
                    
                    // Add event listener to open PDF URL
                    setTimeout(() => {
                        document.getElementById('openPdfUrl').addEventListener('click', () => {
                            window.open(data.invoice_pdf, '_blank');
                        });
                    }, 100);
                } else {
                    modalContent.innerHTML = `
                        <h3>Invoice Download Result</h3>
                        <p>No invoice URL available.</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    modalContent.appendChild(closeButton);
                }
                
                modal.appendChild(modalContent);
                document.body.appendChild(modal);
            } catch (error) {
                console.error('Error downloading invoice:', error);
                alert(`Error downloading invoice: ${error.message}`);
            }
        }
        
        // Initialize
        getLatestTestUser();
    </script>
</body>
</html>
