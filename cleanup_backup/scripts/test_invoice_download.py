#!/usr/bin/env python3
"""
Script to test the invoice download functionality directly in the backend.
"""

import sys
import os
import json
import uuid
from datetime import datetime, timedelta
import requests

# URL of the backend API
API_URL = "http://localhost:8000"

def create_test_user():
    """Create a test user in the database."""
    print("Creating test user...")

    # Generate a random email to avoid conflicts
    email = f"test_{uuid.uuid4().hex[:8]}@example.com"
    password = "password"

    # Register the user
    response = requests.post(
        f"{API_URL}/auth/register",
        json={
            "email": email,
            "password": password,
            "business_name": "Test Business"
        }
    )

    if response.status_code == 200:
        print(f"User created successfully: {email}")
        return email, password
    else:
        print(f"Failed to create user: {response.status_code} - {response.text}")
        return None, None

def login(email, password):
    """Log in and get an access token."""
    print(f"Logging in as {email}...")

    response = requests.post(
        f"{API_URL}/auth/login",
        json={
            "email": email,
            "password": password
        }
    )

    if response.status_code == 200:
        data = response.json()
        token = data.get("access_token")
        print("Login successful")
        return token
    else:
        print(f"Login failed: {response.status_code} - {response.text}")
        return None

def create_test_invoices(token):
    """Create test invoices for the user."""
    print("Creating test invoices...")

    # Get the user's ID
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{API_URL}/auth/me", headers=headers)

    if response.status_code != 200:
        print(f"Failed to get user info: {response.status_code} - {response.text}")
        return False

    user_data = response.json()
    user_id = user_data.get("id")

    if not user_id:
        print("User ID not found in response")
        return False

    # Create a test pricing plan if needed
    response = requests.get(f"{API_URL}/dashboard/billing/plans", headers=headers)

    if response.status_code != 200:
        print(f"Failed to get pricing plans: {response.status_code} - {response.text}")
        return False

    plans = response.json()
    plan_id = plans[0]["id"] if plans else None

    if not plan_id:
        print("No pricing plan found")
        return False

    # Connect directly to the database to create invoices
    # This is a simplified approach - in a real scenario, you would use the API
    try:
        import psycopg2
        import psycopg2.extras
        # Register UUID adapter
        psycopg2.extras.register_uuid()

        # Connect to the database
        conn = psycopg2.connect(
            host="postgres",  # Use the service name from docker-compose
            port=5432,
            database="fixmycal",
            user="postgres",
            password="postgres"
        )

        # Create a cursor
        cur = conn.cursor()

        # Create test invoices
        for i in range(1, 4):
            invoice_id = uuid.uuid4()
            period_start = datetime.now() - timedelta(days=30 * i)
            period_end = datetime.now() - timedelta(days=30 * (i - 1))

            # Insert the invoice
            cur.execute(
                """
                INSERT INTO invoices (
                    id, user_id, plan_id, period_start, period_end, total, stripe_invoice_id, status,
                    invoice_pdf_url, hosted_invoice_url
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (
                    invoice_id,
                    uuid.UUID(user_id),
                    uuid.UUID(plan_id),
                    period_start.date(),
                    period_end.date(),
                    50.0,
                    f"inv_test_{i}",
                    "paid",
                    None,
                    None
                )
            )

        # Create a mock invoice with "real" Stripe URLs for testing
        invoice_id = uuid.uuid4()
        cur.execute(
            """
            INSERT INTO invoices (
                id, user_id, plan_id, period_start, period_end, total, stripe_invoice_id, status,
                invoice_pdf_url, hosted_invoice_url
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """,
            (
                invoice_id,
                uuid.UUID(user_id),
                uuid.UUID(plan_id),
                (datetime.now() - timedelta(days=15)).date(),
                (datetime.now() + timedelta(days=15)).date(),
                50.0,
                "in_test_with_urls",
                "paid",
                "https://files.stripe.com/v1/files/file_test_pdf/pdf",
                "https://dashboard.stripe.com/test/invoices/in_test_with_urls"
            )
        )

        # Commit the changes
        conn.commit()

        # Close the cursor and connection
        cur.close()
        conn.close()

        print("Test invoices created successfully")
        return True

    except Exception as e:
        print(f"Error creating test invoices: {str(e)}")
        return False

def get_invoices(token):
    """Get all invoices for the user."""
    print("Getting invoices...")

    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{API_URL}/dashboard/billing/invoices", headers=headers)

    if response.status_code == 200:
        invoices = response.json()
        print(f"Found {len(invoices)} invoices")
        return invoices
    else:
        print(f"Failed to get invoices: {response.status_code} - {response.text}")
        return []

def download_invoice(token, invoice_id):
    """Download an invoice."""
    print(f"Downloading invoice {invoice_id}...")

    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{API_URL}/dashboard/billing/invoices/{invoice_id}/download", headers=headers)

    if response.status_code == 200:
        data = response.json()
        print("Invoice download successful")
        print(json.dumps(data, indent=2))
        return data
    else:
        print(f"Failed to download invoice: {response.status_code} - {response.text}")
        return None

def main():
    """Main function."""
    # Create a test user
    email, password = create_test_user()
    if not email or not password:
        print("Failed to create test user")
        return

    # Log in
    token = login(email, password)
    if not token:
        print("Failed to log in")
        return

    # Create test invoices
    if not create_test_invoices(token):
        print("Failed to create test invoices")
        return

    # Get invoices
    invoices = get_invoices(token)
    if not invoices:
        print("No invoices found")
        return

    # Print invoice IDs
    print("\nInvoice IDs:")
    for invoice in invoices:
        print(f"- {invoice['id']} ({invoice['stripe_invoice_id']})")

    # Download each invoice
    print("\nTesting invoice downloads:")
    for invoice in invoices:
        download_invoice(token, invoice['id'])
        print()

    print("Test completed successfully")

if __name__ == "__main__":
    main()
