"""
Test a complete conversation with the AI assistant
"""

import sys
import os
import asyncio
import logging
from uuid import UUID, uuid4
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models.client import Client
from app.models.service import Service
from app.services.ai_assistant import AIAssistant
from app.services.embedding_service import embedding_service
from app.models.message import Message
from app.models.ai_context import AIConversationContext, ConversationMessage
from app.services.conversation_service import ConversationService

async def test_conversation():
    """Test a complete conversation with the AI assistant"""
    # Create a database session
    db = SessionLocal()
    
    try:
        # User ID for the test user
        user_id = "b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7"  # <EMAIL>
        user_uuid = UUID(user_id)
        
        # Test phone number
        client_phone = "+1234567890"
        
        # Find the test client
        client = db.query(Client).filter(
            Client.user_id == user_uuid,
            Client.phone.like(f"%{client_phone}%")
        ).first()
        
        if not client:
            logger.info(f"Client not found for phone {client_phone}, creating new client")
            client = Client(
                id=uuid4(),
                user_id=user_uuid,
                name="John Smith",
                phone=client_phone,
                email="",
                tags=["test"],
                notes="Automatically created for WhatsApp AI testing",
                created_at=datetime.now()
            )
            db.add(client)
            db.commit()
            db.refresh(client)
        
        # Get or create conversation context
        conversation_service = ConversationService(db)
        context = conversation_service.get_or_create_context(user_id, str(client.id))
        
        # Clear existing conversation history
        db.query(ConversationMessage).filter(
            ConversationMessage.conversation_id == context.id
        ).delete()
        db.commit()
        
        # Create AI assistant
        ai_assistant = AIAssistant(db, user_id)
        
        # Conversation messages
        messages = [
            "I'd like to book a Basic Haircut tomorrow at 10am",
            "Yes, that's correct. Please book it for me.",
            "Great, thank you!"
        ]
        
        # Process each message
        for i, message in enumerate(messages):
            logger.info(f"Message {i+1}: {message}")
            
            # Process the message
            response, action = await ai_assistant.process_message(str(client.id), message)
            
            logger.info(f"Response {i+1}: {response}")
            
            # Handle any actions
            if action and action.get("action") == "book_appointment":
                success, booking_message = await ai_assistant.book_appointment(action)
                
                if success:
                    logger.info(f"Appointment booked successfully: {booking_message}")
                    logger.info(f"Appointment details: {action}")
                else:
                    logger.error(f"Failed to book appointment: {booking_message}")
            
            # Wait a moment before sending the next message
            if i < len(messages) - 1:
                await asyncio.sleep(2)
        
        logger.info("Conversation test completed successfully!")
    
    except Exception as e:
        logger.error(f"Error testing conversation: {str(e)}")
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_conversation())
