import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Navigation,
  Hero,
  Features,
  TimeSavings,
  HowItWorks,
  Pricing,
  Contact,
  CTA,
  Footer
} from '../components/landing';

export default function LandingPage() {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    countryCode: '+34',
    businessType: '',
    needs: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Form submitted with user data
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      <div className="pt-16">
        <Hero t={t} />
        <Features t={t} />
        <TimeSavings t={t} />
        <HowItWorks t={t} />
        <Pricing t={t} />
        <Contact id="contact" formData={formData} onInputChange={handleInputChange} onSubmit={handleSubmit} t={t} />
        <CTA t={t} />
      </div>
      <Footer t={t} />
    </div>
  );
}
