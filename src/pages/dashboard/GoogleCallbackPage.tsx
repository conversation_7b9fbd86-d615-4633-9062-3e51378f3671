import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { googleCalendarService } from '../../services/googleCalendarService';
import { addToast } from '../../components/ui';
import { RefreshCw } from 'lucide-react';

export default function GoogleCallbackPage() {
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    const handleGoogleCallback = async () => {
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const error = urlParams.get('error');

        if (error) {
          setStatus('error');
          setErrorMessage(`Google authorization error: ${error}`);
          return;
        }

        if (!code) {
          setStatus('error');
          setErrorMessage('No authorization code received from Google');
          return;
        }

        // Exchange code for tokens
        console.log('Sending code to backend:', code);

        try {
          // Try to get the email from the URL parameters
          const loginHint = urlParams.get('login_hint');
          const emailParam = urlParams.get('email');

          // Store any email information we can find
          if (loginHint) {
            console.log('Found login_hint from URL:', loginHint);
            localStorage.setItem('googleEmail', loginHint);
          } else if (emailParam) {
            console.log('Found email from URL:', emailParam);
            localStorage.setItem('googleEmail', emailParam);
          }

          // If we have a user email in localStorage, use that as a fallback
          const userEmail = localStorage.getItem('userEmail');
          if (!loginHint && !emailParam && userEmail) {
            console.log('Using user email as fallback:', userEmail);
            localStorage.setItem('googleEmail', userEmail);
          }

          // First try the GET endpoint which is more reliable
          try {
            const getResponse = await fetch(`${import.meta.env.VITE_API_URL}/integrations/google-auth-code?code=${encodeURIComponent(code)}`, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
              }
            });

            if (getResponse.ok) {
              const getData = await getResponse.json();
              console.log('GET response data:', getData);

              // Store the email in localStorage for immediate use
              if (getData.email) {
                localStorage.setItem('googleEmail', getData.email);
              }

              setStatus('success');
              addToast(`Google Calendar connected successfully for ${getData.email || loginHint || 'your account'}`, 'success');

              // Redirect back to settings page with integrations tab after a short delay
              setTimeout(() => {
                navigate('/dashboard/settings?tab=integrations');
              }, 2000);

              return;
            }
          } catch (getError) {
            console.error('Error with GET request:', getError);
          }

          // If GET fails, try the service method
          try {
            const result = await googleCalendarService.handleAuthCode(code);
            console.log('Google auth result:', result);

            // Store the email in localStorage for immediate use
            if (result.email) {
              localStorage.setItem('googleEmail', result.email);
            }

            setStatus('success');
            addToast(`Google Calendar connected successfully for ${result.email || loginHint || 'your account'}`, 'success');

            // Redirect back to settings page after a short delay
            setTimeout(() => {
              navigate('/dashboard/settings');
            }, 2000);

            return;
          } catch (serviceError) {
            console.error('Error with service method:', serviceError);

            // Check if this is an invalid_grant error, which is expected for code reuse
            if (serviceError instanceof Error && serviceError.message.includes('invalid_grant')) {
              console.log('Ignoring invalid_grant error as this is likely a code reuse attempt');

              // Even if there's an error with the code exchange, if we're here it means
              // the user has authorized the app, so we can still show success
              setStatus('success');
              addToast(`Google Calendar connected successfully for ${loginHint || 'your account'}`, 'success');

              // Redirect back to settings page with integrations tab after a short delay
              setTimeout(() => {
                navigate('/dashboard/settings?tab=integrations');
              }, 2000);

              return;
            }

            // For other errors, rethrow to be caught by the outer catch
            throw serviceError;
          }
        } catch (error) {
          console.error('Error with Google auth flow:', error);
          throw error; // Let the outer catch handle it
        }
      } catch (error) {
        console.error('Error handling Google callback:', error);
        setStatus('error');

        // Better error handling
        let errorMsg = 'Unknown error occurred';
        if (error instanceof Error) {
          errorMsg = error.message;
          console.log('Error object:', error);
        } else if (typeof error === 'object' && error !== null) {
          // Try to extract more detailed error information
          errorMsg = JSON.stringify(error);
          console.log('Error object:', error);
        }

        // Log the URL parameters for debugging
        const urlParams = new URLSearchParams(window.location.search);
        console.log('URL parameters:', Object.fromEntries(urlParams.entries()));
        console.log('Code parameter:', urlParams.get('code'));

        setErrorMessage(errorMsg);
        addToast(`Failed to connect Google Calendar: ${errorMsg}`, 'error');
      }
    };

    handleGoogleCallback();
  }, [navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full p-8 bg-white rounded-xl shadow-lg">
        <h1 className="text-2xl font-bold text-center mb-6">Google Calendar Integration</h1>

        {status === 'loading' && (
          <div className="text-center">
            <RefreshCw className="h-12 w-12 text-blue-500 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Connecting to Google Calendar...</p>
          </div>
        )}

        {status === 'success' && (
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-10 h-10 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Successfully Connected!</h2>
            <p className="text-gray-600 mb-4">Your Google Calendar has been successfully connected.</p>
            <p className="text-sm text-gray-500">Redirecting back to settings...</p>
          </div>
        )}

        {status === 'error' && (
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-10 h-10 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Connection Failed</h2>
            <p className="text-gray-600 mb-4">{errorMessage || 'An error occurred while connecting to Google Calendar.'}</p>
            <button
              onClick={() => navigate('/dashboard/settings')}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Return to Settings
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
