/**
 * MessagesPage Component
 *
 * This component displays WhatsApp conversations and messages.
 * It handles loading conversations, displaying messages, sending messages,
 * and real-time updates through WebSockets or polling.
 *
 * Flow of WhatsApp integration in this component:
 * 1. When the component loads, it checks if WhatsApp is connected
 * 2. If connected, it loads conversations and sets up WebSocket connections
 * 3. When a conversation is selected, it loads messages for that conversation
 * 4. When a message is sent, it sends the message to the backend and updates the UI
 * 5. When a new message is received, it updates the UI with the new message
 *
 * See docs/WHATSAPP_INTEGRATION.md for more details on the WhatsApp integration.
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToastContext as useToast } from '../../components/ui/ToastProvider';
import { DashboardLayout } from '../../components/dashboard';
import { clientService } from '../../services/clientService';
import { Contact, Message } from '../../types/whatsapp';
import { whatsappService } from '../../services/whatsappService';
import ContactList from '../../components/messages/ContactList';
import MessageList from '../../components/messages/MessageList';
import MessageInput from '../../components/messages/MessageInput';
import { User, UserPlus, MessageSquare } from 'lucide-react';
// Import WebSocket services
// We're only using Evolution API Socket.io for now
import { evolutionSocketService, EvolutionSocketEventType } from '../../services/whatsapp/socketio';
import websocketConfig from '../../services/whatsapp/websocketConfig';
import { getInstanceName } from '../../services/whatsapp/config';
import { checkInstanceConnection, fetchInstances } from '../../services/whatsapp/connection';
// Client type is imported but not directly used in this component
// import type { Client } from '../../types/api';

const MessagesPage: React.FC = () => {
  const navigate = useNavigate();
  const { addToast } = useToast();

  // State
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [whatsappConnected, setWhatsappConnected] = useState<boolean | null>(null);
  const [needsWhatsAppSetup, setNeedsWhatsAppSetup] = useState<boolean>(false);

  // State for pagination
  const [displayLimit, setDisplayLimit] = useState(10);
  const [hasMoreContacts, setHasMoreContacts] = useState(false);
  const [allContacts, setAllContacts] = useState<Contact[]>([]);

  // State to prevent repeated API calls
  const [contactsFetched, setContactsFetched] = useState(false);
  const [messagesFetched, setMessagesFetched] = useState<Record<string, boolean>>({});

  // State for client integration
  const [clientPhones, setClientPhones] = useState<Set<string>>(new Set());
  const [isAddingToClients, setIsAddingToClients] = useState(false);

  // Refs for WebSocket subscriptions
  // We're only using Evolution API Socket.io subscriptions for now
  const evolutionMessageSubscriptionRef = useRef<(() => void) | null>(null);
  const evolutionStatusSubscriptionRef = useRef<(() => void) | null>(null);
  const evolutionQrCodeSubscriptionRef = useRef<(() => void) | null>(null);

  // Function to load messages for a contact
  const loadMessages = useCallback(async (contactId: string, forceRefresh = false) => {
    // Check if we've already tried to fetch messages for this contact and not forcing refresh
    if (messagesFetched[contactId] && !forceRefresh) {
      // Skip silently without logging
      return;
    }

    setIsLoading(true);
    try {
      const contactMessages = await whatsappService.fetchMessages(contactId);
      setMessages(contactMessages);

      // Mark messages as fetched for this contact
      setMessagesFetched(prev => ({ ...prev, [contactId]: true }));
    } catch (error) {
      console.error('Error loading messages:', error);
      addToast('Failed to load messages', 'error');
      setMessages([]);
    } finally {
      setIsLoading(false);
    }
  }, [addToast, messagesFetched]);

  // Function to load WhatsApp contacts
  const loadContacts = useCallback(async () => {
    // Don't try to load contacts if WhatsApp is not connected
    if (whatsappConnected === false) {
      return [];
    }

    setIsLoading(true);
    try {
      const whatsappContacts = await whatsappService.fetchRecentConversations(50);

      // Store all contacts for pagination
      setAllContacts(whatsappContacts);

      // Set if there are more contacts than the display limit
      setHasMoreContacts(whatsappContacts.length > displayLimit);

      // Only display the first batch of contacts
      const initialContacts = whatsappContacts.slice(0, displayLimit);
      setContacts(initialContacts);

      // Mark contacts as fetched
      setContactsFetched(true);

      // If no contact is selected and we have contacts, select the first one
      if (!selectedContact && whatsappContacts.length > 0) {
        setSelectedContact(whatsappContacts[0]);
        loadMessages(whatsappContacts[0].id);
      } else if (selectedContact && whatsappContacts.length > 0) {
        // If a contact is already selected, check if it still exists in the new contacts list
        const stillExists = whatsappContacts.some(contact => contact.id === selectedContact.id);
        if (!stillExists) {
          // If the selected contact no longer exists, select the first one
          setSelectedContact(whatsappContacts[0]);
          loadMessages(whatsappContacts[0].id);
        }
      }

      return whatsappContacts;
    } catch (error) {
      // Only show error toast if this is the first load attempt
      if (!contactsFetched) {
        console.error('Error loading WhatsApp contacts:', error);
        addToast('Failed to load WhatsApp contacts', 'error');
      }
      setContactsFetched(true); // Mark as fetched even on error to prevent infinite retries
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [addToast, displayLimit, selectedContact, loadMessages, contactsFetched, whatsappConnected]);

  // Function to handle sending a message
  const handleSendMessage = useCallback(async (text: string, file?: File) => {
    if (!selectedContact) return;

    // Create a new message for the UI immediately with 'sending' status and unique ID
    const newMsg: Message = {
      id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
      text,
      timestamp: new Date().toISOString(),
      sender: 'me',
      status: 'sending'
    };

    // If there's a file, add file properties to the message
    if (file) {
      const fileType = file.type.split('/')[0];

      // Set message type based on file type
      if (fileType === 'image') {
        newMsg.type = 'image';
      } else if (fileType === 'video') {
        newMsg.type = 'video';
      } else if (fileType === 'audio') {
        newMsg.type = 'audio';
      } else {
        newMsg.type = 'document';
      }

      // Add file metadata
      newMsg.fileName = file.name;
      newMsg.fileSize = file.size;
      newMsg.mimeType = file.type;

      // Create a temporary URL for preview
      newMsg.mediaUrl = URL.createObjectURL(file);
    } else {
      newMsg.type = 'text';
    }

    // Add the message to the list
    setMessages(prev => [...prev, newMsg]);

    // Send the message
    setIsSending(true);
    try {
      const success = await whatsappService.sendDirectMessage(selectedContact.phone, text);

      if (success) {
        addToast('Message sent successfully', 'success');

        // Update message status to 'sent'
        setMessages(prev =>
          prev.map(m => (m.id === newMsg.id ? { ...m, status: 'sent' } : m))
        );

        // Simulate message being delivered after 2 seconds
        setTimeout(() => {
          setMessages(prev =>
            prev.map(m => (m.id === newMsg.id ? { ...m, status: 'delivered' } : m))
          );

          // Simulate message being read after 3 more seconds
          setTimeout(() => {
            setMessages(prev =>
              prev.map(m => (m.id === newMsg.id ? { ...m, status: 'read' } : m))
            );
          }, 3000);
        }, 2000);
      } else {
        // Update message status to 'failed'
        setMessages(prev =>
          prev.map(m => (m.id === newMsg.id ? { ...m, status: 'failed' } : m))
        );

        addToast('Failed to send message', 'error');
      }
    } catch (error) {
      console.error('Error sending message:', error);

      // Update message status to 'failed'
      setMessages(prev =>
        prev.map(m => (m.id === newMsg.id ? { ...m, status: 'failed' } : m))
      );

      addToast('Failed to send message', 'error');
    } finally {
      setIsSending(false);
    }
  }, [addToast, selectedContact]);

  // Function to load more contacts
  const loadMoreContacts = useCallback(() => {
    const newLimit = displayLimit + 10;
    setDisplayLimit(newLimit);

    // Update the displayed contacts
    if (allContacts.length > 0) {
      const moreContacts = allContacts.slice(0, newLimit);
      setContacts(moreContacts);
      setHasMoreContacts(allContacts.length > newLimit);
    }
  }, [allContacts, displayLimit]);

  // Function to load client data to check which WhatsApp contacts are already clients
  const loadClientData = useCallback(async () => {
    try {
      // Get all clients with a reasonable page size
      const clientsResponse = await clientService.getClients({
        page_size: 100 // Use a smaller page size to avoid 422 errors
      });

      // Create a set of client phone numbers for quick lookup
      const phoneSet = new Set<string>();
      clientsResponse.items.forEach(client => {
        if (client.phone) {
          // Normalize phone number for comparison by removing all non-digit characters
          const normalizedPhone = client.phone.replace(/\D/g, '');
          phoneSet.add(normalizedPhone);

          // Also add the last 9 digits for comparison with WhatsApp numbers
          // This helps match numbers regardless of country code format
          if (normalizedPhone.length >= 9) {
            const last9Digits = normalizedPhone.slice(-9);
            phoneSet.add(last9Digits);
          }
        }
      });

      setClientPhones(phoneSet);
      // Only log in development mode and only if verbose logging is enabled
      if (import.meta.env.DEV && localStorage.getItem('verboseLogging') === 'true') {
        console.log('Loaded client phone numbers:', [...phoneSet]);
      }
    } catch (error) {
      console.error('Error loading client data:', error);
      // Create an empty set to avoid errors
      setClientPhones(new Set<string>());
      // Don't show an error toast as this is a background operation
    }
  }, []);

  // Function to add a WhatsApp contact to clients
  const handleAddToClients = useCallback(async (contact: Contact) => {
    if (isAddingToClients) return;

    setIsAddingToClients(true);
    try {
      // Create a new client from the WhatsApp contact
      await clientService.createClient({
        name: contact.name,
        email: '', // WhatsApp contacts don't have email
        phone: `+${contact.phone}`,
        tags: ['WhatsApp Contact'],
        notes: 'Added from WhatsApp contacts',
        lastAppointment: null,
        nextAppointment: null,
        profileImage: contact.avatar // Include the WhatsApp avatar as profile image
      });

      // Update the client phones set with multiple formats for better matching
      setClientPhones(prev => {
        const newSet = new Set(prev);
        // Add the original phone number
        newSet.add(contact.phone);

        // Add normalized version (digits only)
        const normalizedPhone = contact.phone.replace(/\D/g, '');
        newSet.add(normalizedPhone);

        // Add last 9 digits for better matching
        if (normalizedPhone.length >= 9) {
          newSet.add(normalizedPhone.slice(-9));
        }

        return newSet;
      });

      addToast(`Added ${contact.name} to clients successfully`, 'success');

      // Stay on the current page instead of navigating
      // You can uncomment this when you have a client details page
      // To enable this, store the client ID and uncomment:
      // navigate(`/dashboard/clients/${clientId}`);
    } catch (error) {
      console.error('Error adding contact to clients:', error);
      addToast('Failed to add contact to clients', 'error');
    } finally {
      setIsAddingToClients(false);
    }
  }, [isAddingToClients, addToast]); // Removed navigate from dependencies

  // Check WhatsApp connection and load data on component mount
  useEffect(() => {
    // Skip if we've already fetched contacts
    if (contactsFetched) {
      return;
    }

    // Check if user is authenticated
    const token = localStorage.getItem('accessToken');
    if (!token) {
      console.warn('No authentication token found, redirecting to login');
      navigate('/login');
      return;
    }

    // First check WhatsApp connection before loading any data
    const checkConnectionAndLoadData = async () => {
      try {
        setIsLoading(true);

        // First try to fetch instances to see if we have any available
        try {
          const instances = await fetchInstances();
          console.log('Found WhatsApp instances:', instances);

          // If we have any instances, we don't need setup
          if (Array.isArray(instances) && instances.length > 0) {
            console.log('Found WhatsApp instances, no setup needed');
            setNeedsWhatsAppSetup(false);

            // Check if any of the instances are connected
            const connectedInstance = instances.find(instance => {
              const instanceObj = instance as Record<string, unknown>;
              return instanceObj.connectionStatus === 'open' ||
                (instanceObj.instance &&
                 typeof instanceObj.instance === 'object' &&
                 (instanceObj.instance as Record<string, unknown>).status === 'open');
            });

            if (connectedInstance) {
              console.log('Found connected WhatsApp instance');
              setWhatsappConnected(true);
            }
          } else {
            // No instances found, we need setup
            console.log('No WhatsApp instances found');
            setNeedsWhatsAppSetup(true);
          }
        } catch (error) {
          console.error('Error fetching WhatsApp instances:', error);
        }

        // Only check connection if we haven't already determined it from the instances
        if (whatsappConnected === null) {
          try {
            // Use the cached connection check to reduce API calls
            const connectionState = await checkInstanceConnection(false);
            setWhatsappConnected(connectionState.connected);

            // Check if WhatsApp needs to be set up based on connection state
            const connectionStateWithSetup = connectionState as { connected: boolean; state?: string; needsSetup?: boolean };
            if (connectionStateWithSetup.needsSetup) {
              setNeedsWhatsAppSetup(true);
            }
          } catch (error) {
            console.warn('Error checking WhatsApp connection state:', error);
            // We'll rely on the instances check instead
          }
        }

        // Only load contacts and client data if WhatsApp is connected
        if (whatsappConnected) {
          await loadClientData();
          await loadContacts();
        }
      } catch (error) {
        console.error('Error checking WhatsApp connection:', error);
        setWhatsappConnected(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkConnectionAndLoadData();
  }, [navigate, contactsFetched, loadContacts, loadClientData, whatsappConnected]);

  // Polling interval for message updates (in milliseconds)
  const POLLING_INTERVAL = 30000; // 30 seconds

  // Polling reference to clear interval when component unmounts
  const pollingIntervalRef = useRef<number | null>(null);

  // Start polling for message updates
  const startPolling = useCallback(() => {
    // Clear any existing polling interval
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    // Set up polling interval
    pollingIntervalRef.current = window.setInterval(() => {
      // Refresh messages for the current contact
      if (selectedContact) {
        loadMessages(selectedContact.id, true);
      }

      // Refresh contacts list to get latest messages
      loadContacts();
    }, POLLING_INTERVAL);

    // Started polling for message updates
  }, [selectedContact, loadMessages, loadContacts]);

  // Periodically check WhatsApp connection status (every 2 minutes)
  useEffect(() => {
    // Skip if WhatsApp is not connected initially
    if (whatsappConnected === false) {
      return;
    }

    // Set up interval to check connection status every 2 minutes
    const connectionCheckInterval = setInterval(async () => {
      try {
        // Use the cached connection check to reduce API calls
        const connectionState = await checkInstanceConnection(false);
        setWhatsappConnected(connectionState.connected);
      } catch (error) {
        console.error('Error checking WhatsApp connection:', error);
        setWhatsappConnected(false);
      }
    }, 120000); // Check every 2 minutes

    return () => {
      clearInterval(connectionCheckInterval);
    };
  }, [whatsappConnected]);

  // Set up WebSocket connection for real-time updates
  useEffect(() => {
    // If WhatsApp is not connected, don't try to set up WebSockets
    if (whatsappConnected === false) {
      return;
    }

    // If we're still checking the connection status, wait
    if (whatsappConnected === null) {
      return;
    }

    // Check if WebSockets are enabled in the configuration
    if (!websocketConfig.enabled) {
      // Only log this once during development
      if (import.meta.env.DEV) {
        console.log('WebSocket connections are disabled, using polling instead');
        console.log('This is intentional to avoid 404 errors when connecting to Evolution API');
      }
      // Start polling since WebSockets are disabled
      startPolling();
      return;
    }

    const setupWebSockets = async () => {
      // Check if WebSockets are enabled in the config
      if (!websocketConfig.enabled) {
        // WebSockets are disabled, using polling
        console.log('WebSocket connections are disabled in setupWebSockets, using polling instead');
        startPolling();
        return;
      }

      try {
        // Try to connect to Evolution API Socket.io
        try {
          // Use the instance name from the config
          const instanceName = getInstanceName();
          if (instanceName) {
            await evolutionSocketService.connect(instanceName);
          } else {
            console.warn('No WhatsApp instance ID available, cannot connect to WebSocket');
            throw new Error('No WhatsApp instance ID available');
          }

          // Only set up subscriptions if connection was successful
          if (evolutionSocketService.isConnected()) {
            // Subscribe to message events from Evolution API
            evolutionMessageSubscriptionRef.current = evolutionSocketService.subscribe(
              EvolutionSocketEventType.MESSAGE,
              () => {
                // Received message from WebSocket

                // Refresh messages for the current contact
                if (selectedContact) {
                  loadMessages(selectedContact.id, true);
                }

                // Refresh contacts list to get latest messages
                loadContacts();
              }
            );

            // Subscribe to status events from Evolution API
            evolutionStatusSubscriptionRef.current = evolutionSocketService.subscribe(
              EvolutionSocketEventType.STATUS,
              () => {
                // Received status update from WebSocket

                // Refresh contacts on status change
                loadContacts();
              }
            );

            // Subscribe to QR code events from Evolution API
            evolutionQrCodeSubscriptionRef.current = evolutionSocketService.subscribe(
              EvolutionSocketEventType.QR_CODE,
              () => {
                // Received QR code from WebSocket
                // Handle QR code if needed
              }
            );

            // Successfully connected to WebSocket
          } else {
            // Connection failed, fall back to polling
            // WebSocket connection not established, falling back to polling
            startPolling();
          }
        } catch (error) {
          console.warn('Error connecting to Evolution API WebSocket:', error);
          // Evolution API Socket.io connection failed
          // We'll fall back to polling if WebSocket connections fail
          startPolling();
        }
      } catch (error) {
        console.error('Error setting up WebSockets:', error);
        // Continue with the component even if WebSocket fails
        // This ensures the component can still function without real-time updates
        startPolling();
      }
    };

    // Set up WebSocket connections
    setupWebSockets().catch(err => {
      console.error('Failed to set up WebSockets:', err);
    });

    // Clean up subscriptions on unmount
    return () => {
      // Only clean up if WebSockets are enabled
      if (websocketConfig.enabled) {
        try {
          // Clean up Evolution API Socket.io subscriptions
          if (evolutionMessageSubscriptionRef.current) {
            evolutionMessageSubscriptionRef.current();
            evolutionMessageSubscriptionRef.current = null;
          }

          if (evolutionStatusSubscriptionRef.current) {
            evolutionStatusSubscriptionRef.current();
            evolutionStatusSubscriptionRef.current = null;
          }

          if (evolutionQrCodeSubscriptionRef.current) {
            evolutionQrCodeSubscriptionRef.current();
            evolutionQrCodeSubscriptionRef.current = null;
          }

          // Disconnect from Evolution API Socket.io
          evolutionSocketService.disconnect();
        } catch (error) {
          console.error('Error cleaning up WebSocket subscriptions:', error);
        }
      }
    };
  }, [selectedContact, loadContacts, loadMessages, startPolling, whatsappConnected]);

  // Clean up polling interval when component unmounts
  useEffect(() => {
    return () => {
      // Clear polling interval
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, []);

  // Media proxy check removed - we're not using it anymore

  // Set up periodic refresh for messages and contacts if not using polling or WebSockets
  useEffect(() => {
    // Skip if no contact is selected or if we're already polling
    if (!selectedContact || pollingIntervalRef.current) return;

    // Set up interval to refresh messages every 20 seconds
    // This is a fallback in case both WebSockets and polling are not working
    const messageRefreshInterval = setInterval(() => {
      if (selectedContact) {
        // Silent refresh without logging
        loadMessages(selectedContact.id, true);
      }
    }, 20000); // 20 seconds

    // Set up interval to refresh contacts every 30 seconds
    const contactsRefreshInterval = setInterval(() => {
      // Silent refresh without logging
      loadContacts();
    }, 30000); // 30 seconds

    // Clean up intervals on unmount or when selected contact changes
    return () => {
      clearInterval(messageRefreshInterval);
      clearInterval(contactsRefreshInterval);
    };
  }, [selectedContact, loadMessages, loadContacts]);

  // Handle contact selection
  const handleContactSelect = (contact: Contact) => {
    // Mark messages as read when selecting a contact
    if (contact.unread && contact.unread > 0) {
      const updatedContacts = contacts.map(c =>
        c.id === contact.id ? { ...c, unread: 0 } : c
      );
      setContacts(updatedContacts);
    }

    // Reset messages fetched state for the previously selected contact
    if (selectedContact && selectedContact.id !== contact.id) {
      setMessagesFetched(prev => ({ ...prev, [selectedContact.id]: false }));
    }

    setSelectedContact(contact);
    // Always force refresh messages when selecting a contact
    loadMessages(contact.id, true);
  };

  // Helper function to toggle WebSocket in development mode
  const toggleWebSocketInDev = useCallback(() => {
    if (import.meta.env.DEV) {
      const currentValue = localStorage.getItem('skipWebSocket') === 'true';
      localStorage.setItem('skipWebSocket', (!currentValue).toString());
      addToast(
        `WebSocket ${!currentValue ? 'disabled' : 'enabled'} for development. Refresh the page.`,
        'info'
      );
    }
  }, [addToast]);

  return (
    <DashboardLayout>
      <div className="flex flex-col h-[calc(100vh-120px)]" style={{ maxHeight: 'calc(100vh - 120px)' }}>
        <div className="p-2 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">Messages</h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">Manage your WhatsApp conversations</p>
            </div>
            {import.meta.env.DEV && (
              <button
                onClick={toggleWebSocketInDev}
                className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md"
                title="Toggle WebSocket for development"
              >
                {localStorage.getItem('skipWebSocket') === 'true' ? 'Enable WebSocket' : 'Disable WebSocket'}
              </button>
            )}
          </div>
        </div>

        {needsWhatsAppSetup ? (
          <div className="flex-1 flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 p-8 m-4">
            <div className="text-center max-w-md">
              <div className="flex justify-center mb-4">
                <MessageSquare className="w-16 h-16 text-gray-400 dark:text-gray-500" />
              </div>
              <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">WhatsApp Setup Required</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                You need to set up your WhatsApp connection to see and send messages. Please go to the Integrations tab in Settings to configure WhatsApp.
              </p>
              <button
                onClick={() => navigate('/dashboard/settings?tab=integrations')}
                className="px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors font-medium"
              >
                Go to WhatsApp Setup
              </button>
            </div>
          </div>
        ) : whatsappConnected === false ? (
          <div className="flex-1 flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 p-8 m-4">
            <div className="text-center max-w-md">
              <div className="flex justify-center mb-4">
                <MessageSquare className="w-16 h-16 text-gray-400 dark:text-gray-500" />
              </div>
              <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">WhatsApp Not Connected</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Your WhatsApp account is not connected. Please go to the Integrations tab in Settings to reconnect WhatsApp.
              </p>
              <button
                onClick={() => navigate('/dashboard/settings?tab=integrations')}
                className="px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors font-medium"
              >
                Go to WhatsApp Connection
              </button>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex overflow-hidden">
          {/* Contacts list */}
          <ContactList
            contacts={contacts}
            selectedContact={selectedContact}
            onSelectContact={handleContactSelect}
            onRefreshContacts={loadContacts}
            isLoading={isLoading && contacts.length === 0}
            hasMoreContacts={hasMoreContacts}
            onLoadMoreContacts={loadMoreContacts}
            clientContacts={clientPhones}
            onAddToClients={handleAddToClients}
            allContacts={allContacts}
          />

          {/* Conversation area */}
          <div className="flex-1 flex flex-col bg-gray-50 dark:bg-gray-900">
            {selectedContact ? (
              <>
                {/* Contact header */}
                <div className="p-2 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 flex items-center sticky top-0 z-10">
                  {selectedContact.avatar ? (
                    <img
                      src={selectedContact.avatar}
                      alt={selectedContact.name}
                      className="h-8 w-8 rounded-full mr-2"
                    />
                  ) : (
                    <div className="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center mr-2">
                      <User className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                    </div>
                  )}
                  <div>
                    <h3 className="font-medium text-sm text-gray-900 dark:text-white">{selectedContact.name}</h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      +{selectedContact.phone}
                    </p>
                  </div>
                </div>

                {/* Messages */}
                <MessageList
                  messages={messages}
                  isLoading={isLoading && messages.length === 0}
                />

                {/* Message input */}
                <div className="sticky bottom-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                  <MessageInput
                    onSendMessage={handleSendMessage}
                    disabled={isSending}
                  />
                </div>
              </>
            ) : (
              <div className="flex-1 flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                <UserPlus className="h-16 w-16 mb-4" />
                <p className="text-xl font-medium mb-2 text-gray-700 dark:text-gray-300">No conversation selected</p>
                <p>Select a contact to start messaging</p>
              </div>
            )}
          </div>
        </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default MessagesPage;
