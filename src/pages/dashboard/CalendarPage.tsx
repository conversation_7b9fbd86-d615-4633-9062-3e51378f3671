import { useState, useCallback, useEffect } from 'react';
import { DashboardLayout } from '../../components/dashboard';
import { AnimatedModal, addToast } from '../../components/ui';
import { AppointmentForm } from '../../components/appointments/AppointmentForm';
import { appointmentService, AppointmentFormData } from '../../services/appointmentService';
import { googleCalendarService, GoogleCalendarEvent } from '../../services/googleCalendarService';
import {
  Appointment,
  CalendarHeader,
  DayView,
  WeekView,
  MonthView,
  AppointmentDetailsModal
} from '../../components/calendar';

export default function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewModeState] = useState<'day' | 'week' | 'month'>('week');
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [appointmentToEdit, setAppointmentToEdit] = useState<Appointment | null>(null);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [googleCalendarConnected, setGoogleCalendarConnected] = useState(false);
  const [isLoadingGoogleEvents, setIsLoadingGoogleEvents] = useState(false);
  const [isAddingAppointment, setIsAddingAppointment] = useState(false);
  const [isEditingAppointment, setIsEditingAppointment] = useState(false);
  const [isSubmittingAppointment, setIsSubmittingAppointment] = useState(false);



  // Functions to navigate between dates
  const goToPreviousDate = () => {
    if (viewMode === 'day') {
      const newDate = new Date(currentDate);
      newDate.setDate(newDate.getDate() - 1);
      setCurrentDate(newDate);
      setSelectedDate(newDate); // Keep selectedDate in sync
    } else if (viewMode === 'week') {
      const newDate = new Date(currentDate);
      newDate.setDate(newDate.getDate() - 7);
      setCurrentDate(newDate);
    } else {
      const newDate = new Date(currentDate);
      newDate.setMonth(newDate.getMonth() - 1);
      setCurrentDate(newDate);
    }
  };

  const goToNextDate = () => {
    if (viewMode === 'day') {
      const newDate = new Date(currentDate);
      newDate.setDate(newDate.getDate() + 1);
      setCurrentDate(newDate);
      setSelectedDate(newDate); // Keep selectedDate in sync
    } else if (viewMode === 'week') {
      const newDate = new Date(currentDate);
      newDate.setDate(newDate.getDate() + 7);
      setCurrentDate(newDate);
    } else {
      const newDate = new Date(currentDate);
      newDate.setMonth(newDate.getMonth() + 1);
      setCurrentDate(newDate);
    }
  };

  const goToToday = () => {
    const today = new Date();
    setCurrentDate(today);
    if (viewMode === 'day') {
      setSelectedDate(today); // Keep selectedDate in sync
    }
  };

  // Get the days for the week view
  const getWeekDays = useCallback(() => {
    const date = new Date(currentDate);
    const day = date.getDay(); // 0 = Sunday, 6 = Saturday

    // Calculate the start of the week (Sunday)
    date.setDate(date.getDate() - day);

    const days = [];
    for (let i = 0; i < 7; i++) {
      days.push(new Date(date));
      date.setDate(date.getDate() + 1);
    }

    return days;
  }, [currentDate]);

  // Filter appointments for the selected date or week
  const getFilteredAppointments = () => {
    // Filter appointments based on view mode

    if (viewMode === 'day') {
      // Create start and end of day for comparison
      const startOfDay = new Date(selectedDate);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(selectedDate);
      endOfDay.setHours(23, 59, 59, 999);

      // Filter appointments for the selected day
      const filteredAppointments = appointments.filter(appointment => {
        // Validate the appointment date first
        if (!(appointment.start instanceof Date) || isNaN(appointment.start.getTime())) {
          return false;
        }

        // Check if the appointment is on the selected date
        return appointment.start.getDate() === selectedDate.getDate() &&
               appointment.start.getMonth() === selectedDate.getMonth() &&
               appointment.start.getFullYear() === selectedDate.getFullYear();
      });

      // Return filtered appointments for day view
      return filteredAppointments;
    } else if (viewMode === 'week') {
      const weekDays = getWeekDays();
      const startOfWeek = new Date(weekDays[0]);
      startOfWeek.setHours(0, 0, 0, 0);

      const endOfWeek = new Date(weekDays[6]);
      endOfWeek.setHours(23, 59, 59, 999);

      // Filter appointments for the week
      const filteredAppointments = appointments.filter(appointment => {
        // Validate the appointment date first
        if (!(appointment.start instanceof Date) || isNaN(appointment.start.getTime())) {
          return false;
        }

        const appointmentTime = appointment.start.getTime();
        return appointmentTime >= startOfWeek.getTime() &&
               appointmentTime <= endOfWeek.getTime();
      });

      // Return filtered appointments for week view
      return filteredAppointments;
    } else {
      // Month view
      const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
      startOfMonth.setHours(0, 0, 0, 0);

      const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
      endOfMonth.setHours(23, 59, 59, 999);

      // Filter appointments for the month
      const filteredAppointments = appointments.filter(appointment => {
        // Validate the appointment date first
        if (!(appointment.start instanceof Date) || isNaN(appointment.start.getTime())) {
          return false;
        }

        const appointmentTime = appointment.start.getTime();
        return appointmentTime >= startOfMonth.getTime() &&
               appointmentTime <= endOfMonth.getTime();
      });

      // Return filtered appointments for month view
      return filteredAppointments;
    }
  };

  // Store fetched Google Calendar events to prevent unnecessary API calls
  const [fetchedGoogleEvents, setFetchedGoogleEvents] = useState<{
    events: GoogleCalendarEvent[];
    startDate: Date;
    endDate: Date;
    lastFetched: Date;
  } | null>(null);

  // Load Google Calendar events
  const loadGoogleCalendarEvents = useCallback(async () => {
    setIsLoadingGoogleEvents(true);

    try {
      // Calculate start and end dates based on view mode
      let startDate = new Date(currentDate);
      let endDate = new Date(currentDate);

      if (viewMode === 'day') {
        // For day view, use the selected date
        startDate = new Date(selectedDate);
        startDate.setHours(0, 0, 0, 0);

        endDate = new Date(selectedDate);
        endDate.setHours(23, 59, 59, 999);

        // Day view - use selected date
      } else if (viewMode === 'month') {
        startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
      } else if (viewMode === 'week') {
        const weekDays = getWeekDays();
        startDate = weekDays[0];
        endDate = weekDays[6];
      }

      // Check if we already have events for this date range
      // and they were fetched less than 5 minutes ago
      const now = new Date();
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

      let googleEvents;

      // Track if we're fetching new events (to show notification only when actually fetching)
      let fetchedNewEvents = false;

      if (fetchedGoogleEvents &&
          fetchedGoogleEvents.lastFetched > fiveMinutesAgo &&
          fetchedGoogleEvents.startDate <= startDate &&
          fetchedGoogleEvents.endDate >= endDate) {
        // Use already fetched events
        console.log('Using already fetched Google Calendar events');
        googleEvents = fetchedGoogleEvents.events;
      } else {
        // Fetch new events from Google Calendar API
        console.log('Fetching new Google Calendar events');
        googleEvents = await googleCalendarService.getEvents(startDate, endDate);
        fetchedNewEvents = true;

        // Store the fetched events
        setFetchedGoogleEvents({
          events: googleEvents,
          startDate,
          endDate,
          lastFetched: new Date()
        });
      }

      // Convert Google Calendar events to appointments
      const googleAppointments = googleEvents.map(event => {
        try {
          // Check if the event has dateTime properties
          if (!event.start?.dateTime || !event.end?.dateTime) {
            // Handle all-day events or events with date instead of dateTime
            if (event.start?.date && event.end?.date) {
              // All-day event
              const startDate = new Date(event.start.date);
              // End date is exclusive in Google Calendar, so subtract 1 day
              const endDate = new Date(event.end.date);
              endDate.setDate(endDate.getDate() - 1);
              endDate.setHours(23, 59, 59, 999);

              return {
                id: `google-${event.id}`,
                title: event.summary || 'Untitled Event',
                start: startDate,
                end: endDate,
                client: event.attendees?.length ? event.attendees[0].email : 'No attendees',
                service: event.description || 'All-day Event',
                status: event.status === 'confirmed' ? 'confirmed' : 'pending',
                source: 'google' as const,
                googleEventId: event.id,
                allDay: true
              };
            }
            return null;
          }

          // Regular event with dateTime
          const startDate = new Date(event.start.dateTime);
          const endDate = new Date(event.end.dateTime);

          // Check if the dates are valid
          if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            return null;
          }

          return {
            id: `google-${event.id}`,
            title: event.summary || 'Untitled Event',
            start: startDate,
            end: endDate,
            client: event.attendees?.length ? event.attendees[0].email : 'No attendees',
            service: event.description || 'Google Calendar Event',
            status: event.status === 'confirmed' ? 'confirmed' : 'pending',
            source: 'google' as const,
            googleEventId: event.id
          };
        } catch (err) {
          // Error parsing Google event, skip it
          console.error('Error parsing Google event:', err);
          return null;
        }
      }).filter(Boolean); // Filter out null values

      // Merge with existing appointments, avoiding duplicates
      setAppointments(prevAppointments => {
        // Remove existing Google Calendar appointments
        const appAppointments = prevAppointments.filter(app => app.source !== 'google');

        // Store the combined appointments
        return [...appAppointments, ...googleAppointments] as Appointment[];
      });

      // Only show notification when we actually fetch new events
      if (fetchedNewEvents) {
        addToast('Google Calendar events loaded successfully', 'success');
      }
    } catch (error) {
      console.error('Error loading Google Calendar events:', error);

      // Check if this is an authentication error
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('401') || errorMessage.includes('UNAUTHENTICATED') || errorMessage.includes('Invalid Credentials')) {
        addToast(
          'Your Google Calendar connection has expired. Please reconnect in Settings.',
          'error',
          10000 // Show for 10 seconds
        );

        // Set connected to false
        setGoogleCalendarConnected(false);
      }
      // Check if this is the specific API not enabled error
      else if (errorMessage.includes('Google Calendar API has not been used in project') ||
          errorMessage.includes('SERVICE_DISABLED')) {
        addToast(
          'Google Calendar API is not enabled. Please enable it in the Google Cloud Console.',
          'error',
          10000 // Show for 10 seconds
        );

        // Open the activation URL in a new tab
        const activationUrl = 'https://console.developers.google.com/apis/api/calendar-json.googleapis.com/overview?project=11367222904';
        window.open(activationUrl, '_blank');
      } else {
        addToast('Failed to load Google Calendar events', 'error');
      }
    } finally {
      setIsLoadingGoogleEvents(false);
    }
  }, [currentDate, selectedDate, viewMode, getWeekDays, fetchedGoogleEvents, setFetchedGoogleEvents]);

  // Function to load appointments from the API
  const loadAppointments = useCallback(async () => {
    // Always load appointments from the API to ensure we have the latest data
    // We'll preserve Google Calendar appointments in the setAppointments call

    try {
      // Calculate date range based on view mode
      let startDate, endDate;

      if (viewMode === 'day') {
        // For day view, use the selected date
        startDate = new Date(selectedDate);
        startDate.setHours(0, 0, 0, 0);

        endDate = new Date(selectedDate);
        endDate.setHours(23, 59, 59, 999);

        // Day view - use selected date
      } else if (viewMode === 'week') {
        // For week view, use the week range
        const weekDays = getWeekDays();
        startDate = weekDays[0];
        endDate = new Date(weekDays[6]);
        endDate.setHours(23, 59, 59, 999);
      } else {
        // For month view, use the month range
        startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0, 23, 59, 59, 999);
      }

      // Format dates for API
      let startDateStr, endDateStr;
      try {
        startDateStr = startDate.toISOString();
        endDateStr = endDate.toISOString();
      } catch (err) {
        console.error('Error formatting dates for API:', err);
        // Use fallback dates if there's an error
        const now = new Date();
        startDateStr = now.toISOString();
        endDateStr = new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString(); // 1 day later
      }

      // Call the API to get appointments
      const result = await appointmentService.getAppointments({
        start_date: startDateStr,
        end_date: endDateStr,
        page: 1,
        limit: 100 // Get more appointments at once
      });

      // If we have appointments, convert them to calendar appointments
      if (result.items && result.items.length > 0) {
        // Convert API appointments to calendar appointments
        const calendarAppointments = await Promise.all(
          result.items.map(async (appointment) => {
            return await appointmentService.toCalendarAppointment(appointment);
          })
        );

        // Appointments loaded successfully

        // Update appointments state, preserving Google Calendar appointments
        setAppointments(prev => {
          // Keep all Google Calendar appointments regardless of date range
          const googleAppointments = prev.filter(app => app.source === 'google');

          // Combine Google Calendar appointments with API appointments
          // Make sure all appointments conform to the Appointment interface
          return [...googleAppointments, ...calendarAppointments] as Appointment[];
        });
      } else {
        // No appointments found, just keep Google Calendar appointments
        setAppointments(prev => {
          // Keep all Google Calendar appointments regardless of date range
          return prev.filter(app => app.source === 'google') as Appointment[];
        });
      }
    } catch (error) {
      console.error('Error loading appointments:', error);
      // Don't show error toast for 404 errors, as the endpoint might not be implemented yet
      if ((error as { status?: number })?.status !== 404) {
        addToast('Failed to load appointments', 'error');
      }
    }
  }, [viewMode, selectedDate, currentDate, getWeekDays]);

  // Custom setViewMode function to keep dates in sync
  const setViewMode = useCallback((mode: 'day' | 'week' | 'month') => {
    // Change view mode

    // When switching to day view, ensure currentDate matches selectedDate
    if (mode === 'day') {
      setCurrentDate(new Date(selectedDate));

      // We'll skip counting appointments here to avoid dependencies

      // Reload Google Calendar events for the selected date
      if (googleCalendarConnected) {
        loadGoogleCalendarEvents();
      }
    }

    // Set the view mode state after processing
    setViewModeState(mode);
  }, [selectedDate, setCurrentDate, setViewModeState, loadGoogleCalendarEvents, googleCalendarConnected]);

  // Load appointments when view mode or dates change
  useEffect(() => {
    loadAppointments();
  }, [viewMode, selectedDate, currentDate, loadAppointments]);

  // Check Google Calendar connection status and load events
  useEffect(() => {
    const checkGoogleCalendarStatus = async () => {
      try {
        const status = await googleCalendarService.getStatus();

        // Validate the token if connected
        let isTokenValid = false;
        if (status.connected) {
          isTokenValid = await googleCalendarService.validateToken();
          console.log('Google Calendar token validation result:', isTokenValid);

          // If token is invalid but we're connected, try to refresh the token
          if (!isTokenValid) {
            try {
              console.log('Attempting to refresh Google Calendar token...');
              const refreshResult = await googleCalendarService.refreshToken();
              console.log('Token refresh result:', refreshResult);

              // Check if the token is now valid
              isTokenValid = await googleCalendarService.validateToken();
              console.log('Token validation after refresh:', isTokenValid);

              // If refresh was successful, show a toast
              if (isTokenValid) {
                addToast('Google Calendar connection refreshed successfully', 'success');
              }
            } catch (refreshError) {
              console.error('Error refreshing token:', refreshError);
              addToast('Failed to refresh Google Calendar connection', 'error');
            }
          }
        }

        // Only set as connected if both status is connected and token is valid
        setGoogleCalendarConnected(status.connected && isTokenValid);

        if (status.connected && isTokenValid) {
          try {
            await loadGoogleCalendarEvents();
          } catch (error) {
            console.error('Error loading Google Calendar events:', error);
            // Show a toast but don't let it break the app
            addToast('Failed to load Google Calendar events. You may need to reconnect.', 'error');
            // Set connected to false to prompt user to reconnect
            setGoogleCalendarConnected(false);
          }
        } else if (status.connected && !isTokenValid) {
          // If status is connected but token is invalid, show a message
          addToast('Your Google Calendar connection has expired. Please reconnect in Settings.', 'warning', 8000);
        }
      } catch (error) {
        console.error('Error checking Google Calendar status:', error);
      }
    };

    checkGoogleCalendarStatus();
  }, [loadGoogleCalendarEvents]);

  // Handle updating an appointment
  const handleUpdateAppointment = async (formData: AppointmentFormData) => {
    if (!selectedAppointment) return;

    setIsSubmittingAppointment(true);
    try {
      // Check if this is a mock appointment (ID starts with 'mock-')
      const isMockAppointment = selectedAppointment.id.startsWith('mock-');

      let updatedAppointment;
      if (!isMockAppointment) {
        // Call the API to update the appointment
        updatedAppointment = await appointmentService.updateAppointment(selectedAppointment.id, formData);
      } else {
        console.log('Updating mock appointment:', selectedAppointment.id);
        // Create a mock updated appointment that matches the API Appointment type
        updatedAppointment = {
          id: selectedAppointment.id,
          user_id: 'current-user',
          client_id: formData.client_id,
          service_id: formData.service_id,
          title: `${formData.client_id} - ${formData.service_id}`,
          description: formData.notes || '',
          start_time: formData.start_time,
          end_time: formData.end_time,
          status: (formData.status || 'CONFIRMED') as 'PENDING' | 'CONFIRMED' | 'CANCELLED',
          created_at: new Date().toISOString(),
          googleEventId: selectedAppointment.googleEventId,
          source: 'app' as 'app' | 'google'
        };
      }

      // Convert the API appointment to a calendar appointment
      const calendarAppointment = await appointmentService.toCalendarAppointment(updatedAppointment);

      // Preserve Google Calendar info if it exists
      if (selectedAppointment.googleEventId) {
        calendarAppointment.googleEventId = selectedAppointment.googleEventId;
        // Add googleLink if it exists
        if ('googleLink' in selectedAppointment) {
          (calendarAppointment as Record<string, unknown>).googleLink = selectedAppointment.googleLink;
        }
      }

      // Update the appointments state
      setAppointments(prev =>
        prev.map(app => app.id === selectedAppointment.id ? {
          ...calendarAppointment,
          // Ensure required properties are present
          client: selectedAppointment.client,
          service: selectedAppointment.service
        } : app) as Appointment[]
      );

      // Close the modals
      setIsEditingAppointment(false);
      setSelectedAppointment(null);

      // Show success message
      addToast('Appointment updated successfully', 'success');

      // If Google Calendar is connected and the appointment has a Google Calendar event ID, update it there too
      if (googleCalendarConnected && selectedAppointment.googleEventId) {
        try {
          console.log('Updating appointment in Google Calendar...');
          // Create a properly formatted appointment for Google Calendar
          const appointmentForGoogle = {
            client_id: formData.client_id,
            service_id: formData.service_id,
            start_time: formData.start_time,
            end_time: formData.end_time,
            notes: formData.notes,
            title: calendarAppointment.title
          };

          // Convert to Google Calendar event format
          const googleEvent = googleCalendarService.appointmentToGoogleEvent(appointmentForGoogle);

          // Update the event in Google Calendar
          await googleCalendarService.updateEvent(selectedAppointment.googleEventId, googleEvent);

          addToast('Appointment also updated in Google Calendar', 'success');
        } catch (googleError) {
          console.error('Error updating appointment in Google Calendar:', googleError);
          addToast('Appointment updated but failed to update in Google Calendar', 'warning', 5000);
        }
      }
    } catch (error) {
      console.error('Error updating appointment:', error);
      addToast('Failed to update appointment', 'error');
    } finally {
      setIsSubmittingAppointment(false);
    }
  };

  // Handle deleting an appointment
  const handleDeleteAppointment = async () => {
    if (!selectedAppointment) return;

    try {
      // Check if this is a mock appointment (ID starts with 'mock-')
      const isMockAppointment = selectedAppointment.id.startsWith('mock-');

      if (!isMockAppointment) {
        // Call the API to delete the appointment
        await appointmentService.deleteAppointment(selectedAppointment.id);
      } else {
        console.log('Deleting mock appointment:', selectedAppointment.id);
      }

      // Remove the appointment from the state
      setAppointments(prev => prev.filter(app => app.id !== selectedAppointment.id));

      // Close the modal
      setSelectedAppointment(null);

      // Show success message
      addToast('Appointment deleted successfully', 'success');

      // If Google Calendar is connected and the appointment has a Google Calendar event ID, delete it there too
      if (googleCalendarConnected && selectedAppointment.googleEventId) {
        try {
          console.log('Deleting appointment from Google Calendar...');
          await googleCalendarService.deleteEvent(selectedAppointment.googleEventId);

          addToast('Appointment also deleted from Google Calendar', 'success');
        } catch (googleError) {
          console.error('Error deleting appointment from Google Calendar:', googleError);
          addToast('Appointment deleted but failed to delete from Google Calendar', 'warning', 5000);
        }
      }
    } catch (error) {
      console.error('Error deleting appointment:', error);

      // Even if the API call fails, still remove the appointment from the state
      // This ensures the user can still delete appointments even if the backend is unavailable
      setAppointments(prev => prev.filter(app => app.id !== selectedAppointment.id));
      setSelectedAppointment(null);
      addToast('Appointment deleted locally', 'success');
    }
  };

  // Handle adding a new appointment
  const handleAddAppointment = async (formData: AppointmentFormData) => {
    setIsSubmittingAppointment(true);
    try {
      // Call the API to create the appointment
      const newAppointment = await appointmentService.createAppointment(formData);

      // Convert the API appointment to a calendar appointment
      const calendarAppointment = await appointmentService.toCalendarAppointment(newAppointment);

      // Add the new appointment to the state
      setAppointments(prev => [...prev, calendarAppointment as unknown as Appointment]);

      // Close the modal
      setIsAddingAppointment(false);

      // Show success message
      addToast('Appointment created successfully', 'success');

      // If it's a mock appointment, show a different message
      if (newAppointment.id.startsWith('mock-')) {
        addToast('Appointment created locally. It will be synced with Google Calendar.', 'info', 3000);
      }

      // If Google Calendar is connected, create the event there too
      if (googleCalendarConnected) {
        try {
          console.log('Creating appointment in Google Calendar...');
          // Create a properly formatted appointment for Google Calendar
          const appointmentForGoogle = {
            client_id: formData.client_id,
            service_id: formData.service_id,
            start_time: formData.start_time,
            end_time: formData.end_time,
            notes: formData.notes,
            title: calendarAppointment.title
          };

          const googleEvent = await googleCalendarService.createAppointmentInGoogleCalendar(appointmentForGoogle);

          // Update the appointment with the Google Calendar event ID
          const updatedCalendarAppointment = {
            ...calendarAppointment,
            googleEventId: googleEvent.id,
            googleLink: googleEvent.htmlLink
          };

          // Update the appointments state
          setAppointments(prev =>
            prev.map(app => app.id === calendarAppointment.id ? {
              ...updatedCalendarAppointment,
              // Ensure required properties are present
              client: (calendarAppointment as Record<string, unknown>).client as string || 'Unknown client',
              service: (calendarAppointment as Record<string, unknown>).service as string || 'Unknown service'
            } : app) as Appointment[]
          );

          addToast('Appointment also added to Google Calendar', 'success');
        } catch (googleError) {
          console.error('Error creating appointment in Google Calendar:', googleError);
          addToast('Appointment created but failed to add to Google Calendar', 'warning', 5000);
        }
      }
    } catch (error) {
      console.error('Error creating appointment:', error);
      addToast('Failed to create appointment', 'error');
    } finally {
      setIsSubmittingAppointment(false);
    }
  };

  // Handle opening the add appointment modal
  const handleOpenAddAppointmentModal = (date?: Date | string) => {
    // Use provided date or current date, ensuring it's a valid Date object
    let baseDate: Date;

    if (date instanceof Date && !isNaN(date.getTime())) {
      // Valid Date object
      baseDate = date;
    } else if (typeof date === 'string') {
      // Try to parse string to Date
      const parsedDate = new Date(date);
      if (!isNaN(parsedDate.getTime())) {
        baseDate = parsedDate;
      } else {
        // Fallback to current date if parsing fails
        console.warn('Invalid date string provided:', date);
        baseDate = new Date();
      }
    } else {
      // Use current date as fallback
      baseDate = new Date();
    }

    // Check if the selected date has any all-day events
    const hasAllDayEvent = appointments.some(appointment => {
      // First check if appointment.allDay is true
      if (appointment.allDay !== true) return false;

      // Validate that appointment.start is a valid Date object
      if (!appointment.start || !(appointment.start instanceof Date) || isNaN(appointment.start.getTime())) {
        console.warn('Invalid appointment date:', appointment);
        return false;
      }

      // Check if the appointment is on the same day as the selected date
      return appointment.start.getDate() === baseDate.getDate() &&
             appointment.start.getMonth() === baseDate.getMonth() &&
             appointment.start.getFullYear() === baseDate.getFullYear();
    });

    if (hasAllDayEvent) {
      // If there's an all-day event, show a message and don't allow adding appointments
      addToast('This day has an all-day event. New appointments cannot be added.', 'error');
      return;
    }

    // Set the selected date with time rounded to nearest 15 minutes
    const appointmentDate = new Date(baseDate);
    const minutes = appointmentDate.getMinutes();
    const roundedMinutes = Math.ceil(minutes / 15) * 15;
    appointmentDate.setMinutes(roundedMinutes);
    appointmentDate.setSeconds(0);
    appointmentDate.setMilliseconds(0);

    setSelectedDate(appointmentDate);
    setIsAddingAppointment(true);
  };

  return (
    <DashboardLayout>
      <div className="space-y-4">
        <CalendarHeader
          currentDate={currentDate}
          selectedDate={selectedDate}
          viewMode={viewMode}
          googleCalendarConnected={googleCalendarConnected}
          isLoadingGoogleEvents={isLoadingGoogleEvents}
          getWeekDays={getWeekDays}
          goToPreviousDate={goToPreviousDate}
          goToNextDate={goToNextDate}
          goToToday={goToToday}
          setViewMode={setViewMode}
          loadGoogleCalendarEvents={loadGoogleCalendarEvents}
          handleAddAppointment={handleOpenAddAppointmentModal}
        />

        <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm">
          {viewMode === 'day' && (
            <DayView
              key={selectedDate instanceof Date && !isNaN(selectedDate.getTime())
                ? selectedDate.toISOString()
                : 'invalid-date'} // Add key to force re-render when date changes
              selectedDate={selectedDate}
              appointments={getFilteredAppointments()}
              setSelectedAppointment={setSelectedAppointment}
            />
          )}

          {viewMode === 'week' && (
            <WeekView
              weekDays={getWeekDays()}
              appointments={getFilteredAppointments()}
              setSelectedAppointment={setSelectedAppointment}
              setSelectedDate={setSelectedDate}
              setCurrentDate={setCurrentDate}
              setViewMode={setViewModeState}
            />
          )}

          {viewMode === 'month' && (
            <MonthView
              currentDate={currentDate}
              appointments={getFilteredAppointments()}
              setSelectedDate={setSelectedDate}
              setCurrentDate={setCurrentDate}
              setViewMode={setViewMode}
              onAddAppointment={handleOpenAddAppointmentModal}
            />
          )}
        </div>
      </div>

      {/* Appointment Details Modal */}
      {selectedAppointment && (
        <AppointmentDetailsModal
          appointment={selectedAppointment}
          onClose={() => setSelectedAppointment(null)}
          onEdit={() => {
            setAppointmentToEdit(selectedAppointment);
            setSelectedAppointment(null);
            setIsEditingAppointment(true);
          }}
          onDelete={() => {
            if (window.confirm('Are you sure you want to delete this appointment?')) {
              handleDeleteAppointment();
            }
          }}
        />
      )}

      {/* New Appointment Modal */}
      <AnimatedModal
        isOpen={isAddingAppointment}
        onClose={() => setIsAddingAppointment(false)}
        width="max-w-lg"
      >
        <AppointmentForm
          onSubmit={handleAddAppointment}
          onCancel={() => setIsAddingAppointment(false)}
          title="New Appointment"
          submitLabel="Create Appointment"
          isSubmitting={isSubmittingAppointment}
          initialData={{
            start_time: selectedDate instanceof Date && !isNaN(selectedDate.getTime())
              ? selectedDate.toISOString().slice(0, 16)
              : new Date().toISOString().slice(0, 16),
            end_time: selectedDate instanceof Date && !isNaN(selectedDate.getTime())
              ? new Date(selectedDate.getTime() + 30 * 60 * 1000).toISOString().slice(0, 16)
              : new Date(Date.now() + 30 * 60 * 1000).toISOString().slice(0, 16)
          }}
        />
      </AnimatedModal>

      {/* Edit Appointment Modal */}
      <AnimatedModal
        isOpen={isEditingAppointment && !!appointmentToEdit}
        onClose={() => {
          setIsEditingAppointment(false);
          setAppointmentToEdit(null);
        }}
        width="max-w-lg"
      >
        {appointmentToEdit && (
          <AppointmentForm
            onSubmit={handleUpdateAppointment}
            onCancel={() => setIsEditingAppointment(false)}
            title="Edit Appointment"
            submitLabel="Update Appointment"
            isSubmitting={isSubmittingAppointment}
            initialData={{
              client_id: appointmentToEdit.client && typeof appointmentToEdit.client === 'string' ? appointmentToEdit.client : '',
              service_id: appointmentToEdit.service && typeof appointmentToEdit.service === 'string' ? appointmentToEdit.service : '',
              start_time: appointmentToEdit.start instanceof Date && !isNaN(appointmentToEdit.start.getTime())
                ? appointmentToEdit.start.toISOString().slice(0, 16)
                : new Date().toISOString().slice(0, 16),
              end_time: appointmentToEdit.end instanceof Date && !isNaN(appointmentToEdit.end.getTime())
                ? appointmentToEdit.end.toISOString().slice(0, 16)
                : new Date(Date.now() + 30 * 60 * 1000).toISOString().slice(0, 16),
              notes: ''
            }}
          />
        )}
      </AnimatedModal>
    </DashboardLayout>
  );
}
