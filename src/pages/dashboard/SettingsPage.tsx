import { useState, useEffect } from 'react';
import { DashboardLayout } from '../../components/dashboard';
import { useAuthStore } from '../../lib/auth';
import { UserProfile } from '../../services/userService';
import { useLocation } from 'react-router-dom';
import {
  SettingsSidebar,
  BusinessProfileTab,
  IntegrationsTab,
  CalendarSettingsTab,
  ReminderSettingsTab,
  FeedbackSettingsTab,
  NotificationsTab,
  SecurityTab,
  BillingTab,
  ServicesTab
} from '../../components/settings';
import type { SettingsTab } from '../../components/settings/SettingsSidebar';

export default function SettingsPage() {
  const { user } = useAuthStore();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState<SettingsTab>('profile');

  // Set active tab based on URL parameter
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tabParam = params.get('tab');

    if (tabParam && ['profile', 'services', 'integrations', 'calendar', 'reminders', 'feedback', 'notifications', 'security', 'billing'].includes(tabParam)) {
      setActiveTab(tabParam as SettingsTab);
    }
  }, [location]);

  // Initialize profile form with default values
  const initialProfile: UserProfile = {
    businessName: user?.businessName || '',
    email: user?.email || user?.sub || '',
    phone: '',
    address: '',
    website: '',
    currency: '',
    logo: null
  };

  return (
    <DashboardLayout>
      <div className="space-y-4">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
          <div className="sm:flex">
            {/* Sidebar */}
            <SettingsSidebar activeTab={activeTab} setActiveTab={setActiveTab} />

            {/* Content */}
            <div className="flex-1 p-6 dark:text-gray-100">
              {/* Business Profile */}
              {activeTab === 'profile' && <BusinessProfileTab initialProfile={initialProfile} />}

              {/* Services */}
              {activeTab === 'services' && <ServicesTab />}

              {/* Integrations */}
              {activeTab === 'integrations' && <IntegrationsTab />}

              {/* WhatsApp tab has been integrated into Integrations */}

              {/* Calendar Settings */}
              {activeTab === 'calendar' && <CalendarSettingsTab />}

              {/* Appointment Reminders */}
              {activeTab === 'reminders' && <ReminderSettingsTab />}

              {/* Client Feedback */}
              {activeTab === 'feedback' && <FeedbackSettingsTab />}

              {/* Notifications */}
              {activeTab === 'notifications' && <NotificationsTab />}

              {/* Security */}
              {activeTab === 'security' && <SecurityTab />}

              {/* Billing & Plans */}
              {activeTab === 'billing' && <BillingTab />}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
