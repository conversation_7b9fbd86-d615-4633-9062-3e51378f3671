import { useState, useRef, useEffect } from 'react';
import { DashboardLayout } from '../../components/dashboard';
import {
  Brain,
  Upload,
  X,
  File,
  HelpCircle,
  Database,
  MessageSquare,
  RefreshCw,
  Wand2,
  // Settings is imported but not used
  // Settings,
  Loader
} from 'lucide-react';
import { aiConfig } from '../../config/aiConfig';
import { aiConfigurationService, AIConfiguration, KnowledgeDocument } from '../../services/aiConfigurationService';
import { addToast, Button } from '../../components/ui';

type ConfigTab = 'instructions' | 'knowledge' | 'whatsapp';

export default function AIConfigPage() {
  const [activeTab, setActiveTab] = useState<ConfigTab>('instructions');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Loading state
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // AI Configuration states
  // This state is set but not directly used in the component
  // Keeping it for future implementation
  const [, setAiConfiguration] = useState<AIConfiguration | null>(null);
  const [systemPrompt, setSystemPrompt] = useState(
    "Your AI assistant excels in managing service appointments. It's professional and concise, aimed at providing the best scheduling experience.\n\nAlways confirm appointment details and send reminders. Respect business hours (9 AM - 6 PM) and don't schedule appointments outside these hours. For support inquiries, always be helpful and friendly."
  );
  const [tone, setTone] = useState('professional');
  const [primaryLanguage, setPrimaryLanguage] = useState('en');
  const [supportedLanguages, setSupportedLanguages] = useState<string[]>(['en']);
  const [isGeneratingPrompt, setIsGeneratingPrompt] = useState(false);

  // Knowledge base states
  const [knowledgeDocuments, setKnowledgeDocuments] = useState<KnowledgeDocument[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [isUploadingDocument, setIsUploadingDocument] = useState(false);

  // WhatsApp AI settings
  const [enableAutoResponses, setEnableAutoResponses] = useState(aiConfig.enableAutoResponses);
  const [responseDelay, setResponseDelay] = useState(aiConfig.responseDelay);
  const [maxResponseLength, setMaxResponseLength] = useState(aiConfig.maxResponseLength);
  const [enableDetailedLogging, setEnableDetailedLogging] = useState(aiConfig.enableDetailedLogging);

  // Handle file upload for AI knowledge
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setUploadedFiles(prev => [...prev, ...newFiles]);
    }
  };

  // Remove an uploaded file
  const handleRemoveFile = (fileName: string) => {
    setUploadedFiles(prev => prev.filter(file => file.name !== fileName));
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  // Load AI configuration from the server
  useEffect(() => {
    const loadAIConfiguration = async () => {
      setIsLoading(true);
      try {
        const config = await aiConfigurationService.getConfiguration();
        setAiConfiguration(config);
        setSystemPrompt(config.system_prompt);
        setTone(config.tone);
        setPrimaryLanguage(config.primary_language);
        setSupportedLanguages(config.supported_languages);

        // Load WhatsApp settings if available
        if (config.whatsapp_settings) {
          setEnableAutoResponses(config.whatsapp_settings.enableAutoResponses);
          setResponseDelay(config.whatsapp_settings.responseDelay);
          setMaxResponseLength(config.whatsapp_settings.maxResponseLength);
          setEnableDetailedLogging(config.whatsapp_settings.enableDetailedLogging);

          // Update the local aiConfig object
          aiConfig.enableAutoResponses = config.whatsapp_settings.enableAutoResponses;
          aiConfig.responseDelay = config.whatsapp_settings.responseDelay;
          aiConfig.maxResponseLength = config.whatsapp_settings.maxResponseLength;
          aiConfig.enableDetailedLogging = config.whatsapp_settings.enableDetailedLogging;
        }
      } catch (error) {
        console.error('Error loading AI configuration:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadAIConfiguration();
  }, []);

  // Load knowledge documents from the server
  useEffect(() => {
    const loadKnowledgeDocuments = async () => {
      try {
        const documents = await aiConfigurationService.getKnowledgeDocuments();
        setKnowledgeDocuments(documents);
      } catch (error) {
        console.error('Error loading knowledge documents:', error);
      }
    };

    if (activeTab === 'knowledge') {
      loadKnowledgeDocuments();
    }
  }, [activeTab]);

  // Update the WhatsApp config when settings change
  useEffect(() => {
    // Update the local aiConfig object for immediate use
    aiConfig.enableAutoResponses = enableAutoResponses;
    aiConfig.responseDelay = responseDelay;
    aiConfig.maxResponseLength = maxResponseLength;
    aiConfig.enableDetailedLogging = enableDetailedLogging;

    // We'll save these settings to the backend when the user clicks "Save Changes"
  }, [enableAutoResponses, responseDelay, maxResponseLength, enableDetailedLogging]);

  // Generate system prompt based on user settings
  const generateSystemPrompt = async () => {
    setIsGeneratingPrompt(true);
    try {
      const result = await aiConfigurationService.generateSystemPrompt();
      setSystemPrompt(result.system_prompt);
      addToast('System prompt generated successfully', 'success');
    } catch (error) {
      console.error('Error generating system prompt:', error);
      addToast('Error generating system prompt. Please try again.', 'error');
    } finally {
      setIsGeneratingPrompt(false);
    }
  };

  // Save AI configuration to the server
  const saveAIConfiguration = async () => {
    setIsSaving(true);
    try {
      const updatedConfig = await aiConfigurationService.updateConfiguration({
        system_prompt: systemPrompt,
        tone,
        primary_language: primaryLanguage,
        supported_languages: supportedLanguages,
        whatsapp_settings: {
          enableAutoResponses,
          responseDelay,
          maxResponseLength,
          enableDetailedLogging
        }
      });
      setAiConfiguration(updatedConfig);
      addToast('AI configuration saved successfully!', 'success');
    } catch (error) {
      console.error('Error saving AI configuration:', error);
      addToast('Error saving AI configuration. Please try again.', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  // Upload a knowledge document
  const uploadKnowledgeDocument = async (file: File) => {
    setIsUploadingDocument(true);
    try {
      // Upload the file directly to the server
      const document = await aiConfigurationService.uploadKnowledgeDocument(file);

      // Add the new document to the list
      setKnowledgeDocuments(prev => [...prev, document]);

      // Remove the file from the uploaded files list
      setUploadedFiles(prev => prev.filter(f => f.name !== file.name));

      // Show success message
      addToast('Document uploaded successfully', 'success');
    } catch (error) {
      console.error('Error uploading knowledge document:', error);
      addToast('Error uploading document. Please try again.', 'error');
    } finally {
      setIsUploadingDocument(false);
    }
  };

  // Delete a knowledge document
  const deleteKnowledgeDocument = async (id: string) => {
    try {
      await aiConfigurationService.deleteKnowledgeDocument(id);
      setKnowledgeDocuments(prev => prev.filter(doc => doc.id !== id));
      addToast('Document deleted successfully', 'success');
    } catch (error) {
      console.error('Error deleting knowledge document:', error);
      addToast('Error deleting document. Please try again.', 'error');
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">AI Configuration</h1>
            <p className="text-gray-600 dark:text-gray-300">Customize your AI assistant's behavior and capabilities</p>
          </div>

          <button
            className={`btn-primary dark:bg-blue-700 dark:hover:bg-blue-800 ${isSaving ? 'opacity-70 cursor-not-allowed' : ''}`}
            onClick={saveAIConfiguration}
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <Loader className="h-4 w-4 animate-spin mr-2" />
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </button>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm overflow-hidden">
          {/* Tabs */}
          <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
            <div className="flex overflow-x-auto">
              {[
                { id: 'instructions', label: 'Instructions', icon: Brain },
                { id: 'knowledge', label: 'Knowledge', icon: Database },
                { id: 'whatsapp', label: 'WhatsApp AI', icon: MessageSquare }
              ].map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as ConfigTab)}
                  className={`flex items-center gap-2 px-4 py-3 font-medium border-b-2 whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'border-blue-600 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                >
                  <tab.icon className="h-5 w-5" />
                  {tab.label}
                </button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          <div className="px-1">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <Loader className="h-8 w-8 animate-spin text-blue-500" />
                <span className="ml-3 text-lg text-gray-600 dark:text-gray-400">Loading configuration...</span>
              </div>
            ) : activeTab === 'instructions' && (
              <div className="space-y-6">
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <label className="block text-lg font-medium text-gray-900 dark:text-white">
                      System Prompt
                    </label>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={generateSystemPrompt}
                        disabled={isGeneratingPrompt}
                        className="flex items-center gap-1 text-sm px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                      >
                        {isGeneratingPrompt ? (
                          <>
                            <RefreshCw className="h-4 w-4 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <Wand2 className="h-4 w-4" />
                            Auto-Generate
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-2">
                    Provide detailed instructions about how your AI should behave, what tone to use, and any specific rules it should follow.
                  </p>
                  <p className="text-gray-600 dark:text-gray-300 mb-4 text-sm italic">
                    Click "Auto-Generate" to create a prompt based on your business profile, services, and calendar settings.
                  </p>
                  <div className="relative">
                    <textarea
                      value={systemPrompt}
                      onChange={(e) => setSystemPrompt(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600"
                      rows={10}
                      placeholder="Provide detailed instructions on how the AI should behave and respond..."
                    ></textarea>
                    <div className="absolute bottom-3 right-3 text-sm text-gray-500 dark:text-gray-400">
                      {systemPrompt.length} characters
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-md font-medium text-gray-900 dark:text-white mb-2">
                      Tone
                    </label>
                    <select
                      value={tone}
                      onChange={(e) => setTone(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600"
                    >
                      <option value="professional">Professional</option>
                      <option value="friendly">Friendly</option>
                      <option value="casual">Casual</option>
                      <option value="formal">Formal</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-md font-medium text-gray-900 dark:text-white mb-2">
                      Primary Language
                    </label>
                    <select
                      value={primaryLanguage}
                      onChange={(e) => setPrimaryLanguage(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600"
                    >
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="ca">Catalan</option>
                      <option value="fr">French</option>
                      <option value="de">German</option>
                    </select>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-4 rounded-lg">
                  <div className="flex gap-3">
                    <HelpCircle className="h-5 w-5 text-blue-500 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300">Tips for effective instructions</h4>
                      <ul className="text-sm text-blue-700 dark:text-blue-400 mt-2 space-y-2 list-disc pl-4">
                        <li>Be specific about what the AI should and should not do</li>
                        <li>Define the tone (professional, friendly, casual, etc.)</li>
                        <li>Include any domain-specific rules for your business</li>
                        <li>Specify how to handle common questions or concerns</li>
                        <li>Include information about your services, pricing, and availability rules</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'knowledge' && (
              <div className="space-y-6">
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">Knowledge Base</h3>
                    {uploadedFiles.length > 0 && (
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => uploadedFiles.length > 0 && uploadKnowledgeDocument(uploadedFiles[0])}
                        disabled={isUploadingDocument || uploadedFiles.length === 0}
                        icon={isUploadingDocument ? <Loader className="h-3 w-3 animate-spin" /> : <Upload className="h-3 w-3" />}
                      >
                        {isUploadingDocument ? 'Uploading...' : 'Upload Selected File'}
                      </Button>
                    )}
                  </div>
                  <p className="text-gray-600 mb-4">
                    Upload documents to provide additional context to your AI. These files will be used to answer client queries more accurately.
                  </p>

                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileUpload}
                    className="hidden"
                    accept=".txt,.pdf,.doc,.docx"
                  />

                  <div
                    onClick={triggerFileInput}
                    className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors mb-6 group"
                  >
                    <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-full inline-flex group-hover:bg-blue-100 dark:group-hover:bg-blue-900/50 transition-colors">
                      <Upload className="h-8 w-8 text-blue-500 dark:text-blue-400 mx-auto mb-0 group-hover:text-blue-600 dark:group-hover:text-blue-300 transition-colors" />
                    </div>
                    <p className="text-gray-700 dark:text-gray-300 font-medium mt-3">Click to upload files or drag and drop</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">PDF, DOC, DOCX, TXT (max 10MB each)</p>
                    <p className="text-xs text-blue-600 dark:text-blue-400 mt-3">These documents will be used to train your AI assistant</p>
                  </div>

                  {/* Uploaded files waiting to be processed */}
                  {uploadedFiles.length > 0 && (
                    <div className="space-y-3 mb-6">
                      <h4 className="font-medium text-gray-700 dark:text-gray-300">Files to Upload</h4>
                      {uploadedFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <div className="flex items-center gap-3">
                            <File className="h-5 w-5 text-blue-500 dark:text-blue-400" />
                            <div>
                              <p className="text-sm font-medium text-gray-800 dark:text-white">{file.name}</p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">{(file.size / 1024).toFixed(1)} KB</p>
                            </div>
                          </div>
                          <button
                            onClick={() => handleRemoveFile(file.name)}
                            className="text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400"
                          >
                            <X className="h-5 w-5" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Knowledge documents already in the database */}
                  {knowledgeDocuments.length > 0 && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-700 dark:text-gray-300">Knowledge Documents</h4>
                        <span className="text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 py-1 px-2 rounded-full">
                          {knowledgeDocuments.length} {knowledgeDocuments.length === 1 ? 'document' : 'documents'}
                        </span>
                      </div>
                      <div className="grid gap-3 md:grid-cols-2">
                        {knowledgeDocuments.map((doc) => (
                          <div key={doc.id} className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                            <div className="flex items-center gap-3 overflow-hidden">
                              <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full flex-shrink-0">
                                <Database className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                              </div>
                              <div className="overflow-hidden">
                                <p className="text-sm font-medium text-gray-800 dark:text-white truncate" title={doc.title}>{doc.title}</p>
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  Added on {new Date(doc.created_at).toLocaleDateString()}
                                </p>
                              </div>
                            </div>
                            <button
                              onClick={() => deleteKnowledgeDocument(doc.id)}
                              className="text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20 p-1.5 rounded-full transition-colors"
                              title="Delete document"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 p-4 rounded-lg">
                  <div className="flex gap-3">
                    <HelpCircle className="h-5 w-5 text-green-500 dark:text-green-400 flex-shrink-0 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-green-800 dark:text-green-300">Recommended documents to upload</h4>
                      <ul className="text-sm text-green-700 dark:text-green-400 mt-2 space-y-2 list-disc pl-4">
                        <li>Service descriptions and price lists</li>
                        <li>Frequently asked questions (FAQs)</li>
                        <li>Booking policies and cancellation rules</li>
                        <li>Staff information and specialties</li>
                        <li>Business hours and location details</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'whatsapp' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">WhatsApp AI Auto-Responses</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Configure how the AI responds to incoming WhatsApp messages. These auto-responses work 24/7, even when you're logged out of the application.
                  </p>

                  <div className="space-y-6">
                    {/* Enable Auto-Responses */}
                    <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div>
                        <h4 className="font-medium text-gray-800 dark:text-white">Enable Auto-Responses</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          Automatically respond to incoming WhatsApp messages
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={enableAutoResponses}
                          onChange={() => setEnableAutoResponses(!enableAutoResponses)}
                        />
                        <div className="w-11 h-6 bg-gray-200 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 dark:after:border-gray-600 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 dark:peer-checked:bg-blue-700"></div>
                      </label>
                    </div>

                    {/* Response Delay */}
                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="mb-3">
                        <h4 className="font-medium text-gray-800 dark:text-white">Response Delay</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          Delay before sending an AI response (in seconds)
                        </p>
                      </div>
                      <div className="flex items-center gap-4">
                        <input
                          type="range"
                          min="0"
                          max="10"
                          step="0.5"
                          value={responseDelay / 1000}
                          onChange={(e) => setResponseDelay(parseFloat(e.target.value) * 1000)}
                          disabled={!enableAutoResponses}
                          className={`w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer ${!enableAutoResponses ? 'opacity-50' : ''}`}
                        />
                        <span className="w-12 text-center font-medium">{(responseDelay / 1000).toFixed(1)}s</span>
                      </div>
                    </div>

                    {/* Max Response Length */}
                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="mb-3">
                        <h4 className="font-medium text-gray-800 dark:text-gray-200">Maximum Response Length</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          Maximum number of characters in AI responses
                        </p>
                      </div>
                      <div className="flex items-center gap-4">
                        <input
                          type="range"
                          min="100"
                          max="1000"
                          step="50"
                          value={maxResponseLength}
                          onChange={(e) => setMaxResponseLength(parseInt(e.target.value))}
                          disabled={!enableAutoResponses}
                          className={`w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer ${!enableAutoResponses ? 'opacity-50' : ''}`}
                        />
                        <span className="w-12 text-center font-medium dark:text-gray-200">{maxResponseLength}</span>
                      </div>
                    </div>

                    {/* Detailed Logging */}
                    <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div>
                        <h4 className="font-medium text-gray-800 dark:text-gray-200">Detailed Logging</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          Log detailed information about AI responses
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={enableDetailedLogging}
                          onChange={() => setEnableDetailedLogging(!enableDetailedLogging)}
                          disabled={!enableAutoResponses}
                        />
                        <div className={`w-11 h-6 bg-gray-200 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 dark:after:border-gray-600 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 dark:peer-checked:bg-blue-500 ${!enableAutoResponses ? 'opacity-50' : ''}`}></div>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 p-4 rounded-lg">
                  <div className="flex gap-3">
                    <HelpCircle className="h-5 w-5 text-blue-500 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300">About WhatsApp AI Auto-Responses</h4>
                      <p className="text-sm text-blue-700 dark:text-blue-400 mt-2">
                        When enabled, your AI assistant will automatically respond to incoming WhatsApp messages
                        <strong> even when you're logged out of the application</strong>. This means your business
                        can provide 24/7 customer service without requiring you to be actively using the app.
                      </p>
                      <p className="text-sm text-blue-700 dark:text-blue-400 mt-2">
                        The AI will identify greetings, questions, and appointment-related queries to provide helpful
                        responses based on your business information and knowledge base.
                      </p>
                      <p className="text-sm text-blue-700 dark:text-blue-400 mt-2">
                        In the future, this feature will be enhanced with more advanced AI capabilities to provide
                        even more personalized and context-aware responses.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
