import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, User } from 'lucide-react';
import { DashboardLayout } from '../../components/dashboard';
import { ChatInterface } from '../../components/chat/ChatInterface';
import { clientService } from '../../services/clientService';
import { addToast } from '../../components/ui';

export default function ClientChatPage() {
  const { clientId } = useParams<{ clientId: string }>();
  const navigate = useNavigate();
  const [client, setClient] = useState<{ id: string; name: string; email?: string; phone?: string } | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadClient = async () => {
      if (!clientId) {
        navigate('/dashboard/clients');
        return;
      }

      setIsLoading(true);
      try {
        const clientData = await clientService.getClient(clientId);
        setClient(clientData);
      } catch (error) {
        console.error('Error loading client:', error);
        addToast('Failed to load client information', 'error');
        navigate('/dashboard/clients');
      } finally {
        setIsLoading(false);
      }
    };

    loadClient();
  }, [clientId, navigate]);

  return (
    <DashboardLayout>
      <div className="h-full flex flex-col">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <button
              onClick={() => navigate('/dashboard/clients')}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
            <h1 className="text-2xl font-bold text-gray-900">Client Chat</h1>
          </div>
        </div>

        {isLoading ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : client ? (
          <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-4 h-[calc(100vh-12rem)]">
            {/* Client info sidebar */}
            <div className="hidden md:block bg-white p-4 rounded-lg shadow-sm">
              <div className="flex flex-col items-center mb-6">
                <div className="h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center mb-3">
                  <User className="h-10 w-10 text-gray-500" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">{client.name}</h2>
              </div>

              <div className="space-y-4">
                {client.email && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Email</h3>
                    <p className="text-gray-900">{client.email}</p>
                  </div>
                )}

                {client.phone && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Phone</h3>
                    <p className="text-gray-900">{client.phone}</p>
                  </div>
                )}

                <div className="pt-4 border-t">
                  <button
                    onClick={() => navigate(`/dashboard/clients/${clientId}`)}
                    className="w-full py-2 px-4 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition-colors"
                  >
                    View Client Profile
                  </button>
                </div>
              </div>
            </div>

            {/* Chat interface */}
            <div className="md:col-span-3 h-full">
              <ChatInterface clientId={clientId || ''} clientName={client.name} />
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <p className="text-gray-500">Client not found</p>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
