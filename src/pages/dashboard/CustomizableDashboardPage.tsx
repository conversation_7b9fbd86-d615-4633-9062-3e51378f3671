import React, { useState, useEffect } from 'react';
import { Calendar<PERSON>heck2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Users, Loader, Edit, Plus, Save, X } from 'lucide-react';
import {
  DashboardLayout,
  DashboardWidget,
  WidgetConfigModal,
  DashboardConfigModal,
  AddWidgetModal
} from '../../components/dashboard';
import { dashboardService } from '../../services/dashboardService';
import { dashboardConfigService } from '../../services/dashboardConfigService';
import { DashboardConfig, DashboardWidgetConfig } from '../../types/dashboard';
// UUID is imported but not used directly in this component
// import { v4 as uuidv4 } from 'uuid';

export default function CustomizableDashboardPage() {
  // Loading state
  const [isLoading, setIsLoading] = useState(true);

  // Dashboard data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [dashboardData, setDashboardData] = useState<Record<string, any>>({});

  // Dashboard configuration
  const [dashboardConfigs, setDashboardConfigs] = useState<DashboardConfig[]>([]);
  const [activeConfig, setActiveConfig] = useState<DashboardConfig | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // Modal states
  const [configModalOpen, setConfigModalOpen] = useState(false);
  const [widgetConfigModalOpen, setWidgetConfigModalOpen] = useState(false);
  const [addWidgetModalOpen, setAddWidgetModalOpen] = useState(false);
  const [selectedWidget, setSelectedWidget] = useState<DashboardWidgetConfig | null>(null);

  // Load dashboard configurations
  useEffect(() => {
    const configs = dashboardConfigService.getConfigurations();
    const activeConfig = dashboardConfigService.getActiveConfiguration();

    setDashboardConfigs(configs);
    setActiveConfig(activeConfig);
  }, []);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      setIsLoading(true);
      try {
        // Fetch stats
        const statsData = await dashboardService.getStats();

        // Fetch chart data
        const statusData = await dashboardService.getAppointmentsByStatus();
        const monthlyData = await dashboardService.getAppointmentsByMonth();
        const clientsData = await dashboardService.getClientsByMonth();
        const messagesData = await dashboardService.getMessagesByDay();

        // Fetch list data
        const appointments = await dashboardService.getUpcomingAppointments();
        const messages = await dashboardService.getRecentMessages();

        // Format appointments for the list component
        const formattedAppointments = appointments.map(appointment => {
          const startDate = new Date(appointment.start_time);
          return {
            id: appointment.id,
            clientName: appointment.client_name,
            service: appointment.service_name,
            date: startDate.toLocaleDateString(),
            time: startDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            status: appointment.status as 'confirmed' | 'pending' | 'cancelled'
          };
        });

        // Set dashboard data
        setDashboardData({
          stats: statsData,
          appointmentsByStatus: statusData,
          appointmentsByMonth: monthlyData,
          clientsByMonth: clientsData,
          messagesByDay: messagesData,
          upcomingAppointments: appointments,
          recentMessages: messages,
          formattedAppointments,
          // Icons for stat widgets
          statIcons: {
            totalAppointments: CalendarCheck2,
            activeClients: Users,
            messagesHandled: CheckCheck,
            timeSaved: Clock
          },
          // Trend values for stat widgets
          statTrends: {
            appointmentTrend: `${statsData.appointmentTrend >= 0 ? '+' : ''}${statsData.appointmentTrend}%`,
            clientTrend: `${statsData.clientTrend >= 0 ? '+' : ''}${statsData.clientTrend}%`,
            messageTrend: `${statsData.messageTrend >= 0 ? '+' : ''}${statsData.messageTrend}%`,
            timeTrend: `${statsData.timeTrend >= 0 ? '+' : ''}${statsData.timeTrend}%`
          },
          // Positive/negative indicators for stat widgets
          statPositive: {
            appointmentTrend: statsData.appointmentTrend >= 0,
            clientTrend: statsData.clientTrend >= 0,
            messageTrend: statsData.messageTrend >= 0,
            timeTrend: statsData.timeTrend >= 0
          }
        });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Save dashboard configuration
  const saveDashboardConfig = () => {
    if (activeConfig) {
      dashboardConfigService.saveConfiguration(activeConfig);
      setIsEditMode(false);
    }
  };

  // Handle widget removal
  const handleRemoveWidget = (widgetId: string) => {
    if (!activeConfig) return;

    const updatedWidgets = activeConfig.widgets.filter(widget => widget.id !== widgetId);

    setActiveConfig({
      ...activeConfig,
      widgets: updatedWidgets,
      updatedAt: new Date().toISOString()
    });
  };

  // Handle widget configuration
  const handleConfigureWidget = (widgetId: string) => {
    if (!activeConfig) return;

    const widget = activeConfig.widgets.find(w => w.id === widgetId);
    if (widget) {
      setSelectedWidget(widget);
      setWidgetConfigModalOpen(true);
    }
  };

  // Handle widget update
  const handleUpdateWidget = (updatedWidget: DashboardWidgetConfig) => {
    if (!activeConfig) return;

    const updatedWidgets = activeConfig.widgets.map(widget =>
      widget.id === updatedWidget.id ? updatedWidget : widget
    );

    setActiveConfig({
      ...activeConfig,
      widgets: updatedWidgets,
      updatedAt: new Date().toISOString()
    });
  };

  // Handle adding a new widget
  const handleAddWidget = (newWidget: DashboardWidgetConfig) => {
    if (!activeConfig) return;

    // Find the highest y position to place the new widget at the bottom
    const maxY = activeConfig.widgets.reduce((max, widget) => {
      const widgetBottom = widget.position.y + widget.height;
      return widgetBottom > max ? widgetBottom : max;
    }, 0);

    // Update the widget position
    const widgetWithPosition = {
      ...newWidget,
      position: {
        x: 0, // Start at the left
        y: maxY // Place below the lowest widget
      }
    };

    setActiveConfig({
      ...activeConfig,
      widgets: [...activeConfig.widgets, widgetWithPosition],
      updatedAt: new Date().toISOString()
    });
  };

  // Handle switching dashboard configuration
  const handleSwitchConfig = (configId: string) => {
    const config = dashboardConfigs.find(c => c.id === configId);
    if (config) {
      setActiveConfig(config);
      dashboardConfigService.setActiveConfiguration(configId);
    }
  };

  // Handle creating a new dashboard configuration
  const handleCreateConfig = (name: string) => {
    const newConfig = dashboardConfigService.createConfiguration(name);
    setDashboardConfigs([...dashboardConfigs, newConfig]);
    setActiveConfig(newConfig);
    dashboardConfigService.setActiveConfiguration(newConfig.id);
  };

  // Handle deleting a dashboard configuration
  const handleDeleteConfig = (configId: string) => {
    dashboardConfigService.deleteConfiguration(configId);
    const updatedConfigs = dashboardConfigs.filter(c => c.id !== configId);
    setDashboardConfigs(updatedConfigs);

    // If the active config was deleted, set the first available as active
    if (activeConfig?.id === configId) {
      const newActiveConfig = updatedConfigs[0];
      setActiveConfig(newActiveConfig);
      dashboardConfigService.setActiveConfiguration(newActiveConfig.id);
    }
  };

  // Handle resetting to default dashboard
  const handleResetToDefault = () => {
    dashboardConfigService.resetToDefault();
    const configs = dashboardConfigService.getConfigurations();
    const activeConfig = dashboardConfigService.getActiveConfiguration();

    setDashboardConfigs(configs);
    setActiveConfig(activeConfig);
    setIsEditMode(false);
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header with dashboard controls */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">
              {activeConfig ? activeConfig.name : 'Welcome back! Here\'s what\'s happening with your business.'}
            </p>
          </div>

          <div className="flex items-center gap-2">
            {isEditMode ? (
              <>
                <button
                  onClick={() => setAddWidgetModalOpen(true)}
                  className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4" />
                  <span>Add Widget</span>
                </button>
                <button
                  onClick={saveDashboardConfig}
                  className="flex items-center gap-1 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                >
                  <Save className="h-4 w-4" />
                  <span>Save Layout</span>
                </button>
                <button
                  onClick={() => setIsEditMode(false)}
                  className="flex items-center gap-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  <X className="h-4 w-4" />
                  <span>Cancel</span>
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => setConfigModalOpen(true)}
                  className="flex items-center gap-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  <span>Dashboards</span>
                </button>
                <button
                  onClick={() => setIsEditMode(true)}
                  className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <Edit className="h-4 w-4" />
                  <span>Edit Dashboard</span>
                </button>
              </>
            )}
          </div>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <Loader className="h-8 w-8 animate-spin text-blue-500" />
            <span className="ml-3 text-lg text-gray-600">Loading dashboard data...</span>
          </div>
        ) : activeConfig ? (
          <div className="grid grid-cols-12 gap-6">
            {activeConfig.widgets.map(widget => (
              <DashboardWidget
                key={widget.id}
                config={widget}
                data={dashboardData}
                isEditMode={isEditMode}
                onRemove={handleRemoveWidget}
                onConfigure={handleConfigureWidget}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500">No dashboard configuration found. Please create one.</p>
            <button
              onClick={() => setConfigModalOpen(true)}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Create Dashboard
            </button>
          </div>
        )}
      </div>

      {/* Widget Configuration Modal */}
      <WidgetConfigModal
        widget={selectedWidget}
        isOpen={widgetConfigModalOpen}
        onClose={() => {
          setWidgetConfigModalOpen(false);
          setSelectedWidget(null);
        }}
        onSave={handleUpdateWidget}
      />

      {/* Dashboard Configuration Modal */}
      <DashboardConfigModal
        configs={dashboardConfigs}
        activeConfigId={activeConfig?.id || ''}
        isOpen={configModalOpen}
        onClose={() => setConfigModalOpen(false)}
        onSave={handleSwitchConfig}
        onCreate={handleCreateConfig}
        onDelete={handleDeleteConfig}
        onReset={handleResetToDefault}
      />

      {/* Add Widget Modal */}
      <AddWidgetModal
        isOpen={addWidgetModalOpen}
        onClose={() => setAddWidgetModalOpen(false)}
        onAdd={handleAddWidget}
      />
    </DashboardLayout>
  );
}
