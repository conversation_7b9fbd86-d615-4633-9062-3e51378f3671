import { useState, useEffect } from 'react';
import { DashboardLayout } from '../../components/dashboard';
import { Plus, Download } from 'lucide-react';
import { useClients } from '../../hooks/useClients';
import { Client, ClientFormData } from '../../types/client';
import {
  <PERSON>lient<PERSON>ilter,
  ClientList,
  ClientForm,
  ClientDetail,
  ClientDeleteConfirmation
} from '../../components/clients';
import { AnimatedModal, Button, addToast } from '../../components/ui';
import { useNavigate, useLocation } from 'react-router-dom';
import { API_URL } from '../../config/api';

export default function ClientsPage() {
  // Navigation and location hooks
  const navigate = useNavigate();
  const location = useLocation();

  // Client state management with custom hook
  const {
    paginatedClients,
    loading,
    error,
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    totalPages,
    totalClients,
    sortField,
    setSortField,
    sortOrder,
    setSortOrder,
    searchQuery,
    setSearchQuery,
    filterTag,
    setFilterTag,
    allTags,
    addClient,
    updateClient,
    deleteClient
  } = useClients();

  // UI state
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [isAddingClient, setIsAddingClient] = useState(false);
  const [editingClient, setEditingClient] = useState<Client | null>(null);
  const [clientToDelete, setClientToDelete] = useState<Client | null>(null);

  // Check for query parameters on component mount
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const action = params.get('action');

    if (action === 'add') {
      setIsAddingClient(true);
    }
  }, [location.search]);

  // Handle sort change
  const handleSortChange = (field: string) => {
    if (field === sortField) {
      // If clicking the same field, toggle the sort order
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // If clicking a different field, set it as the new sort field with ascending order
      setSortField(field);
      setSortOrder('asc');
    }
  };

  // Handle client selection
  const handleSelectClient = (client: Client) => {
    setSelectedClient(client);
  };

  // Handle starting a conversation with a client
  const handleStartConversation = (client: Client) => {
    // Navigate to the messages page with the client ID
    navigate(`/dashboard/messages?client=${client.id}`);
  };

  // Handle client edit
  const handleEditClient = (client: Client) => {
    setEditingClient(client);
  };

  // Handle client delete
  const handleDeleteClient = (client: Client) => {
    setClientToDelete(client);
  };

  // Handle add client form submission
  const handleAddClient = async (formData: ClientFormData) => {
    try {
      await addClient(formData as Partial<Client>);
      setIsAddingClient(false);
      addToast('Client added successfully', 'success');
    } catch (error: unknown) {
      console.error('Error adding client:', error);

      // Show appropriate toast notification based on error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      if (errorMessage.includes('rate limit') || errorMessage.includes('Too many operations')) {
        addToast(errorMessage, 'error', 10000); // Show rate limit errors for longer
      } else if (errorMessage.includes('already exists')) {
        addToast(errorMessage, 'warning');
      } else {
        addToast('Error adding client', 'error');
      }
    }
  };

  // Handle edit client form submission
  const handleEditClientSubmit = async (formData: ClientFormData) => {
    if (editingClient) {
      try {
        const updatedClient = {
          ...editingClient,
          ...formData
        };
        await updateClient(editingClient.id, updatedClient);
        setEditingClient(null);
        setSelectedClient(null);
        addToast('Client updated successfully', 'success');
      } catch (error: unknown) {
        console.error('Error updating client:', error);

        // Show appropriate toast notification based on error message
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';

        if (errorMessage.includes('already exists')) {
          addToast(errorMessage, 'warning');
        } else {
          addToast('Error updating client', 'error');
        }
      }
    }
  };

  // Handle delete client confirmation
  const handleDeleteClientConfirm = async () => {
    if (clientToDelete) {
      try {
        await deleteClient(clientToDelete.id);
        setClientToDelete(null);
        addToast('Client deleted successfully', 'success');
      } catch (error: unknown) {
        console.error('Error deleting client:', error);
        addToast('Error deleting client', 'error');
      }
    }
  };

  // Handle CSV export
  const handleExportCSV = async () => {
    try {
      // Get the auth token from localStorage
      const token = localStorage.getItem('accessToken');
      if (!token) {
        addToast('You must be logged in to export clients', 'error');
        return;
      }

      // Create a download link
      const downloadLink = document.createElement('a');
      downloadLink.href = `${API_URL}/dashboard/clients/export/csv`;
      downloadLink.setAttribute('download', `clients_${new Date().toISOString().split('T')[0]}.csv`);

      // Add the auth token to the request
      const headers = new Headers();
      headers.append('Authorization', `Bearer ${token}`);

      // Fetch the CSV file
      const response = await fetch(downloadLink.href, {
        headers: headers
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.status} ${response.statusText}`);
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);
      downloadLink.href = url;

      // Trigger the download
      document.body.appendChild(downloadLink);
      downloadLink.click();

      // Clean up
      document.body.removeChild(downloadLink);
      window.URL.revokeObjectURL(url);

      addToast('Clients exported successfully', 'success');
    } catch (error) {
      console.error('Error exporting clients:', error);
      addToast('Error exporting clients', 'error');
    }
  };

  // Render loading state
  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <p>Loading clients...</p>
        </div>
      </DashboardLayout>
    );
  }

  // Render error state
  if (error) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <p>Error: {error}</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-full mx-auto px-2 sm:px-3 md:px-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Clients</h1>
            <div className="flex space-x-2">
              <Button
                onClick={handleExportCSV}
                variant="secondary"
                size="md"
                icon={<Download className="h-5 w-5" />}
              >
                Export CSV
              </Button>
              <Button
                onClick={() => setIsAddingClient(true)}
                variant="primary"
                size="md"
                icon={<Plus className="h-5 w-5" />}
              >
                Add Client
              </Button>
            </div>
          </div>
        </div>

        <div className="max-w-full mx-auto px-2 sm:px-3 md:px-4 mt-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
            {/* Filter Component */}
            <ClientFilter
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              filterTag={filterTag}
              setFilterTag={setFilterTag}
              allTags={allTags}
              sortField={sortField}
              setSortField={setSortField}
              sortOrder={sortOrder}
              setSortOrder={setSortOrder}
            />

            {/* Client List Component */}
            <ClientList
              clients={paginatedClients}
              onSelectClient={handleSelectClient}
              onEditClient={handleEditClient}
              onDeleteClient={handleDeleteClient}
              sortField={sortField}
              sortOrder={sortOrder}
              handleSortChange={handleSortChange}
            />

            {/* Pagination */}
            {paginatedClients.length > 0 ? (
              <div className="flex items-center justify-between px-4 py-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-700">
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  Showing {paginatedClients.length > 0 ? (currentPage - 1) * pageSize + 1 : 0} to{' '}
                  {(currentPage - 1) * pageSize + paginatedClients.length} of{' '}
                  {totalClients} clients
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-2 mr-2 bg-white dark:bg-gray-800 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md">
                      <label htmlFor="pageSize" className="text-sm text-gray-700 dark:text-gray-300 whitespace-nowrap">
                        Show per page:
                      </label>
                      <select
                        id="pageSize"
                        value={pageSize}
                        onChange={(e) => {
                          setPageSize(Number(e.target.value));
                          // Reset to first page is handled by the useEffect in useClients
                        }}
                        className="px-2 py-1 text-sm bg-transparent border-none focus:outline-none focus:ring-0 text-gray-900 dark:text-white"
                      >
                        <option value="5">5</option>
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                      </select>
                    </div>
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <span className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300">
                      Page {currentPage} of {totalPages || 1}
                    </span>
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage >= totalPages}
                      className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center p-6 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-700">
                <p className="text-gray-500 dark:text-gray-400">
                  {filterTag ? `No clients found with tag "${filterTag}"` : 'No clients found'}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modal for adding a client */}
      <AnimatedModal
        isOpen={isAddingClient}
        onClose={() => setIsAddingClient(false)}
        width="max-w-md"
      >
        <ClientForm
          onSubmit={handleAddClient}
          onCancel={() => setIsAddingClient(false)}
          title="Add New Client"
          submitLabel="Add Client"
        />
      </AnimatedModal>

      {/* Modal for viewing client details */}
      <AnimatedModal
        isOpen={!!selectedClient}
        onClose={() => setSelectedClient(null)}
        width="max-w-2xl"
      >
        {selectedClient && (
          <ClientDetail
            client={selectedClient}
            onEdit={() => {
              setEditingClient(selectedClient);
              setSelectedClient(null);
            }}
            onDelete={() => {
              setClientToDelete(selectedClient);
              setSelectedClient(null);
            }}
            onClose={() => setSelectedClient(null)}
            onStartConversation={() => handleStartConversation(selectedClient)}
          />
        )}
      </AnimatedModal>

      {/* Modal for editing a client */}
      <AnimatedModal
        isOpen={!!editingClient}
        onClose={() => setEditingClient(null)}
        width="max-w-md"
      >
        {editingClient && (
          <ClientForm
            initialData={editingClient}
            onSubmit={handleEditClientSubmit}
            onCancel={() => setEditingClient(null)}
            title="Edit Client"
            submitLabel="Save Changes"
          />
        )}
      </AnimatedModal>

      {/* Modal for confirming client deletion */}
      <AnimatedModal
        isOpen={!!clientToDelete}
        onClose={() => setClientToDelete(null)}
        width="max-w-md"
      >
        {clientToDelete && (
          <ClientDeleteConfirmation
            client={clientToDelete}
            onConfirm={handleDeleteClientConfirm}
            onCancel={() => setClientToDelete(null)}
          />
        )}
      </AnimatedModal>
    </DashboardLayout>
  );
}
