import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>ck2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Users, Loader, Edit, Plus, Save, X, Trash2, <PERSON>ota<PERSON><PERSON>c<PERSON>, AlertCircle } from 'lucide-react';
import { DashboardLayout } from '../../components/dashboard';
import { dashboardService } from '../../services/dashboardService';
import GridLayout from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import {
  AppointmentStatusChart,
  AppointmentTrendChart,
  ClientGrowthChart,
  MessageActivityChart
} from '../../components/dashboard/charts';
import { Appointments<PERSON>ist, MessagesList, StatsCard } from '../../components/dashboard';
import { AddWidgetModal } from '../../components/dashboard/AddWidgetModal';
import { FeedbackWidget } from '../../components/dashboard/FeedbackWidget';

// Define widget types
interface DashboardWidget {
  id: string;
  type: 'stat' | 'pie-chart' | 'line-chart' | 'bar-chart' | 'area-chart' | 'list' | 'feedback';
  title: string;
  dataSource: string;
  w: number;
  h: number;
  x: number;
  y: number;
  minW?: number;
  minH?: number;
  static?: boolean;
}

// Default dashboard layout
const DEFAULT_WIDGETS: DashboardWidget[] = [
  // Stats widgets
  {
    id: 'total-appointments',
    type: 'stat',
    title: 'Total Appointments',
    dataSource: 'stats.totalAppointments',
    w: 3,
    h: 2,
    x: 0,
    y: 0,
    minW: 2,
    minH: 2
  },
  {
    id: 'active-clients',
    type: 'stat',
    title: 'Active Clients',
    dataSource: 'stats.activeClients',
    w: 3,
    h: 2,
    x: 3,
    y: 0,
    minW: 2,
    minH: 2
  },
  {
    id: 'messages-handled',
    type: 'stat',
    title: 'Messages Handled',
    dataSource: 'stats.messagesHandled',
    w: 3,
    h: 2,
    x: 6,
    y: 0,
    minW: 2,
    minH: 2
  },
  {
    id: 'time-saved',
    type: 'stat',
    title: 'Time Saved',
    dataSource: 'stats.timeSaved',
    w: 3,
    h: 2,
    x: 9,
    y: 0,
    minW: 2,
    minH: 2
  },

  // Chart widgets
  {
    id: 'appointment-status',
    type: 'pie-chart',
    title: 'Appointment Status',
    dataSource: 'appointmentsByStatus',
    w: 6,
    h: 8,
    x: 0,
    y: 2,
    minW: 3,
    minH: 6
  },
  {
    id: 'appointment-trends',
    type: 'line-chart',
    title: 'Appointment Trends',
    dataSource: 'appointmentsByMonth',
    w: 6,
    h: 8,
    x: 6,
    y: 2,
    minW: 3,
    minH: 6
  },

  // List widgets
  {
    id: 'upcoming-appointments',
    type: 'list',
    title: 'Upcoming Appointments',
    dataSource: 'upcomingAppointments',
    w: 6,
    h: 8,
    x: 0,
    y: 10,
    minW: 3,
    minH: 6
  },
  {
    id: 'recent-messages',
    type: 'list',
    title: 'Recent Messages',
    dataSource: 'recentMessages',
    w: 6,
    h: 8,
    x: 6,
    y: 10,
    minW: 3,
    minH: 6
  },
  {
    id: 'client-feedback',
    type: 'feedback',
    title: 'Client Feedback',
    dataSource: 'feedback',
    w: 6,
    h: 10,
    x: 0,
    y: 18,
    minW: 4,
    minH: 8
  }
];

// All available widget templates
const ALL_WIDGET_TEMPLATES: DashboardWidget[] = [
  // Stats widgets
  {
    id: 'total-appointments',
    type: 'stat',
    title: 'Total Appointments',
    dataSource: 'stats.totalAppointments',
    w: 3,
    h: 2,
    x: 0,
    y: 0,
    minW: 2,
    minH: 2
  },
  {
    id: 'active-clients',
    type: 'stat',
    title: 'Active Clients',
    dataSource: 'stats.activeClients',
    w: 3,
    h: 2,
    x: 3,
    y: 0,
    minW: 2,
    minH: 2
  },
  {
    id: 'messages-handled',
    type: 'stat',
    title: 'Messages Handled',
    dataSource: 'stats.messagesHandled',
    w: 3,
    h: 2,
    x: 6,
    y: 0,
    minW: 2,
    minH: 2
  },
  {
    id: 'time-saved',
    type: 'stat',
    title: 'Time Saved',
    dataSource: 'stats.timeSaved',
    w: 3,
    h: 2,
    x: 9,
    y: 0,
    minW: 2,
    minH: 2
  },
  // Chart widgets
  {
    id: 'appointment-status',
    type: 'pie-chart',
    title: 'Appointment Status',
    dataSource: 'appointmentsByStatus',
    w: 6,
    h: 8,
    x: 0,
    y: 2,
    minW: 3,
    minH: 6
  },
  {
    id: 'appointment-trends',
    type: 'line-chart',
    title: 'Appointment Trends',
    dataSource: 'appointmentsByMonth',
    w: 6,
    h: 8,
    x: 6,
    y: 2,
    minW: 3,
    minH: 6
  },
  // List widgets
  {
    id: 'upcoming-appointments',
    type: 'list',
    title: 'Upcoming Appointments',
    dataSource: 'upcomingAppointments',
    w: 6,
    h: 8,
    x: 0,
    y: 10,
    minW: 3,
    minH: 6
  },
  {
    id: 'recent-messages',
    type: 'list',
    title: 'Recent Messages',
    dataSource: 'recentMessages',
    w: 6,
    h: 8,
    x: 6,
    y: 10,
    minW: 3,
    minH: 6
  },
  // Additional widgets
  {
    id: 'client-growth',
    type: 'bar-chart',
    title: 'Client Growth',
    dataSource: 'clientsByMonth',
    w: 6,
    h: 8,
    x: 0,
    y: 0,
    minW: 3,
    minH: 6
  },
  {
    id: 'message-activity',
    type: 'area-chart',
    title: 'Message Activity',
    dataSource: 'messagesByDay',
    w: 6,
    h: 8,
    x: 0,
    y: 0,
    minW: 3,
    minH: 6
  },
  {
    id: 'client-feedback',
    type: 'feedback',
    title: 'Client Feedback',
    dataSource: 'feedback',
    w: 6,
    h: 10,
    x: 0,
    y: 0,
    minW: 4,
    minH: 8
  }
];

/* eslint-disable @typescript-eslint/no-explicit-any */
export default function DashboardPage() {
  // Loading state
  const [isLoading, setIsLoading] = useState(true);

  // Dashboard data
  interface DashboardData {
    stats: any;
    appointmentsByStatus: any;
    appointmentsByMonth: any;
    clientsByMonth: any;
    messagesByDay: any;
    upcomingAppointments: any[];
    recentMessages: any[];
    formattedAppointments: any[];
    statIcons: {
      totalAppointments: any;
      activeClients: any;
      messagesHandled: any;
      timeSaved: any;
    };
    statTrends: {
      appointmentTrend: string;
      clientTrend: string;
      messageTrend: string;
      timeTrend: string;
    };
    statPositive: {
      appointmentTrend: boolean;
      clientTrend: boolean;
      messageTrend: boolean;
      timeTrend: boolean;
    };
  }

  const [dashboardData, setDashboardData] = useState<DashboardData>({} as DashboardData);

  // Window width for responsive layout
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Calculate the available width for the grid
  const getGridWidth = () => {
    // Subtract sidebar width and some padding
    const sidebarWidth = 72; // Collapsed sidebar width
    const padding = 64; // Add more padding for better spacing
    return windowWidth - sidebarWidth - padding;
  };

  // Dashboard layout
  const [widgets, setWidgets] = useState<DashboardWidget[]>([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [originalWidgets, setOriginalWidgets] = useState<DashboardWidget[]>([]);
  const [removedWidgets, setRemovedWidgets] = useState<DashboardWidget[]>([]);

  // Modals
  const [showAddWidgetModal, setShowAddWidgetModal] = useState(false);
  const [showRemoveConfirmation, setShowRemoveConfirmation] = useState(false);
  const [widgetToRemove, setWidgetToRemove] = useState<string | null>(null);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    // Initial call
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Load saved dashboard layout and removed widgets from localStorage
  useEffect(() => {
    const savedLayout = localStorage.getItem('dashboard_widgets');
    const savedRemovedWidgets = localStorage.getItem('dashboard_removed_widgets');

    if (savedLayout) {
      try {
        setWidgets(JSON.parse(savedLayout));
      } catch (error) {
        console.error('Error loading saved dashboard layout:', error);
        setWidgets(DEFAULT_WIDGETS);
      }
    } else {
      setWidgets(DEFAULT_WIDGETS);
    }

    if (savedRemovedWidgets) {
      try {
        setRemovedWidgets(JSON.parse(savedRemovedWidgets));
      } catch (error) {
        console.error('Error loading removed widgets:', error);
        setRemovedWidgets([]);
      }
    }
  }, []);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      setIsLoading(true);
      try {
        // Fetch stats
        const statsData = await dashboardService.getStats();

        // Fetch chart data
        const statusData = await dashboardService.getAppointmentsByStatus();
        const monthlyData = await dashboardService.getAppointmentsByMonth();
        const clientsData = await dashboardService.getClientsByMonth();
        const messagesData = await dashboardService.getMessagesByDay();

        // Fetch list data
        const appointments = await dashboardService.getUpcomingAppointments();
        const messages = await dashboardService.getRecentMessages();

        // Format appointments for the list component
        const formattedAppointments = appointments.map((appointment: any) => {
          const startDate = new Date(appointment.start_time);
          return {
            id: appointment.id,
            clientName: appointment.client_name,
            service: appointment.service_name,
            date: startDate.toLocaleDateString(),
            time: startDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            status: appointment.status as 'confirmed' | 'pending' | 'cancelled'
          };
        });

        // Set dashboard data
        setDashboardData({
          stats: statsData,
          appointmentsByStatus: statusData,
          appointmentsByMonth: monthlyData,
          clientsByMonth: clientsData,
          messagesByDay: messagesData,
          upcomingAppointments: appointments,
          recentMessages: messages,
          formattedAppointments,
          // Icons for stat widgets
          statIcons: {
            totalAppointments: CalendarCheck2,
            activeClients: Users,
            messagesHandled: CheckCheck,
            timeSaved: Clock
          },
          // Trend values for stat widgets
          statTrends: {
            appointmentTrend: `${statsData.appointmentTrend >= 0 ? '+' : ''}${statsData.appointmentTrend}%`,
            clientTrend: `${statsData.clientTrend >= 0 ? '+' : ''}${statsData.clientTrend}%`,
            messageTrend: `${statsData.messageTrend >= 0 ? '+' : ''}${statsData.messageTrend}%`,
            timeTrend: `${statsData.timeTrend >= 0 ? '+' : ''}${statsData.timeTrend}%`
          },
          // Positive/negative indicators for stat widgets
          statPositive: {
            appointmentTrend: statsData.appointmentTrend >= 0,
            clientTrend: statsData.clientTrend >= 0,
            messageTrend: statsData.messageTrend >= 0,
            timeTrend: statsData.timeTrend >= 0
          }
        });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Save dashboard layout to localStorage
  const saveDashboardLayout = () => {
    localStorage.setItem('dashboard_widgets', JSON.stringify(widgets));
    localStorage.setItem('dashboard_removed_widgets', JSON.stringify(removedWidgets));
    // Update the original widgets to match the current layout
    setOriginalWidgets([...widgets]);
    setIsEditMode(false);
  };

  // Handle layout change
  const handleLayoutChange = (layout: { i: string; x: number; y: number; w: number; h: number }[]) => {
    if (!isEditMode) return;

    // Update widget positions based on the new layout
    const updatedWidgets = widgets.map(widget => {
      const layoutItem = layout.find((item) => item.i === widget.id);
      if (layoutItem) {
        return {
          ...widget,
          x: layoutItem.x,
          y: layoutItem.y,
          w: layoutItem.w,
          h: layoutItem.h
        };
      }
      return widget;
    });

    setWidgets(updatedWidgets);
  };

  // Enter edit mode
  const enterEditMode = () => {
    // Save the current layout before entering edit mode
    setOriginalWidgets([...widgets]);
    setIsEditMode(true);
  };

  // Cancel edit mode
  const cancelEditMode = () => {
    // Restore the original widget layout
    setWidgets(originalWidgets);
    setIsEditMode(false);
  };

  // Confirm widget removal
  const confirmRemoveWidget = (widgetId: string) => {
    setWidgetToRemove(widgetId);
    setShowRemoveConfirmation(true);
  };

  // Remove a widget
  const removeWidget = () => {
    if (!widgetToRemove) return;

    const widgetToBeRemoved = widgets.find(widget => widget.id === widgetToRemove);
    if (widgetToBeRemoved) {
      // Add to removed widgets list
      setRemovedWidgets([...removedWidgets, widgetToBeRemoved]);
      // Remove from active widgets
      setWidgets(widgets.filter(widget => widget.id !== widgetToRemove));
    }

    // Close confirmation dialog
    setShowRemoveConfirmation(false);
    setWidgetToRemove(null);
  };

  // State for reset confirmation modal
  const [showResetConfirmation, setShowResetConfirmation] = useState(false);

  // Show reset confirmation modal
  const confirmResetDashboard = () => {
    setShowResetConfirmation(true);
  };

  // Reset dashboard to default layout
  const resetDashboard = () => {
    console.log('Resetting dashboard to default widgets:', DEFAULT_WIDGETS);
    setWidgets(DEFAULT_WIDGETS);
    setRemovedWidgets([]);
    localStorage.setItem('dashboard_widgets', JSON.stringify(DEFAULT_WIDGETS));
    localStorage.removeItem('dashboard_removed_widgets');
    setOriginalWidgets([...DEFAULT_WIDGETS]);
    setShowResetConfirmation(false);
    console.log('Dashboard reset complete. New widgets:', DEFAULT_WIDGETS);
  };

  // Add widgets from the modal
  const handleAddWidgets = (widgetConfigs: any) => {
    // Handle both single widget and array of widgets
    const configs = Array.isArray(widgetConfigs) ? widgetConfigs : [widgetConfigs];
    if (configs.length === 0) return;

    // Find the highest y position to place new widgets at the bottom
    const maxY = widgets.reduce((max, widget) => {
      const widgetBottom = widget.y + widget.h;
      return widgetBottom > max ? widgetBottom : max;
    }, 0);

    // Process all widgets to add
    let yOffset = maxY;
    const newWidgets: DashboardWidget[] = [];
    const removedWidgetIds: string[] = [];

    for (const widgetConfig of configs) {
      // Find the template for this widget
      const template = ALL_WIDGET_TEMPLATES.find(t => t.id === widgetConfig.id);
      if (!template) continue;

      // Check if widget already exists
      if (widgets.some(w => w.id === template.id)) {
        console.warn(`Widget ${template.id} is already on the dashboard.`);
        continue;
      }

      // Create the new widget with proper y position
      const newWidget = {
        ...template,
        y: yOffset // Place below the previous widget
      };

      // Increment y position for next widget
      yOffset += template.h;

      // Add to new widgets array
      newWidgets.push(newWidget);

      // Track removed widget IDs
      removedWidgetIds.push(template.id);
    }

    if (newWidgets.length === 0) return;

    // Add all new widgets to active widgets
    const updatedWidgets = [...widgets, ...newWidgets];
    setWidgets(updatedWidgets);

    // Remove from removed widgets list if they were there
    setRemovedWidgets(removedWidgets.filter(w => !removedWidgetIds.includes(w.id)));

    // Save the updated widgets to localStorage immediately
    localStorage.setItem('dashboard_widgets', JSON.stringify(updatedWidgets));
  };

  // Get available widgets for the add widget modal
  const getAvailableWidgets = () => {
    // Return all widgets that are not currently on the dashboard
    return ALL_WIDGET_TEMPLATES.filter(template =>
      !widgets.some(widget => widget.id === template.id)
    );
  };

  // Render widget content based on type
  const renderWidgetContent = (widget: DashboardWidget) => {
    const { type, dataSource } = widget;

    // Extract data based on dataSource path
    const getNestedData = (path: string): any => {
      return path.split('.').reduce((obj: any, key: string) => {
        return (obj && obj[key] !== undefined) ? obj[key] : null;
      }, dashboardData as any);
    };

    const widgetData = getNestedData(dataSource);

    if (!widgetData && type !== 'list') {
      return (
        <div className="flex items-center justify-center h-full bg-gray-50 dark:bg-gray-700 rounded-lg">
          <p className="text-gray-500 dark:text-gray-400">No data available</p>
        </div>
      );
    }

    switch (type) {
      case 'stat':
        // For stat widgets, we need to find the corresponding stat
        if (dataSource === 'stats.totalAppointments') {
          return (
            <StatsCard
              icon={dashboardData.statIcons?.totalAppointments}
              label="Total Appointments"
              value={widgetData?.toString() || '0'}
              change={dashboardData.statTrends?.appointmentTrend || '0%'}
              positive={dashboardData.statPositive?.appointmentTrend || true}
            />
          );
        } else if (dataSource === 'stats.activeClients') {
          return (
            <StatsCard
              icon={dashboardData.statIcons?.activeClients}
              label="Active Clients"
              value={widgetData?.toString() || '0'}
              change={dashboardData.statTrends?.clientTrend || '0%'}
              positive={dashboardData.statPositive?.clientTrend || true}
            />
          );
        } else if (dataSource === 'stats.messagesHandled') {
          return (
            <StatsCard
              icon={dashboardData.statIcons?.messagesHandled}
              label="Messages Handled"
              value={widgetData?.toString() || '0'}
              change={dashboardData.statTrends?.messageTrend || '0%'}
              positive={dashboardData.statPositive?.messageTrend || true}
            />
          );
        } else if (dataSource === 'stats.timeSaved') {
          return (
            <StatsCard
              icon={dashboardData.statIcons?.timeSaved}
              label="Time Saved"
              value={`${widgetData || '0'}h`}
              change={dashboardData.statTrends?.timeTrend || '0%'}
              positive={dashboardData.statPositive?.timeTrend || true}
            />
          );
        }
        return null;

      case 'pie-chart':
        return <AppointmentStatusChart data={widgetData} />;

      case 'line-chart':
        return <AppointmentTrendChart data={widgetData} />;

      case 'bar-chart':
        return <ClientGrowthChart data={widgetData} />;

      case 'area-chart':
        return <MessageActivityChart data={widgetData} />;

      case 'list':
        if (dataSource === 'upcomingAppointments') {
          return <AppointmentsList appointments={dashboardData.formattedAppointments || []} />;
        } else if (dataSource === 'recentMessages') {
          return dashboardData.recentMessages?.length > 0 ? (
            <MessagesList messages={dashboardData.recentMessages} />
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p>No recent messages</p>
            </div>
          );
        }
        return null;

      case 'feedback':
        return <FeedbackWidget />;

      default:
        return (
          <div className="flex items-center justify-center h-full bg-gray-50 dark:bg-gray-700 rounded-lg">
            <p className="text-gray-500 dark:text-gray-400">Unknown widget type</p>
          </div>
        );
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6 w-full px-4">
        {/* Header with dashboard controls */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
            <p className="text-gray-600 dark:text-gray-300">Welcome back! Here's what's happening with your business.</p>
          </div>

          <div className="flex items-center gap-2">
            {isEditMode ? (
              <>
                <button
                  onClick={() => setShowAddWidgetModal(true)}
                  className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  disabled={getAvailableWidgets().length === 0}
                  title={getAvailableWidgets().length === 0 ? 'All widgets are already on your dashboard' : 'Add a widget'}
                >
                  <Plus className="h-4 w-4" />
                  <span>Add Widget</span>
                </button>
                <button
                  onClick={confirmResetDashboard}
                  className="flex items-center gap-1 px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                  title="Reset dashboard to default layout"
                >
                  <RotateCcw className="h-4 w-4" />
                  <span>Reset</span>
                </button>
                <button
                  onClick={saveDashboardLayout}
                  className="flex items-center gap-1 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                >
                  <Save className="h-4 w-4" />
                  <span>Save Layout</span>
                </button>
                <button
                  onClick={cancelEditMode}
                  className="flex items-center gap-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  <X className="h-4 w-4" />
                  <span>Done</span>
                </button>
              </>
            ) : (
              <button
                onClick={enterEditMode}
                className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-all"
              >
                <Edit className="h-4 w-4" />
                <span>Edit Dashboard</span>
              </button>
            )}
          </div>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <Loader className="h-8 w-8 animate-spin text-blue-500" />
            <span className="ml-3 text-lg text-gray-600">Loading dashboard data...</span>
          </div>
        ) : (
          <div className={`dashboard-grid w-full overflow-hidden ${isEditMode ? 'edit-mode' : ''}`}>
            <GridLayout
              className="layout"
              layout={widgets.map(widget => ({
                i: widget.id,
                x: widget.x || 0,
                y: widget.y || 0,
                w: widget.w,
                h: widget.h,
                minW: widget.minW,
                minH: widget.minH,
                static: !isEditMode
              }))}
              cols={12}
              rowHeight={40}
              width={getGridWidth()}
              containerPadding={[12, 8]}
              margin={[16, 16]}
              autoSize={true}
              useCSSTransforms={true}
              draggableCancel=".widget-remove-button"
              onLayoutChange={handleLayoutChange}
              isDraggable={isEditMode}
              isResizable={false}
              compactType="vertical"
            >
              {widgets.map(widget => {
                console.log('Rendering widget:', widget.id, widget.type);
                return (
                <div key={widget.id} className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden h-full border relative ${isEditMode ? 'border-blue-200 dark:border-blue-800' : 'border-gray-100 dark:border-gray-700'}`}>
                  {widget.type !== 'stat' && (
                    <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{widget.title}</h2>

                      {isEditMode && (
                        <div
                          className="widget-remove-button"
                          onClick={(e) => e.stopPropagation()}
                          onMouseDown={(e) => e.stopPropagation()} // Prevent drag start
                          onTouchStart={(e) => e.stopPropagation()} // Prevent touch drag
                        >
                          <button
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent event bubbling to parent (grid item)
                              e.preventDefault(); // Prevent any default behavior
                              confirmRemoveWidget(widget.id);
                            }}
                            onMouseDown={(e) => e.stopPropagation()} // Prevent drag start
                            onTouchStart={(e) => e.stopPropagation()} // Prevent touch drag
                            className="p-3 bg-red-100 text-red-500 hover:bg-red-200 hover:text-red-600 dark:bg-red-900/50 dark:text-red-400 dark:hover:bg-red-900/70 dark:hover:text-red-300 rounded-md transition-colors shadow-sm"
                            title="Remove widget"
                          >
                            <Trash2 className="h-5 w-5" />
                          </button>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Widget Content */}

                  <div className={`${widget.type === 'stat' ? 'h-full overflow-hidden' : 'p-4 h-[calc(100%-60px)] overflow-auto'} dark:text-gray-200`}>
                    {renderWidgetContent(widget)}
                  </div>
                </div>
                );
              })}
            </GridLayout>
          </div>
        )}
      </div>

      {/* Add Widget Modal */}
      <AddWidgetModal
        isOpen={showAddWidgetModal}
        onClose={() => setShowAddWidgetModal(false)}
        onAdd={handleAddWidgets}
        existingWidgetIds={widgets.map(w => w.id)}
      />

      {/* Remove Widget Confirmation */}
      {showRemoveConfirmation && widgetToRemove && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 transition-opacity duration-200 ease-out">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md confirmation-dialog transform transition-all duration-200 ease-out scale-100 opacity-100">
            <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 confirmation-dialog-header">
              <h2 className="text-lg font-semibold text-red-600 dark:text-red-400 flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Remove Widget
              </h2>
              <button
                onClick={() => setShowRemoveConfirmation(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-6">
              <p className="mb-4 text-gray-700 dark:text-gray-300">
                Are you sure you want to remove this widget from your dashboard?
                You can add it back later from the "Add Widget" menu.
              </p>
              <div className="p-4 border rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600">
                <p className="font-medium text-gray-900 dark:text-white">
                  {widgets.find(w => w.id === widgetToRemove)?.title}
                </p>
              </div>
            </div>

            <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 flex justify-end gap-2 confirmation-dialog-footer rounded-b-lg">
              <button
                onClick={() => setShowRemoveConfirmation(false)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={removeWidget}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800"
              >
                Remove
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Reset Dashboard Confirmation */}
      {showResetConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 transition-opacity duration-200 ease-out">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md confirmation-dialog transform transition-all duration-200 ease-out scale-100 opacity-100">

            <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 confirmation-dialog-header">
              <h2 className="text-lg font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                <RotateCcw className="h-5 w-5" />
                Reset Dashboard
              </h2>
              <button
                onClick={() => setShowResetConfirmation(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-6">
              <p className="mb-4 text-gray-700 dark:text-gray-300">
                Are you sure you want to reset the dashboard to its default layout? This will remove all customizations.
              </p>
              <div className="p-4 border rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600">
                <p className="font-medium text-gray-900 dark:text-white">
                  All custom widget arrangements and removals will be lost.
                </p>
              </div>
            </div>

            <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 flex justify-end gap-2 confirmation-dialog-footer rounded-b-lg">
              <button
                onClick={() => setShowResetConfirmation(false)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={resetDashboard}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-800"
              >
                Reset Dashboard
              </button>
            </div>
          </div>
        </div>
      )}

      {/* CSS is now in dashboard-grid.css */}
    </DashboardLayout>
  );
}
