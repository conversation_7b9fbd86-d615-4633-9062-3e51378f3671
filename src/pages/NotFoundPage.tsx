import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '../components/ui';
import { ArrowLeft, Home, LogIn } from 'lucide-react';
import { useAuthStore } from '../lib/auth';

const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated } = useAuthStore();

  const goBack = () => {
    navigate(-1); // Go back to the previous page
  };

  const goHome = () => {
    navigate('/'); // Go to home/landing page
  };

  const goToDashboard = () => {
    navigate('/dashboard'); // Go to dashboard
  };

  const goToLogin = () => {
    navigate('/login', { state: { from: location } }); // Go to login page
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-white dark:bg-gray-900 p-4">
      <div className="w-full max-w-md p-8 space-y-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg text-center border border-gray-200 dark:border-gray-700">
        <h1 className="text-6xl font-bold text-blue-600 dark:text-blue-400">404</h1>
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">Page Not Found</h2>
        <p className="text-gray-600 dark:text-gray-300">
          The page you are looking for doesn't exist or has been moved.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center mt-6">
          <Button
            variant="outline"
            onClick={goBack}
            className="flex items-center gap-2"
            icon={<ArrowLeft size={16} />}
          >
            Go Back
          </Button>

          {isAuthenticated ? (
            <Button
              variant="primary"
              onClick={goToDashboard}
              icon={<Home size={16} />}
            >
              Go to Dashboard
            </Button>
          ) : (
            <>
              <Button
                variant="secondary"
                onClick={goHome}
                icon={<Home size={16} />}
              >
                Go to Home
              </Button>
              <Button
                variant="primary"
                onClick={goToLogin}
                icon={<LogIn size={16} />}
              >
                Log In
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;
