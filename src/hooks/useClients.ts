import { useState, useEffect, useCallback } from 'react';
import { clientService } from '../services';
import { Client, mapApiClientToClient, mapClientToApiClient } from '../types/client';
import { useAuthStore } from '../lib/auth';
import { clientCreationLimiter } from '../utils/rateLimiter';

interface ClientsState {
  clients: Client[];
  loading: boolean;
  error: string | null;
  totalPages: number;
}

interface ClientsOptions {
  initialPage?: number;
  pageSize?: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Custom hook for managing clients
 */
export function useClients(options: ClientsOptions = {}) {
  const {
    initialPage = 1,
    pageSize = 10,
    sortField = 'name',
    sortOrder = 'asc'
  } = options;

  const [state, setState] = useState<ClientsState>({
    clients: [],
    loading: true,
    error: null,
    totalPages: 1
  });

  // Track the total number of clients
  const [totalClients, setTotalClients] = useState(0);

  const [currentPage, setCurrentPage] = useState(initialPage);
  const [currentPageSize, setCurrentPageSize] = useState(pageSize);
  const [currentSortField, setSortField] = useState(sortField);
  const [currentSortOrder, setSortOrder] = useState<'asc' | 'desc'>(sortOrder);
  const [filterTag, setFilterTag] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Get all unique tags from clients
  const allTags = Array.from(new Set(state.clients.flatMap(client => client.tags)));

  // We're using server-side filtering and pagination
  // This is just a reference to the current clients
  const filteredClients = state.clients;

  // Use the total pages from the API response
  const calculatedTotalPages = state.totalPages;

  // Since we're using server-side pagination, the clients we have are already paginated
  const paginatedClients = filteredClients;

  /**
   * Fetch clients from the API
   */
  const fetchClients = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // Fetch clients with pagination, sorting, and filtering parameters

      // Fetch clients using the client service
      const data = await clientService.getClients({
        page: currentPage,
        page_size: currentPageSize,
        sort_by: currentSortField,
        order: currentSortOrder,
        tag_filter: filterTag || undefined,
        search: searchQuery || undefined
      });

      // Process received client data

      // Map the response data to our client format
      const mappedClients = data.items.map(mapApiClientToClient);

      // Update state with the data
      setState({
        clients: mappedClients,
        loading: false,
        error: null,
        totalPages: data.total_pages
      });

      // Update total clients count
      setTotalClients(data.total);
    } catch (err) {
      console.error('Error fetching clients in useClients hook:', err);
      setState(prev => ({
        ...prev,
        clients: [], // Clear clients on error
        loading: false,
        error: err instanceof Error ? err.message : 'An error occurred'
      }));

      // Reset total clients count on error
      setTotalClients(0);
    }
  }, [currentPage, currentPageSize, currentSortField, currentSortOrder, filterTag, searchQuery]);

  /**
   * Check if a client with the same email or phone already exists
   */
  const checkForDuplicateClient = (clientData: Partial<Client>, excludeClientId?: string): { isDuplicate: boolean; field?: string; value?: string } => {
    // Check for duplicate email
    if (clientData.email) {
      const duplicateEmail = state.clients.find(client =>
        client.email.toLowerCase() === clientData.email.toLowerCase() &&
        client.id !== excludeClientId
      );

      if (duplicateEmail) {
        return { isDuplicate: true, field: 'email', value: clientData.email };
      }
    }

    // Check for duplicate phone
    if (clientData.phone) {
      const duplicatePhone = state.clients.find(client =>
        client.phone === clientData.phone &&
        client.id !== excludeClientId
      );

      if (duplicatePhone) {
        return { isDuplicate: true, field: 'phone', value: clientData.phone };
      }
    }

    return { isDuplicate: false };
  };

  /**
   * Add a new client
   */
  const addClient = async (clientData: Partial<Client>): Promise<Client> => {
    try {
      // Get current user ID for rate limiting
      const user = useAuthStore.getState().user;
      const userId = user?.id || 'anonymous';

      // Check rate limiter
      const rateLimitCheck = clientCreationLimiter.checkOperation(userId);
      if (!rateLimitCheck.allowed) {
        throw new Error(rateLimitCheck.message);
      }

      // Check for duplicate client first
      const duplicateCheck = checkForDuplicateClient(clientData);
      if (duplicateCheck.isDuplicate) {
        throw new Error(`A client with this ${duplicateCheck.field} already exists: ${duplicateCheck.value}`);
      }

      // Show warning if close to rate limit
      if (rateLimitCheck.message) {
        console.warn(rateLimitCheck.message);
      }

      // For debugging, log the current user state
      // Use current user for client creation

      // Create a properly formatted client object
      const clientToAdd = {
        name: clientData.name || '',
        email: clientData.email || '',
        phone: clientData.phone || '',
        tags: clientData.tags || [],
        notes: clientData.notes || "",
        last_appointment: null,
        next_appointment: null
      };

      // Use the client service to create the client
      const newClient = await clientService.createClient(clientToAdd);

      // Map to frontend format
      const formattedClient = mapApiClientToClient(newClient);

      // Update the local state
      setState(prev => ({
        ...prev,
        clients: [...prev.clients, formattedClient]
      }));

      return formattedClient;
    } catch (error) {
      console.error('Error adding client:', error);
      throw error;
    }
  };

  /**
   * Update an existing client
   */
  const updateClient = async (clientId: string, updatedData: Client): Promise<Client> => {
    try {
      // Check for duplicate client first, excluding the current client being updated
      const duplicateCheck = checkForDuplicateClient(updatedData, clientId);
      if (duplicateCheck.isDuplicate) {
        throw new Error(`A client with this ${duplicateCheck.field} already exists: ${duplicateCheck.value}`);
      }

      // Format the data for the API
      const apiData = mapClientToApiClient(updatedData);

      // Use the client service to update the client
      const updatedClient = await clientService.updateClient(clientId, apiData);

      // Format the response for the UI
      const formattedClient = mapApiClientToClient(updatedClient);

      // Update the clients list
      setState(prev => ({
        ...prev,
        clients: prev.clients.map(client =>
          client.id === clientId ? formattedClient : client
        )
      }));

      return formattedClient;
    } catch (error) {
      console.error('Error updating client:', error);
      throw error;
    }
  };

  /**
   * Delete a client
   */
  const deleteClient = async (clientId: string): Promise<void> => {
    try {
      // Use the client service to delete the client
      await clientService.deleteClient(clientId);

      // Remove client from the list
      setState(prev => ({
        ...prev,
        clients: prev.clients.filter(client => client.id !== clientId)
      }));
    } catch (error) {
      console.error('Error deleting client:', error);
      throw error;
    }
  };

  // Fetch clients when dependencies change
  useEffect(() => {
    fetchClients();
  }, [fetchClients]);

  // Reset to page 1 when page size or search query changes
  useEffect(() => {
    setCurrentPage(1);
  }, [currentPageSize, searchQuery]);

  // Reset to page 1 when search or filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, filterTag]);

  return {
    // State
    clients: state.clients,
    filteredClients,
    paginatedClients,
    loading: state.loading,
    error: state.error,

    // Pagination
    currentPage,
    setCurrentPage,
    pageSize: currentPageSize,
    setPageSize: setCurrentPageSize,
    totalPages: calculatedTotalPages,
    totalClients,

    // Sorting
    sortField: currentSortField,
    setSortField,
    sortOrder: currentSortOrder,
    setSortOrder,

    // Filtering
    searchQuery,
    setSearchQuery,
    filterTag,
    setFilterTag,
    allTags,

    // CRUD operations
    fetchClients,
    addClient,
    updateClient,
    deleteClient
  };
}
