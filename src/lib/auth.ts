import { jwtDecode } from 'jwt-decode';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const API_URL = import.meta.env.VITE_API_URL;

interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

interface AuthUser {
  id?: string;
  email?: string;
  businessName?: string;
  sub?: string; // JWT subject (usually email)
  exp?: number; // JWT expiration timestamp
}

interface AuthState {
  tokens: AuthTokens | null;
  user: AuthUser | null;
  isAuthenticated: boolean;
  setTokens: (tokens: AuthTokens) => void;
  setUser: (user: AuthUser) => void;
  logout: () => void;
}

// Initialize auth state from localStorage if available
const getInitialState = () => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    const refreshToken = localStorage.getItem('refreshToken');
    const businessName = localStorage.getItem('userBusinessName') || '';

    if (accessToken && refreshToken) {
      // Decode the token to get user info
      try {
        const decoded = jwtDecode<AuthUser>(accessToken);

        // Create user object with business name
        const email = decoded.email || decoded.sub || '';
        const user: AuthUser = {
          id: decoded.id || decoded.sub || '',
          email: email,
          businessName: businessName,
          sub: decoded.sub,
          exp: decoded.exp
        };

        // Store email in localStorage
        if (email) {
          localStorage.setItem('userEmail', email);
        }

        return {
          tokens: { accessToken, refreshToken },
          user,
          isAuthenticated: true
        };
      } catch (e) {
        console.warn('Failed to decode token from localStorage:', e);
      }
    }
  } catch (e) {
    console.warn('Failed to access localStorage:', e);
  }

  return {
    tokens: null,
    user: null,
    isAuthenticated: false
  };
};

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      ...getInitialState(),
      setTokens: (tokens) => {
        set({ tokens, isAuthenticated: true });
        // Store the token in localStorage for API calls
        try {
          localStorage.setItem('accessToken', tokens.accessToken);
          localStorage.setItem('refreshToken', tokens.refreshToken);
        } catch (error) {
          console.warn('Could not store tokens in localStorage:', error);
        }

        // Decode and set user from access token
        try {
          if (typeof tokens.accessToken !== 'string') {
            throw new Error(`Access token must be a string, got: ${typeof tokens.accessToken}`);
          }
          const decoded = jwtDecode<AuthUser>(tokens.accessToken);

          // Get business name from localStorage if available
          const businessName = localStorage.getItem('userBusinessName') || '';

          // Extract user info from the JWT token
          const email = decoded.email || decoded.sub || '';
          const user: AuthUser = {
            id: decoded.id || decoded.sub || '',
            email: email,
            businessName: businessName || decoded.businessName || '',
            sub: decoded.sub,
            exp: decoded.exp
          };

          // Store email in localStorage
          if (email) {
            localStorage.setItem('userEmail', email);
          }

          set({ user });
        } catch (error) {
          console.error('Failed to decode token:', error instanceof Error ? error.message : 'Invalid token');
        }
      },
      setUser: (user) => {
        set({ user });

        // Save the business name and email to localStorage
        if (user) {
          try {
            if (user.businessName) {
              localStorage.setItem('userBusinessName', user.businessName);
            }
            if (user.email) {
              localStorage.setItem('userEmail', user.email);
            }
          } catch (error) {
            console.warn('Could not save user data to localStorage:', error);
          }
        }
      },
      logout: () => {
        // Clear tokens from localStorage
        try {
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
          // DO NOT remove the business name from localStorage
          // localStorage.removeItem('userBusinessName');
        } catch (error) {
          console.warn('Could not remove tokens from localStorage:', error);
        }

        // Clear state
        set({ tokens: null, user: null, isAuthenticated: false });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        tokens: state.tokens,
        user: state.user,
        isAuthenticated: state.isAuthenticated
      }),
    }
  )
);

export async function loginUser(email: string, password: string): Promise<void> {
  try {
    const response = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        password,
        business_name: null
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Login failed');
    }

    const data = await response.json();

    if (!data.access_token || !data.refresh_token) {
      throw new Error('Invalid token response from server');
    }

    useAuthStore.getState().setTokens({
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
    });
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
}

export async function registerUser(email: string, password: string, businessName: string): Promise<void> {
  try {
    const response = await fetch(`${API_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        password,
        business_name: businessName
      }),
    });

    if (!response.ok) {
      throw new Error('Registration failed');
    }

    const data = await response.json();
    useAuthStore.getState().setTokens({
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
    });
  } catch (error) {
    console.error('Registration error:', error);
    throw error;
  }
}
