const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

// Helper function to safely access localStorage
const safeGetFromStorage = (key: string): string | null => {
  try {
    return localStorage.getItem(key);
  } catch (error) {
    console.warn(`Unable to access localStorage: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return null;
  }
};

export const fetchWithAuth = async (url: string, options: RequestInit = {}) => {
  const token = safeGetFromStorage('accessToken');

  // Prepare request with authentication

  const headers = new Headers(options.headers || {});
  if (token) {
    headers.append('Authorization', `Bearer ${token}`);
  } else {
    console.warn('No auth token found for request to:', url);
    // If we're in a protected route but have no token, try to get it from the auth store
    // This helps with page refreshes where localStorage might be available but not yet loaded into state
    const { useAuthStore } = await import('./auth');
    const storeToken = useAuthStore.getState().tokens?.accessToken;

    if (storeToken) {
      // Using token from auth store
      headers.append('Authorization', `Bearer ${storeToken}`);
    }
  }

  // Headers prepared

  // Check if the URL already includes the API_URL to avoid duplication
  const fullUrl = url.startsWith('http') ? url : `${API_URL}${url}`;
  const response = await fetch(fullUrl, {
    ...options,
    headers
  });

  // If we get a 401 Unauthorized, the token might be expired
  if (response.status === 401) {
    console.warn('Received 401 Unauthorized, token might be expired');
    // You could implement token refresh logic here
    // For now, we'll just log the user out if they're on a protected route
    if (url.startsWith('/dashboard')) {
      const { useAuthStore } = await import('./auth');
      useAuthStore.getState().logout();
      window.location.href = '/login';
    }
  }

  return response;
};

// Common API functions
export const api = {
  // User management
  async login(email: string, password: string) {
    const response = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Login failed');
    }

    // Safely store the token
    try {
      const data = await response.json();
      if (data.token) {
        localStorage.setItem('accessToken', data.token);
      }
      return data;
    } catch (error) {
      console.warn('Could not store authentication token:', error);
      return await response.json();
    }
  },

  async register(email: string, password: string, businessName: string) {
    const response = await fetch(`${API_URL}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password, business_name: businessName }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Registration failed');
    }

    return response.json();
  },

  // Example function for getting data from an authenticated endpoint
  async getDashboardData() {
    const response = await fetchWithAuth('/dashboard/data');
    if (!response.ok) {
      throw new Error('Failed to fetch dashboard data');
    }
    return response.json();
  }
};
