import { useAuthStore } from './auth';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

/**
 * Types for API requests and responses
 */
export interface ApiError {
  status: number;
  message: string;
  details?: Record<string, unknown>;
}

/**
 * Safe localStorage access
 */
const safeGetFromStorage = (key: string): string | null => {
  try {
    return localStorage.getItem(key);
  } catch (error) {
    console.warn(`Unable to access localStorage: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return null;
  }
};

/**
 * API client for making authenticated requests
 */
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  /**
   * Get the authentication token
   */
  private getAuthToken(): string | null {
    // First try localStorage
    const token = safeGetFromStorage('accessToken');
    if (token) return token;

    // Fall back to auth store
    const storeToken = useAuthStore.getState().tokens?.accessToken;
    return storeToken || null;
  }

  /**
   * Get the refresh token
   */
  private getRefreshToken(): string | null {
    // First try localStorage
    const token = safeGetFromStorage('refreshToken');
    if (token) return token;

    // Fall back to auth store
    const storeToken = useAuthStore.getState().tokens?.refreshToken;
    return storeToken || null;
  }

  /**
   * Refresh the access token using the refresh token
   */
  private async refreshToken(): Promise<boolean> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      console.warn('No refresh token available');
      return false;
    }

    try {
      const response = await fetch(`${this.baseUrl}/auth/refresh-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ refresh_token: refreshToken })
      });

      if (!response.ok) {
        console.warn('Failed to refresh token:', response.statusText);
        return false;
      }

      const data = await response.json();
      if (!data.access_token) {
        console.warn('Invalid response from refresh token endpoint');
        return false;
      }

      // Update tokens in auth store and localStorage
      useAuthStore.getState().setTokens({
        accessToken: data.access_token,
        refreshToken: data.refresh_token
      });

      return true;
    } catch (error) {
      console.error('Error refreshing token:', error);
      return false;
    }
  }

  /**
   * Handle API errors
   */
  private async handleError(response: Response): Promise<ApiError> {
    let errorMessage = 'An unknown error occurred';
    let details = undefined;

    try {
      const data = await response.json();
      errorMessage = data.detail || data.message || errorMessage;
      details = data;

      // Check for 2FA required response
      if (response.status === 200 && data.requires_2fa === true) {
        // This is not an error, but a special case for 2FA
        return {
          status: 200,
          message: 'Two-factor authentication required',
          details: data
        };
      }
    } catch {
      // If we can't parse the JSON, just use the status text
      errorMessage = response.statusText;
    }

    // Handle authentication errors
    if (response.status === 401) {
      console.warn('Received 401 Unauthorized, token might be expired');
      // We'll handle token refresh in the request method
      // Only log out if we're on a protected route and it's not a token refresh request
      if (window.location.pathname.startsWith('/dashboard') &&
          !response.url.includes('/auth/refresh-token')) {
        // We'll try to refresh the token in the request method before logging out
      }
    }

    return {
      status: response.status,
      message: errorMessage,
      details
    };
  }

  /**
   * Make an authenticated request
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    let token = this.getAuthToken();

    const headers = new Headers(options.headers || {});
    headers.set('Content-Type', 'application/json');

    if (token) {
      headers.set('Authorization', `Bearer ${token}`);
    } else if (endpoint.startsWith('/dashboard')) {
      console.warn('No auth token found for protected request to:', endpoint);
    }

    const config: RequestInit = {
      ...options,
      headers,
      credentials: 'include',  // Keep this to include cookies
      mode: 'cors'
    };

    try {
      let response = await fetch(url, config);

      // If we get a 401 Unauthorized and we're not trying to refresh the token already,
      // try to refresh the token and retry the request
      if (response.status === 401 && !endpoint.includes('/auth/refresh-token')) {
        // Token expired, attempting to refresh
        const refreshSuccess = await this.refreshToken();

        if (refreshSuccess) {
          // Get the new token
          token = this.getAuthToken();
          if (token) {
            // Update the Authorization header with the new token
            headers.set('Authorization', `Bearer ${token}`);
            const newConfig = { ...config, headers };

            // Retry the request with the new token
            response = await fetch(url, newConfig);
          }
        }
      }

      if (!response.ok) {
        const error = await this.handleError(response);

        // Special case for 2FA required
        if (error.status === 200 && error.details?.requires_2fa === true) {
          return error.details as T;
        }

        throw error;
      }

      // For 204 No Content responses
      if (response.status === 204) {
        return {} as T;
      }

      // Parse the response JSON
      const data = await response.json();

      // Check for 2FA required in successful responses
      if (data.requires_2fa === true) {
        return data as T;
      }

      // Log the response for debugging purposes if it's related to 2FA
      if (endpoint.includes('/2fa/')) {
        // Log 2FA-related responses for debugging
      }

      return data as T;
    } catch (error) {
      if ((error as ApiError).status) {
        throw error;
      }

      // Network errors or other exceptions
      throw {
        status: 0,
        message: error instanceof Error ? error.message : 'Network error',
      } as ApiError;
    }
  }

  /**
   * HTTP GET request
   */
  async get<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'GET'
    });
  }

  /**
   * HTTP POST request
   */
  async post<T>(endpoint: string, data?: Record<string, unknown>, options: RequestInit = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  /**
   * HTTP PUT request
   */
  async put<T>(endpoint: string, data?: Record<string, unknown>, options: RequestInit = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  /**
   * HTTP PATCH request
   */
  async patch<T>(endpoint: string, data?: Record<string, unknown>, options: RequestInit = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  /**
   * HTTP DELETE request
   */
  async delete<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'DELETE'
    });
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient(API_URL);

// Export a hook for use in components
export function useApi() {
  return apiClient;
}
