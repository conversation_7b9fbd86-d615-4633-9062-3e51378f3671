@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

html {
  scroll-behavior: smooth;
  @apply overflow-x-hidden;
}

body {
  @apply overflow-x-hidden bg-white text-gray-900 dark:bg-gray-900 dark:text-gray-100;
}

.btn-transition {
  @apply transform transition-all duration-300 ease-in-out hover:scale-105 active:scale-95;
}

.nav-link {
  @apply relative text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300;
}

.nav-link::after {
  content: '';
  @apply absolute left-0 bottom-0 w-0 h-0.5 bg-blue-600 dark:bg-blue-400 transition-all duration-300;
}

.nav-link:hover::after {
  @apply w-full;
}

.btn-primary {
  @apply bg-blue-600 dark:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold
         transform transition-all duration-300 ease-in-out
         hover:bg-blue-700 dark:hover:bg-blue-600 hover:shadow-lg hover:-translate-y-0.5
         active:translate-y-0 active:shadow-md;
}

.btn-secondary {
  @apply bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 px-6 py-3 rounded-lg font-semibold
         transform transition-all duration-300 ease-in-out
         hover:bg-blue-50 dark:hover:bg-gray-700 hover:shadow-lg hover:-translate-y-0.5
         active:translate-y-0 active:shadow-md
         ring-2 ring-white/50 dark:ring-gray-700/50;
}

.btn-outline {
  @apply border-2 border-current px-6 py-3 rounded-lg font-semibold
         transform transition-all duration-300 ease-in-out
         hover:shadow-lg hover:-translate-y-0.5 hover:bg-white/10 dark:hover:bg-gray-800/50
         active:translate-y-0 active:shadow-md
         ring-2 ring-white/30 dark:ring-gray-700/30;
}

.btn-demo {
  @apply border-2 border-blue-400 dark:border-blue-500 text-blue-400 dark:text-blue-300 px-6 py-3 rounded-lg font-semibold
         transform transition-all duration-300 ease-in-out
         hover:border-blue-300 dark:hover:border-blue-400 hover:text-blue-300 dark:hover:text-blue-200 hover:shadow-lg hover:-translate-y-0.5
         active:translate-y-0 active:shadow-md
         bg-white/10 dark:bg-gray-800/30 backdrop-blur-sm
         ring-2 ring-blue-400/30 dark:ring-blue-500/20;
}