import { apiClient } from '../lib/apiClient';
import { clientService } from './clientService';

export interface Contact {
  id: string;
  name: string;
  avatar: string;
  lastMessage: string;
  timestamp: string;
  unread: number;
  source: 'whatsapp' | 'ai' | 'app';
  phone?: string;
  email?: string;
}

// Helper type for WhatsApp contacts from Evolution API
export interface WhatsAppContact {
  id?: string;
  jid?: string;
  name?: string;
  pushname?: string;
  phone?: string;
  avatar?: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
}

export interface Message {
  id: string;
  text: string;
  timestamp: string;
  sender: 'me' | 'them';
  status?: 'sent' | 'delivered' | 'read';
  source: 'whatsapp' | 'ai' | 'app';
  similarity?: number;
  aiResponse?: Message; // For AI messages, this contains the AI's response
}

// New interfaces for AI conversation context
export interface AIMessage {
  id: string;
  conversation_id: string;
  content: string;
  role: string;
  created_at: string;
  detected_language?: string;
  detected_intent?: string;
  message_metadata?: Record<string, unknown>;
}

export interface AIConversation {
  id: string;
  client_id: string;
  message_count: number;
  conversation_language: string;
  last_message_at?: string;
  has_booking_intent: boolean;
  booking_stage?: string;
}

export interface SearchResult {
  messages: Message[];
}

/**
 * Service for handling conversations (messages from different sources)
 */
export const conversationService = {
  /**
   * Get all contacts with their latest messages
   */
  async getWhatsAppContacts(): Promise<Contact[]> {
    try {
      // First check if Evolution API is enabled
      const evolutionApiEnabled = import.meta.env.VITE_EVOLUTION_API_ENABLED === 'true';

      // Only try the Evolution API if it's enabled
      if (evolutionApiEnabled) {
        try {

          // Use the specific instance ID associated with the user
          const instanceId = localStorage.getItem('whatsapp_instance_id');

          // If no instance ID is available, we can't fetch contacts
          if (!instanceId) {
            console.warn('No WhatsApp instance ID available, cannot fetch contacts');
            return [];
          }

          // Use the Evolution API to find contacts
          const evolutionApiUrl = `${import.meta.env.VITE_EVOLUTION_API_URL || 'http://localhost:8080'}/chat/findContacts/${instanceId}`;
          try {
            // Get the Evolution API key from environment variables
            const apiKey = import.meta.env.VITE_EVOLUTION_API_KEY;
            const response = await fetch(evolutionApiUrl, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'apikey': apiKey || ''
              }
            });

            if (!response.ok) {
              console.warn(`Evolution API returned ${response.status}: ${response.statusText}`);
              throw new Error(`Evolution API returned ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            if (data && data.contacts && Array.isArray(data.contacts)) {
              // Convert Evolution API contacts to our Contact format
              const whatsappContacts: Contact[] = data.contacts.map((contact: WhatsAppContact) => ({
                id: contact.id || contact.jid || contact.phone || `whatsapp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                name: contact.name || contact.pushname || contact.phone || 'Unknown',
                avatar: contact.avatar || '',
                lastMessage: contact.lastMessage || '',
                timestamp: contact.lastMessageTime || new Date().toISOString(),
                unread: contact.unreadCount || 0,
                source: 'whatsapp',
                phone: contact.phone || (contact.id ? contact.id.replace('@c.us', '') : '') || '',
                email: ''
              }));

              return whatsappContacts;
            }
          } catch (fetchError) {
            console.error('Error fetching from Evolution API:', fetchError);
            throw new Error(`Evolution API fetch error: ${fetchError instanceof Error ? fetchError.message : 'Unknown error'}`);
          }
        } catch (evolutionError) {
          console.error('Error fetching WhatsApp contacts from Evolution API:', evolutionError);
          // Continue to fallback method
        }
      } else {
        // Evolution API is disabled, skipping direct API call
      }

      // Check if backend WhatsApp endpoint is enabled
      const backendWhatsAppEnabled = import.meta.env.VITE_BACKEND_WHATSAPP_ENABLED === 'true';

      // Only try the backend WhatsApp endpoint if it's explicitly enabled
      if (backendWhatsAppEnabled) {
        try {
          const response = await apiClient.get<{ contacts: Contact[] }>('/dashboard/messages/whatsapp-contacts');

          if (response.contacts && response.contacts.length > 0) {
            // Ensure all contacts have the WhatsApp source
            const whatsappContacts = response.contacts.map(contact => ({
              ...contact,
              source: 'whatsapp' as 'whatsapp' | 'ai' | 'app'
            }));

            return whatsappContacts;
          }
        } catch (whatsappError) {
          // Check if this is a 404 or other expected error
          const errorObj = whatsappError as { status?: number; message?: string };
          if (errorObj.status === 404 || errorObj.status === 400) {
            console.log('Backend WhatsApp endpoint not available or not configured:', errorObj.message || 'Unknown error');
          } else {
            console.error('Error fetching WhatsApp contacts from backend:', whatsappError);
          }
          // Continue to next fallback method
        }
      } else {
        // Backend WhatsApp endpoint is disabled, skipping
      }

      // Final fallback: Get all contacts and filter for those with WhatsApp messages or phone numbers
      try {
        const response = await apiClient.get<{ contacts: Contact[] }>('/dashboard/messages/contacts');

        if (!response.contacts) {
          console.error('No contacts property in response:', response);
          return [];
        }

        // Filter for contacts that have WhatsApp as source or have a phone number
        const whatsappContacts = response.contacts.filter(contact =>
          contact.source === 'whatsapp' || (contact.phone && contact.phone.trim() !== '')
        ).map(contact => ({
          ...contact,
          source: 'whatsapp' as 'whatsapp' | 'ai' | 'app' // Ensure all contacts have WhatsApp source
        }));

        return whatsappContacts;
      } catch (error) {
        console.error('Error fetching WhatsApp contacts:', error);
        return [];
      }
    } catch (error) {
      console.error('Unexpected error in getWhatsAppContacts:', error);
      return [];
    }
  },

  async getContacts(): Promise<Contact[]> {
    try {
      const response = await apiClient.get<{ contacts: Contact[] }>('/dashboard/messages/contacts');

      if (!response.contacts) {
        console.error('No contacts property in response:', response);
        return [];
      }

      return response.contacts;
    } catch (error) {
      console.error('Error fetching contacts:', error);
      return [];
    }
  },

  // Get a single contact by ID
  async getContact(contactId: string): Promise<Contact | null> {
    try {
      // First try to get from the contacts endpoint
      const contacts = await this.getContacts();
      const contact = contacts.find(c => c.id === contactId);
      if (contact) {
        return contact;
      }

      // If not found, try to get from the client service
      try {
        const client = await clientService.getClient(contactId);
        if (client) {
          // Create a contact from the client
          const contact: Contact = {
            id: client.id,
            name: client.name,
            avatar: this.getAvatarUrl(client.name),
            lastMessage: '',
            timestamp: new Date().toISOString(),
            unread: 0,
            source: client.phone && client.phone.trim() !== '' ? 'whatsapp' : 'app',
            phone: client.phone || '',
            email: client.email || ''
          };
          return contact;
        }
      } catch (clientError) {
        console.error('Error fetching client:', clientError);
      }

      return null;
    } catch (error) {
      console.error('Error fetching contact:', error);
      return null;
    }
  },

  /**
   * Get messages for a specific contact
   * @deprecated Use _getMessages instead
   */
  async getMessagesLegacy(contactId: string, source: 'whatsapp' | 'ai' | 'app' = 'app'): Promise<Message[]> {
    try {
      // Use the appropriate endpoint based on the source
      let endpoint = `/dashboard/messages/${contactId}`;

      // Add source as a query parameter
      endpoint += `?source=${source}`;

      // Make the request with explicit headers for debugging
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };

      // Get the token from localStorage
      const token = localStorage.getItem('token');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Get the API URL from environment or use default
      const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

      // Make the request manually to debug
      const response = await fetch(`${API_URL}${endpoint}`, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        console.error(`Error response from ${endpoint}:`, response.status, response.statusText);
        return [];
      }

      try {
        const data = await response.json();
        // Check if the response is an array (some endpoints might return an array directly)
        if (Array.isArray(data)) {
          // Response is an array
          // Convert to Message[] type
          return data.map((msg: { id?: string; text?: string; body?: string; content?: string; timestamp?: string; sender?: string; fromMe?: boolean }) => ({
            id: msg.id || `msg-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
            text: msg.text || msg.body || msg.content || '',
            timestamp: msg.timestamp || new Date().toISOString(),
            sender: (msg.sender === 'me' || msg.sender === 'them') ? msg.sender : (msg.fromMe ? 'me' : 'them'),
            source: source as 'whatsapp' | 'ai' | 'app'
          }));
        }

        // Check if the response has a messages property
        if (data && typeof data === 'object') {
          // If the response has a messages property, use it
          if (data.messages) {
            return data.messages.map((msg: { id?: string; text?: string; body?: string; content?: string; timestamp?: string; sender?: string; fromMe?: boolean }) => ({
              id: msg.id || `msg-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
              text: msg.text || msg.body || msg.content || '',
              timestamp: msg.timestamp || new Date().toISOString(),
              sender: (msg.sender === 'me' || msg.sender === 'them') ? msg.sender : (msg.fromMe ? 'me' : 'them'),
              source: source as 'whatsapp' | 'ai' | 'app'
            }));
          }

          // If the response has a message property (singular), wrap it in an array
          if (data.message) {
            // Response has a message property, wrapping in array
            const msg = data.message;
            return [{
              id: msg.id || `msg-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
              text: msg.text || msg.body || msg.content || '',
              timestamp: msg.timestamp || new Date().toISOString(),
              sender: msg.sender || (msg.fromMe ? 'me' : 'them'),
              source: source as 'whatsapp' | 'ai' | 'app'
            }];
          }

          // If the response is an object but doesn't have a messages property,
          // check if it looks like a message object itself
          if (data.id && (data.text || data.body || data.content)) {
            // Response looks like a single message, wrapping in array
            return [{
              id: data.id,
              text: data.text || data.body || data.content || '',
              timestamp: data.timestamp || new Date().toISOString(),
              sender: data.sender || (data.fromMe ? 'me' : 'them'),
              source: source as 'whatsapp' | 'ai' | 'app'
            }];
          }
        }

        // If we get here, we don't know how to handle the response
        console.error(`Unknown response format from ${endpoint}:`, data);
        return [];
      } catch (jsonError) {
        console.error(`Error parsing JSON from ${endpoint}:`, jsonError);
        return [];
      }
    } catch (error) {
      console.error(`Error fetching messages for contact ${contactId} (${source}):`, error);
      return [];
    }
  },

  // Helper function to process messages with source
  processMessages(messages: Array<Record<string, unknown>>, source: 'whatsapp' | 'ai' | 'app' = 'app'): Message[] {
    if (!messages || !Array.isArray(messages)) {
      return [];
    }

    // Add source to messages if not already present and log each message
    const messagesWithSource = messages.map(message => {
      // Ensure source is set - the backend might not have the source column yet
      // so we'll default to the requested source
      const messageWithSource: Message = {
        id: typeof message.id === 'string' ? message.id : `msg-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
        text: typeof message.text === 'string' ? message.text :
              typeof message.body === 'string' ? message.body :
              typeof message.content === 'string' ? message.content : '',
        timestamp: typeof message.timestamp === 'string' ? message.timestamp : new Date().toISOString(),
        sender: (typeof message.sender === 'string' && (message.sender === 'me' || message.sender === 'them')) ? message.sender :
                (typeof message.fromMe === 'boolean' && message.fromMe) ? 'me' : 'them',
        source: (typeof message.source === 'string' && (message.source === 'whatsapp' || message.source === 'ai' || message.source === 'app')) ?
                message.source as 'whatsapp' | 'ai' | 'app' :
                source as 'whatsapp' | 'ai' | 'app'
      };

      return messageWithSource;
    });

    return messagesWithSource;
  },

  // Ensure WhatsApp instance exists and is connected
  async ensureWhatsAppInstance(): Promise<boolean> {
    try {
      // Use the dynamic instance ID associated with the user
      const instanceName = localStorage.getItem('whatsapp_instance_id');
      // If no instance ID is stored, use the user ID from the JWT token
      const token = localStorage.getItem('accessToken');
      let finalInstanceName = instanceName;

      if (!finalInstanceName && token) {
        try {
          // Simple JWT parsing (token is in format header.payload.signature)
          const payload = token.split('.')[1];
          const decodedPayload = JSON.parse(atob(payload));
          if (decodedPayload.sub) {
            finalInstanceName = decodedPayload.sub; // Use user ID as instance name
          }
        } catch (e) {
          console.error('Error parsing JWT token:', e);
        }
      }

      // Fallback to default instance ID if all else fails
      if (!finalInstanceName) {
        finalInstanceName = 'b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7';
      }

      const evolutionApiUrl = import.meta.env.VITE_EVOLUTION_API_URL || 'http://localhost:8080';

      // First, check if the instance exists
      // Check if the WhatsApp instance exists
      try {
        const checkResponse = await fetch(`${evolutionApiUrl}/instance/connectionState/${finalInstanceName}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': token ? `Bearer ${token}` : ''
          }
        });

        if (checkResponse.ok) {
          const stateData = await checkResponse.json();
          // If the instance exists but is not connected, try to connect
          if (stateData.state !== 'open') {
            // Instance exists but is not connected. Trying to connect...
            // You might need to implement reconnection logic here
            return false;
          }

          return true;
        } else if (checkResponse.status === 404) {
          // Instance doesn't exist, create it
          // Instance doesn't exist, creating it...

          const createResponse = await fetch(`${evolutionApiUrl}/instance/create`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': token ? `Bearer ${token}` : ''
            },
            body: JSON.stringify({
              instanceName: finalInstanceName,
              token: finalInstanceName,
              qrcode: true,  // Generate QR code for connection
              webhook: null,
              webhookUrl: '',
              webhookAutoDownload: false
            })
          });

          if (createResponse.ok) {
            const createData = await createResponse.json();
            // Instance created successfully

            // Store the instance name in localStorage
            localStorage.setItem('whatsapp_instance_id', finalInstanceName);

            // The instance is created but not connected yet
            // User needs to scan QR code to connect
            return false;
          } else {
            console.error(`Failed to create instance ${finalInstanceName}:`, await createResponse.text());
            return false;
          }
        } else {
          console.error(`Error checking instance ${finalInstanceName}:`, await checkResponse.text());
          return false;
        }
      } catch (error) {
        console.error(`Error ensuring WhatsApp instance ${finalInstanceName}:`, error);
        return false;
      }
    } catch (error) {
      console.error('Unexpected error in ensureWhatsAppInstance:', error);
      return false;
    }
  },

  // Get WhatsApp messages using Evolution API
  async getWhatsAppMessages(contactId: string): Promise<Message[]> {
    try {
      // Fetch WhatsApp messages from Evolution API

      // Use the specific instance ID associated with the user
      const instanceId = localStorage.getItem('whatsapp_instance_id');

      // If no instance ID is available, we can't fetch messages
      if (!instanceId) {
        console.warn('No WhatsApp instance ID available, cannot fetch messages');
        return [];
      }

      // Extract phone number from contact ID or use the ID itself
      let phoneNumber = contactId;
      // If the contact has a phone property, use that instead
      const contact = await this.getContact(contactId);
      if (contact && contact.phone) {
        phoneNumber = contact.phone;
      }

      // Format phone number for WhatsApp (remove + and add @c.us)
      phoneNumber = phoneNumber.replace(/^\+/, '');
      if (!phoneNumber.includes('@')) {
        phoneNumber = `${phoneNumber}@c.us`;
      }

      // Use the Evolution API to find messages
      const evolutionApiUrl = `${import.meta.env.VITE_EVOLUTION_API_URL || 'http://localhost:8080'}/chat/findMessages/${instanceId}`;
      // Call Evolution API to find messages

      try {
        // Get the Evolution API key from environment variables
        const apiKey = import.meta.env.VITE_EVOLUTION_API_KEY;
        const response = await fetch(evolutionApiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'apikey': apiKey || ''
          },
          body: JSON.stringify({
            phone: phoneNumber,
            count: 50, // Get the last 50 messages
            includeFromMe: true, // Include messages sent by the user
            onlyMedia: false // Include all messages, not just media
          })
        });

        if (!response.ok) {
          console.warn(`Evolution API returned ${response.status}: ${response.statusText}`);
          throw new Error(`Evolution API returned ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        // Process Evolution API response

        if (data && data.messages && Array.isArray(data.messages)) {
          // Convert Evolution API messages to our Message format
          const whatsappMessages: Message[] = data.messages.map((message: { id?: string; body?: string; content?: string; timestamp?: string; fromMe?: boolean; mediaUrl?: string; mediaType?: string }) => ({
            id: message.id || `whatsapp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            text: message.body || message.content || '',
            timestamp: message.timestamp || new Date().toISOString(),
            sender: message.fromMe ? 'me' : 'them',
            source: 'whatsapp',
            mediaUrl: message.mediaUrl || undefined,
            mediaType: message.mediaType || undefined
          }));

          // Sort messages by timestamp
          whatsappMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

          return whatsappMessages;
        }
      } catch (evolutionError) {
        console.error('Error fetching WhatsApp messages from Evolution API:', evolutionError);
      }

      // If we get here, we couldn't get messages from the Evolution API
      // No WhatsApp messages found or Evolution API not available
      return [];
    } catch (error) {
      console.error('Error in getWhatsAppMessages:', error);
      return [];
    }
  },

  // Internal method to get messages with a specific source
  async _getMessages(contactId: string, source: 'whatsapp' | 'ai' | 'app' = 'whatsapp'): Promise<Message[]> {
    try {
      // Use the appropriate endpoint based on the source
      let endpoint = `/dashboard/messages/${contactId}`;

      // Add source as a query parameter
      endpoint += `?source=${source}`;

      // Get the token from localStorage
      const token = localStorage.getItem('accessToken');
      if (!token) {
        console.warn('No authentication token found in localStorage');
        // Don't redirect here, just continue with the request and let it fail naturally
        // This prevents immediate redirects that can cause UI issues
      }

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`
      };

      // Get the API URL from environment or use default
      const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

      // Make the request
      const response = await fetch(`${API_URL}${endpoint}`, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        console.error(`Error response from ${endpoint}:`, response.status, response.statusText);
        return [];
      }

      try {
        const data = await response.json();
        // Process the response data
        if (Array.isArray(data)) {
          // If the response is an array, assume it's an array of messages
          return this.processMessages(data, source);
        } else if (data && typeof data === 'object') {
          // If the response has a messages property, use it
          if (data.messages && Array.isArray(data.messages)) {
            return this.processMessages(data.messages, source);
          }

          // If the response has a message property (singular), wrap it in an array
          if (data.message) {
            return this.processMessages([data.message], source);
          }

          // If the response looks like a message object itself
          if (data.id && (data.text || data.content)) {
            return this.processMessages([data], source);
          }
        }

        // If we get here, we don't know how to handle the response
        console.error(`Unknown response format from ${endpoint}:`, data);
        return [];
      } catch (jsonError) {
        console.error(`Error parsing JSON from ${endpoint}:`, jsonError);
        return [];
      }
    } catch (error) {
      console.error(`Error fetching messages for contact ${contactId} (${source}):`, error);
      return [];
    }
  },

  /**
   * Send a message to a contact
   */
  async sendMessage(contactId: string, text: string, source: 'whatsapp' | 'ai' | 'app' = 'app'): Promise<Message | null> {
    try {
      if (source === 'ai') {
        // For AI messages, use the AI endpoint
        const endpoint = '/ai/send-message';
        const payload = {
          client_id: contactId,
          message: text
        };

        const response = await apiClient.post<{ message: Message; aiResponse: Message; success: boolean }>(endpoint, payload);
        if (response.success) {
          // Ensure source is set on both messages
          const userMessage = {
            ...response.message,
            source: 'ai',
            aiResponse: {
              ...response.aiResponse,
              source: 'ai'
            }
          } as Message & { aiResponse: Message };

          return userMessage;
        }
      } else if (source === 'whatsapp') {
        // For WhatsApp messages
        const endpoint = `/dashboard/messages/${contactId}/send`;
        const payload = {
          text,
          source: 'whatsapp'
        };

        const response = await apiClient.post<{ message: Message }>(endpoint, payload);

        if (response.message) {
          // Ensure source is set
          return {
            ...response.message,
            source: 'whatsapp'
          };
        }
      } else {
        // For regular app messages
        const endpoint = `/dashboard/messages/${contactId}/send`;
        const payload = {
          text,
          source: 'app'
        };

        const response = await apiClient.post<{ message: Message }>(endpoint, payload);

        if (response.message) {
          // Ensure source is set
          return {
            ...response.message,
            source: 'app'
          };
        }
      }

      return null;
    } catch (error) {
      console.error(`Error sending message to contact ${contactId}:`, error);
      return null;
    }
  },

  /**
   * Mark messages as read
   */
  async markAsRead(contactId: string): Promise<boolean> {
    try {
      await apiClient.post(`/dashboard/messages/${contactId}/read`, {});
      return true;
    } catch (error) {
      console.error(`Error marking messages as read for contact ${contactId}:`, error);
      return false;
    }
  },

  /**
   * Search messages using vector similarity
   */
  async searchMessages(query: string, limit: number = 10): Promise<Message[]> {
    try {
      const response = await apiClient.post<SearchResult>('/dashboard/messages/search', {
        query,
        limit
      });

      return response.messages;
    } catch (error) {
      console.error('Error searching messages:', error);
      return [];
    }
  },

  /**
   * Get all AI conversations
   */
  async getAIConversations(limit = 20, offset = 0): Promise<AIConversation[]> {
    try {
      return await apiClient.get<AIConversation[]>(`/conversations?limit=${limit}&offset=${offset}`);
    } catch (error) {
      console.error('Error getting AI conversations:', error);
      return [];
    }
  },

  /**
   * Get a specific AI conversation
   */
  async getAIConversation(id: string): Promise<AIConversation | null> {
    try {
      return await apiClient.get<AIConversation>(`/conversations/${id}`);
    } catch (error) {
      console.error('Error getting AI conversation:', error);
      return null;
    }
  },

  /**
   * Get messages for an AI conversation
   */
  async getAIConversationMessages(conversationId: string, limit = 50, offset = 0): Promise<AIMessage[]> {
    try {
      return await apiClient.get<AIMessage[]>(`/conversations/${conversationId}/messages?limit=${limit}&offset=${offset}`);
    } catch (error) {
      console.error('Error getting AI conversation messages:', error);
      return [];
    }
  },

  /**
   * Add a message to an AI conversation
   */
  async addAIMessage(conversationId: string, content: string, role: string, metadata?: Record<string, unknown>): Promise<AIMessage | null> {
    try {
      return await apiClient.post<AIMessage>(`/conversations/${conversationId}/messages`, {
        content,
        role,
        metadata
      });
    } catch (error) {
      console.error('Error adding AI message:', error);
      return null;
    }
  },

  /**
   * Send a message to the AI and get a response
   */
  async sendMessageToAI(clientId: string, message: string): Promise<{ response: string; action?: Record<string, unknown> }> {
    try {
      const result = await apiClient.post<{ response: string; action?: Record<string, unknown> }>('/ai/message', {
        client_id: clientId,
        message
      });
      return result;
    } catch (error) {
      console.error('Error sending message to AI:', error);
      throw error;
    }
  },

  /**
   * Get avatar URL for a contact
   */
  getAvatarUrl(name: string): string {
    // Generate a consistent color based on the name
    const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const hue = hash % 360;

    // Return a placeholder avatar with the first letter of the name
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=${hue.toString(16)}0&color=fff`;
  }
};
