/**
 * AI Image Analysis Service
 * This service provides functions for analyzing images using AI.
 */

import { apiClient } from '../lib/apiClient';

/**
 * Response from the AI image analysis
 */
export interface ImageAnalysisResponse {
  description: string;
  tags: string[];
  objects: string[];
  text?: string;
  error?: string;
}

/**
 * Analyze an image using AI
 * @param imageUrl URL of the image to analyze
 * @returns Promise with analysis results
 */
export async function analyzeImage(imageUrl: string): Promise<ImageAnalysisResponse> {
  try {
    // Call the backend API to analyze the image
    const response = await apiClient.post<ImageAnalysisResponse>('/ai/analyze-image', {
      imageUrl
    });
    
    return response;
  } catch (error) {
    console.error('Error analyzing image:', error);
    return {
      description: 'Failed to analyze image',
      tags: [],
      objects: [],
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Generate a response to an image message
 * @param imageUrl URL of the image
 * @param prompt Optional prompt to guide the AI response
 * @returns Promise with AI response text
 */
export async function generateImageResponse(imageUrl: string, prompt?: string): Promise<string> {
  try {
    // First analyze the image
    const analysis = await analyzeImage(imageUrl);
    
    // If there was an error, return a generic response
    if (analysis.error) {
      return "I received your image, but I couldn't analyze it properly. Could you please describe what's in the image?";
    }
    
    // Call the backend API to generate a response based on the analysis
    const response = await apiClient.post<{ response: string }>('/ai/generate-image-response', {
      imageUrl,
      analysis,
      prompt
    });
    
    return response.response;
  } catch (error) {
    console.error('Error generating image response:', error);
    return "I received your image, but I'm having trouble generating a response. Could you please provide more context?";
  }
}

export default {
  analyzeImage,
  generateImageResponse
};
