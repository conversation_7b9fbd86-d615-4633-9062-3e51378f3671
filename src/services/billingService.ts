import { apiClient } from '../lib/apiClient';

export interface PricingPlan {
  id: string;
  name: string;
  monthlyFee: number;
  features: string[];
  aiResponsesLimit: number;
  appointmentsLimit: number | null; // null means unlimited
}

export interface BillingInfo {
  currentPlan: PricingPlan;
  renewalDate: string;
  aiResponsesUsed: number;
  aiResponsesLimit: number;
  paymentMethod: {
    type: string;
    last4: string;
    expiryMonth: number;
    expiryYear: number;
  };
}

export interface Invoice {
  id: string;
  date: string;
  amount: number;
  status: 'paid' | 'pending' | 'failed';
  type: 'subscription' | 'usage';
  invoiceUrl: string;
  // Stripe-specific fields
  stripe_invoice_id?: string;
  hosted_invoice_url?: string;
  invoice_pdf?: string;
  // Additional fields that might be returned from the API
  period_start?: string;
  period_end?: string;
  plan_id?: string;
}

// Default plan values used in the mockBillingInfo object

// Default billing info for fallback
const mockBillingInfo: BillingInfo = {
  currentPlan: {
    id: '1',
    name: 'Starter',
    monthlyFee: 50,
    features: ['Up to 50 appointments/month', '500 AI responses/month', 'Email support'],
    aiResponsesLimit: 500,
    appointmentsLimit: 50
  },
  renewalDate: new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString(),
  aiResponsesUsed: 345,
  aiResponsesLimit: 500,
  paymentMethod: {
    type: 'visa',
    last4: '4242',
    expiryMonth: 12,
    expiryYear: 2025
  }
};

// Empty array for fallback when no invoices are available
const mockInvoices: Invoice[] = [];

/**
 * Billing service for managing subscription plans and invoices
 */
export const billingService = {
  /**
   * Get all available pricing plans
   */
  async getPlans(): Promise<PricingPlan[]> {
    try {
      // Try to get plans from the API
      try {
        const plans = await apiClient.get<PricingPlan[]>('/dashboard/billing/plans');
        return plans;
      } catch (apiError) {
        console.warn('Could not fetch plans from API, using default plans:', apiError);
        // Fall back to default plans if API call fails
        return [
          {
            id: '1',
            name: 'Starter',
            monthlyFee: 50,
            features: ['Up to 50 appointments/month', '500 AI responses/month', 'Email support'],
            aiResponsesLimit: 500,
            appointmentsLimit: 50
          },
          {
            id: '2',
            name: 'Professional',
            monthlyFee: 100,
            features: ['Unlimited appointments', '5,000 AI responses/month', 'Priority support'],
            aiResponsesLimit: 5000,
            appointmentsLimit: null
          },
          {
            id: '3',
            name: 'Enterprise',
            monthlyFee: 0, // Custom pricing
            features: ['Unlimited appointments', 'Unlimited AI responses', '24/7 support', 'Custom integrations'],
            aiResponsesLimit: 999999,
            appointmentsLimit: null
          }
        ];
      }
    } catch (error) {
      console.error('Error fetching pricing plans:', error);
      throw error;
    }
  },

  /**
   * Get the current user's billing information
   */
  async getBillingInfo(): Promise<BillingInfo> {
    try {
      // Try to get billing info from the API
      try {
        const billingInfo = await apiClient.get<BillingInfo>('/dashboard/billing/info');
        return billingInfo;
      } catch (apiError) {
        console.warn('Could not fetch billing info from API, using mock data:', apiError);
        // Fall back to mock data if API call fails
        // Ensure the AI response limit matches the current plan
        const planName = mockBillingInfo.currentPlan.name;
        if (planName === 'Starter') {
          mockBillingInfo.aiResponsesLimit = 500;
          mockBillingInfo.aiResponsesUsed = 345;
        } else if (planName === 'Professional') {
          mockBillingInfo.aiResponsesLimit = 5000;
          mockBillingInfo.aiResponsesUsed = 3245;
        } else if (planName === 'Enterprise') {
          mockBillingInfo.aiResponsesLimit = 999999; // Unlimited
          mockBillingInfo.aiResponsesUsed = 5000;
        }
        return mockBillingInfo;
      }
    } catch (error) {
      console.error('Error fetching billing info:', error);
      throw error;
    }
  },

  /**
   * Get the user's invoice history
   */
  async getInvoices(): Promise<Invoice[]> {
    try {
      // Try to get invoices from the API
      try {
        const invoices = await apiClient.get<Invoice[]>('/dashboard/billing/invoices');
        return invoices;
      } catch (apiError) {
        console.warn('Could not fetch invoices from API, using mock data:', apiError);
        // Fall back to mock data if API call fails
        return mockInvoices;
      }
    } catch (error) {
      console.error('Error fetching invoices:', error);
      throw error;
    }
  },

  /**
   * Change the user's subscription plan
   */
  async changePlan(planId: string): Promise<BillingInfo> {
    try {
      // Try to change plan via the API
      try {
        const updatedBillingInfo = await apiClient.post<BillingInfo>('/dashboard/billing/change-plan', { planId });
        return updatedBillingInfo;
      } catch (apiError) {
        console.warn('Could not change plan via API, using default plans:', apiError);
        // Fall back to default plans if API call fails

        // Get the default plans
        const defaultPlans = await this.getPlans();

        // Find the requested plan
        const newPlan = defaultPlans.find((plan: PricingPlan) => plan.id === planId);
        if (!newPlan) {
          throw new Error('Plan not found');
        }

        // Update the AI response limit based on the new plan
        let aiResponsesLimit = 500; // Default to Starter plan
        if (newPlan.name === 'Professional') {
          aiResponsesLimit = 5000;
        } else if (newPlan.name === 'Enterprise') {
          aiResponsesLimit = 999999; // Unlimited
        }

        const updatedBillingInfo = {
          ...mockBillingInfo,
          currentPlan: newPlan,
          aiResponsesLimit: aiResponsesLimit
        };

        return updatedBillingInfo;
      }
    } catch (error) {
      console.error('Error changing plan:', error);
      throw error;
    }
  },

  /**
   * Update the user's payment method
   */
  async updatePaymentMethod(paymentMethodId: string): Promise<BillingInfo> {
    try {
      // Try to update payment method via the API
      try {
        const updatedBillingInfo = await apiClient.post<BillingInfo>('/dashboard/billing/update-payment', { paymentMethodId });
        return updatedBillingInfo;
      } catch (apiError) {
        console.warn('Could not update payment method via API, using mock data:', apiError);
        // Fall back to mock data if API call fails
        return mockBillingInfo;
      }
    } catch (error) {
      console.error('Error updating payment method:', error);
      throw error;
    }
  },

  /**
   * Get a specific invoice by ID
   */
  async getInvoice(invoiceId: string): Promise<Invoice> {
    try {
      // Try to get invoice from the API
      try {
        const invoice = await apiClient.get<Invoice>(`/dashboard/billing/invoices/${invoiceId}`);
        return invoice;
      } catch (apiError) {
        console.warn('Could not fetch invoice from API:', apiError);
        // If we can't get the invoice from the API, throw an error
        throw new Error('Invoice not found. Please check your connection and try again.');
      }
    } catch (error) {
      console.error('Error fetching invoice:', error);
      throw error;
    }
  },

  /**
   * Download an invoice by ID
   *
   * @param invoiceId The ID of the invoice to download
   * @returns An object containing invoice download URLs or a message
   *
   * This will return both invoice_pdf and hosted_invoice_url for Stripe invoices
   */
  async downloadInvoice(invoiceId: string): Promise<{ invoice_pdf: string | null; hosted_invoice_url: string | null; message?: string }> {
    try {

      // For real invoices, try to get invoice download URL from the API
      const response = await apiClient.get<{ invoice_pdf: string | null; hosted_invoice_url: string | null; message?: string }>(
        `/dashboard/billing/invoices/${invoiceId}/download`
      );
      return response;
    } catch (error: unknown) {
      console.error('Error downloading invoice:', error);

      // Handle API errors
      if (error && typeof error === 'object' && 'response' in error) {
        const apiError = error as { response?: { status?: number; statusText?: string; data?: { detail?: string } } };

        // If the error is related to Stripe not being configured, provide a more helpful message
        if (apiError.response?.status === 501 &&
            apiError.response?.data?.detail?.includes('Stripe')) {
          throw new Error('Stripe is not configured. Please add your Stripe API keys to the .env file.');
        }

        // For other API errors, provide a generic message
        if (apiError.response?.status) {
          throw new Error(`Failed to download invoice: ${apiError.response.status} ${apiError.response.statusText || ''}`);
        }
      }

      throw error;
    }
  },

  /**
   * Create a Stripe checkout session
   * @param planId The ID of the plan to subscribe to
   * @param successUrl The URL to redirect to after successful payment
   * @param cancelUrl The URL to redirect to if payment is canceled
   * @param isAnnual Whether to use annual billing (default: false)
   * @returns An object containing the checkout URL
   */
  async createCheckoutSession(planId: string, successUrl: string, cancelUrl: string, isAnnual: boolean = false): Promise<{ url: string }> {
    try {
      const response = await apiClient.post<{ url: string }>('/dashboard/billing/checkout', {
        plan_id: planId,
        success_url: successUrl,
        cancel_url: cancelUrl,
        is_annual: isAnnual
      });
      return response;
    } catch (error: unknown) {
      console.error('Error creating checkout session:', error);

      // Handle API errors
      if (error && typeof error === 'object' && 'response' in error) {
        const apiError = error as { response?: { status?: number; statusText?: string; data?: { detail?: string } } };

        // If the error is related to Stripe not being configured, provide a more helpful message
        if (apiError.response?.status === 501 &&
            apiError.response?.data?.detail?.includes('Stripe')) {
          throw new Error('Stripe is not configured. Please add your Stripe API keys to the .env file.');
        }
      }

      throw error;
    }
  },

  /**
   * Create a Stripe customer portal session
   */
  async createCustomerPortalSession(returnUrl: string): Promise<{ url: string }> {
    try {
      const response = await apiClient.post<{ url: string }>('/dashboard/billing/portal', {
        return_url: returnUrl
      });
      return response;
    } catch (error: unknown) {
      console.error('Error creating portal session:', error);

      // Handle API errors
      if (error && typeof error === 'object' && 'response' in error) {
        const apiError = error as { response?: { status?: number; statusText?: string; data?: { detail?: string } } };

        // If the error is related to Stripe not being configured, provide a more helpful message
        if (apiError.response?.status === 501 &&
            apiError.response?.data?.detail?.includes('Stripe')) {
          throw new Error('Stripe is not configured. Please add your Stripe API keys to the .env file.');
        }
      }

      throw error;
    }
  }
};
