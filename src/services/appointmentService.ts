import { apiClient } from '../lib/apiClient';
import { Appointment } from '../types/api';
import { clientService } from './clientService';

export interface AppointmentFormData {
  client_id: string;
  service_id: string;
  start_time: string;
  end_time: string;
  notes?: string;
  status?: 'PENDING' | 'CONFIRMED' | 'CANCELLED' | 'RESCHEDULED';

  // Google Calendar fields
  google_event_id?: string;
  synced_with_google?: boolean;

  // Recurrence fields
  is_recurring?: boolean;
  recurrence_frequency?: 'DAILY' | 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY' | 'YEARLY';
  recurrence_interval?: number;
  recurrence_end_date?: string;
  recurrence_count?: number;
  recurrence_days?: string[];
  recurrence_parent_id?: string;

  // Conflict fields
  has_conflict?: boolean;
  conflict_notes?: string;
}

/**
 * Appointment service for managing appointments
 */
export const appointmentService = {
  /**
   * Get a list of appointments
   */
  async getAppointments(params: {
    start_date?: string;
    end_date?: string;
    client_id?: string;
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<{ items: Appointment[]; total: number; page: number; limit: number }> {
    // Build query parameters
    const queryParams = new URLSearchParams();

    if (params.start_date) queryParams.append('start_date', params.start_date);
    if (params.end_date) queryParams.append('end_date', params.end_date);
    if (params.client_id) queryParams.append('client_id', params.client_id);
    if (params.status) queryParams.append('status', params.status);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());

    const queryString = queryParams.toString();
    const endpoint = `/dashboard/appointments${queryString ? `?${queryString}` : ''}`;

    try {
      return await apiClient.get<{ items: Appointment[]; total: number; page: number; limit: number }>(endpoint);
    } catch (error) {
      console.error('Error fetching appointments:', error);
      // Return empty result if API fails
      return { items: [], total: 0, page: 1, limit: 10 };
    }
  },

  /**
   * Get an appointment by ID
   */
  async getAppointment(id: string): Promise<Appointment> {
    return apiClient.get<Appointment>(`/dashboard/appointments/${id}`);
  },

  /**
   * Create a new appointment
   */
  async createAppointment(appointment: AppointmentFormData): Promise<Appointment> {
    // Try to get client name for the title
    let clientName = '';
    try {
      // Use the clientService to get the client
      const client = await clientService.getClient(appointment.client_id);
      if (client && client.name) {
        clientName = client.name;
        // Found client name for appointment
      } else {
        console.warn('Client not found or has no name');
      }
    } catch (clientError) {
      console.error('Error fetching client name:', clientError);
    }

    // Try to get service name
    let serviceName = '';
    try {
      // Import the service service
      const { serviceService } = await import('./serviceService');
      const service = await serviceService.getService(appointment.service_id);
      if (service && service.name) {
        serviceName = service.name;
        // Found service name for appointment
      } else {
        console.warn('Service not found or has no name');
      }
    } catch (serviceError) {
      console.warn('Could not fetch service name:', serviceError);
    }

    // Create title with client and service name
    let title = 'New Appointment';
    if (clientName && serviceName) {
      title = `${clientName} - ${serviceName}`;
    } else if (clientName) {
      title = `${clientName} - Appointment`;
    } else if (serviceName) {
      title = `${serviceName} Appointment`;
    }
    try {
      // Make sure status is uppercase and default to CONFIRMED
      // The backend expects status to be one of: CONFIRMED, PENDING, CANCELLED, RESCHEDULED
      const status = appointment.status ? appointment.status.toUpperCase() : 'CONFIRMED';
      const formattedAppointment = {
        ...appointment,
        title: title,
        status: status,
        start_time: new Date(appointment.start_time).toISOString(),
        end_time: new Date(appointment.end_time).toISOString()
      };

      const createdAppointment = await apiClient.post<Appointment>('/dashboard/appointments', formattedAppointment);

      return createdAppointment;
    } catch (error) {
      console.error('Error creating appointment:', error);

      // Fallback to mock implementation if API fails
      // Using mock implementation as fallback

      // Even for mock data, use the proper title format
      let mockTitle = 'New Appointment';
      if (clientName) {
        mockTitle = `${clientName} - Appointment`;
        if (serviceName) {
          mockTitle = `${clientName} - ${serviceName}`;
        }
      }

      // Create a mock appointment with a consistent format
      const mockAppointment = {
        id: 'mock-' + Math.random().toString(36).substring(2, 9),
        user_id: 'current-user',
        client_id: appointment.client_id,
        service_id: appointment.service_id,
        title: mockTitle,
        description: appointment.notes || '',
        start_time: appointment.start_time,
        end_time: appointment.end_time,
        status: 'CONFIRMED', // Always set to CONFIRMED
        created_at: new Date().toISOString()
      };

      return mockAppointment;
    }
  },

  /**
   * Check for appointment conflicts
   */
  async checkConflicts(start_time: string, end_time: string, appointment_id?: string): Promise<{ has_conflicts: boolean; conflicts: Appointment[] }> {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('start_time', start_time);
      queryParams.append('end_time', end_time);
      if (appointment_id) queryParams.append('appointment_id', appointment_id);

      return await apiClient.get<{ has_conflicts: boolean; conflicts: Appointment[] }>(`/dashboard/appointments/check-conflicts?${queryParams.toString()}`);
    } catch (error) {
      console.error('Error checking for conflicts:', error);
      // Return empty result if API fails
      return { has_conflicts: false, conflicts: [] };
    }
  },

  /**
   * Sync appointments with Google Calendar
   */
  async syncWithGoogleCalendar(): Promise<{ synced: number; success: boolean }> {
    try {
      return await apiClient.post<{ synced: number; success: boolean }>('/dashboard/appointments/sync-with-google', {});
    } catch (error) {
      console.error('Error syncing with Google Calendar:', error);
      // Return failure result if API fails
      return { synced: 0, success: false };
    }
  },

  /**
   * Update an existing appointment
   */
  async updateAppointment(id: string, appointment: Partial<AppointmentFormData>): Promise<Appointment> {
    return apiClient.put<Appointment>(`/dashboard/appointments/${id}`, appointment);
  },

  /**
   * Delete an appointment
   */
  async deleteAppointment(id: string): Promise<{ success: boolean }> {
    return apiClient.delete<{ success: boolean }>(`/dashboard/appointments/${id}`);
  },

  /**
   * Convert API appointment to calendar appointment
   */
  async toCalendarAppointment(appointment: Appointment): Promise<{
    id: string;
    title: string;
    start: string;
    end: string;
    status: string;
    allDay?: boolean;
    source?: string;
    googleEventId?: string;
    googleLink?: string;
  }> {
    // Try to get client name
    let clientName = '';
    try {
      const client = await clientService.getClient(appointment.client_id);
      if (client && client.name) {
        clientName = client.name;
      }
    } catch (error) {
      console.warn('Could not fetch client name for calendar appointment:', error);
    }

    // Try to get service name
    let serviceName = '';
    try {
      if (appointment.service_id) {
        const { serviceService } = await import('./serviceService');
        const service = await serviceService.getService(appointment.service_id);
        if (service && service.name) {
          serviceName = service.name;
        }
      }
    } catch (error) {
      console.warn('Could not fetch service name for calendar appointment:', error);
    }

    // Always create a better title with client and service
    let title = appointment.title;
    // Always use the client and service name if available
    if (clientName && serviceName) {
      title = `${clientName} - ${serviceName}`;
    } else if (clientName) {
      title = `${clientName} - Appointment`;
    } else if (serviceName) {
      title = `${serviceName} Appointment`;
    } else if (!title || title === 'Untitled Appointment' || title === 'New Appointment' || title === 'Appointment with Client' || title === 'Basic Haircut Appointment') {
      title = 'Untitled Appointment';
    }

    // Create Date objects from the appointment times
    const startDate = new Date(appointment.start_time);
    const endDate = new Date(appointment.end_time);

    // Ensure status is properly set
    let status = 'CONFIRMED';
    if (appointment.status) {
      // If it's a string, convert to uppercase
      if (typeof appointment.status === 'string') {
        status = appointment.status.toUpperCase();
      } else {
        // If it's an object with a value property (enum), use that
        status = appointment.status.value || appointment.status.toString();
      }
    }

    return {
      id: appointment.id,
      title: title,
      start: startDate,
      end: endDate,
      client: clientName || 'Unknown client',
      clientId: appointment.client_id, // Keep the ID for reference
      service: serviceName || appointment.description || '',
      status: status,
      source: 'app',
      googleEventId: appointment.googleEventId,
      googleLink: appointment.googleLink
    };
  }
};
