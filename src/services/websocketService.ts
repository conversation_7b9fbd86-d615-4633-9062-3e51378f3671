/**
 * WebSocket Service
 * This service handles real-time communication with the backend
 */

import { useAuthStore } from '../lib/auth';

// Event types
export enum WebSocketEventType {
  WHATSAPP_MESSAGE = 'whatsapp_message',
  WHATSAPP_STATUS = 'whatsapp_status',
  APPOINTMENT_UPDATE = 'appointment_update',
  NOTIFICATION = 'notification'
}

// Event handlers
type EventHandler = (data: Record<string, unknown>) => void;

class WebSocketService {
  private socket: WebSocket | null = null;
  private eventHandlers: Map<string, Set<EventHandler>> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private isConnecting = false;

  /**
   * Connect to the WebSocket server
   * @returns Promise that resolves when connected or rejects on error
   */
  public connect(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (this.socket?.readyState === WebSocket.OPEN) {
        resolve(true);
        return;
      }

      if (this.isConnecting) {
        resolve(false);
        return;
      }

      this.isConnecting = true;

      // Get the access token
      const token = useAuthStore.getState().tokens?.accessToken;
      if (!token) {
        console.error('No authentication token found, cannot connect to WebSocket');
        this.isConnecting = false;
        reject(new Error('No authentication token found'));
        return;
      }

      // Create the WebSocket connection
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      let host = import.meta.env.VITE_API_URL?.replace(/^https?:\/\//, '') || 'localhost:8000';

      // Remove any trailing slash from the host
      if (host.endsWith('/')) {
        host = host.slice(0, -1);
      }

      const wsUrl = `${protocol}//${host}/ws?token=${token}`;

      try {
        this.socket = new WebSocket(wsUrl);

        // Set up event handlers
        this.socket.onopen = (event) => {
          this.handleOpen(event);
          resolve(true);
        };
        this.socket.onmessage = this.handleMessage.bind(this);
        this.socket.onclose = this.handleClose.bind(this);
        this.socket.onerror = (event) => {
          this.handleError(event);
          this.isConnecting = false;
          reject(new Error('WebSocket connection error'));
        };
      } catch (error) {
        console.error('Error creating WebSocket connection:', error);
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * Disconnect from the WebSocket server
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    this.reconnectAttempts = 0;
    this.isConnecting = false;
  }

  /**
   * Subscribe to an event
   * @param eventType Event type to subscribe to
   * @param handler Handler function to call when the event occurs
   * @returns Unsubscribe function
   */
  public subscribe(eventType: WebSocketEventType, handler: EventHandler): () => void {
    // Get or create the set of handlers for this event type
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set());
    }

    // Add the handler
    const handlers = this.eventHandlers.get(eventType)!;
    handlers.add(handler);

    // Return an unsubscribe function
    return () => {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.eventHandlers.delete(eventType);
      }
    };
  }

  /**
   * Send a message to the WebSocket server
   * @param eventType Event type
   * @param data Data to send
   * @returns True if the message was sent, false otherwise
   */
  public send(eventType: WebSocketEventType, data: Record<string, unknown>): boolean {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      console.error('WebSocket is not connected');
      return false;
    }

    try {
      this.socket.send(JSON.stringify({
        event: eventType,
        data
      }));
      return true;
    } catch (error) {
      console.error('Error sending WebSocket message:', error);
      return false;
    }
  }

  /**
   * Check if the WebSocket is connected
   * @returns True if connected, false otherwise
   */
  public isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }

  /**
   * Handle WebSocket open event
   */
  private handleOpen(): void {
    console.log('WebSocket connected');
    this.reconnectAttempts = 0;
    this.isConnecting = false;
  }

  /**
   * Handle WebSocket message event
   * @param event WebSocket message event
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message = JSON.parse(event.data);
      const eventType = message.event;
      const data = message.data;

      // Notify all handlers for this event type
      if (this.eventHandlers.has(eventType)) {
        const handlers = this.eventHandlers.get(eventType)!;
        handlers.forEach(handler => {
          try {
            handler(data);
          } catch (error) {
            console.error(`Error in WebSocket event handler for ${eventType}:`, error);
          }
        });
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  /**
   * Handle WebSocket close event
   * @param event WebSocket close event
   */
  private handleClose(event: CloseEvent): void {
    console.log(`WebSocket disconnected: ${event.code} ${event.reason}`);
    this.socket = null;
    this.isConnecting = false;

    // Attempt to reconnect if not a normal closure
    if (event.code !== 1000 && event.code !== 1001) {
      this.attemptReconnect();
    }
  }

  /**
   * Handle WebSocket error event
   * @param event WebSocket error event
   */
  private handleError(event: Event): void {
    console.error('WebSocket error:', event);
    this.isConnecting = false;
  }

  /**
   * Attempt to reconnect to the WebSocket server
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error(`Failed to reconnect after ${this.maxReconnectAttempts} attempts`);
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    this.reconnectTimeout = setTimeout(() => {
      this.connect();
    }, delay);
  }
}

// Create and export a singleton instance
export const websocketService = new WebSocketService();

// Export a hook for use in components
export function useWebSocket() {
  return websocketService;
}
