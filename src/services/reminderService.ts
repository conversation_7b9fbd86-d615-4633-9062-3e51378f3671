import { apiClient } from '../lib/apiClient';
// WhatsAppMessage is imported but not used in this file
// WhatsAppMessage is imported from the WhatsApp service
import { whatsappService, WhatsAppMessage } from './whatsappService';
import { Appointment } from '../types/api';
import { addToast } from '../components/ui';

export interface ReminderSettings {
  enabled: boolean;
  reminderTimes: number[]; // Hours before appointment (e.g., [24, 1] for 24 hours and 1 hour before)
  messageTemplate: string;
  includeLocation: boolean;
  includeServiceDetails: boolean;
}

export interface ReminderResponse {
  success: boolean;
  message: string;
  details?: Record<string, unknown>;
}

/**
 * Service for managing appointment reminders
 */
export const reminderService = {
  /**
   * Get reminder settings for the current user
   */
  async getSettings(): Promise<ReminderSettings> {
    try {
      return await apiClient.get<ReminderSettings>('/dashboard/reminder-settings');
    } catch (error) {
      console.error('Error getting reminder settings:', error);
      // Return default settings if API call fails
      return {
        enabled: true,
        reminderTimes: [24, 1], // Default: 24 hours and 1 hour before
        messageTemplate: 'Hi {{clientName}}, this is a reminder for your appointment on {{date}} at {{time}}. Please let us know if you need to reschedule.',
        includeLocation: true,
        includeServiceDetails: true
      };
    }
  },

  /**
   * Update reminder settings
   */
  async updateSettings(settings: ReminderSettings): Promise<ReminderSettings> {
    try {
      return await apiClient.put<ReminderSettings>('/dashboard/reminder-settings', settings);
    } catch (error) {
      console.error('Error updating reminder settings:', error);
      // Return the settings that were passed in, so the UI doesn't break
      return settings;
    }
  },

  /**
   * Send a reminder for a specific appointment
   */
  async sendReminder(appointmentId: string): Promise<ReminderResponse> {
    try {
      return await apiClient.post<ReminderResponse>(`/dashboard/appointments/${appointmentId}/send-reminder`);
    } catch (error) {
      console.error('Error sending reminder:', error);
      return {
        success: false,
        message: 'Failed to send reminder'
      };
    }
  },

  /**
   * Send a test reminder
   */
  async sendTestReminder(phone: string): Promise<ReminderResponse> {
    try {
      return await apiClient.post<ReminderResponse>('/dashboard/send-test-reminder', { phone });
    } catch (error) {
      console.error('Error sending test reminder:', error);
      return {
        success: false,
        message: 'Failed to send test reminder'
      };
    }
  },

  /**
   * Check for upcoming appointments and send reminders if needed
   * This is a client-side implementation that can be called periodically
   */
  async checkAndSendReminders(): Promise<void> {
    try {
      // Get reminder settings
      const settings = await this.getSettings();

      if (!settings.enabled) {
        console.log('Reminders are disabled');
        return;
      }

      // Get upcoming appointments that need reminders
      const response = await apiClient.get<{ appointments: Appointment[] }>('/dashboard/appointments/upcoming-reminders');
      const appointments = response.appointments;

      if (appointments.length === 0) {
        console.log('No upcoming appointments need reminders');
        return;
      }

      console.log(`Found ${appointments.length} appointments that need reminders`);

      // Send reminders for each appointment
      for (const appointment of appointments) {
        await this.sendReminder(appointment.id);
      }

      addToast(`Sent reminders for ${appointments.length} upcoming appointments`, 'success');
    } catch (error) {
      console.error('Error checking and sending reminders:', error);
      addToast('Failed to send appointment reminders', 'error');
    }
  },

  /**
   * Format a reminder message using the template and appointment details
   */
  formatReminderMessage(
    template: string,
    appointment: {
      clientName: string;
      date: string;
      time: string;
      service?: string;
      location?: string;
    }
  ): string {
    let message = template;

    // Replace placeholders with actual values
    message = message.replace(/{{clientName}}/g, appointment.clientName);
    message = message.replace(/{{date}}/g, appointment.date);
    message = message.replace(/{{time}}/g, appointment.time);

    if (appointment.service) {
      message = message.replace(/{{service}}/g, appointment.service);
    }

    if (appointment.location) {
      message = message.replace(/{{location}}/g, appointment.location);
    }

    return message;
  },

  /**
   * Send a WhatsApp reminder directly (client-side implementation)
   */
  async sendWhatsAppReminder(
    phone: string,
    appointment: {
      clientName: string;
      date: string;
      time: string;
      service?: string;
      location?: string;
    }
  ): Promise<ReminderResponse> {
    try {
      // Get reminder settings to use the template
      const settings = await this.getSettings();

      // Format the message using the template
      const message = this.formatReminderMessage(settings.messageTemplate, appointment);

      // Send the WhatsApp message
      const result = await whatsappService.sendMessage({
        phone,
        message
      });

      return {
        success: result.success,
        message: result.message,
        details: result.details
      };
    } catch (error) {
      console.error('Error sending WhatsApp reminder:', error);
      return {
        success: false,
        message: 'Failed to send WhatsApp reminder'
      };
    }
  }
};
