import { DashboardConfig, DEFAULT_DASHBOARD_CONFIG } from '../types/dashboard';
import { v4 as uuidv4 } from 'uuid';

// Local storage key for dashboard configurations
const DASHBOARD_CONFIGS_KEY = 'dashboard_configs';
const ACTIVE_CONFIG_KEY = 'active_dashboard_config';

export const dashboardConfigService = {
  /**
   * Get all dashboard configurations
   */
  getConfigurations(): DashboardConfig[] {
    try {
      const configsJson = localStorage.getItem(DASHBOARD_CONFIGS_KEY);
      if (!configsJson) {
        // If no configurations exist, create the default one
        const defaultConfig = DEFAULT_DASHBOARD_CONFIG;
        this.saveConfiguration(defaultConfig);
        return [defaultConfig];
      }
      
      return JSON.parse(configsJson);
    } catch (error) {
      console.error('Error getting dashboard configurations:', error);
      return [DEFAULT_DASHBOARD_CONFIG];
    }
  },
  
  /**
   * Get active dashboard configuration
   */
  getActiveConfiguration(): DashboardConfig {
    try {
      const configs = this.getConfigurations();
      const activeConfigId = localStorage.getItem(ACTIVE_CONFIG_KEY) || 'default';
      
      const activeConfig = configs.find(config => config.id === activeConfigId);
      return activeConfig || configs[0] || DEFAULT_DASHBOARD_CONFIG;
    } catch (error) {
      console.error('Error getting active dashboard configuration:', error);
      return DEFAULT_DASHBOARD_CONFIG;
    }
  },
  
  /**
   * Set active dashboard configuration
   */
  setActiveConfiguration(configId: string): void {
    localStorage.setItem(ACTIVE_CONFIG_KEY, configId);
  },
  
  /**
   * Save a dashboard configuration
   */
  saveConfiguration(config: DashboardConfig): void {
    try {
      const configs = this.getConfigurations();
      const existingIndex = configs.findIndex(c => c.id === config.id);
      
      // Update the updatedAt timestamp
      const updatedConfig = {
        ...config,
        updatedAt: new Date().toISOString()
      };
      
      if (existingIndex >= 0) {
        // Update existing configuration
        configs[existingIndex] = updatedConfig;
      } else {
        // Add new configuration
        configs.push(updatedConfig);
      }
      
      localStorage.setItem(DASHBOARD_CONFIGS_KEY, JSON.stringify(configs));
    } catch (error) {
      console.error('Error saving dashboard configuration:', error);
    }
  },
  
  /**
   * Create a new dashboard configuration
   */
  createConfiguration(name: string): DashboardConfig {
    const newConfig: DashboardConfig = {
      id: uuidv4(),
      name,
      isDefault: false,
      widgets: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    this.saveConfiguration(newConfig);
    return newConfig;
  },
  
  /**
   * Delete a dashboard configuration
   */
  deleteConfiguration(configId: string): void {
    try {
      const configs = this.getConfigurations();
      const filteredConfigs = configs.filter(config => config.id !== configId);
      
      // Don't allow deleting all configurations
      if (filteredConfigs.length === 0) {
        filteredConfigs.push(DEFAULT_DASHBOARD_CONFIG);
      }
      
      localStorage.setItem(DASHBOARD_CONFIGS_KEY, JSON.stringify(filteredConfigs));
      
      // If the active configuration was deleted, set the first available as active
      const activeConfigId = localStorage.getItem(ACTIVE_CONFIG_KEY);
      if (activeConfigId === configId) {
        this.setActiveConfiguration(filteredConfigs[0].id);
      }
    } catch (error) {
      console.error('Error deleting dashboard configuration:', error);
    }
  },
  
  /**
   * Reset to default dashboard configuration
   */
  resetToDefault(): void {
    try {
      const configs = this.getConfigurations();
      const defaultConfig = configs.find(config => config.isDefault) || DEFAULT_DASHBOARD_CONFIG;
      
      this.setActiveConfiguration(defaultConfig.id);
    } catch (error) {
      console.error('Error resetting to default dashboard configuration:', error);
    }
  }
};
