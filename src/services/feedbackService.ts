import { apiClient } from '../lib/apiClient';

export interface FeedbackSettings {
  enabled: boolean;
  autoRequestAfterAppointment: boolean;
  requestDelayHours: number;
  allowAnonymousFeedback: boolean;
  requireRating: boolean;
  requireComment: boolean;
  showPublicReviews: boolean;
  minimumRatingForPublic: number;
  requestTemplate: string;
}

export interface FeedbackSettingsResponse extends FeedbackSettings {
  id: string;
  userId: string;
  createdAt: string;
  updatedAt?: string;
}

export interface Feedback {
  id: string;
  userId: string;
  clientId: string;
  appointmentId: string;
  rating: number;
  comment?: string;
  serviceRating?: number;
  status: 'pending' | 'submitted' | 'responded';
  isAnonymous: boolean;
  isPublic: boolean;
  businessResponse?: string;
  respondedAt?: string;
  requestSentAt?: string;
  submittedAt: string;
  createdAt: string;
  updatedAt?: string;
  
  // Related data
  clientName?: string;
  serviceName?: string;
  appointmentDate?: string;
}

export interface FeedbackCreateRequest {
  appointmentId: string;
  rating: number;
  comment?: string;
  serviceRating?: number;
  isAnonymous: boolean;
}

export interface BusinessResponseRequest {
  businessResponse: string;
}

export interface FeedbackAnalytics {
  totalFeedback: number;
  averageRating: number;
  ratingDistribution: Record<number, number>;
  recentFeedbackCount: number;
  responseRate: number;
}

export interface PaginatedFeedbackResponse {
  items: Feedback[];
  total: number;
  page: number;
  limit: number;
}

export interface PublicFeedback {
  rating: number;
  comment?: string;
  serviceRating?: number;
  serviceName?: string;
  submittedAt: string;
  clientName: string;
  businessResponse?: string;
}

/**
 * Service for managing client feedback
 */
export const feedbackService = {
  /**
   * Get feedback settings for the current user
   */
  async getSettings(): Promise<FeedbackSettings> {
    try {
      const response = await apiClient.get<FeedbackSettingsResponse>('/dashboard/feedback-settings');
      
      // Convert response to frontend format
      return {
        enabled: response.enabled,
        autoRequestAfterAppointment: response.autoRequestAfterAppointment,
        requestDelayHours: response.requestDelayHours,
        allowAnonymousFeedback: response.allowAnonymousFeedback,
        requireRating: response.requireRating,
        requireComment: response.requireComment,
        showPublicReviews: response.showPublicReviews,
        minimumRatingForPublic: response.minimumRatingForPublic,
        requestTemplate: response.requestTemplate
      };
    } catch (error) {
      console.error('Error getting feedback settings:', error);
      // Return default settings if API call fails
      return {
        enabled: true,
        autoRequestAfterAppointment: true,
        requestDelayHours: 2,
        allowAnonymousFeedback: true,
        requireRating: true,
        requireComment: false,
        showPublicReviews: true,
        minimumRatingForPublic: 3,
        requestTemplate: 'Hi {{clientName}}! We hope you enjoyed your {{serviceName}} appointment. We\'d love to hear your feedback! Please rate your experience: {{feedbackLink}}'
      };
    }
  },

  /**
   * Update feedback settings for the current user
   */
  async updateSettings(settings: FeedbackSettings): Promise<FeedbackSettings> {
    try {
      const response = await apiClient.put<FeedbackSettingsResponse>('/dashboard/feedback-settings', {
        enabled: settings.enabled,
        auto_request_after_appointment: settings.autoRequestAfterAppointment,
        request_delay_hours: settings.requestDelayHours,
        allow_anonymous_feedback: settings.allowAnonymousFeedback,
        require_rating: settings.requireRating,
        require_comment: settings.requireComment,
        show_public_reviews: settings.showPublicReviews,
        minimum_rating_for_public: settings.minimumRatingForPublic,
        request_template: settings.requestTemplate
      });

      return {
        enabled: response.enabled,
        autoRequestAfterAppointment: response.autoRequestAfterAppointment,
        requestDelayHours: response.requestDelayHours,
        allowAnonymousFeedback: response.allowAnonymousFeedback,
        requireRating: response.requireRating,
        requireComment: response.requireComment,
        showPublicReviews: response.showPublicReviews,
        minimumRatingForPublic: response.minimumRatingForPublic,
        requestTemplate: response.requestTemplate
      };
    } catch (error) {
      console.error('Error updating feedback settings:', error);
      throw error;
    }
  },

  /**
   * Get feedback for the current user with pagination and filters
   */
  async getFeedback(
    page: number = 1,
    limit: number = 20,
    ratingFilter?: number,
    statusFilter?: string
  ): Promise<PaginatedFeedbackResponse> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });

      if (ratingFilter) {
        params.append('rating_filter', ratingFilter.toString());
      }

      if (statusFilter) {
        params.append('status_filter', statusFilter);
      }

      return await apiClient.get<PaginatedFeedbackResponse>(`/dashboard/feedback?${params.toString()}`);
    } catch (error) {
      console.error('Error getting feedback:', error);
      return {
        items: [],
        total: 0,
        page,
        limit
      };
    }
  },

  /**
   * Respond to client feedback
   */
  async respondToFeedback(feedbackId: string, response: string): Promise<{ success: boolean; message: string }> {
    try {
      return await apiClient.post<{ success: boolean; message: string }>(`/dashboard/feedback/${feedbackId}/respond`, {
        business_response: response
      });
    } catch (error) {
      console.error('Error responding to feedback:', error);
      throw error;
    }
  },

  /**
   * Get feedback analytics for the current user
   */
  async getAnalytics(days: number = 30): Promise<FeedbackAnalytics> {
    try {
      const params = new URLSearchParams({
        days: days.toString()
      });

      return await apiClient.get<FeedbackAnalytics>(`/dashboard/feedback/analytics?${params.toString()}`);
    } catch (error) {
      console.error('Error getting feedback analytics:', error);
      return {
        totalFeedback: 0,
        averageRating: 0,
        ratingDistribution: {},
        recentFeedbackCount: 0,
        responseRate: 0
      };
    }
  },

  /**
   * Submit feedback for an appointment (public endpoint for clients)
   */
  async submitFeedback(feedbackData: FeedbackCreateRequest): Promise<{ success: boolean; message: string }> {
    try {
      return await apiClient.post<{ success: boolean; message: string }>('/dashboard/feedback/submit', {
        appointment_id: feedbackData.appointmentId,
        rating: feedbackData.rating,
        comment: feedbackData.comment,
        service_rating: feedbackData.serviceRating,
        is_anonymous: feedbackData.isAnonymous
      });
    } catch (error) {
      console.error('Error submitting feedback:', error);
      throw error;
    }
  },

  /**
   * Get public feedback for a business (for public reviews display)
   */
  async getPublicFeedback(userId: string, limit: number = 10): Promise<{ items: PublicFeedback[]; message?: string }> {
    try {
      const params = new URLSearchParams({
        limit: limit.toString()
      });

      return await apiClient.get<{ items: PublicFeedback[]; message?: string }>(`/dashboard/feedback/public/${userId}?${params.toString()}`);
    } catch (error) {
      console.error('Error getting public feedback:', error);
      return {
        items: [],
        message: 'Unable to load reviews'
      };
    }
  },

  /**
   * Send a test feedback request
   */
  async sendTestRequest(phone: string): Promise<{ success: boolean; message: string }> {
    try {
      return await apiClient.post<{ success: boolean; message: string }>('/dashboard/feedback/test-request', {
        phone
      });
    } catch (error) {
      console.error('Error sending test feedback request:', error);
      throw error;
    }
  }
};
