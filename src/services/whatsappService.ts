/**
 * WhatsApp Service
 *
 * This service provides methods for interacting with WhatsApp through Evolution API.
 * It combines the functionality of the original whatsappService.ts and evolutionApiService.ts.
 */

import { apiClient } from '../lib/apiClient';
import { Contact, Message } from '../types/whatsapp';
import * as WhatsApp from './whatsapp';

// Evolution API instance interface
interface EvolutionInstance extends Record<string, unknown> {
  instance?: {
    status?: string;
    instanceName?: string;
    owner?: string;
    profileName?: string;
    state?: string;
  };
}

// WhatsApp status interface
export interface WhatsAppStatus {
  connected: boolean;
  phone?: string;
  name?: string;
  status?: string;
  message?: string;
  lastSynced?: string;
}

// WhatsApp QR code interface
export interface WhatsAppQRCode {
  success: boolean;
  qrcode?: string;
  message: string;
  instanceId?: string;
}

// WhatsApp message interface
export interface WhatsAppMessage extends Record<string, unknown> {
  phone: string;
  message: string;
}

/**
 * WhatsApp service for managing WhatsApp integration
 */
export const whatsappService = {
  /**
   * Get WhatsApp connection status
   */
  async getStatus(): Promise<WhatsAppStatus> {
    try {
      // First try to get status from the backend
      try {
        const status = await apiClient.get<WhatsAppStatus & { instanceId?: string }>('/whatsapp/status');

        // If we got an instance ID, store it
        if (status.connected && status.instanceId) {
          WhatsApp.setInstanceId(status.instanceId);
          return status;
        }
      } catch {
        console.warn('Failed to get WhatsApp status from backend, trying Evolution API directly');
      }

      // If backend status check failed, try to get instances directly from Evolution API
      const instances = await WhatsApp.fetchInstances();

      // If we have instances, check if any of them are connected
      if (Array.isArray(instances) && instances.length > 0) {
        // Find an instance that is connected
        const connectedInstance = instances.find(instance => {
          const typedInstance = instance as EvolutionInstance;
          return typedInstance.instance && typedInstance.instance.status === 'open';
        }) as EvolutionInstance | undefined;

        // If we found a connected instance, return it
        if (connectedInstance && connectedInstance.instance) {
          // Store the instance ID
          WhatsApp.setInstanceId(connectedInstance.instance.instanceName as string);

          return {
            connected: true,
            phone: connectedInstance.instance.owner as string || 'Unknown',
            name: connectedInstance.instance.profileName as string || 'WhatsApp',
            status: 'connected',
            lastSynced: new Date().toISOString()
          };
        }
      }

      // No connected instances found
      return { connected: false, message: 'No connected WhatsApp instances found' };
    } catch (error) {
      console.error('Error getting WhatsApp status:', error);
      // Error getting WhatsApp status
      return { connected: false, message: 'Failed to get WhatsApp status' };
    }
  },

  /**
   * Connect to WhatsApp
   */
  async connect(): Promise<WhatsAppQRCode> {
    try {
      const response = await apiClient.post<WhatsAppQRCode>('/whatsapp/connect');

      // If the connection was successful and we got an instance ID, store it
      if (response.success && response.instanceId) {
        WhatsApp.setInstanceId(response.instanceId);
      }

      return response;
    } catch {
      // Error connecting to WhatsApp
      return { success: false, message: 'Failed to connect to WhatsApp' };
    }
  },

  /**
   * Disconnect from WhatsApp
   */
  async disconnect(): Promise<{ success: boolean; message: string }> {
    try {
      return await apiClient.delete<{ success: boolean; message: string }>('/whatsapp/disconnect');
    } catch {
      // Error disconnecting from WhatsApp
      return { success: false, message: 'Failed to disconnect from WhatsApp' };
    }
  },

  /**
   * Send a WhatsApp message
   */
  async sendMessage(messageData: WhatsAppMessage): Promise<{ success: boolean; message: string; details?: Record<string, unknown> }> {
    try {
      return await apiClient.post<{ success: boolean; message: string; details?: Record<string, unknown> }>('/whatsapp/send-message', messageData);
    } catch {
      // Error sending WhatsApp message
      return { success: false, message: 'Failed to send WhatsApp message' };
    }
  },

  /**
   * Fetch recent WhatsApp conversations
   */
  async fetchRecentConversations(limit = 20): Promise<Contact[]> {
    try {
      return await WhatsApp.fetchRecentConversations(limit);
    } catch (error) {
      console.error('Error fetching recent WhatsApp conversations:', error);
      return [];
    }
  },

  /**
   * Fetch WhatsApp contacts
   */
  async fetchWhatsAppContacts(): Promise<Contact[]> {
    try {
      return await WhatsApp.fetchWhatsAppContacts();
    } catch (error) {
      console.error('Error fetching WhatsApp contacts:', error);
      return [];
    }
  },

  /**
   * Fetch messages for a specific contact
   */
  async fetchMessages(contactId: string): Promise<Message[]> {
    try {
      return await WhatsApp.fetchMessages(contactId);
    } catch (error) {
      console.error('Error fetching messages:', error);
      return [];
    }
  },

  /**
   * Send a message to a contact
   */
  async sendDirectMessage(phoneNumber: string, text: string): Promise<boolean> {
    try {
      return await WhatsApp.sendMessage(phoneNumber, text);
    } catch (error) {
      console.error('Error sending message:', error);
      return false;
    }
  },

  /**
   * Ensure WhatsApp instance exists and is connected
   */
  async ensureWhatsAppInstance(): Promise<{ connected: boolean }> {
    try {
      return await WhatsApp.ensureWhatsAppInstance();
    } catch (error) {
      console.error('Error ensuring WhatsApp instance:', error);
      return { connected: false };
    }
  },

  /**
   * Fetch all WhatsApp instances
   */
  async fetchInstances(): Promise<Array<Record<string, unknown>>> {
    try {
      return await WhatsApp.fetchInstances();
    } catch (error) {
      console.error('Error fetching WhatsApp instances:', error);
      return [];
    }
  }
};

/**
 * Get an authenticated media URL for WhatsApp media
 * @param mediaUrl The media URL to authenticate
 * @returns An authenticated URL that can be used to fetch the media
 */
export function getAuthenticatedMediaUrl(mediaUrl: string): string {
  // This is a simplified version that returns the original URL
  // The actual implementation would authenticate the URL
  return mediaUrl;
}

// Export the service
export default whatsappService;
