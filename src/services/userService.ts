import { User } from '../types/api';
import { useAuthStore } from '../lib/auth';
import { jwtDecode } from 'jwt-decode';
import { fetchWithAuth } from '../lib/api';

// Interface for extended user profile data stored in localStorage
interface ExtendedUserProfile {
  phone?: string;
  address?: string;
  website?: string;
  currency?: string;
  logoUrl?: string;
  settings?: Record<string, any>;
}

/**
 * User service for managing user profile
 */
export const userService = {
  /**
   * Get the current user profile
   */
  async getCurrentUser(): Promise<User & ExtendedUserProfile> {
    try {
      // First check if we're authenticated
      const authState = useAuthStore.getState();
      if (!authState.isAuthenticated) {
        throw new Error('User not authenticated');
      }

      // Fetch user profile from backend
      const response = await fetchWithAuth('/profile/me');

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to get user profile');
      }

      let userData;
      try {
        const text = await response.text();
        // Sanitize the response text for logging
        const sanitizedText = text.includes('logo_url') ?
          text.replace(/"logo_url":"data:image\/[^"]+"/, '"logo_url":"<base64_image_data_omitted>"') :
          text;
        // Process raw response from profile endpoint
        userData = text ? JSON.parse(text) : {};

        // Sanitize the parsed data for logging
        const sanitizedUserData = { ...userData };
        if (sanitizedUserData.logo_url && typeof sanitizedUserData.logo_url === 'string' && sanitizedUserData.logo_url.startsWith('data:')) {
          sanitizedUserData.logo_url = '<base64_image_data_omitted>';
        }
        // User data successfully parsed
      } catch (parseError) {
        console.error('Error parsing /profile/me response:', parseError);
        userData = {};
      }

      // Update the auth store with the latest business name
      const currentUser = useAuthStore.getState().user;
      if (currentUser && userData.business_name !== currentUser.businessName) {
        useAuthStore.getState().setUser({
          ...currentUser,
          businessName: userData.business_name
        });
      }

      // Extract settings from userData
      const settings = userData.settings || {};
      // Extract user settings from backend response

      // Convert backend format to frontend format
      return {
        id: userData.id,
        email: userData.email,
        business_name: userData.business_name,
        created_at: userData.created_at,
        businessName: userData.business_name || '',
        phone: settings.phone || userData.phone || '',
        address: settings.address || userData.address || '',
        website: settings.website || userData.website || '',
        currency: userData.currency || '',
        logoUrl: settings.logo_url || userData.logo_url || null
      };
    } catch (error) {
      console.error('Error getting user profile:', error);

      // Fallback to local data if API fails
      try {
        // Get user from auth store
        const authState = useAuthStore.getState();
        const user = authState.user;

        if (!user) {
          throw new Error('User not found in auth store');
        }

        // Try to get the business name from localStorage
        let businessName = user.businessName || '';
        try {
          const savedBusinessName = localStorage.getItem('userBusinessName');
          if (savedBusinessName) {
            businessName = savedBusinessName;
          }
        } catch (error) {
          console.warn('Failed to load business name from localStorage:', error);
        }

        // Create a user object from auth store data
        const userData = {
          id: user.id || user.sub || '',
          email: user.email || user.sub || '',
          business_name: businessName,
          created_at: new Date().toISOString()
        };

        // Get extended profile data from localStorage
        const extendedProfile = this.getExtendedProfile();

        // Combine core user data with extended profile
        return {
          ...userData,
          ...extendedProfile,
          businessName: businessName
        };
      } catch (fallbackError) {
        console.error('Fallback error getting user profile:', fallbackError);
        throw error; // Throw the original error
      }
    }
  },

  /**
   * Get the user's logo URL
   */
  getUserLogoUrl(): string | null {
    const extendedProfile = this.getExtendedProfile();
    // Handle the case where logoUrl is a boolean or the string 'true'
    if (
      extendedProfile.logoUrl &&
      (typeof extendedProfile.logoUrl === 'boolean' || extendedProfile.logoUrl === 'true')
    ) {
      return null; // Return null if logoUrl is a boolean or the string 'true'
    }
    return extendedProfile.logoUrl || null;
  },

  /**
   * Update user settings
   */
  async updateSettings(settings: Record<string, any>): Promise<{ success: boolean; settings: Record<string, any> }> {
    try {
      // Convert settings to profile format
      const profileData = {
        business_name: settings.business_name,
        email: settings.email,
        phone: settings.phone,
        address: settings.address,
        website: settings.website,
        currency: settings.currency,
        logo_url: settings.logo_url
      };

      // Use the profile update endpoint
      const response = await fetchWithAuth('/profile/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to update settings');
      }

      const result = await response.json();
      return { success: true, settings: result };
    } catch (error) {
      console.error('Error updating user settings:', error);
      throw error;
    }
  },

  /**
   * Get extended profile data from localStorage
   */
  getExtendedProfile(): ExtendedUserProfile {
    try {
      // Get from localStorage (but not for address)
      const profileData = localStorage.getItem('userExtendedProfile');
      const parsedData = profileData ? JSON.parse(profileData) : {};

      // Sanitize the parsed data for logging
      const sanitizedData = { ...parsedData };
      if (sanitizedData.logoUrl && typeof sanitizedData.logoUrl === 'string' && sanitizedData.logoUrl.startsWith('data:')) {
        sanitizedData.logoUrl = '<base64_image_data_omitted>';
      }
      // Extended profile retrieved from localStorage

      // Remove address from localStorage data
      const { address, ...rest } = parsedData;
      return rest;
    } catch (error) {
      console.warn('Failed to load extended profile from localStorage:', error);
      return {};
    }
  },

  /**
   * Update the user profile
   */
  async updateProfile(profileData: Partial<UserProfile>): Promise<User & ExtendedUserProfile> {
    try {
      // Handle logo upload or deletion
      let logoUrl = null;
      if (profileData.logo instanceof File) {
        // In a real app, you would upload the file to a server
        // For this demo, we'll convert it to a data URL
        logoUrl = await this.convertFileToDataURL(profileData.logo);
      } else if (typeof profileData.logo === 'string') {
        // If it's a string (existing URL), keep it
        logoUrl = profileData.logo;
      }
      // If profileData.logo is null, logoUrl remains null (logo deletion)

      // Prepare data for the API
      const apiData = {
        business_name: profileData.businessName,
        email: profileData.email,
        phone: profileData.phone,
        address: profileData.address,
        website: profileData.website,
        currency: profileData.currency,
        logo_url: logoUrl
      };

      // Sanitize the API data for logging
      const sanitizedApiData = { ...apiData };
      if (sanitizedApiData.logo_url && typeof sanitizedApiData.logo_url === 'string' && sanitizedApiData.logo_url.startsWith('data:')) {
        sanitizedApiData.logo_url = '<base64_image_data_omitted>';
      }
      // Send sanitized profile data to backend

      // Send update to backend
      const response = await fetchWithAuth('/profile/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to update profile');
      }

      let updatedData;
      try {
        const text = await response.text();
        // Sanitize the response text for logging
        const sanitizedText = text.includes('logo_url') ?
          text.replace(/"logo_url":"data:image\/[^"]+"/, '"logo_url":"<base64_image_data_omitted>"') :
          text;
        // Process raw response from profile update
        updatedData = text ? JSON.parse(text) : {};

        // Sanitize the parsed data for logging
        const sanitizedUpdatedData = { ...updatedData };
        if (sanitizedUpdatedData.logo_url && typeof sanitizedUpdatedData.logo_url === 'string' && sanitizedUpdatedData.logo_url.startsWith('data:')) {
          sanitizedUpdatedData.logo_url = '<base64_image_data_omitted>';
        }
        // Updated profile data successfully parsed
      } catch (parseError) {
        console.error('Error parsing response:', parseError);
        updatedData = {};
      }

      // Update the auth store with the latest business name
      const currentUser = useAuthStore.getState().user;
      if (currentUser) {
        useAuthStore.getState().setUser({
          ...currentUser,
          businessName: updatedData.business_name,
          email: updatedData.email
        });
      }

      // Also update localStorage as a fallback
      try {
        if (updatedData.business_name) {
          localStorage.setItem('userBusinessName', updatedData.business_name);
        }

        // Save extended profile data to localStorage as fallback (except address)
        const extendedProfile = {
          phone: updatedData.phone,
          // Don't store address in localStorage
          website: updatedData.website,
          currency: updatedData.currency,
          // Store the actual logo URL
          logoUrl: updatedData.logo_url || null
        };

        localStorage.setItem('userExtendedProfile', JSON.stringify(extendedProfile));
      } catch (error) {
        console.warn('Could not save profile data to localStorage:', error);
      }

      // Return combined user data
      return {
        id: updatedData.id,
        email: updatedData.email,
        business_name: updatedData.business_name,
        created_at: updatedData.created_at,
        businessName: updatedData.business_name || '',
        phone: updatedData.phone || '',
        address: updatedData.address || '',
        website: updatedData.website || '',
        currency: updatedData.currency || '',
        logoUrl: updatedData.logo_url || null
      };
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  },

  /**
   * Convert a File to a data URL
   */
  async convertFileToDataURL(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  },

  /**
   * Change user password
   */
  async changePassword(currentPassword: string): Promise<void> {
    try {
      // For demo purposes, we'll simulate a password change
      // In a real app, you would validate the current password against the stored password hash
      // and then update the password in the database

      // Simulate a delay to make it feel like a real API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // If the current password is 'password', simulate success, otherwise fail
      if (currentPassword !== 'password') {
        throw new Error('Current password is incorrect');
      }

      // If we get here, the password change was "successful"
      // In a real app, you would update the password in the database
    } catch (error) {
      console.error('Error changing password:', error);
      throw error;
    }
  }
};

/**
 * User profile interface for frontend use
 */
export interface UserProfile {
  businessName: string;
  email: string;
  phone: string;
  address: string;
  website: string;
  currency: string;
  logo?: File | string | null;
}

/**
 * Helper function to decode JWT token
 */
export function decodeToken(token: string): { sub: string; exp: number } | null {
  try {
    return jwtDecode(token);
  } catch (error) {
    console.error('Failed to decode token:', error);
    return null;
  }
}
