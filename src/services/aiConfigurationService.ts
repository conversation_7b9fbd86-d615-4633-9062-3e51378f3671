import { apiClient } from '../lib/apiClient';

export interface AIConfiguration {
  id: string;
  user_id: string;
  system_prompt: string;
  tone: string;
  primary_language: string;
  supported_languages: string[];
  business_rules?: Record<string, unknown>;
  response_templates?: Record<string, unknown>;
  whatsapp_settings?: {
    enableAutoResponses: boolean;
    responseDelay: number;
    maxResponseLength: number;
    enableDetailedLogging: boolean;
  };
  created_at: string;
  updated_at: string;
}

export interface AIConfigurationCreate {
  system_prompt: string;
  tone?: string;
  primary_language?: string;
  supported_languages?: string[];
  business_rules?: Record<string, unknown>;
  response_templates?: Record<string, unknown>;
  whatsapp_settings?: {
    enableAutoResponses: boolean;
    responseDelay: number;
    maxResponseLength: number;
    enableDetailedLogging: boolean;
  };
}

export interface KnowledgeDocument {
  id: string;
  user_id: string;
  title: string;
  content: string;
  document_metadata?: Record<string, unknown>;
  created_at: string;
}

export interface KnowledgeDocumentCreate {
  title: string;
  content: string;
  document_metadata?: Record<string, unknown>;
}

/**
 * Service for managing AI configuration
 */
export const aiConfigurationService = {
  /**
   * Get the current AI configuration
   */
  async getConfiguration(): Promise<AIConfiguration> {
    try {
      return await apiClient.get<AIConfiguration>('/ai/ai-configuration');
    } catch (error) {
      console.error('Error getting AI configuration:', error);
      throw error;
    }
  },

  /**
   * Create or update AI configuration
   */
  async updateConfiguration(config: AIConfigurationCreate): Promise<AIConfiguration> {
    try {
      return await apiClient.post<AIConfiguration>('/ai/ai-configuration', config);
    } catch (error) {
      console.error('Error updating AI configuration:', error);
      throw error;
    }
  },

  /**
   * Get all knowledge documents
   */
  async getKnowledgeDocuments(): Promise<KnowledgeDocument[]> {
    try {
      return await apiClient.get<KnowledgeDocument[]>('/ai/knowledge-documents');
    } catch (error) {
      console.error('Error getting knowledge documents:', error);
      throw error;
    }
  },

  /**
   * Create a new knowledge document
   */
  async createKnowledgeDocument(document: KnowledgeDocumentCreate): Promise<KnowledgeDocument> {
    try {
      return await apiClient.post<KnowledgeDocument>('/ai/knowledge-documents', document);
    } catch (error) {
      console.error('Error creating knowledge document:', error);
      throw error;
    }
  },

  /**
   * Upload a file as a knowledge document
   */
  async uploadKnowledgeDocument(file: File): Promise<KnowledgeDocument> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      return await apiClient.post<KnowledgeDocument>('/ai/knowledge-documents/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    } catch (error) {
      console.error('Error uploading knowledge document:', error);
      throw error;
    }
  },

  /**
   * Get a specific knowledge document
   */
  async getKnowledgeDocument(id: string): Promise<KnowledgeDocument> {
    try {
      return await apiClient.get<KnowledgeDocument>(`/ai/knowledge-documents/${id}`);
    } catch (error) {
      console.error('Error getting knowledge document:', error);
      throw error;
    }
  },

  /**
   * Delete a knowledge document
   */
  async deleteKnowledgeDocument(id: string): Promise<{ success: boolean; message: string }> {
    try {
      return await apiClient.delete<{ success: boolean; message: string }>(`/ai/knowledge-documents/${id}`);
    } catch (error) {
      console.error('Error deleting knowledge document:', error);
      throw error;
    }
  },

  /**
   * Generate a system prompt based on user settings
   */
  async generateSystemPrompt(): Promise<{ system_prompt: string }> {
    try {
      return await apiClient.get<{ system_prompt: string }>('/ai/generate-system-prompt');
    } catch (error) {
      console.error('Error generating system prompt:', error);
      throw error;
    }
  }
};
