import { fetchWithAuth } from '../lib/api';
import { clientService } from './clientService';
import { serviceService } from './serviceService';

export interface GoogleCalendarEvent {
  id: string;
  summary: string;
  description?: string;
  location?: string;
  start: {
    dateTime?: string;
    timeZone?: string;
    date?: string; // For all-day events
  };
  end: {
    dateTime?: string;
    timeZone?: string;
    date?: string; // For all-day events
  };
  attendees?: Array<{
    email: string;
    displayName?: string;
  }>;
  status?: 'confirmed' | 'tentative' | 'cancelled';
  created?: string;
  updated?: string;
  htmlLink?: string;
}

export interface GoogleCalendarStatus {
  connected: boolean;
  email?: string;
  last_synced?: string;
}

export const googleCalendarService = {
  /**
   * Get cached Google Calendar events from the database
   * Currently disabled due to backend issues
   */
  getCachedEvents: async (startDate: Date, endDate: Date): Promise<GoogleCalendarEvent[]> => {
    // Disabled caching for now
    return [];
  },

  /**
   * Store Google Calendar events in the database cache
   * Currently disabled due to backend issues
   */
  cacheEvents: async (events: GoogleCalendarEvent[]): Promise<boolean> => {
    // Disabled caching for now
    return true;
  },
  /**
   * Exchange authorization code for tokens
   */
  handleAuthCode: async (code: string): Promise<GoogleCalendarStatus> => {
    const response = await fetchWithAuth(`/integrations/google-auth`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ code: code }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Google auth error response:', errorData);

      // Extract detailed error message
      let errorMessage = 'Failed to authenticate with Google';
      if (errorData.detail) {
        errorMessage = typeof errorData.detail === 'string'
          ? errorData.detail
          : JSON.stringify(errorData.detail);
      }

      throw new Error(errorMessage);
    }

    return response.json();
  },

  /**
   * Get Google Calendar connection status
   */
  getStatus: async (): Promise<GoogleCalendarStatus> => {
    const response = await fetchWithAuth(`/integrations/google-calendar/status`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get Google Calendar status');
    }

    return response.json();
  },

  /**
   * Validate token by making a test request
   */
  validateToken: async (): Promise<boolean> => {
    try {
      // First try to refresh the token explicitly
      try {
        const refreshResponse = await fetchWithAuth('/integrations/google-calendar/refresh-token', {
          method: 'POST',
        });

        if (refreshResponse.ok) {
          await refreshResponse.json(); // refreshData is not used but might be needed in future implementation
          return true;
        } else {
          const errorData = await refreshResponse.text();
          console.error('Token refresh failed:', errorData);
        }
      } catch (refreshError) {
        console.error('Error refreshing token:', refreshError);
        // Continue with the validation request
      }

      // Get current date for a simple test query
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Try to fetch events for today
      const response = await fetchWithAuth(
        `/integrations/google-calendar/events?start_date=${now.toISOString()}&end_date=${tomorrow.toISOString()}`
      );

      // If we get a 401 or 500 error, the token is invalid
      if (response.status === 401 || response.status === 500) {
        return false;
      }

      return response.ok;
    } catch (error) {
      console.error('Error validating Google Calendar token:', error);
      return false;
    }
  },

  /**
   * Refresh the Google Calendar token
   */
  refreshToken: async (): Promise<GoogleCalendarStatus> => {
    try {
      const response = await fetchWithAuth('/integrations/google-calendar/refresh-token', {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to refresh token');
      }

      return await response.json();
    } catch (error) {
      console.error('Error refreshing Google Calendar token:', error);
      throw error;
    }
  },

  /**
   * Disconnect Google Calendar
   */
  disconnect: async (): Promise<void> => {
    const response = await fetchWithAuth(`/integrations/google-calendar/disconnect`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to disconnect Google Calendar');
    }
  },

  /**
   * Get events from Google Calendar
   */
  getEvents: async (startDate: Date, endDate: Date): Promise<GoogleCalendarEvent[]> => {
    const startDateStr = startDate.toISOString();
    const endDateStr = endDate.toISOString();

    const response = await fetchWithAuth(
      `/integrations/google-calendar/events?start_date=${startDateStr}&end_date=${endDateStr}`
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get Google Calendar events');
    }

    return response.json();
  },

  /**
   * Create a new event in Google Calendar
   */
  createEvent: async (event: Partial<GoogleCalendarEvent>): Promise<GoogleCalendarEvent> => {
    const response = await fetchWithAuth(`/integrations/google-calendar/events`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(event),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to create Google Calendar event');
    }

    return response.json();
  },

  /**
   * Update an event in Google Calendar
   */
  updateEvent: async (eventId: string, event: Partial<GoogleCalendarEvent>): Promise<GoogleCalendarEvent> => {
    const response = await fetchWithAuth(`/integrations/google-calendar/events/${eventId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(event),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to update Google Calendar event');
    }

    return response.json();
  },

  /**
   * Delete an event from Google Calendar
   */
  deleteEvent: async (eventId: string): Promise<void> => {
    const response = await fetchWithAuth(`/integrations/google-calendar/events/${eventId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to delete Google Calendar event');
    }
  },

  /**
   * Sync appointments with Google Calendar
   */
  syncAppointments: async (): Promise<void> => {
    const response = await fetchWithAuth(`/integrations/google-calendar/sync`, {
      method: 'POST',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to sync appointments with Google Calendar');
    }
  },

  /**
   * Convert an appointment to a Google Calendar event
   */
  appointmentToGoogleEvent: (appointment: {
    title?: string;
    client?: { name: string };
    client_id?: string;
    service?: { name: string };
    service_id?: string;
    notes?: string;
    start_time: string;
    end_time: string;
    location?: string;
  }): Partial<GoogleCalendarEvent> => {
    // Use the appointment title if available, otherwise create a default title
    const title = appointment.title || `Appointment with ${appointment.client?.name || appointment.client_id}`;

    return {
      summary: title,
      description: `Service: ${appointment.service?.name || appointment.service_id || 'Not specified'}\nNotes: ${appointment.notes || 'None'}`,
      start: {
        dateTime: typeof appointment.start_time === 'string'
          ? new Date(appointment.start_time).toISOString()
          : appointment.start_time.toISOString(),
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      },
      end: {
        dateTime: typeof appointment.end_time === 'string'
          ? new Date(appointment.end_time).toISOString()
          : appointment.end_time.toISOString(),
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      },
      attendees: appointment.client?.email ? [
        {
          email: appointment.client.email,
          displayName: appointment.client.name,
        },
      ] : [],
    };
  },

  /**
   * Create an appointment in Google Calendar
   */
  createAppointmentInGoogleCalendar: async (appointment: {
    client_id: string;
    service_id?: string;
    start_time: string;
    end_time: string;
    title?: string;
    notes?: string;
    location?: string;
  }): Promise<GoogleCalendarEvent> => {
    try {
      // Fetch client and service details to get their names and email
      let clientName = appointment.client_id;
      let clientEmail = null;
      let serviceName = appointment.service_id;

      try {
        // Try to get client details
        const client = await clientService.getClient(appointment.client_id);
        if (client) {
          if (client.name) {
            clientName = client.name;
          }
          if (client.email) {
            clientEmail = client.email;
          }
        }
      } catch (clientError) {
        console.warn(`Could not fetch client details: ${clientError}`);
      }

      try {
        // Try to get service name
        const service = await serviceService.getService(appointment.service_id);
        if (service && service.name) {
          serviceName = service.name;
          // Service name found
        }
      } catch (serviceError) {
        console.warn(`Could not fetch service details: ${serviceError}`);
      }

      // Create a modified appointment with the names, email, and title
      const enhancedAppointment = {
        ...appointment,
        client: {
          name: clientName,
          email: clientEmail
        },
        service: { name: serviceName },
        // Make sure we use the appointment title if available
        title: appointment.title || `Appointment with ${clientName}`
      };

      // Convert the appointment to a Google Calendar event
      const googleEvent = googleCalendarService.appointmentToGoogleEvent(enhancedAppointment);

      // Add metadata to link this event to our app
      if (googleEvent.description) {
        googleEvent.description += `\n\nCreated by FixMyCal`;
      }

      // Create the event in Google Calendar
      const createdEvent = await googleCalendarService.createEvent(googleEvent);
      // Event successfully created in Google Calendar

      return createdEvent;
    } catch (error) {
      console.error('Error creating appointment in Google Calendar:', error);
      throw error;
    }
  },
};
