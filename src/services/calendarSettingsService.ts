import { apiClient } from '../lib/apiClient';
import { userService } from './userService';

export interface WorkingHours {
  [key: string]: {
    start: string;
    end: string;
    enabled: boolean;
  };
}

export interface CalendarSettings {
  workingHours: WorkingHours;
  defaultDuration: string;
  bufferTime: string;
  allowWeekends: boolean;
}

/**
 * Service for managing calendar settings
 */
export const calendarSettingsService = {
  /**
   * Get calendar settings for the current user
   */
  async getSettings(): Promise<CalendarSettings> {
    try {
      // Get user profile which contains settings
      const user = await userService.getCurrentUser();
      
      // Extract calendar settings from user settings
      const settings = user.settings || {};
      const calendarSettings = settings.calendar || {};
      
      // Return calendar settings with defaults for missing values
      return {
        workingHours: calendarSettings.workingHours || {
          monday: { start: '09:00', end: '17:00', enabled: true },
          tuesday: { start: '09:00', end: '17:00', enabled: true },
          wednesday: { start: '09:00', end: '17:00', enabled: true },
          thursday: { start: '09:00', end: '17:00', enabled: true },
          friday: { start: '09:00', end: '17:00', enabled: true },
          saturday: { start: '10:00', end: '15:00', enabled: false },
          sunday: { start: '10:00', end: '15:00', enabled: false }
        },
        defaultDuration: calendarSettings.defaultDuration || '30min',
        bufferTime: calendarSettings.bufferTime || '15min',
        allowWeekends: calendarSettings.allowWeekends || false
      };
    } catch (error) {
      console.error('Error getting calendar settings:', error);
      
      // Return default settings if API call fails
      return {
        workingHours: {
          monday: { start: '09:00', end: '17:00', enabled: true },
          tuesday: { start: '09:00', end: '17:00', enabled: true },
          wednesday: { start: '09:00', end: '17:00', enabled: true },
          thursday: { start: '09:00', end: '17:00', enabled: true },
          friday: { start: '09:00', end: '17:00', enabled: true },
          saturday: { start: '10:00', end: '15:00', enabled: false },
          sunday: { start: '10:00', end: '15:00', enabled: false }
        },
        defaultDuration: '30min',
        bufferTime: '15min',
        allowWeekends: false
      };
    }
  },

  /**
   * Update calendar settings
   */
  async updateSettings(settings: CalendarSettings): Promise<CalendarSettings> {
    try {
      // Get current user profile
      const user = await userService.getCurrentUser();
      
      // Get current settings or initialize if not present
      const currentSettings = user.settings || {};
      
      // Update calendar settings
      const updatedSettings = {
        ...currentSettings,
        calendar: settings
      };
      
      // Save updated settings to user profile
      await apiClient.put('/auth/settings', { settings: updatedSettings });
      
      return settings;
    } catch (error) {
      console.error('Error updating calendar settings:', error);
      // Return the settings that were passed in, so the UI doesn't break
      return settings;
    }
  }
};
