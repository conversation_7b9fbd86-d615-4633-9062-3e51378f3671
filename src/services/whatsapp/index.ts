/**
 * WhatsApp Integration
 * This file exports all WhatsApp functionality.
 */

// Export connection functions
export {
  ensureWhatsAppInstance,
  checkInstanceConnection,
  connectToInstance,
  createNewInstance,
  fetchInstances
} from './connection';

// Export message functions
export {
  fetchMessages,
  sendMessage,
  formatPhoneNumber,
  formatPhoneWithSuffix,
  processMessages
} from './messages';

// Export contact functions
export {
  fetchRecentConversations,
  fetchWhatsAppContacts
} from './contacts';

// Export media functions
export {
  getMediaBase64,
  getMediaBlobUrl,
  base64ToBlob,
  cleanupMediaCache
} from './media';

// Export configuration
export {
  getInstanceName,
  getBaseUrl,
  setInstanceId
} from './config';

// Export API utilities
export {
  makeRequest,
  WhatsAppApiError
} from './apiUtils';
