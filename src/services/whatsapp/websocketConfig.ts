/**
 * WebSocket Configuration for Evolution API
 *
 * This file contains configuration settings for connecting to the Evolution API WebSocket.
 * The Evolution API supports two WebSocket modes:
 *
 * 1. Global Mode: Connect to ws://api.yoursite.com (no instance name in URL)
 *    - Requires WEBSOCKET_GLOBAL_EVENTS=true in Evolution API .env
 *
 * 2. Traditional Mode: Connect to ws://api.yoursite.com/instance_name
 *    - Default mode when WEBSOCKET_GLOBAL_EVENTS is not set or false
 *
 * See Evolution API documentation for more details.
 */

import { getBaseUrl } from './config';

// WebSocket connection settings
export const websocketConfig = {
  // Whether WebSockets are enabled
  // Set to true to enable real-time updates, false to use polling instead
  enabled: false, // Disable WebSockets and use polling instead

  // Whether to use global mode (true) or traditional mode (false)
  globalMode: true,

  // Maximum number of reconnection attempts
  maxReconnectAttempts: 5,

  // Reconnection delay in milliseconds
  reconnectionDelay: 2000,

  // Maximum reconnection delay in milliseconds
  reconnectionDelayMax: 10000,

  // Connection timeout in milliseconds
  timeout: 20000,

  // Get the WebSocket URL based on the mode and instance name
  getWebSocketUrl: (instanceId: string): string => {
    const baseUrl = getBaseUrl();

    // In global mode, the instance name is not included in the URL
    if (websocketConfig.globalMode) {
      return baseUrl;
    }

    // In traditional mode, the instance name is included in the URL
    return `${baseUrl}/${instanceId}`;
  }
};

// Export default config
export default websocketConfig;
