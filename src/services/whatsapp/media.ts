/**
 * WhatsApp Media Handling
 * This file contains functions for handling media in WhatsApp messages.
 */

import { makeRequest, getAuthenticatedMediaUrl } from './apiUtils';
import { getBaseUrl, getInstanceName } from './config';

/**
 * Placeholder function to maintain compatibility
 * @returns Promise that resolves to false (proxy not available)
 */
export async function checkMediaProxyAvailability(): Promise<boolean> {
  // Always return false since we're not using the proxy anymore
  return false;
}

/**
 * Get base64 encoded media content from a message
 * @param messageId The ID of the message containing the media
 * @param mediaUrl Optional URL of the media, used for MIME type detection
 * @returns Promise with base64 encoded media and MIME type
 */
export async function getMediaBase64(messageId: string, mediaUrl?: string): Promise<{ base64: string; mime: string } | null> {
  try {
    // Use the Evolution API to get base64 of the media
    // Evolution API v2 uses /chat/getBase64FromMediaMessage/{instance} endpoint
    const instanceId = getInstanceName();

    // If no instance ID is available, we can't fetch media
    if (!instanceId) {
      console.warn('No WhatsApp instance ID available, cannot fetch media');
      return null;
    }

    const mediaEndpoint = `${getBaseUrl()}/chat/getBase64FromMediaMessage/${instanceId}`;

    // Prepare the request body according to the API documentation
    // Extract the message ID from the format "msg-timestamp-random"
    let processedMessageId = messageId;
    if (messageId.includes('-')) {
      // The message ID might be in the format "msg-timestamp-random"
      // Extract just the random part at the end
      const parts = messageId.split('-');
      if (parts.length > 2) {
        // Use the last part as the message ID
        processedMessageId = parts[parts.length - 1];
      }
    }

    const requestBody = {
      message: {
        key: {
          id: processedMessageId
        }
      },
      // Convert videos to MP4 if it's a video
      convertToMp4: mediaUrl ? mediaUrl.includes('t62.7119-24') : false
    };

    // Request media from Evolution API
    console.log(`Requesting media for message ID: ${messageId} (processed: ${processedMessageId})`);
    console.log('Request body:', requestBody);

    // Get the base64 content using the Evolution API
    try {
      const response = await makeRequest(mediaEndpoint, {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      console.log('Media API response:', response);

      // Check if we got a base64 response
      if (response && response.base64 && typeof response.base64 === 'string') {
        // Determine MIME type from response or default to a generic type
        let mimeType: string = (typeof response.mimetype === 'string' ? response.mimetype : 'application/octet-stream');

        // If the MIME type is not set, try to determine it from the media URL
        if (mimeType === 'application/octet-stream' && mediaUrl) {
          if (mediaUrl.includes('t62.7117-24')) {
            mimeType = 'audio/ogg';
          } else if (mediaUrl.includes('t62.7118-24')) {
            mimeType = 'image/jpeg';
          } else if (mediaUrl.includes('t62.7119-24')) {
            mimeType = 'video/mp4';
          }
        }

        return {
          base64: response.base64,
          mime: mimeType
        };
      }

      // No base64 data found
      return null;
    } catch (error) {
      // Error getting base64 media
      console.warn(`Error getting base64 media for message ${messageId}:`, error);
      return null;
    }

  } catch (error) {
    // Error getting base64 media
    console.warn(`Error getting base64 media for message ${messageId}:`, error);
    return null;
  }
}

/**
 * Convert base64 to blob URL for display in the browser
 * @param base64 Base64 encoded data
 * @param mimeType MIME type of the data
 * @returns Blob URL that can be used in src attributes
 */
export function base64ToBlob(base64: string, mimeType: string): string {
  try {
    if (!base64) {
      // Empty base64 string provided
      return '';
    }

    // Remove data URL prefix if present
    const base64Data = base64.startsWith('data:')
      ? base64.split(',')[1]
      : base64.includes('base64,') ? base64.split('base64,')[1] : base64;

    // Convert base64 to binary
    const byteCharacters = atob(base64Data);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += 512) {
      const slice = byteCharacters.slice(offset, offset + 512);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    // Create blob from binary data
    const blob = new Blob(byteArrays, { type: mimeType });

    // Create and return URL for the blob
    const blobUrl = URL.createObjectURL(blob);
    return blobUrl;
  } catch (error) {
    // Error converting base64 to blob
    console.warn('Error converting base64 to blob:', error);
    return '';
  }
}

/**
 * Cache for storing blob URLs to prevent memory leaks
 */
const mediaCache: Map<string, string> = new Map();

/**
 * Get media as a blob URL, with caching
 * @param messageId The ID of the message containing the media
 * @param mediaUrl Optional direct URL to the media
 * @returns Promise with blob URL for the media
 */
export async function getMediaBlobUrl(messageId: string, mediaUrl?: string): Promise<string | null> {
  try {
    // Check if we already have this media in the cache
    if (mediaCache.has(messageId)) {
      return mediaCache.get(messageId) || null;
    }

    // If we have a direct media URL, use it directly
    if (mediaUrl) {
      // For WhatsApp media URLs, we need to handle them specially
      if (mediaUrl.includes('mmg.whatsapp.net')) {
        // Determine the media type based on URL pattern (for debugging purposes)
        const mediaTypeForLogging = mediaUrl.includes('t62.7117-24') ? 'audio' :
                        mediaUrl.includes('t62.7118-24') ? 'image' :
                        mediaUrl.includes('t62.7119-24') ? 'video' : 'document';
        console.log(`Detected WhatsApp media type: ${mediaTypeForLogging} for URL: ${mediaUrl.substring(0, 50)}...`);

        // Try to get the media using the Evolution API
        try {
          // Get the base64 data using our improved function
          const mediaData = await getMediaBase64(messageId, mediaUrl);

          if (mediaData && mediaData.base64) {
            // Convert the base64 data to a blob URL
            const blobUrl = base64ToBlob(mediaData.base64, mediaData.mime);
            if (blobUrl) {
              mediaCache.set(messageId, blobUrl);
              return blobUrl;
            }
          }
        } catch (error) {
          // Error handling - fall back to direct URL
          console.warn('Failed to load media from Evolution API, falling back to direct URL:', error);
        }

        // If Evolution API failed, try to use the direct URL
        // Note: Direct WhatsApp media URLs often return 403 Forbidden or 410 Gone
        // because they're temporary and require authentication

        // Instead of trying to access the URL directly, just show a placeholder
        console.warn(`Using placeholder for media: ${mediaUrl}`);

        // Return null to show a placeholder
        return null;
      }

      // For non-WhatsApp URLs, use the direct URL with authentication
      try {
        // Get authenticated URL with cache busting
        const authenticatedUrl = getAuthenticatedMediaUrl(mediaUrl);
        const cacheBuster = `_cb=${Date.now()}`;
        const finalUrl = authenticatedUrl.includes('?') ? `${authenticatedUrl}&${cacheBuster}` : `${authenticatedUrl}?${cacheBuster}`;

        // Store in cache
        mediaCache.set(messageId, finalUrl);
        return finalUrl;
      } catch (error) {
        // Error processing media URL
        console.warn('Error processing media URL:', error);
      }
    }

    // If we don't have a URL or URL processing failed, try base64 method as fallback
    try {
      const mediaData = await getMediaBase64(messageId, mediaUrl);
      if (mediaData && mediaData.base64) {
        // Convert base64 to blob URL
        const blobUrl = base64ToBlob(mediaData.base64, mediaData.mime);
        if (blobUrl) {
          mediaCache.set(messageId, blobUrl);
          return blobUrl;
        }
      }
    } catch (error) {
      console.warn('Error getting media using base64 method:', error);
    }

    // Failed to get media
    return null;
  } catch (error) {
    // Error handling
    console.warn('Error loading media:', error);
    return null;
  }
}

/**
 * Clean up cached blob URLs to prevent memory leaks
 * Call this when components unmount or when the cache gets too large
 */
export function cleanupMediaCache(): void {
  // Revoke all blob URLs
  mediaCache.forEach((url) => {
    URL.revokeObjectURL(url);
  });

  // Clear the cache
  mediaCache.clear();
}
