/**
 * WhatsApp Connection Management
 * This file contains functions for managing WhatsApp instance connections.
 */

import { getInstanceName, getBaseUrl, setInstanceId } from './config';
import { makeRequest, WhatsAppApiError } from './apiUtils';

// Cache for connection status to reduce API calls
let connectionStatusCache: {
  status: { connected: boolean; state?: string; qrCode?: string } | null;
  timestamp: number;
} = {
  status: null,
  timestamp: 0
};

// Cache expiration time in milliseconds (30 seconds)
const CACHE_EXPIRATION = 30000;

/**
 * Check if a WhatsApp instance is connected
 * @param forceRefresh Force a refresh of the connection status (bypass cache)
 * @returns Promise with the connection state
 */
export async function checkInstanceConnection(forceRefresh = false): Promise<{
  connected: boolean;
  state?: string;
  qrCode?: string;
}> {
  // Check if we have a cached result that's still valid
  const now = Date.now();
  if (!forceRefresh &&
      connectionStatusCache.status &&
      now - connectionStatusCache.timestamp < CACHE_EXPIRATION) {
    return connectionStatusCache.status;
  }

  try {
    // Get the current instance name
    const instanceId = getInstanceName();

    // If no instance ID is available, we can't check connection
    if (!instanceId) {
      console.warn('No WhatsApp instance ID available, cannot check connection');
      const result = { connected: false, state: 'not_configured', needsSetup: true };
      connectionStatusCache = { status: result, timestamp: now };
      return result;
    }

    // Check connection state
    const stateUrl = `${getBaseUrl()}/instance/connectionState/${instanceId}`;
    const stateData = await makeRequest(stateUrl, { method: 'GET' });

    // Cast stateData to a more specific type
    const stateDataTyped = stateData as { instance?: { state?: string } };

    // If the instance exists and is connected, return true
    if (stateDataTyped.instance?.state === 'open') {
      const result = { connected: true, state: 'open' };
      // Update cache
      connectionStatusCache = {
        status: result,
        timestamp: now
      };
      return result;
    }

    // If the instance state is 'not_found', it means the instance doesn't exist
    if (stateDataTyped.instance?.state === 'not_found') {
      // Don't clear the instance ID here, as we want to show the proper UI state
      // We'll try to fetch instances first to see if there are any available
      const result = { connected: false, state: 'not_found', needsSetup: true };
      // Update cache
      connectionStatusCache = {
        status: result,
        timestamp: now
      };
      return result;
    }

    // If the instance exists but is not connected
    if (stateDataTyped.instance?.state) {
      const result = {
        connected: false,
        state: stateDataTyped.instance.state
      };
      // Update cache
      connectionStatusCache = {
        status: result,
        timestamp: now
      };
      return result;
    }

    // If we get here, the instance doesn't exist or is in an unknown state
    const result = { connected: false, state: 'unknown', needsSetup: true };
    // Update cache
    connectionStatusCache = {
      status: result,
      timestamp: now
    };
    return result;
  } catch (error) {
    // If checking the state fails, the instance might not exist
    if (error instanceof WhatsAppApiError) {
      // Don't clear the instance ID here, as we want to show the proper UI state
      // We'll try to fetch instances first to see if there are any available
      const result = { connected: false, state: 'error', needsSetup: true };
      // Update cache
      connectionStatusCache = {
        status: result,
        timestamp: now
      };
      return result;
    }
    throw error;
  }
}

/**
 * Try to connect to an existing WhatsApp instance
 * @returns Promise with the connection result
 */
export async function connectToInstance(): Promise<{
  success: boolean;
  qrCode?: string;
  pairingCode?: string;
  error?: string;
}> {
  try {
    // Get the current instance name
    const instanceId = getInstanceName();

    // Try to reconnect
    const connectUrl = `${getBaseUrl()}/instance/connect/${instanceId}`;
    const connectData = await makeRequest(connectUrl, { method: 'GET' });

    // If we get a QR code or pairing code, the user needs to scan it
    // Cast connectData to a more specific type
    const connectDataTyped = connectData as { code?: string; pairingCode?: string };

    if (connectDataTyped.code || connectDataTyped.pairingCode) {
      return {
        success: false,
        qrCode: connectDataTyped.code,
        pairingCode: connectDataTyped.pairingCode
      };
    }

    // Check if the connection was successful after a delay
    const connectionResult = await checkConnectionAfterDelay(3000);

    if (connectionResult) {
      // If connection was successful, configure settings
      try {
        // Get the current instance name
        const instanceId = getInstanceName();

        // First configure WebSocket settings
        await configureWebSocketSettings(instanceId);
        console.log('WebSocket settings configured successfully');

        // Then configure instance settings
        await configureInstanceSettings(instanceId);
        console.log('Instance settings configured successfully');
      } catch (error) {
        console.error('Failed to configure settings:', error);
        // Continue even if configuration fails
      }
    }

    return {
      success: connectionResult,
      error: connectionResult ? undefined : 'Failed to connect after attempt'
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error connecting to WhatsApp'
    };
  }
}

/**
 * Create a new WhatsApp instance
 * @returns Promise with the creation result
 */
export async function createNewInstance(): Promise<{
  success: boolean;
  qrCode?: string;
  error?: string;
}> {
  try {
    // Create the instance
    const createUrl = `${getBaseUrl()}/instance/create`;
    const createData = await makeRequest(createUrl, {
      method: 'POST',
      body: JSON.stringify({
        instanceName: getInstanceName(),
        token: getInstanceName(),
        qrcode: true,  // Generate QR code for connection
        webhook: null,
        webhookUrl: '',
        webhookAutoDownload: false
      })
    });

    // Get the current instance name
    const instanceId = getInstanceName();

    // Configure settings for the new instance
    try {
      // First configure WebSocket settings
      await configureWebSocketSettings(instanceId);
      console.log('WebSocket settings configured successfully');

      // Then configure instance settings
      await configureInstanceSettings(instanceId);
      console.log('Instance settings configured successfully');
    } catch (error) {
      console.error('Failed to configure settings:', error);
      // Continue even if configuration fails
    }

    // Cast createData to a more specific type
    const createDataTyped = createData as { qrcode?: string; code?: string };

    // Return the QR code for the user to scan
    return {
      success: true,
      qrCode: createDataTyped.qrcode || createDataTyped.code
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error creating WhatsApp instance'
    };
  }
}

/**
 * Configure WebSocket settings for an instance
 * @param instanceId The name of the instance to configure
 * @returns Promise that resolves when configuration is complete
 */
async function configureWebSocketSettings(instanceId: string | null): Promise<void> {
  // If no instance ID is provided, we can't configure WebSocket settings
  if (!instanceId) {
    console.warn('No instance ID provided, cannot configure WebSocket settings');
    return;
  }
  try {
    // Define all the events we want to enable
    const allEvents = [
      "APPLICATION_STARTUP",
      "CALL",
      "CHATS_DELETE",
      "CHATS_SET",
      "CHATS_UPDATE",
      "CHATS_UPSERT",
      "CONNECTION_UPDATE",
      "CONTACTS_SET",
      "CONTACTS_UPDATE",
      "CONTACTS_UPSERT",
      "GROUP_PARTICIPANTS_UPDATE",
      "GROUP_UPDATE",
      "GROUPS_UPSERT",
      "LABELS_ASSOCIATION",
      "LABELS_EDIT",
      "LOGOUT_INSTANCE",
      "MESSAGES_DELETE",
      "MESSAGES_SET",
      "MESSAGES_UPDATE",
      "MESSAGES_UPSERT",
      "PRESENCE_UPDATE",
      "QRCODE_UPDATED",
      "REMOVE_INSTANCE",
      "SEND_MESSAGE",
      "TYPEBOT_CHANGE_STATUS",
      "TYPEBOT_START"
    ];

    // Configure WebSocket settings using the webhook endpoint
    const webhookUrl = `${getBaseUrl()}/webhook/set/${instanceId}`;
    const response = await makeRequest(webhookUrl, {
      method: 'POST',
      body: JSON.stringify({
        enabled: true,
        webhookByEvents: true,
        events: allEvents,
        // We're not setting a webhook URL because we're only interested in WebSocket events
        url: ""
      })
    });

    // Enable WebSocket globally
    try {
      const enableWebSocketUrl = `${getBaseUrl()}/settings/websocket`;
      const webSocketResponse = await makeRequest(enableWebSocketUrl, {
        method: 'POST',
        body: JSON.stringify({
          enabled: true,
          global: true
        })
      });
      console.log('WebSocket global settings response:', webSocketResponse);
    } catch (wsError) {
      console.error('Error enabling global WebSocket:', wsError);
      // Continue even if this fails
    }

    console.log('WebSocket configuration response:', response);
    return;
  } catch (error) {
    console.error('Error configuring WebSocket settings:', error);
    throw error;
  }
}

/**
 * Configure instance settings
 * @param instanceId The name of the instance to configure
 * @returns Promise that resolves when configuration is complete
 */
async function configureInstanceSettings(instanceId: string | null): Promise<void> {
  // If no instance ID is provided, we can't configure instance settings
  if (!instanceId) {
    console.warn('No instance ID provided, cannot configure instance settings');
    return;
  }
  try {
    // Configure settings to enable all the requested features
    // According to the Evolution API v2 documentation, the correct endpoint is /settings/set/{instance}
    const settingsUrl = `${getBaseUrl()}/settings/set/${instanceId}`;
    const settingsResponse = await makeRequest(settingsUrl, {
      method: 'POST',
      body: JSON.stringify({
        // Reject all incoming calls
        reject_call: true,
        // Message to send when rejecting calls (optional)
        msg_call: "Sorry, I can't take calls right now. Please leave a message.",
        // Ignore all messages from groups
        groups_ignore: true,
        // Keep WhatsApp always online
        always_online: true,
        // Mark all messages as read
        read_messages: true,
        // Sync all complete chat history on scan QR code
        sync_full_history: true,
        // Mark all statuses as read
        read_status: true
      })
    });

    console.log('Instance settings configuration response:', settingsResponse);

    return;
  } catch (error) {
    console.error('Error configuring instance settings:', error);
    throw error;
  }
}

/**
 * Check connection state after a delay
 * @param delay Delay in milliseconds
 * @returns Promise with connection status (true if connected)
 */
async function checkConnectionAfterDelay(delay: number): Promise<boolean> {
  // Wait for the specified delay
  await new Promise(resolve => setTimeout(resolve, delay));

  try {
    // Force refresh the connection status (bypass cache)
    const connectionState = await checkInstanceConnection(true);
    return connectionState.connected;
  } catch (error) {
    console.error(`Error checking connection state after delay:`, error);
    return false;
  }
}

/**
 * Fetch all WhatsApp instances
 * @returns Promise with array of instances
 */
export async function fetchInstances(): Promise<Array<Record<string, unknown>>> {
  try {
    // Get all instances
    const fetchUrl = `${getBaseUrl()}/instance/fetchInstances`;
    const instances = await makeRequest(fetchUrl, { method: 'GET' });

    // If we have instances, use the first one
    if (Array.isArray(instances) && instances.length > 0) {
      console.log('Found WhatsApp instances:', instances);

      // Get the first instance
      const firstInstance = instances[0] as Record<string, unknown>;

      // Try to get the instance name
      if (firstInstance.name && typeof firstInstance.name === 'string') {
        // This is the format from the logs: {name: 'b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7'}
        const instanceName = firstInstance.name;
        console.log(`Using WhatsApp instance: ${instanceName}`);
        setInstanceId(instanceName);
      } else if (firstInstance.instance && typeof firstInstance.instance === 'object') {
        // This is the format from the Evolution API docs
        const instanceData = firstInstance.instance as Record<string, unknown>;
        if (instanceData.instanceName && typeof instanceData.instanceName === 'string') {
          const instanceName = instanceData.instanceName;
          console.log(`Using WhatsApp instance: ${instanceName}`);
          setInstanceId(instanceName);
        }
      }
    }

    // Ensure we return an array
    return Array.isArray(instances) ? instances : [];
  } catch (error) {
    console.error('Error fetching WhatsApp instances:', error);
    return [];
  }
}

/**
 * Ensure WhatsApp instance exists and is connected
 * This is the main function that should be called to ensure a working connection
 * @returns Promise with connection status
 */
export async function ensureWhatsAppInstance(): Promise<{
  connected: boolean;
  qrCode?: string;
  error?: string;
}> {
  try {
    // First, fetch all instances to see if we already have one
    await fetchInstances();

    // Then, check if the instance exists and is connected (force refresh)
    const connectionState = await checkInstanceConnection(true);

    // If connected, return success
    if (connectionState.connected) {
      return { connected: true };
    }

    // If the instance exists but is not connected, try to connect
    if (connectionState.state && connectionState.state !== 'unknown') {
      const connectionResult = await connectToInstance();

      if (connectionResult.success) {
        return { connected: true };
      }

      // If connection failed but we got a QR code, return it
      if (connectionResult.qrCode) {
        return {
          connected: false,
          qrCode: connectionResult.qrCode
        };
      }

      // If connection failed and we didn't get a QR code, try creating a new instance
      const creationResult = await createNewInstance();
      return {
        connected: false,
        qrCode: creationResult.qrCode,
        error: creationResult.success ? undefined : creationResult.error || 'Failed to create instance'
      };
    }

    // If the instance doesn't exist or is in an unknown state, create a new one
    const creationResult = await createNewInstance();
    return {
      connected: false,
      qrCode: creationResult.qrCode,
      error: creationResult.success ? undefined : creationResult.error || 'Failed to create instance'
    };
  } catch (error) {
    return {
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown error ensuring WhatsApp instance'
    };
  }
}
