/**
 * WhatsApp Integration Configuration
 * This file contains configuration settings for the WhatsApp integration.
 */

// Get the user's WhatsApp instance ID from local storage or use user ID as fallback
export const getInstanceName = (): string | null => {
  // Try to get the instance ID from local storage
  const storedInstanceId = localStorage.getItem('whatsapp_instance_id');
  if (storedInstanceId) {
    return storedInstanceId;
  }

  // If not found, try to get the user ID from JWT token
  const token = localStorage.getItem('accessToken');
  if (token) {
    try {
      // Simple JWT parsing (token is in format header.payload.signature)
      const payload = token.split('.')[1];
      const decodedPayload = JSON.parse(atob(payload));
      if (decodedPayload.sub) {
        return decodedPayload.sub; // Use user ID as instance name
      }
    } catch (e) {
      console.error('Error parsing JWT token:', e);
    }
  }

    // No fallback - if we can't determine the instance ID, return null
  console.warn('Could not determine WhatsApp instance ID');
  return null;
};

/**
 * Clear the WhatsApp instance ID from localStorage
 * Call this when we detect that the instance ID is no longer valid
 */
export const clearInstanceId = (): void => {
  localStorage.removeItem('whatsapp_instance_id');
  console.log('Cleared WhatsApp instance ID from localStorage');
};

// API key from environment variables
export const apiKey = import.meta.env.VITE_EVOLUTION_API_KEY;

// Base URL for Evolution API from environment variables
export const getBaseUrl = () => import.meta.env.VITE_EVOLUTION_API_URL;

// Maximum number of retries for API calls
export const MAX_RETRIES = 3;

// Delay between retries (in milliseconds)
export const RETRY_DELAY = 2000;

// Set the WhatsApp instance ID in local storage
export const setInstanceId = (instanceId: string): void => {
  localStorage.setItem('whatsapp_instance_id', instanceId);
};
