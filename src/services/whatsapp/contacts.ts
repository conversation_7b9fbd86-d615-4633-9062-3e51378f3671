/**
 * WhatsApp Contacts Management
 * This file contains functions for managing WhatsApp contacts.
 */

import { getInstanceName, getBaseUrl } from './config';
import { makeRequest } from './apiUtils';
import { Contact } from '../../types/whatsapp';

/**
 * Fetch recent WhatsApp conversations
 * @param limit Maximum number of conversations to fetch
 * @returns Promise with array of contacts
 */
export async function fetchRecentConversations(limit = 20): Promise<Contact[]> {
  try {
    // Use the Evolution API to get recent chats
    const chatsUrl = `${getBaseUrl()}/chat/findChats/${getInstanceName()}`;

    const chatsData = await makeRequest(chatsUrl, {
      method: 'POST',
      body: JSON.stringify({
        where: {},
        limit: limit,
        sort: { updatedAt: -1 } // Sort by most recent first
      })
    });

    // Process the chats into contacts
    const contacts: Contact[] = [];

    // Handle the response from the API
    const chats = Array.isArray(chatsData) ? chatsData :
                 (chatsData.chats && Array.isArray(chatsData.chats)) ? chatsData.chats : [];

    for (const chat of chats) {
      // Only include individual chats (not group chats)
      if (chat.remoteJid && typeof chat.remoteJid === 'string' &&
          (chat.remoteJid.includes('@s.whatsapp.net') || chat.remoteJid.includes('@c.us')) &&
          !chat.remoteJid.includes('@g.us')) {

          // Extract phone number from JID
          const phone = chat.remoteJid.split('@')[0];

          // Create a contact object
          const contactId = `whatsapp-${phone}`;
          const displayName = chat.pushName || chat.name || phone;

          // Get the last message
          let lastMessage = '';
          if (chat.lastMessage) {
            if (typeof chat.lastMessage === 'string') {
              lastMessage = chat.lastMessage;
            } else if (chat.lastMessage.body) {
              lastMessage = chat.lastMessage.body;
            }
          }

          // Try to get the most accurate timestamp
          let timestamp;
          if (chat.updatedAt) {
            timestamp = new Date(chat.updatedAt).toISOString();
          } else if (chat.lastMessageTimestamp) {
            timestamp = new Date(chat.lastMessageTimestamp).toISOString();
          } else if (chat.lastMessageTime) {
            timestamp = new Date(chat.lastMessageTime).toISOString();
          } else if (chat.timestamp) {
            // Check if timestamp needs to be multiplied by 1000 (seconds vs milliseconds)
            const timestampValue = typeof chat.timestamp === 'number' && chat.timestamp < 10000000000
              ? chat.timestamp * 1000 : chat.timestamp;
            timestamp = new Date(timestampValue).toISOString();
          } else {
            timestamp = new Date().toISOString();
          }

          contacts.push({
            id: contactId,
            name: displayName,
            avatar: chat.profilePicUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(displayName)}&background=random`,
            phone: phone,
            lastMessage: lastMessage,
            timestamp: timestamp,
            unread: chat.unreadCount || 0
          });
        }
    }

    // Sort contacts by timestamp (most recent first)
    contacts.sort((a, b) => {
      // Parse timestamps to milliseconds
      let timeA = 0;
      let timeB = 0;

      try {
        timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
      } catch (e) {
        console.error(`Error parsing timestamp for contact ${a.name}:`, e);
      }

      try {
        timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
      } catch (e) {
        console.error(`Error parsing timestamp for contact ${b.name}:`, e);
      }

      return timeB - timeA;
    });

    // If we didn't get any contacts, fall back to fetching all contacts
    if (contacts.length === 0) {
      return fetchWhatsAppContacts();
    }

    return contacts;
  } catch (error) {
    console.error('Error fetching recent WhatsApp conversations:', error);

    // Fall back to fetching all contacts
    return fetchWhatsAppContacts();
  }
}

/**
 * Fetch all WhatsApp contacts
 * @returns Promise with array of contacts
 */
export async function fetchWhatsAppContacts(): Promise<Contact[]> {
  try {
    // Use the Evolution API to get all contacts
    const contactsUrl = `${getBaseUrl()}/chat/findContacts/${getInstanceName()}`;

    const contactsData = await makeRequest(contactsUrl, {
      method: 'POST',
      body: JSON.stringify({
        // Get all contacts
        where: {}
      })
    });

    // Process contacts data to get all valid WhatsApp contacts
    const contactsMap = new Map<string, { id: string, pushName?: string, name?: string }>();

    // Handle the response from the API
    const contactsList = Array.isArray(contactsData) ? contactsData :
                       (contactsData.contacts && Array.isArray(contactsData.contacts)) ? contactsData.contacts : [];

    contactsList.forEach((contact: { remoteJid?: string; pushName?: string; notifyName?: string; name?: string; updatedAt?: string; createdAt?: string; profilePicUrl?: string }) => {
      // Check for remoteJid instead of id
      if (contact.remoteJid &&
          typeof contact.remoteJid === 'string' &&
          (contact.remoteJid.includes('@s.whatsapp.net') || contact.remoteJid.includes('@c.us'))) {

        // Extract phone number from JID
        const phone = contact.remoteJid.split('@')[0];

        // Store in map for later use
        contactsMap.set(phone, {
          id: contact.remoteJid,
          pushName: contact.pushName || phone,
          name: contact.pushName || phone
        });
      }
    });

    // Create a map to track unique contacts by phone number
    const uniqueContacts = new Map<string, Contact>();

    // Add contacts from the contacts map
    contactsMap.forEach((contact, phone) => {
      // Create a contact for each phone number
      const contactId = `whatsapp-${phone}`;
      const displayName = contact.pushName || contact.name || phone;

      // Find the original contact to get the profile picture URL and timestamp
      const originalContact = contactsData.find((c: { remoteJid?: string; profilePicUrl?: string; updatedAt?: string; createdAt?: string }) => c.remoteJid === contact.id);
      const profilePicUrl = originalContact?.profilePicUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(displayName)}&background=random`;

      // Use the updatedAt timestamp from the original contact if available
      const timestamp = originalContact?.updatedAt || originalContact?.createdAt || new Date().toISOString();

      uniqueContacts.set(contactId, {
        id: contactId,
        name: displayName,
        avatar: profilePicUrl,
        phone: phone,
        lastMessage: '',
        timestamp: timestamp
      });
    });

    // Convert map to array
    const whatsappContacts = Array.from(uniqueContacts.values());

    // Sort contacts by timestamp (most recent first) and then by name
    const sortedContacts = [...whatsappContacts].sort((a, b) => {
      // If both have timestamps, sort by timestamp (most recent first)
      if (a.timestamp && b.timestamp) {
        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      }
      // If only one has a timestamp, prioritize that one
      if (a.timestamp) return -1;
      if (b.timestamp) return 1;
      // Otherwise sort by name
      return a.name.localeCompare(b.name);
    });

    return sortedContacts;
  } catch (error) {
    console.error('Error fetching WhatsApp contacts:', error);
    throw error;
  }
}
