/**
 * WhatsApp API Utilities
 * This file contains utility functions for making requests to the Evolution API.
 */

import { api<PERSON><PERSON>, MAX_RETRIES, RETRY_DELAY, getBaseUrl, clearInstanceId } from './config';

/**
 * Custom error class for WhatsApp API errors
 */
export class WhatsAppApiError extends Error {
  status: number;

  constructor(message: string, status: number = 0) {
    super(message);
    this.name = 'WhatsAppApiError';
    this.status = status;
  }
}

/**
 * Make a request to the Evolution API with retry logic
 * @param url API endpoint URL
 * @param options Request options
 * @param retryCount Current retry count
 * @returns Promise with the API response
 */
export async function makeRequest(url: string, options: RequestInit, retryCount = 0): Promise<Record<string, unknown>> {
  try {
    // Add API key to headers if not present
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'apikey': apiKey,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      ...options.headers
    };

    // Add cache-busting parameter to URL
    const cacheBuster = `_t=${Date.now()}`;
    const urlWithCacheBuster = url.includes('?') ? `${url}&${cacheBuster}` : `${url}?${cacheBuster}`;

    const requestOptions = {
      ...options,
      headers
    };

    const response = await fetch(urlWithCacheBuster, requestOptions);

    // If the request was successful, return the response
    if (response.ok) {
      return await response.json();
    }

    // Handle rate limiting
    if (response.status === 429 && retryCount < MAX_RETRIES) {
      const waitTime = RETRY_DELAY * (retryCount + 1);
      console.warn(`Rate limited, retrying in ${waitTime}ms...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      return makeRequest(url, options, retryCount + 1);
    }

    // Handle server errors
    if (response.status >= 500 && retryCount < MAX_RETRIES) {
      const waitTime = RETRY_DELAY * (retryCount + 1);
      console.warn(`Server error (${response.status}), retrying in ${waitTime}ms...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      return makeRequest(url, options, retryCount + 1);
    }

    // Handle 404 and 400 errors for WhatsApp API endpoints more gracefully
    if (response.status === 404 || response.status === 400) {
      // For connection checks
      if (url.includes('/instance/connectionState/')) {
        // Return a standard response for connection checks when instance doesn't exist
        // Don't clear the instance ID here, as we want to show the proper UI state
        return { instance: { state: 'not_found' } };
      }

      // For chat/findChats endpoint
      if (url.includes('/chat/findChats/')) {
        // Return empty chats array
        return { chats: [] };
      }

      // For chat/findContacts endpoint
      if (url.includes('/chat/findContacts/')) {
        // Return empty contacts array
        return { contacts: [] };
      }

      // Handle media-related endpoints
      if (url.includes('/chat/getBase64FromMediaMessage/') || url.includes('/chat/base64FromMedia')) {
        // Don't clear the instance ID here, as we want to show the proper UI state
        // We'll try to fetch instances first to see if there are any available

        // Return empty object for media endpoints with a more specific structure
        // to prevent errors in the media handling code
        return {
          base64: null,
          mimetype: null,
          error: `Media not available (${response.status})`
        };
      }
    }

    // Handle other errors
    throw new WhatsAppApiError(
      `API request failed: ${response.status} - ${response.statusText}`,
      response.status
    );
  } catch (error) {
    // Retry on network errors
    if (!(error instanceof WhatsAppApiError) && retryCount < MAX_RETRIES) {
      const waitTime = RETRY_DELAY * (retryCount + 1);
      console.warn(`Network error, retrying in ${waitTime}ms...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      return makeRequest(url, options, retryCount + 1);
    }

    // Rethrow WhatsAppApiError or create a new one for other errors
    if (error instanceof WhatsAppApiError) {
      throw error;
    } else {
      throw new WhatsAppApiError(`Request failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

/**
 * Get a media URL with authentication
 * @param mediaUrl The media URL from the API
 * @returns A URL with authentication parameters
 */
export function getAuthenticatedMediaUrl(mediaUrl: string): string {
  if (!mediaUrl) return '';

  // Check if the URL is already absolute
  if (!mediaUrl.startsWith('http')) {
    // Add the base URL
    mediaUrl = `${getBaseUrl()}${mediaUrl}`;
  }

  // For WhatsApp CDN URLs, we need to use them directly
  // WhatsApp CDN URLs are already authenticated and don't need our API key
  if (mediaUrl.includes('mmg.whatsapp.net')) {
    return mediaUrl;
  }

  // For Evolution API URLs, add the API key
  const separator = mediaUrl.includes('?') ? '&' : '?';
  return `${mediaUrl}${separator}apikey=${apiKey}`;
}
