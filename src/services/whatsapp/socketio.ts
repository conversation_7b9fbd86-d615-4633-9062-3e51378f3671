/**
 * Evolution API Socket.io Service
 *
 * This file provides a Socket.io connection to the Evolution API for real-time updates.
 * It handles connecting to the Evolution API WebSocket, subscribing to events, and
 * notifying subscribers when events occur.
 *
 * Flow of WebSocket integration:
 * 1. MessagesPage connects to the WebSocket when it loads
 * 2. It subscribes to message and status events
 * 3. When a new message is received, the WebSocket sends an event to the frontend
 * 4. The frontend updates the UI with the new message
 * 5. If WebSockets are disabled or the connection fails, the application falls back to polling
 *
 * See docs/WHATSAPP_INTEGRATION.md for more details on the WebSocket integration.
 */

import { io, Socket } from 'socket.io-client';
import { getBaseUrl } from './config';
import { addToast } from '../../components/ui';
import websocketConfig from './websocketConfig';

// Event types from Evolution API
export enum EvolutionSocketEventType {
  MESSAGE = 'messages.upsert',
  STATUS = 'connection.update',
  QR_CODE = 'qrcode',
  READY = 'ready',
  DISCONNECTED = 'disconnected'
}

// Event handlers
type EventHandler = (data: Record<string, unknown>) => void;

class EvolutionSocketService {
  private socket: Socket | null = null;
  private eventHandlers: Map<string, Set<EventHandler>> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private isConnecting = false;
  private instanceId: string | null = null;

  /**
   * Connect to the Evolution API Socket.io server
   * @param instanceId The WhatsApp instance name to connect to
   * @returns Promise that resolves when connected or rejects on error
   */
  public connect(instanceId: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve(true);
        return;
      }

      if (this.isConnecting) {
        resolve(false);
        return;
      }

      // Check if WebSockets are enabled in the config
      if (!websocketConfig.enabled) {
        console.log('WebSockets are disabled in the configuration');
        reject(new Error('WebSockets are disabled'));
        return;
      }

      this.isConnecting = true;
      this.instanceId = instanceId;

      // Get the WebSocket URL from the config
      const socketUrl = websocketConfig.getWebSocketUrl(instanceId);

      console.log(`Connecting to Evolution API Socket.io at ${socketUrl}`);

      try {
        // Connect to the Socket.io server with polling as the primary transport
        // This is more reliable than WebSocket in some environments
        this.socket = io(socketUrl, {
          transports: ['polling', 'websocket'],  // Use polling first, then try WebSocket
          reconnectionAttempts: websocketConfig.maxReconnectAttempts,
          reconnectionDelay: websocketConfig.reconnectionDelay,
          reconnectionDelayMax: websocketConfig.reconnectionDelayMax,
          timeout: websocketConfig.timeout,
          forceNew: true,  // Force a new connection
          autoConnect: true  // Automatically connect
        });

        // Set up event handlers
        this.socket.on('connect', () => {
          console.log('Connected to Evolution API Socket.io');
          this.reconnectAttempts = 0;
          this.isConnecting = false;
          resolve(true);
        });

        this.socket.on('disconnect', (reason) => {
          console.log('Disconnected from Evolution API Socket.io:', reason);
          if (reason === 'io server disconnect') {
            // The server has forcefully disconnected the socket
            this.socket?.connect();
          }
        });

        this.socket.on('connect_error', (error) => {
          console.error('Socket.io connection error:', error);
          this.isConnecting = false;
          this.reconnectAttempts++;

          // Log more detailed information about the error
          console.log(`Connection error details: ${error.message}`);
          console.log(`Current transport: ${this.socket?.io?.engine?.transport?.name || 'unknown'}`);
          console.log(`Available transports: ${this.socket?.io?.engine?.opts?.transports?.join(', ') || 'unknown'}`);

          // If we've tried WebSocket and it failed, force polling only
          if (this.reconnectAttempts >= 2 && this.socket?.io?.opts?.transports?.includes('websocket')) {
            console.log('Forcing polling transport after multiple failures');
            this.socket.io.opts.transports = ['polling'];
          }

          if (this.reconnectAttempts >= websocketConfig.maxReconnectAttempts) {
            console.error('Failed to connect to Evolution API Socket.io after', websocketConfig.maxReconnectAttempts, 'attempts');

            // If we're in traditional mode and failed, try global mode as a fallback
            if (!websocketConfig.globalMode) {
              console.log('Trying global mode as a fallback...');
              websocketConfig.globalMode = true;
              this.reconnectAttempts = 0;
              this.connect(this.instanceId || '').then(resolve).catch(reject);
              return;
            }

            addToast('Failed to connect to WhatsApp real-time updates. Falling back to polling.', 'warning');
            reject(new Error('Socket.io connection error after max retries'));
          }
        });

        // Set up handlers for Evolution API events
        this.setupEventHandlers();
      } catch (error) {
        console.error('Error creating Socket.io connection:', error);
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * Set up handlers for Evolution API events
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    // Handle messages.upsert event (new messages)
    this.socket.on(EvolutionSocketEventType.MESSAGE, (data) => {
      console.log('Received message event from Evolution API:', data);
      this.notifyHandlers(EvolutionSocketEventType.MESSAGE, data);
    });

    // Handle connection.update event (connection status changes)
    this.socket.on(EvolutionSocketEventType.STATUS, (data) => {
      console.log('Received status event from Evolution API:', data);
      this.notifyHandlers(EvolutionSocketEventType.STATUS, data);
    });

    // Handle qrcode event (QR code for WhatsApp Web)
    this.socket.on(EvolutionSocketEventType.QR_CODE, (data) => {
      console.log('Received QR code event from Evolution API:', data);
      this.notifyHandlers(EvolutionSocketEventType.QR_CODE, data);
    });

    // Handle ready event (WhatsApp is ready)
    this.socket.on(EvolutionSocketEventType.READY, (data) => {
      console.log('Received ready event from Evolution API:', data);
      this.notifyHandlers(EvolutionSocketEventType.READY, data);
    });

    // Handle disconnected event (WhatsApp is disconnected)
    this.socket.on(EvolutionSocketEventType.DISCONNECTED, (data) => {
      console.log('Received disconnected event from Evolution API:', data);
      this.notifyHandlers(EvolutionSocketEventType.DISCONNECTED, data);
    });
  }

  /**
   * Notify all handlers for a specific event type
   * @param eventType Event type
   * @param data Event data
   */
  private notifyHandlers(eventType: EvolutionSocketEventType, data: Record<string, unknown>): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in ${eventType} event handler:`, error);
        }
      });
    }
  }

  /**
   * Disconnect from the Socket.io server
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    this.reconnectAttempts = 0;
    this.isConnecting = false;
  }

  /**
   * Subscribe to an event type
   * @param eventType Event type to subscribe to
   * @param handler Function to call when the event occurs
   * @returns Function to unsubscribe
   */
  public subscribe(eventType: EvolutionSocketEventType, handler: EventHandler): () => void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set());
    }

    const handlers = this.eventHandlers.get(eventType)!;
    handlers.add(handler);

    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(eventType);
      if (handlers) {
        handlers.delete(handler);
      }
    };
  }

  /**
   * Check if the Socket.io connection is connected
   */
  public isConnected(): boolean {
    return this.socket !== null && this.socket.connected;
  }
}

// Create a singleton instance
export const evolutionSocketService = new EvolutionSocketService();
