/**
 * WhatsApp Messages Management
 * This file contains functions for sending and receiving WhatsApp messages.
 */

import { getInstanceName, getBaseUrl } from './config';
import { makeRequest } from './apiUtils';
import { Message } from '../../types/whatsapp';

// Declare a global store for WhatsApp messages
// This will allow us to access the original message IDs from anywhere in the application
declare global {
  interface Window {
    whatsappMessages?: Message[];
  }
}

// Initialize the global store
if (typeof window !== 'undefined') {
  window.whatsappMessages = window.whatsappMessages || [];
}

/**
 * Format a phone number for WhatsApp
 * @param phoneNumber Phone number to format
 * @returns Formatted phone number
 */
export function formatPhoneNumber(phoneNumber: string): string {
  // Ensure it has country code
  let formattedNumber = phoneNumber;
  if (!formattedNumber.startsWith('+')) {
    formattedNumber = `+${formattedNumber}`;
  }

  // Remove any non-digit characters except the + sign
  formattedNumber = formattedNumber.replace(/[^\d+]/g, '');

  return formattedNumber;
}

/**
 * Format a phone number with WhatsApp suffix
 * @param phoneNumber Phone number to format
 * @returns Phone number with WhatsApp suffix
 */
export function formatPhoneWithSuffix(phoneNumber: string): string {
  const formattedNumber = formatPhoneNumber(phoneNumber);
  return `${formattedNumber.replace('+', '')}@s.whatsapp.net`;
}

/**
 * Process messages from different API formats into a standardized format
 * @param responseData Raw response data from the API
 * @returns Array of standardized Message objects
 */
export function processMessages(responseData: Record<string, unknown> | Array<Record<string, unknown>>): Message[] {
  // Process the messages
  const messages: Message[] = [];

  // Check if the response is in v2 API format
  if (responseData && Array.isArray(responseData.messages)) {
    // Process v2 API format
    responseData.messages.forEach((message: Record<string, unknown> & { key?: { id: string; remoteJid: string }; messageTimestamp?: number; message?: Record<string, unknown> }) => {
      // Extract message text
      const messageText = message.body || '';

      // Use the original WhatsApp message ID directly
      const originalId = message.id || (message.key?.id);

      // Create a message object using the original ID when available
      const newMessage: Message = {
        // Use the original WhatsApp message ID if available, otherwise create a fallback ID
        id: originalId || `msg-${message.messageTimestamp || Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
        text: messageText,
        timestamp: new Date(message.timestamp ? message.timestamp * 1000 : Date.now()).toISOString(),
        sender: message.fromMe ? 'me' : 'them',
        status: message.fromMe ? 'read' : undefined
      };

      // Log the message ID for debugging
      if (originalId) {
        console.log(`Using original WhatsApp ID for message: ${originalId}`);
      } else {
        console.log(`No original ID available, using generated ID: ${newMessage.id}`);
      }

      // Add media information if available
      if (message.type === 'image') {
        newMessage.type = 'image';
        if (message.mediaUrl) {
          newMessage.mediaUrl = message.mediaUrl;
        }
      } else if (message.type === 'video') {
        newMessage.type = 'video';
        if (message.mediaUrl) {
          newMessage.mediaUrl = message.mediaUrl;
        }
      } else if (message.type === 'audio' || message.type === 'ptt') {
        newMessage.type = 'audio';
        if (message.mediaUrl) {
          newMessage.mediaUrl = message.mediaUrl;
        }
      } else if (message.type === 'document') {
        newMessage.type = 'document';
        if (message.mediaUrl) {
          newMessage.mediaUrl = message.mediaUrl;
        }
        if (message.fileName) {
          newMessage.fileName = message.fileName;
        }
      } else {
        newMessage.type = 'text';
      }

      messages.push(newMessage);
    });
  }
  // Check if the response has a messages property (v1 API format with pagination)
  else if (responseData && responseData.messages && responseData.messages.records && Array.isArray(responseData.messages.records)) {
    // Process v1 API format with pagination
    responseData.messages.records.forEach((message: Record<string, unknown> & { key?: { id: string; remoteJid: string }; messageTimestamp?: number; message?: Record<string, unknown> }) => {
      // Extract message text based on message type
      let messageText = '';

      // Handle different message types
      if (message.message?.conversation) {
        messageText = message.message.conversation;
      } else if (message.message?.extendedTextMessage?.text) {
        messageText = message.message.extendedTextMessage.text;
      } else if (message.message?.imageMessage) {
        messageText = message.message.imageMessage.caption || '[Image]';
      } else if (message.message?.videoMessage) {
        messageText = message.message.videoMessage.caption || '[Video]';
      } else if (message.message?.audioMessage) {
        messageText = '[Audio]';
      } else if (message.message?.documentMessage) {
        messageText = message.message.documentMessage.fileName || '[Document]';
      } else {
        messageText = `[Unknown Message Type]`;
      }

      // Use the original WhatsApp message ID directly
      const originalId = message.key?.id;

      // Create a message object using the original ID when available
      const newMessage: Message = {
        // Use the original WhatsApp message ID if available, otherwise create a fallback ID
        id: originalId || `msg-${message.messageTimestamp || Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
        text: messageText,
        timestamp: new Date(message.messageTimestamp ? message.messageTimestamp * 1000 : Date.now()).toISOString(),
        sender: message.key?.fromMe ? 'me' : 'them',
        status: message.key?.fromMe ? 'read' : undefined
      };

      // Message ID is set

      // Add media information based on message type
      if (message.message?.imageMessage) {
        newMessage.type = 'image';

        // Add caption as text if available
        if (message.message.imageMessage.caption) {
          newMessage.text = message.message.imageMessage.caption;
        }

        // Add media URL
        if (message.message.imageMessage.url) {
          newMessage.mediaUrl = message.message.imageMessage.url;
          // Image URL found
        } else {
          // Image without URL
        }

        if (message.message.imageMessage.mimetype) {
          newMessage.mimeType = message.message.imageMessage.mimetype;
        }

        if (message.message.imageMessage.fileLength) {
          newMessage.fileSize = message.message.imageMessage.fileLength;
        }
      } else if (message.message?.videoMessage) {
        newMessage.type = 'video';

        // Add caption as text if available
        if (message.message.videoMessage.caption) {
          newMessage.text = message.message.videoMessage.caption;
        }

        // Add media URL
        if (message.message.videoMessage.url) {
          newMessage.mediaUrl = message.message.videoMessage.url;
          console.log('Found video message with URL:', message.message.videoMessage.url);
        } else {
          console.log('Video message without URL:', message.message.videoMessage);
        }

        if (message.message.videoMessage.mimetype) {
          newMessage.mimeType = message.message.videoMessage.mimetype;
        }

        if (message.message.videoMessage.fileLength) {
          newMessage.fileSize = message.message.videoMessage.fileLength;
        }
      } else if (message.message?.audioMessage) {
        newMessage.type = 'audio';
        newMessage.text = 'Voice message';

        // Add media URL
        if (message.message.audioMessage.url) {
          newMessage.mediaUrl = message.message.audioMessage.url;
          // Audio URL found
        } else {
          // Audio without URL
        }

        if (message.message.audioMessage.mimetype) {
          newMessage.mimeType = message.message.audioMessage.mimetype;
        }

        if (message.message.audioMessage.fileLength) {
          newMessage.fileSize = message.message.audioMessage.fileLength;
        }
      } else if (message.message?.documentMessage) {
        newMessage.type = 'document';

        // Add file metadata
        if (message.message.documentMessage.fileName) {
          newMessage.fileName = message.message.documentMessage.fileName;
          // Use filename as text if no text is set
          if (!newMessage.text) {
            newMessage.text = message.message.documentMessage.fileName;
          }
        }

        // Add media URL
        if (message.message.documentMessage.url) {
          newMessage.mediaUrl = message.message.documentMessage.url;
          console.log('Found document message with URL:', message.message.documentMessage.url);
        } else {
          console.log('Document message without URL:', message.message.documentMessage);
        }

        if (message.message.documentMessage.mimetype) {
          newMessage.mimeType = message.message.documentMessage.mimetype;
        }

        if (message.message.documentMessage.fileLength) {
          newMessage.fileSize = message.message.documentMessage.fileLength;
        }
      } else {
        newMessage.type = 'text';
      }

      messages.push(newMessage);
    });
  }
  // Check if the response is an array (old v1 API format)
  else if (responseData && Array.isArray(responseData)) {
    // Process old v1 API format
    responseData.forEach((message: Record<string, unknown> & { key?: { id: string; remoteJid: string }; messageTimestamp?: number; message?: Record<string, unknown> }) => {
      // Extract message text based on message type
      let messageText = '';

      // Handle different message types
      if (message.message?.conversation) {
        messageText = message.message.conversation;
      } else if (message.message?.extendedTextMessage?.text) {
        messageText = message.message.extendedTextMessage.text;
      } else if (message.message?.imageMessage) {
        messageText = message.message.imageMessage.caption || '[Image]';
      } else if (message.message?.videoMessage) {
        messageText = message.message.videoMessage.caption || '[Video]';
      } else if (message.message?.audioMessage) {
        messageText = '[Audio]';
      } else if (message.message?.documentMessage) {
        messageText = message.message.documentMessage.fileName || '[Document]';
      } else {
        messageText = `[Unknown Message Type]`;
      }

      // Use the original WhatsApp message ID directly
      const originalId = message.key?.id;

      // Create a message object using the original ID when available
      const newMessage: Message = {
        // Use the original WhatsApp message ID if available, otherwise create a fallback ID
        id: originalId || `msg-${message.messageTimestamp || Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
        text: messageText,
        timestamp: new Date(message.messageTimestamp ? message.messageTimestamp * 1000 : Date.now()).toISOString(),
        sender: message.key?.fromMe ? 'me' : 'them',
        status: message.key?.fromMe ? 'read' : undefined
      };

      // Log the message ID for debugging
      if (originalId) {
        console.log(`Using original WhatsApp ID for message: ${originalId}`);
      } else {
        console.log(`No original ID available, using generated ID: ${newMessage.id}`);
      }

      // Add media information based on message type
      if (message.message?.imageMessage) {
        newMessage.type = 'image';
        if (message.message.imageMessage.url) {
          newMessage.mediaUrl = message.message.imageMessage.url;
        }
      } else if (message.message?.videoMessage) {
        newMessage.type = 'video';
        if (message.message.videoMessage.url) {
          newMessage.mediaUrl = message.message.videoMessage.url;
        }
      } else if (message.message?.audioMessage) {
        newMessage.type = 'audio';
        if (message.message.audioMessage.url) {
          newMessage.mediaUrl = message.message.audioMessage.url;
        }
      } else if (message.message?.documentMessage) {
        newMessage.type = 'document';
        if (message.message.documentMessage.url) {
          newMessage.mediaUrl = message.message.documentMessage.url;
        }
        if (message.message.documentMessage.fileName) {
          newMessage.fileName = message.message.documentMessage.fileName;
        }
      } else {
        newMessage.type = 'text';
      }

      messages.push(newMessage);
    });
  }

  // Store the messages in the global store
  if (typeof window !== 'undefined') {
    // Add new messages to the store, avoiding duplicates
    messages.forEach(message => {
      if (!window.whatsappMessages?.some(m => m.id === message.id)) {
        window.whatsappMessages?.push(message);
      }
    });

    // Keep the store size manageable (limit to 1000 messages)
    if (window.whatsappMessages && window.whatsappMessages.length > 1000) {
      window.whatsappMessages = window.whatsappMessages.slice(-1000);
    }

    // Global store updated
  }

  // Sort messages by timestamp (oldest first)
  return messages.sort((a, b) =>
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );
}

/**
 * Fetch messages for a specific contact
 * @param contactId Contact ID (format: 'whatsapp-{phoneNumber}')
 * @returns Promise with array of messages
 */
export async function fetchMessages(contactId: string): Promise<Message[]> {
  try {
    // Extract phone number from contact ID
    const phone = contactId.replace('whatsapp-', '');

    // Format phone number for WhatsApp
    const formattedPhoneNumber = formatPhoneWithSuffix(phone);

    // Try the findMessages endpoint first
    try {
      const evolutionApiUrl = `${getBaseUrl()}/chat/findMessages/${getInstanceName()}`;
      const responseData = await makeRequest(evolutionApiUrl, {
        method: 'POST',
        body: JSON.stringify({
          where: {
            key: {
              remoteJid: formattedPhoneNumber
            }
          },
          limit: 100,  // Increased limit to get more messages
          sort: { messageTimestamp: -1 }  // Sort by timestamp descending (newest first)
        })
      });

      // Process the messages
      const messages = processMessages(responseData);

      return messages;
    } catch {
      // If findMessages fails, try the conversation endpoint
      try {
        const conversationUrl = `${getBaseUrl()}/chat/conversation/${getInstanceName()}`;
        const conversationData = await makeRequest(conversationUrl, {
          method: 'POST',
          body: JSON.stringify({
            remoteJid: formattedPhoneNumber,
            count: 100  // Request more messages
          })
        });

        // Process conversation data
        const messages = processMessages(conversationData);

        return messages;
      } catch {
        // If conversation endpoint fails, try getChats
        try {
          const chatsUrl = `${getBaseUrl()}/chat/getChats/${getInstanceName()}`;
          const chatsData = await makeRequest(chatsUrl, {
            method: 'GET'
          });

          // Try to find the chat with the matching phone number
          const matchingChat = chatsData.find((chat: Record<string, unknown> & { id?: string }) =>
            chat.id === formattedPhoneNumber ||
            chat.id.includes(phone.replace('+', ''))
          );

          if (matchingChat) {
            // Create a placeholder message with a unique ID
            return [{
              id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
              text: 'Chat found but no messages available',
              timestamp: new Date().toISOString(),
              sender: 'system',
              status: 'read'
            }];
          }

          // If no matching chat found, return empty array
          return [];
        } catch {
          // All methods failed
          return [];
        }
      }
    }
  } catch (error) {
    console.error('Error fetching messages:', error);
    return [];
  }
}

/**
 * Send a message to a contact
 * @param phoneNumber Phone number to send message to
 * @param text Message text
 * @returns Promise with send status
 */
export async function sendMessage(phoneNumber: string, text: string): Promise<boolean> {
  try {
    // Format phone number for WhatsApp
    const formattedNumber = formatPhoneNumber(phoneNumber);

    // Remove any non-digit characters for Evolution API
    // Make sure to remove the + sign as Evolution API doesn't accept it
    const cleanNumber = formattedNumber.replace(/\D/g, '');

    // Add country code if not present (assuming Spain as default)
    const finalNumber = cleanNumber.length <= 9 ? `34${cleanNumber}` : cleanNumber;

    // Use the v1 format which is confirmed to work with the current Evolution API
    const evolutionApiUrl = `${getBaseUrl()}/message/sendText/${getInstanceName()}`;
    await makeRequest(evolutionApiUrl, {
      method: 'POST',
      body: JSON.stringify({
        number: finalNumber,
        text: text,
        delay: 1200,
        linkPreview: false
      })
    });

    return true;
  } catch (error) {
    console.error('Error sending message:', error);
    return false;
  }
}
