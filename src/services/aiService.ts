/**
 * AI Service
 * This service provides AI response generation for WhatsApp messages.
 * It uses the enhanced AI service for improved responses.
 */

import { Message } from '../types/whatsapp';
import { whatsappService } from './whatsappService';
import { aiConfig } from '../config/aiConfig';
import { generateAIResponse as enhancedGenerateAIResponse } from './enhancedAiService';

/**
 * Process a message and generate an AI response
 */
export const generateAIResponse = async (
  contactPhone: string,
  contactName: string,
  message: Message
): Promise<boolean> => {
  try {
    // Use the enhanced AI service for better responses
    return await enhancedGenerateAIResponse(contactPhone, contactName, message);
  } catch (error) {
    console.error('Error generating AI response:', error);

    // Fall back to the simple implementation if the enhanced one fails
    return await generateSimpleAIResponse(contactPhone, contactName, message);
  }
};

/**
 * Simple implementation of AI response generation (fallback)
 */
const generateSimpleAIResponse = async (
  contactPhone: string,
  contactName: string,
  message: Message
): Promise<boolean> => {
  // Check if auto-responses are enabled
  if (!aiConfig.enableAutoResponses) {
    console.log(`Auto-responses are disabled. Skipping response to ${contactName}`);
    return false;
  }

  try {
    if (aiConfig.enableDetailedLogging) {
      console.log(`Generating simple AI response for message from ${contactName}: ${message.text}`);
    }

    // Simple response generation logic
    const response = await getAIResponseText(contactName, message.text);

    // Add a delay to make the response seem more natural
    if (aiConfig.responseDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, aiConfig.responseDelay));
    }

    // Send the AI response back via WhatsApp
    const success = await whatsappService.sendDirectMessage(contactPhone, response);

    if (success) {
      console.log(`Simple AI response sent successfully to ${contactName}: "${response}"`);
    } else {
      console.error(`Failed to send simple AI response to ${contactName}`);
    }

    return success;
  } catch (error) {
    console.error('Error generating simple AI response:', error);
    return false;
  }
};

/**
 * Generate AI response text based on the message content
 * This is a placeholder implementation that will be enhanced later
 */
const getAIResponseText = async (contactName: string, messageText: string): Promise<string> => {
  // For now, we'll use a simple rule-based approach
  // Later, this will be replaced with a proper AI model call

  // Convert message to lowercase for easier matching
  const lowerMessage = messageText.toLowerCase();

  let response = '';

  // Simple greeting detection
  if (
    lowerMessage.includes('hello') ||
    lowerMessage.includes('hi') ||
    lowerMessage.includes('hey') ||
    lowerMessage.includes('hola')
  ) {
    response = `Hello ${contactName}! This is an automated response from FixMyCal. How can I help you today?`;
  }
  // Question detection
  else if (
    lowerMessage.includes('?') ||
    lowerMessage.includes('what') ||
    lowerMessage.includes('how') ||
    lowerMessage.includes('when') ||
    lowerMessage.includes('where') ||
    lowerMessage.includes('why')
  ) {
    response = `Thanks for your question, ${contactName}. I'm an AI assistant for FixMyCal. I'll help you with that shortly.`;
  }
  // Appointment related
  else if (
    lowerMessage.includes('appointment') ||
    lowerMessage.includes('schedule') ||
    lowerMessage.includes('booking') ||
    lowerMessage.includes('cancel') ||
    lowerMessage.includes('reschedule')
  ) {
    response = `I see you're asking about an appointment, ${contactName}. Let me check that for you and get back to you soon.`;
  }
  // Thank you detection
  else if (
    lowerMessage.includes('thank') ||
    lowerMessage.includes('thanks') ||
    lowerMessage.includes('gracias')
  ) {
    response = `You're welcome, ${contactName}! I'm here to help. Is there anything else you need?`;
  }
  // Default response
  else {
    response = `Thank you for your message, ${contactName}. This is an automated response from FixMyCal. I'll get back to you shortly.`;
  }

  // Ensure the response doesn't exceed the maximum length
  if (aiConfig.maxResponseLength > 0 && response.length > aiConfig.maxResponseLength) {
    response = response.substring(0, aiConfig.maxResponseLength - 3) + '...';
  }

  return response;
};
