/**
 * Enhanced AI Service
 * This service provides improved AI response generation with context management,
 * intent detection, and multi-language support.
 */

import { Message } from '../types/whatsapp';
import { whatsappService } from './whatsappService';
import { apiClient } from '../lib/apiClient';
import { aiConfig } from '../config/aiConfig';

// Define message context interface
interface MessageContext {
  clientId: string;
  clientName: string;
  conversationHistory: Message[];
  lastResponseTime: number;
}

// Define intent types
type Intent =
  | 'greeting'
  | 'goodbye'
  | 'thanks'
  | 'book_appointment'
  | 'cancel_appointment'
  | 'check_appointment'
  | 'help'
  | 'hours'
  | 'services'
  | 'prices'
  | 'location'
  | 'unknown';

// Store conversation contexts
const conversationContexts = new Map<string, MessageContext>();

// Maximum context history size
const MAX_CONTEXT_SIZE = 10;

// Context expiration time (30 minutes)
const CONTEXT_EXPIRATION_MS = 30 * 60 * 1000;

/**
 * Process a message and generate an AI response
 */
export const generateAIResponse = async (
  contactPhone: string,
  contactName: string,
  message: Message
): Promise<boolean> => {
  // Check if auto-responses are enabled
  if (!aiConfig.enableAutoResponses) {
    console.log(`Auto-responses are disabled. Skipping response to ${contactName}`);
    return false;
  }

  try {
    if (aiConfig.enableDetailedLogging) {
      console.log(`Generating AI response for message from ${contactName}: ${message.text}`);
    }

    // Get or create conversation context
    const context = getOrCreateContext(contactPhone, contactName);

    // Add the new message to the context
    context.conversationHistory.push(message);

    // Trim context if it exceeds the maximum size
    if (context.conversationHistory.length > MAX_CONTEXT_SIZE) {
      context.conversationHistory = context.conversationHistory.slice(-MAX_CONTEXT_SIZE);
    }

    // Update the context in the map
    conversationContexts.set(contactPhone, context);

    // Detect the language of the message
    const language = detectLanguage(message.text);

    // Detect the intent of the message
    const intent = detectIntent(message.text);

    // First, try to find or create a client based on the phone number
    let clientId = contactPhone;
    try {
      const clientResponse = await apiClient.post('/clients/find-by-phone', {
        phone: contactPhone.replace(/^whatsapp-/, ''),
        name: contactName
      });

      if (clientResponse && clientResponse.id) {
        clientId = clientResponse.id;
        console.log(`Found client with ID ${clientId} for phone ${contactPhone}`);
      }
    } catch (clientError) {
      console.warn('Could not find client by phone, will use phone as ID:', clientError);
    }

    // Generate response based on intent, language, and context
    const response = await getAIResponseText(contactName, message.text, intent, language, context);

    // Add a delay to make the response seem more natural
    if (aiConfig.responseDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, aiConfig.responseDelay));
    }

    // Send the AI response back via WhatsApp
    const success = await whatsappService.sendDirectMessage(contactPhone, response);

    if (success) {
      // Add the response to the conversation history
      context.conversationHistory.push({
        id: `response-${Date.now()}`,
        text: response,
        timestamp: new Date().toISOString(),
        sender: 'me',
        status: 'read'
      });

      // Update the last response time
      context.lastResponseTime = Date.now();

      // Update the context in the map
      conversationContexts.set(contactPhone, context);

      console.log(`AI response sent successfully to ${contactName}: "${response}"`);
    } else {
      console.error(`Failed to send AI response to ${contactName}`);
    }

    return success;
  } catch (error) {
    console.error('Error generating AI response:', error);
    return false;
  }
};

/**
 * Get or create a conversation context for a contact
 */
const getOrCreateContext = (contactPhone: string, contactName: string): MessageContext => {
  // Check if we have an existing context
  if (conversationContexts.has(contactPhone)) {
    const context = conversationContexts.get(contactPhone)!;

    // Check if the context has expired
    if (Date.now() - context.lastResponseTime > CONTEXT_EXPIRATION_MS) {
      // Context has expired, create a new one
      return {
        clientId: contactPhone,
        clientName: contactName,
        conversationHistory: [],
        lastResponseTime: Date.now()
      };
    }

    return context;
  }

  // Create a new context
  return {
    clientId: contactPhone,
    clientName: contactName,
    conversationHistory: [],
    lastResponseTime: Date.now()
  };
};

/**
 * Detect the language of a message
 */
const detectLanguage = (text: string): string => {
  // Simple language detection based on common words
  const lowerText = text.toLowerCase();

  // Spanish detection
  if (
    lowerText.includes('hola') ||
    lowerText.includes('gracias') ||
    lowerText.includes('por favor') ||
    lowerText.includes('buenos días') ||
    lowerText.includes('buenas tardes') ||
    lowerText.includes('buenas noches') ||
    lowerText.includes('cómo estás') ||
    lowerText.includes('qué tal')
  ) {
    return 'es';
  }

  // Catalan detection
  if (
    lowerText.includes('bon dia') ||
    lowerText.includes('bona tarda') ||
    lowerText.includes('bona nit') ||
    lowerText.includes('gràcies') ||
    lowerText.includes('si us plau') ||
    lowerText.includes('com estàs')
  ) {
    return 'ca';
  }

  // Default to English
  return 'en';
};

/**
 * Detect the intent of a message
 */
const detectIntent = (text: string): Intent => {
  const lowerText = text.toLowerCase();

  // Greeting detection
  if (
    lowerText.match(/^(hi|hello|hey|good morning|good afternoon|good evening|hola|buenos dias|buenas tardes)[\s.,!]*$/i) ||
    lowerText.includes('how are you') ||
    lowerText.includes('nice to meet you')
  ) {
    return 'greeting';
  }

  // Goodbye detection
  if (
    lowerText.match(/^(bye|goodbye|see you|talk to you later|adios|hasta luego)[\s.,!]*$/i) ||
    lowerText.includes('have a good day') ||
    lowerText.includes('have a nice day')
  ) {
    return 'goodbye';
  }

  // Thanks detection
  if (
    lowerText.match(/^(thanks|thank you|gracias|thank)[\s.,!]*$/i) ||
    lowerText.includes('appreciate it') ||
    lowerText.includes('grateful')
  ) {
    return 'thanks';
  }

  // Appointment booking detection
  if (
    lowerText.includes('book') ||
    lowerText.includes('schedule') ||
    lowerText.includes('appointment') ||
    lowerText.includes('reserve') ||
    lowerText.includes('make an appointment') ||
    lowerText.includes('set up an appointment')
  ) {
    return 'book_appointment';
  }

  // Appointment cancellation detection
  if (
    lowerText.includes('cancel') ||
    lowerText.includes('reschedule') ||
    lowerText.includes('postpone') ||
    lowerText.includes('change appointment')
  ) {
    return 'cancel_appointment';
  }

  // Appointment check detection
  if (
    lowerText.includes('check appointment') ||
    lowerText.includes('confirm appointment') ||
    lowerText.includes('my appointment') ||
    lowerText.includes('appointment time')
  ) {
    return 'check_appointment';
  }

  // Help detection
  if (
    lowerText.includes('help') ||
    lowerText.includes('assist') ||
    lowerText.includes('support') ||
    lowerText.includes('how do i')
  ) {
    return 'help';
  }

  // Hours detection
  if (
    lowerText.includes('hours') ||
    lowerText.includes('open') ||
    lowerText.includes('close') ||
    lowerText.includes('when are you open') ||
    lowerText.includes('business hours')
  ) {
    return 'hours';
  }

  // Services detection
  if (
    lowerText.includes('services') ||
    lowerText.includes('what do you offer') ||
    lowerText.includes('what services') ||
    lowerText.includes('treatments')
  ) {
    return 'services';
  }

  // Prices detection
  if (
    lowerText.includes('price') ||
    lowerText.includes('cost') ||
    lowerText.includes('fee') ||
    lowerText.includes('how much') ||
    lowerText.includes('pricing')
  ) {
    return 'prices';
  }

  // Location detection
  if (
    lowerText.includes('location') ||
    lowerText.includes('address') ||
    lowerText.includes('where are you') ||
    lowerText.includes('directions') ||
    lowerText.includes('how to get there')
  ) {
    return 'location';
  }

  // Unknown intent
  return 'unknown';
};

/**
 * Generate AI response text based on the message content, intent, and language
 */
const getAIResponseText = async (
  contactName: string,
  messageText: string,
  intent: Intent,
  language: string,
  context: MessageContext
): Promise<string> => {
  // Try to use the backend AI assistant if available
  try {
    // Create a conversation history format for the backend
    const conversationHistory = context.conversationHistory.map(msg => ({
      role: msg.sender === 'me' ? 'assistant' : 'user',
      content: msg.text
    }));

    // First, try to find or create a client based on the phone number
    let clientId = context.clientId;
    try {
      const clientResponse = await apiClient.post('/clients/find-by-phone', {
        phone: context.clientId.replace(/^whatsapp-/, '')
      });

      if (clientResponse && clientResponse.id) {
        clientId = clientResponse.id;
      }
    } catch (clientError) {
      console.warn('Could not find client by phone, will use phone as ID:', clientError);
    }

    // Call the backend AI assistant through the messaging endpoint
    const response = await apiClient.post('/ai/send-message', {
      client_id: clientId,
      message: messageText
    });

    if (response && response.aiResponse && response.aiResponse.text) {
      // Check if an appointment was booked
      if (response.appointment_booked) {
        console.log('Appointment booked:', response.appointment_details);
      }

      return response.aiResponse.text;
    }

    // Fallback to the older endpoint if the new one fails
    const legacyResponse = await apiClient.post('/ai/generate-response', {
      message: messageText,
      client_name: contactName,
      intent: intent,
      language: language,
      conversation_history: conversationHistory
    });

    if (legacyResponse && legacyResponse.text) {
      return legacyResponse.text;
    }
  } catch (error) {
    console.error('Error calling backend AI assistant:', error);
    // Fall back to rule-based responses
  }

  // Fall back to rule-based responses if the backend call fails
  return getRuleBasedResponse(contactName, messageText, intent, language);
};

/**
 * Get a rule-based response based on intent and language
 */
const getRuleBasedResponse = (
  contactName: string,
  messageText: string,
  intent: Intent,
  language: string
): string => {
  // English responses
  if (language === 'en') {
    switch (intent) {
      case 'greeting':
        return `Hello ${contactName}! This is an automated response from FixMyCal. How can I help you today?`;

      case 'goodbye':
        return `Goodbye ${contactName}! Thank you for contacting FixMyCal. Have a great day!`;

      case 'thanks':
        return `You're welcome, ${contactName}! Is there anything else I can help you with?`;

      case 'book_appointment':
        return `I'd be happy to help you book an appointment, ${contactName}. Could you please let me know what day and time would work best for you?`;

      case 'cancel_appointment':
        return `I understand you'd like to cancel or reschedule an appointment, ${contactName}. Could you please confirm which appointment you're referring to?`;

      case 'check_appointment':
        return `I'll check your appointment details for you, ${contactName}. One moment please.`;

      case 'help':
        return `I'm here to help, ${contactName}! I can assist with booking appointments, answering questions about our services, or providing information about our business hours and location. What would you like to know?`;

      case 'hours':
        return `Our business hours are Monday to Friday from 9 AM to 6 PM, and Saturday from 10 AM to 4 PM. We're closed on Sundays.`;

      case 'services':
        return `We offer a variety of services including consultations, treatments, and follow-up appointments. Would you like more specific information about any particular service?`;

      case 'prices':
        return `Our prices vary depending on the specific service. I'd be happy to provide you with detailed pricing information. Which service are you interested in?`;

      case 'location':
        return `We're located at 123 Main Street, Suite 456, in downtown. Would you like me to send you directions?`;

      default:
        return `Thank you for your message, ${contactName}. This is an automated response from FixMyCal. I'll get back to you shortly.`;
    }
  }

  // Spanish responses
  if (language === 'es') {
    switch (intent) {
      case 'greeting':
        return `¡Hola ${contactName}! Esta es una respuesta automatizada de FixMyCal. ¿Cómo puedo ayudarte hoy?`;

      case 'goodbye':
        return `¡Adiós ${contactName}! Gracias por contactar a FixMyCal. ¡Que tengas un buen día!`;

      case 'thanks':
        return `¡De nada, ${contactName}! ¿Hay algo más en lo que pueda ayudarte?`;

      case 'book_appointment':
        return `Me encantaría ayudarte a reservar una cita, ${contactName}. ¿Podrías decirme qué día y hora te vendría mejor?`;

      case 'cancel_appointment':
        return `Entiendo que deseas cancelar o reprogramar una cita, ${contactName}. ¿Podrías confirmar a qué cita te refieres?`;

      case 'check_appointment':
        return `Verificaré los detalles de tu cita, ${contactName}. Un momento por favor.`;

      case 'help':
        return `¡Estoy aquí para ayudar, ${contactName}! Puedo asistirte con reservas de citas, responder preguntas sobre nuestros servicios o proporcionar información sobre nuestro horario y ubicación. ¿Qué te gustaría saber?`;

      case 'hours':
        return `Nuestro horario de atención es de lunes a viernes de 9 AM a 6 PM, y sábados de 10 AM a 4 PM. Cerramos los domingos.`;

      case 'services':
        return `Ofrecemos una variedad de servicios que incluyen consultas, tratamientos y citas de seguimiento. ¿Te gustaría información más específica sobre algún servicio en particular?`;

      case 'prices':
        return `Nuestros precios varían según el servicio específico. Me encantaría proporcionarte información detallada sobre precios. ¿En qué servicio estás interesado?`;

      case 'location':
        return `Estamos ubicados en Calle Principal 123, Suite 456, en el centro. ¿Te gustaría que te enviara indicaciones?`;

      default:
        return `Gracias por tu mensaje, ${contactName}. Esta es una respuesta automatizada de FixMyCal. Me pondré en contacto contigo pronto.`;
    }
  }

  // Catalan responses
  if (language === 'ca') {
    switch (intent) {
      case 'greeting':
        return `Hola ${contactName}! Aquesta és una resposta automatitzada de FixMyCal. Com puc ajudar-te avui?`;

      case 'goodbye':
        return `Adéu ${contactName}! Gràcies per contactar amb FixMyCal. Que tinguis un bon dia!`;

      case 'thanks':
        return `De res, ${contactName}! Hi ha alguna cosa més en què pugui ajudar-te?`;

      case 'book_appointment':
        return `M'encantaria ajudar-te a reservar una cita, ${contactName}. Podries dir-me quin dia i hora et vindria millor?`;

      case 'cancel_appointment':
        return `Entenc que vols cancel·lar o reprogramar una cita, ${contactName}. Podries confirmar a quina cita et refereixes?`;

      case 'check_appointment':
        return `Verificaré els detalls de la teva cita, ${contactName}. Un moment si us plau.`;

      case 'help':
        return `Estic aquí per ajudar, ${contactName}! Puc assistir-te amb reserves de cites, respondre preguntes sobre els nostres serveis o proporcionar informació sobre el nostre horari i ubicació. Què t'agradaria saber?`;

      case 'hours':
        return `El nostre horari d'atenció és de dilluns a divendres de 9 AM a 6 PM, i dissabtes de 10 AM a 4 PM. Tanquem els diumenges.`;

      case 'services':
        return `Oferim una varietat de serveis que inclouen consultes, tractaments i cites de seguiment. T'agradaria informació més específica sobre algun servei en particular?`;

      case 'prices':
        return `Els nostres preus varien segons el servei específic. M'encantaria proporcionar-te informació detallada sobre preus. En quin servei estàs interessat?`;

      case 'location':
        return `Estem ubicats al Carrer Principal 123, Suite 456, al centre. T'agradaria que t'enviés indicacions?`;

      default:
        return `Gràcies pel teu missatge, ${contactName}. Aquesta és una resposta automatitzada de FixMyCal. Em posaré en contacte amb tu aviat.`;
    }
  }

  // Default to English for other languages
  return `Thank you for your message, ${contactName}. This is an automated response from FixMyCal. I'll get back to you shortly.`;
};

/**
 * Clear expired conversation contexts
 */
export const clearExpiredContexts = (): void => {
  const now = Date.now();

  for (const [phone, context] of conversationContexts.entries()) {
    if (now - context.lastResponseTime > CONTEXT_EXPIRATION_MS) {
      conversationContexts.delete(phone);
    }
  }
};

// Set up a timer to clear expired contexts every hour
setInterval(clearExpiredContexts, 60 * 60 * 1000);
