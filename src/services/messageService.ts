import { apiClient } from '../lib/apiClient';
// AIMessage is imported but not used directly in this file
// import { aiMessagingService, Message as AIMessage } from './aiMessagingService';
import { aiMessagingService } from './aiMessagingService';

export interface Contact {
  id: string;
  name: string;
  avatar: string;
  lastMessage: string;
  timestamp: string;
  unread: number;
  source: 'whatsapp' | 'ai' | 'app';
  phone?: string;
  email?: string;
}

export interface Message {
  id: string;
  text: string;
  timestamp: string;
  sender: 'me' | 'them' | 'ai';
  status?: 'sent' | 'delivered' | 'read';
  source: 'whatsapp' | 'ai' | 'app';
}

/**
 * Service for handling messages from different sources (WhatsApp, AI, app)
 */
export const messageService = {
  /**
   * Get all contacts with their latest messages
   */
  async getContacts(): Promise<Contact[]> {
    try {
      // Get contacts from the API
      const response = await apiClient.get<{ contacts: Contact[] }>('/dashboard/messages/contacts');
      return response.contacts;
    } catch (error) {
      console.error('Error fetching contacts:', error);

      // Return empty array if API call fails
      return [];
    }
  },

  /**
   * Get messages for a specific contact
   */
  async getMessages(contactId: string, source: 'whatsapp' | 'ai' | 'app'): Promise<Message[]> {
    try {
      if (source === 'ai') {
        // For AI messages, use the AI messaging service
        const aiMessages = await aiMessagingService.getMessages(contactId);

        // Convert AI messages to the common Message format
        return aiMessages.map(msg => ({
          id: msg.id,
          text: msg.content,
          timestamp: msg.timestamp,
          sender: msg.direction === 'incoming' ? 'them' : 'me',
          source: 'ai'
        }));
      } else {
        // For WhatsApp or app messages, use the regular API
        const response = await apiClient.get<{ messages: Message[] }>(`/dashboard/messages/${contactId}`);
        return response.messages;
      }
    } catch (error) {
      console.error(`Error fetching messages for contact ${contactId}:`, error);
      return [];
    }
  },

  /**
   * Send a message to a contact
   */
  async sendMessage(contactId: string, message: string, source: 'whatsapp' | 'ai' | 'app'): Promise<Message | null> {
    try {
      if (source === 'ai') {
        // For AI messages, use the AI messaging service
        const response = await aiMessagingService.sendMessage({
          client_id: contactId,
          message
        });

        if (response.success) {
          // Create a sent message object
          const sentMessage: Message = {
            id: `sent-${Date.now()}`,
            text: message,
            timestamp: new Date().toISOString(),
            sender: 'me',
            status: 'sent',
            source: 'ai'
          };

          // Create an AI response message object
          // This message is created but not directly used in this function
          // It will be used in future implementation when we handle AI responses
          // const aiResponseMessage: Message = {
          //   id: `ai-${Date.now()}`,
          //   text: response.message,
          //   timestamp: new Date().toISOString(),
          //   sender: 'them',
          //   source: 'ai'
          // };

          // Return the sent message (the component will handle adding both messages)
          return sentMessage;
        }
      } else {
        // For WhatsApp or app messages, use the regular API
        const response = await apiClient.post<{ message: Message }>(`/dashboard/messages/${contactId}/send`, {
          text: message,
          source
        });

        return response.message;
      }

      return null;
    } catch (error) {
      console.error(`Error sending message to contact ${contactId}:`, error);
      return null;
    }
  },

  /**
   * Mark messages as read
   */
  async markAsRead(contactId: string, source: 'whatsapp' | 'ai' | 'app'): Promise<boolean> {
    try {
      await apiClient.post(`/dashboard/messages/${contactId}/read`, { source });
      return true;
    } catch (error) {
      console.error(`Error marking messages as read for contact ${contactId}:`, error);
      return false;
    }
  },

  /**
   * Get client avatar URL
   */
  getAvatarUrl(name: string): string {
    // Generate a consistent color based on the name
    const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const hue = hash % 360;

    // Return a placeholder avatar with the first letter of the name
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=${hue.toString(16)}0&color=fff`;
  }
};
