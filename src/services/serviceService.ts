import { apiClient } from '../lib/apiClient';

export interface ServiceType {
  id: string;
  name: string;
}

export interface Service {
  id: string;
  user_id: string;
  name: string;
  description: string;
  price: number;
  duration_minutes: number;
  type_id: string;
  active: boolean;
}

export interface PaginatedServices {
  items: Service[];
  total: number;
  page: number;
  limit: number;
}

/**
 * Service service for managing services and service types
 */
export const serviceService = {
  /**
   * Get all service types
   */
  async getServiceTypes(): Promise<ServiceType[]> {
    return apiClient.get<ServiceType[]>('/dashboard/service-types');
  },

  /**
   * Create a new service type
   */
  async createServiceType(name: string): Promise<ServiceType> {
    return apiClient.post<ServiceType>('/dashboard/service-types', { name });
  },

  /**
   * Update an existing service type
   */
  async updateServiceType(id: string, name: string): Promise<ServiceType> {
    return apiClient.put<ServiceType>(`/dashboard/service-types/${id}`, { name });
  },

  /**
   * Delete a service type
   */
  async deleteServiceType(id: string): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.delete<{ success: boolean; message: string }>(`/dashboard/service-types/${id}`);

    // If the response has success: false, throw an error with the message
    if (response && response.success === false) {
      throw new Error(response.message);
    }

    return response;
  },

  /**
   * Get a paginated list of services
   */
  async getServices(params: {
    page?: number;
    limit?: number;
  } = {}): Promise<PaginatedServices> {
    // Build query parameters
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('page_size', params.limit.toString());

    const queryString = queryParams.toString();
    const endpoint = `/dashboard/services${queryString ? `?${queryString}` : ''}`;

    return apiClient.get<PaginatedServices>(endpoint);
  },

  /**
   * Get a service by ID
   * Note: This uses the list endpoint with a filter since the direct GET by ID endpoint may not be available
   */
  async getService(id: string): Promise<Service | null> {
    try {
      // Get all services (usually a small list)
      const response = await serviceService.getServices({ limit: 100 });

      // Find the service in the response
      const service = response.items.find(service => service.id === id);

      if (!service) {
        console.warn(`Service with ID ${id} not found in response`);
        return null;
      }

      return service;
    } catch (error) {
      console.error(`Error fetching service with ID ${id}:`, error);
      return null;
    }
  },

  /**
   * Create a new service
   */
  async createService(service: Omit<Service, 'id' | 'user_id'>): Promise<Service> {
    return apiClient.post<Service>('/dashboard/services', service);
  },

  /**
   * Update an existing service
   */
  async updateService(id: string, service: Partial<Omit<Service, 'id' | 'user_id'>>): Promise<Service> {
    return apiClient.put<Service>(`/dashboard/services/${id}`, service);
  },

  /**
   * Delete a service
   */
  async deleteService(id: string): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.delete<{ success: boolean; message: string }>(`/dashboard/services/${id}`);

    // If the response has success: false, throw an error with the message
    if (response && response.success === false) {
      throw new Error(response.message);
    }

    return response;
  }
};
