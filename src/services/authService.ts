import { apiClient } from '../lib/apiClient';
import { AuthTokens, User } from '../types/api';
import { useAuthStore } from '../lib/auth';

/**
 * Authentication service for handling login, registration, and token management
 */
export const authService = {
  /**
   * Login a user with email and password
   * @param email User's email
   * @param password User's password
   * @param token Optional 2FA token
   * @param backupCode Optional 2FA backup code
   * @returns Promise that resolves when login is successful, or returns 2FA required info
   */
  async login(email: string, password: string, token?: string, backupCode?: string): Promise<void | { requires_2fa: boolean, user_id: string }> {
    try {
      const data = await apiClient.post<AuthTokens | { requires_2fa: boolean, user_id: string }>('/auth/login', {
        email,
        password,
        token,
        backup_code: backupCode
      });

      // Check if 2FA is required
      if ('requires_2fa' in data && data.requires_2fa) {
        return {
          requires_2fa: true,
          user_id: data.user_id
        };
      }

      // Normal login flow
      useAuthStore.getState().setTokens({
        accessToken: data.access_token,
        refreshToken: data.refresh_token,
      });
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  /**
   * Register a new user
   */
  async register(email: string, password: string, businessName: string): Promise<void> {
    try {
      const data = await apiClient.post<AuthTokens>('/auth/register', {
        email,
        password,
        business_name: businessName
      });

      useAuthStore.getState().setTokens({
        accessToken: data.access_token,
        refreshToken: data.refresh_token,
      });
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  },

  /**
   * Logout the current user
   */
  logout(): void {
    useAuthStore.getState().logout();
  },

  /**
   * Get the current user profile
   */
  async getCurrentUser(): Promise<User> {
    return apiClient.get<User>('/auth/me');
  },

  /**
   * Check if the user is authenticated
   */
  isAuthenticated(): boolean {
    return useAuthStore.getState().isAuthenticated;
  },

  /**
   * Refresh the access token using the refresh token
   */
  async refreshToken(refreshToken: string): Promise<AuthTokens> {
    try {
      const data = await apiClient.post<AuthTokens>('/auth/refresh-token', {
        refresh_token: refreshToken
      });

      useAuthStore.getState().setTokens({
        accessToken: data.access_token,
        refreshToken: data.refresh_token,
      });

      return data;
    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    }
  }
};
