import { apiClient } from '../lib/apiClient';

export interface Message {
  id: string;
  content: string;
  direction: 'incoming' | 'outgoing';
  timestamp: string;
}

export interface SendMessageRequest {
  client_id: string;
  message: string;
}

export interface SendMessageResponse {
  success: boolean;
  message: string;
  appointment_booked?: boolean;
  appointment_details?: {
    client_name: string;
    service_id: string;
    start_time: string;
  };
}

/**
 * Service for AI messaging functionality
 */
export const aiMessagingService = {
  /**
   * Send a message to the AI assistant
   */
  async sendMessage(data: SendMessageRequest): Promise<SendMessageResponse> {
    try {
      return await apiClient.post<SendMessageResponse>('/ai/send-message', data);
    } catch (error) {
      console.error('Error sending message to AI:', error);
      return {
        success: false,
        message: 'Failed to send message to AI assistant'
      };
    }
  },

  /**
   * Get message history for a client
   */
  async getMessages(clientId: string): Promise<Message[]> {
    try {
      return await apiClient.get<Message[]>(`/ai/messages/${clientId}`);
    } catch (error) {
      console.error('Error getting message history:', error);
      return [];
    }
  }
};
