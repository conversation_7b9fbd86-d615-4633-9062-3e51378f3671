import { apiClient } from '../lib/apiClient';
import { Client as ApiClient, PaginatedClients } from '../types/api';
import { Client, mapClientToApiClient, mapApiClientToClient } from '../types/client';

/**
 * Client service for managing clients
 */
export const clientService = {
  /**
   * Get a paginated list of clients
   */
  async getClients(params: {
    page?: number;
    page_size?: number;
    sort_by?: string;
    order?: 'asc' | 'desc';
    search?: string;
    tag_filter?: string;
  }): Promise<PaginatedClients> {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      if (params.page) queryParams.append('page', params.page.toString());
      if (params.page_size) queryParams.append('page_size', params.page_size.toString());
      if (params.sort_by) queryParams.append('sort_by', params.sort_by);
      if (params.order) queryParams.append('order', params.order);
      if (params.search) queryParams.append('search', params.search);
      if (params.tag_filter) queryParams.append('tag_filter', params.tag_filter);

      const queryString = queryParams.toString();
      const endpoint = `/dashboard/clients${queryString ? `?${queryString}` : ''}`;

      return await apiClient.get<PaginatedClients>(endpoint);
    } catch (error) {
      console.error('Error fetching clients:', error);
      // Return empty result if API fails
      return {
        items: [],
        total: 0,
        page: params.page || 1,
        page_size: params.page_size || 10,
        total_pages: 0
      };
    }
  },

  /**
   * Get a client by ID
   * Note: This uses the list endpoint with a filter since the direct GET by ID endpoint may not be available
   */
  async getClient(id: string): Promise<Client | null> {
    if (!id) {
      console.warn('No client ID provided to getClient');
      return null;
    }

    try {
      // First try to get the client directly by ID
      try {
        const directClient = await apiClient.get<ApiClient>(`/dashboard/clients/${id}`);
        console.log('Client fetched directly:', directClient);
        return mapApiClientToClient(directClient);
      } catch (directError) {
        console.log('Could not fetch client directly, trying list endpoint');
      }

      // Fallback to using the list endpoint with a filter
      const response = await apiClient.get<PaginatedClients>(`/dashboard/clients?id=${id}`);
      console.log('Client list response:', response);

      // Find the client in the response
      const clientData = response.items.find(client => client.id === id);

      if (!clientData) {
        console.warn(`Client with ID ${id} not found in response`);
        return null;
      }

      console.log('Found client in list:', clientData);
      return mapApiClientToClient(clientData);
    } catch (error) {
      console.error(`Error fetching client with ID ${id}:`, error);
      return null;
    }
  },

  /**
   * Create a new client
   */
  async createClient(client: Partial<Client>): Promise<Client> {
    // Convert the client to API format
    const clientData = mapClientToApiClient(client as Client);

    // Generate a UUID for the client and add created_at field
    const clientWithRequiredFields = {
      ...clientData,
      id: crypto.randomUUID(),
      created_at: new Date().toISOString()
    };

    const response = await apiClient.post<ApiClient>('/dashboard/clients', clientWithRequiredFields);
    return mapApiClientToClient(response);
  },

  /**
   * Update an existing client
   */
  async updateClient(id: string, client: Partial<Client>): Promise<Client> {
    // Convert the client to API format
    const clientData = mapClientToApiClient(client as Client);

    const response = await apiClient.put<ApiClient>(`/dashboard/clients/${id}`, clientData);
    return mapApiClientToClient(response);
  },

  /**
   * Delete a client
   */
  async deleteClient(id: string): Promise<void> {
    return apiClient.delete<void>(`/dashboard/clients/${id}`);
  }
};
