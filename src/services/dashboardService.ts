// These imports are for future implementation when the dashboard connects to real data
// import { apiClient } from '../lib/apiClient';
import { appointmentService } from './appointmentService';
import { clientService } from './clientService';
// import { evolutionApiService } from './evolutionApiService';

export interface DashboardStats {
  totalAppointments: number;
  activeClients: number;
  messagesHandled: number;
  timeSaved: number;
  appointmentTrend: number;
  clientTrend: number;
  messageTrend: number;
  timeTrend: number;
}

export interface AppointmentsByStatus {
  confirmed: number;
  pending: number;
  cancelled: number;
}

export interface AppointmentsByMonth {
  month: string;
  confirmed: number;
  cancelled: number;
  pending: number;
}

export interface ClientsByMonth {
  month: string;
  count: number;
}

export interface MessagesByDay {
  day: string;
  count: number;
  aiResponses: number;
}

export const dashboardService = {
  /**
   * Get dashboard statistics
   */
  async getStats(): Promise<DashboardStats> {
    // For development/testing, return mock data
    return {
      totalAppointments: 42,
      activeClients: 5,
      messagesHandled: 128,
      timeSaved: 11,
      appointmentTrend: 15,
      clientTrend: 20,
      messageTrend: 30,
      timeTrend: 25
    };
  },

  /**
   * Get appointments by status for pie chart
   */
  async getAppointmentsByStatus(): Promise<AppointmentsByStatus> {
    // For development/testing, return mock data
    return {
      confirmed: 8,
      pending: 0,
      cancelled: 0
    };
  },

  /**
   * Get appointments by month for line chart
   */
  async getAppointmentsByMonth(): Promise<AppointmentsByMonth[]> {
    // For development/testing, return mock data
    return [
      { month: 'Dec', confirmed: 0, cancelled: 0, pending: 0 },
      { month: 'Jan', confirmed: 0, cancelled: 0, pending: 0 },
      { month: 'Feb', confirmed: 0, cancelled: 0, pending: 0 },
      { month: 'Mar', confirmed: 2, cancelled: 0, pending: 0 },
      { month: 'Apr', confirmed: 8, cancelled: 0, pending: 0 },
      { month: 'May', confirmed: 0, cancelled: 0, pending: 0 }
    ];
  },
  /**
   * Get clients by month for bar chart
   */
  async getClientsByMonth(): Promise<ClientsByMonth[]> {
    try {
      let clients = [];
      try {
        const response = await clientService.getClients({});
        clients = response.items || [];
      } catch (error) {
        console.warn('Could not fetch clients for monthly chart:', error);
        // Continue with empty clients array
      }

      // Create a map to store clients by month
      const monthsMap = new Map<string, number>();

      // Get the last 6 months
      const months = [];
      const today = new Date();
      for (let i = 5; i >= 0; i--) {
        const d = new Date(today.getFullYear(), today.getMonth() - i, 1);
        const monthName = d.toLocaleString('default', { month: 'short' });
        months.push(monthName);
        monthsMap.set(monthName, 0);
      }

      // Count clients by creation month
      clients.forEach(client => {
        const date = new Date(client.created_at);
        const monthName = date.toLocaleString('default', { month: 'short' });

        if (monthsMap.has(monthName)) {
          monthsMap.set(monthName, monthsMap.get(monthName)! + 1);
        }
      });

      // Convert map to array
      return months.map(month => ({
        month,
        count: monthsMap.get(month)!
      }));
    } catch (error) {
      console.error('Error fetching clients by month:', error);
      throw error;
    }
  },

  /**
   * Get messages by day for area chart
   */
  async getMessagesByDay(): Promise<MessagesByDay[]> {
    try {
      // In a real implementation, you would fetch this from the backend
      // For now, we'll generate some sample data

      // days array is not used but might be needed in future implementation
      // const days = [];
      const result = [];
      const today = new Date();

      for (let i = 6; i >= 0; i--) {
        const d = new Date(today);
        d.setDate(d.getDate() - i);
        const dayName = d.toLocaleString('default', { weekday: 'short' });

        // Generate random data
        const count = Math.floor(Math.random() * 20) + 5;
        const aiResponses = Math.floor(count * 0.7);

        result.push({
          day: dayName,
          count,
          aiResponses
        });
      }

      return result;
    } catch (error) {
      console.error('Error fetching messages by day:', error);
      throw error;
    }
  },

  /**
   * Get upcoming appointments for dashboard
   */
  async getUpcomingAppointments(limit = 5) {
    try {
      let appointments = [];
      try {
        const response = await appointmentService.getAppointments({});
        appointments = response.items || [];
      } catch (error) {
        console.warn('Could not fetch upcoming appointments:', error);
        return [];
      }

      // Filter future appointments and sort by date
      const now = new Date();
      const upcomingAppointments = appointments
        .filter(appointment => new Date(appointment.start_time) > now)
        .sort((a, b) => new Date(a.start_time).getTime() - new Date(b.start_time).getTime())
        .slice(0, limit);

      return upcomingAppointments;
    } catch (error) {
      console.error('Error fetching upcoming appointments:', error);
      throw error;
    }
  },

  /**
   * Get recent messages for dashboard
   */
  async getRecentMessages(/* limit = 5 */) {
    try {
      // In a real implementation, you would fetch this from the backend
      // For now, we'll return an empty array
      return [];
    } catch (error) {
      console.error('Error fetching recent messages:', error);
      throw error;
    }
  }
};
