import { Client as ApiClient } from './api';

/**
 * Frontend client model with camelCase properties
 */
export interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  lastAppointment?: string | null;
  nextAppointment?: string | null;
  tags: string[];
  notes: string;
  profileImage?: string;
}

/**
 * Form data for creating/editing a client
 */
export interface ClientFormData {
  name: string;
  email: string;
  phone: string;
  tags: string[];
  notes: string;
  profileImage?: string;
}

/**
 * Convert API client to frontend client
 */
export function mapApiClientToClient(apiClient: ApiClient): Client {
  return {
    id: apiClient.id,
    name: apiClient.name,
    email: apiClient.email,
    phone: apiClient.phone,
    tags: apiClient.tags || [],
    lastAppointment: apiClient.last_appointment || null,
    nextAppointment: apiClient.next_appointment || null,
    notes: apiClient.notes || "",
    profileImage: apiClient.profile_image
  };
}

/**
 * Convert frontend client to API client format
 */
export function mapClientToApiClient(client: Client): Partial<ApiClient> {
  return {
    name: client.name,
    email: client.email,
    phone: client.phone,
    tags: client.tags,
    notes: client.notes,
    last_appointment: client.lastAppointment,
    next_appointment: client.nextAppointment,
    profile_image: client.profileImage
  };
}
