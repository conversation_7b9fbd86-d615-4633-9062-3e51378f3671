/**
 * API response types
 */

// Auth types
export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

export interface User {
  id: string;
  email: string;
  business_name?: string;
  created_at: string;
  phone?: string;
  address?: string;
  website?: string;
  currency?: string;
  logo_url?: string;
  two_factor_enabled?: boolean;
}

// Client types
export interface Client {
  id: string;
  user_id: string;
  name: string;
  email: string;
  phone: string;
  tags: string[];
  notes: string;
  last_appointment: string | null;
  next_appointment: string | null;
  created_at: string;
  profile_image?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export type PaginatedClients = PaginatedResponse<Client>;

// Appointment types
export interface Appointment {
  id: string;
  user_id: string;
  client_id: string;
  title: string;
  description: string;
  start_time: string;
  end_time: string;
  status: 'PENDING' | 'CONFIRMED' | 'CANCELLED';
  created_at: string;
  client?: Client;
  service_id?: string;
  service_name?: string;
  notes?: string;
  location?: string;
  googleEventId?: string;
  source?: 'google' | 'app';
}

// Service types
export interface Service {
  id: string;
  user_id: string;
  name: string;
  description: string;
  duration: number;
  price: number;
  color: string;
  created_at: string;
}

// Message types
export interface Message {
  id: string;
  text: string;
  timestamp: string;
  sender: 'me' | 'them';
  source?: 'whatsapp' | 'ai' | 'app';
}

// WhatsApp types
export interface WhatsAppInstance {
  id: string;
  name: string;
  status: 'connected' | 'disconnected' | 'connecting';
  qrCode?: string;
}

// AI Configuration types
export interface AIConfiguration {
  id: string;
  user_id: string;
  model: string;
  temperature: number;
  max_tokens: number;
  system_prompt: string;
  enabled: boolean;
  created_at: string;
  updated_at: string;
}

// Google Calendar types
export interface GoogleCalendarEvent {
  id: string;
  summary: string;
  description?: string;
  location?: string;
  start: {
    dateTime: string;
    timeZone?: string;
  };
  end: {
    dateTime: string;
    timeZone?: string;
  };
  status?: string;
  attendees?: {
    email: string;
    displayName?: string;
    responseStatus?: string;
  }[];
  created?: string;
  updated?: string;
  htmlLink?: string;
}

// Dashboard types
export interface DashboardStats {
  totalAppointments: number;
  activeClients: number;
  messagesHandled: number;
  timeSaved: number;
}

export interface ChartData {
  month?: string;
  day?: string;
  count: number;
}

// Error types
export interface ApiError {
  detail: string;
  status_code?: number;
  message?: string;
}
