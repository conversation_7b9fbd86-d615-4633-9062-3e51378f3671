// WhatsApp related types

export interface Contact {
  id: string;
  name: string;
  phone: string;
  avatar?: string;
  lastMessage?: string;
  timestamp?: string;
  unread?: number;
  _debug?: Record<string, unknown>; // For debugging purposes
}

export interface Message {
  id: string;
  text: string;
  timestamp: string;
  sender: 'me' | 'them' | 'system';
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  type?: 'text' | 'image' | 'video' | 'audio' | 'document';
  fileName?: string;
  fileSize?: number;
  mimeType?: string;
  mediaUrl?: string;
  originalMessageId?: string; // Original WhatsApp message ID
  source?: 'whatsapp' | 'ai' | 'app'; // Source of the message
  isAppointment?: boolean; // Whether this is an appointment-related message
  appointmentDetails?: {
    clientName?: string;
    serviceId?: string;
    startTime?: string;
    endTime?: string;
  }; // Details of the appointment if this is an appointment message
}

export interface WhatsAppMessageResponse {
  key?: {
    remoteJid?: string;
    fromMe?: boolean;
  };
  message?: {
    conversation?: string;
    extendedTextMessage?: {
      text?: string;
    };
    imageMessage?: {
      caption?: string;
      url?: string;
    };
    videoMessage?: {
      caption?: string;
      url?: string;
    };
    audioMessage?: {
      url?: string;
    };
    documentMessage?: {
      fileName?: string;
      url?: string;
    };
  };
  messageTimestamp?: number;
  pushName?: string;
  messageType?: string;
}

export interface MessageGroup {
  date: string;
  messages: Message[];
}
