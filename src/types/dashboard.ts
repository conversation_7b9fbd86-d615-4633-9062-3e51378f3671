export interface DashboardWidget {
  id: string;
  type: 'stat' | 'pie-chart' | 'line-chart' | 'bar-chart' | 'area-chart' | 'list';
  title: string;
  dataSource: string;
  w: number; // Width in grid units
  h: number; // Height in grid units
  x: number; // Column position
  y: number; // Row position
  minW?: number; // Minimum width
  minH?: number; // Minimum height
  static?: boolean; // Whether the widget can be moved/resized
}

export interface DashboardConfig {
  widgets: DashboardWidget[];
}

// Default dashboard configuration
export const DEFAULT_DASHBOARD_CONFIG: DashboardConfig = {
  widgets: [
    // Stats widgets
    {
      id: 'total-appointments',
      type: 'stat',
      title: 'Total Appointments',
      dataSource: 'stats.totalAppointments',
      w: 3,
      h: 1,
      x: 0,
      y: 0,
      minW: 2,
      minH: 1
    },
    {
      id: 'active-clients',
      type: 'stat',
      title: 'Active Clients',
      dataSource: 'stats.activeClients',
      w: 3,
      h: 1,
      x: 3,
      y: 0,
      minW: 2,
      minH: 1
    },
    {
      id: 'messages-handled',
      type: 'stat',
      title: 'Messages Handled',
      dataSource: 'stats.messagesHandled',
      w: 3,
      h: 1,
      x: 6,
      y: 0,
      minW: 2,
      minH: 1
    },
    {
      id: 'time-saved',
      type: 'stat',
      title: 'Time Saved',
      dataSource: 'stats.timeSaved',
      w: 3,
      h: 1,
      x: 9,
      y: 0,
      minW: 2,
      minH: 1
    },

    // Chart widgets
    {
      id: 'appointment-status',
      type: 'pie-chart',
      title: 'Appointment Status',
      dataSource: 'appointmentsByStatus',
      w: 6,
      h: 8,
      x: 0,
      y: 1,
      minW: 3,
      minH: 6
    },
    {
      id: 'appointment-trends',
      type: 'line-chart',
      title: 'Appointment Trends',
      dataSource: 'appointmentsByMonth',
      w: 6,
      h: 8,
      x: 6,
      y: 1,
      minW: 3,
      minH: 6
    },

    // List widgets
    {
      id: 'upcoming-appointments',
      type: 'list',
      title: 'Upcoming Appointments',
      dataSource: 'upcomingAppointments',
      w: 6,
      h: 8,
      x: 0,
      y: 9,
      minW: 3,
      minH: 6
    },
    {
      id: 'recent-messages',
      type: 'list',
      title: 'Recent Messages',
      dataSource: 'recentMessages',
      w: 6,
      h: 8,
      x: 6,
      y: 9,
      minW: 3,
      minH: 6
    }
  ]
};

// Additional widget templates that can be added to the dashboard
export const WIDGET_TEMPLATES = [
  {
    id: 'client-growth',
    type: 'bar-chart',
    title: 'Client Growth',
    dataSource: 'clientsByMonth',
    w: 6,
    h: 8,
    minW: 3,
    minH: 6
  },
  {
    id: 'message-activity',
    type: 'area-chart',
    title: 'Message Activity',
    dataSource: 'messagesByDay',
    w: 6,
    h: 8,
    minW: 3,
    minH: 6
  }
];
