/**
 * AI Service Types
 * This file contains type definitions for the AI service.
 */

// Intent types
export type Intent =
  | 'greeting'
  | 'goodbye'
  | 'thanks'
  | 'book_appointment'
  | 'cancel_appointment'
  | 'check_appointment'
  | 'help'
  | 'hours'
  | 'services'
  | 'prices'
  | 'location'
  | 'unknown';

// Language types
export type Language = 'en' | 'es' | 'ca' | 'fr' | 'de' | 'it' | 'pt';

// Message item for conversation history
export interface MessageItem {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

// AI response action
export interface AIAction {
  action: string;
  [key: string]: unknown;
}

// AI response
export interface AIResponse {
  text: string;
  intent?: Intent;
  language: Language;
  action?: AIAction;
}

// AI configuration
export interface AISettings {
  enableAutoResponses: boolean;
  responseDelay: number;
  maxResponseLength: number;
  enableDetailedLogging: boolean;
}

// Message context
export interface MessageContext {
  clientId: string;
  clientName: string;
  conversationHistory: MessageItem[];
  lastResponseTime: number;
}
