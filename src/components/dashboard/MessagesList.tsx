import React from 'react';

interface Message {
  id: string;
  clientName: string;
  content: string;
  timestamp: string;
  read: boolean;
}

interface MessagesListProps {
  messages: Message[];
}

interface MessagesListState {
  loading: boolean;
  error: string | null;
}

export class MessagesList extends React.Component<MessagesListProps, MessagesListState> {
  state = {
    loading: false,
    error: null
  };

  renderMessage(message: Message) {
    return (
      <div key={message.id} className="flex items-start gap-4 p-4 border-b border-gray-100">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <p className="font-medium text-gray-900">{message.clientName}</p>
            {!message.read && (
              <span className="inline-block w-2 h-2 bg-blue-600 rounded-full"></span>
            )}
          </div>
          <p className="text-sm text-gray-600 line-clamp-2">{message.content}</p>
          <p className="text-xs text-gray-500 mt-1">{message.timestamp}</p>
        </div>
      </div>
    );
  }

  render() {
    const { messages } = this.props;
    const { loading, error } = this.state;

    if (loading) {
      return <div className="text-center py-8">Loading messages...</div>;
    }

    if (error) {
      return <div className="text-center py-8 text-red-600">{error}</div>;
    }

    if (!messages.length) {
      return (
        <p className="text-gray-600 text-center py-8">
          No new messages
        </p>
      );
    }

    return (
      <div className="divide-y divide-gray-100">
        {messages.map(message => this.renderMessage(message))}
      </div>
    );
  }
}
