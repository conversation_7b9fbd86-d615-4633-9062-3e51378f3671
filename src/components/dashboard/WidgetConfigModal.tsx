import React, { useState, useEffect } from 'react';
import { DashboardWidgetConfig } from '../../types/dashboard';
import { X } from 'lucide-react';

interface WidgetConfigModalProps {
  widget: DashboardWidgetConfig | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (widget: DashboardWidgetConfig) => void;
}

export const WidgetConfigModal: React.FC<WidgetConfigModalProps> = ({
  widget,
  isOpen,
  onClose,
  onSave
}) => {
  const [title, setTitle] = useState('');
  const [type, setType] = useState<DashboardWidgetConfig['type']>('stat');
  const [dataSource, setDataSource] = useState('');
  const [width, setWidth] = useState(6);
  const [height, setHeight] = useState(2);
  
  // Initialize form when widget changes
  useEffect(() => {
    if (widget) {
      setTitle(widget.title);
      setType(widget.type);
      setDataSource(widget.dataSource);
      setWidth(widget.width);
      setHeight(widget.height);
    }
  }, [widget]);
  
  const handleSave = () => {
    if (!widget) return;
    
    onSave({
      ...widget,
      title,
      type,
      dataSource,
      width,
      height
    });
    
    onClose();
  };
  
  if (!isOpen || !widget) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
        <div className="flex items-center justify-between px-6 py-4 border-b">
          <h2 className="text-lg font-semibold">Configure Widget</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-6 space-y-4">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Widget Title
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          {/* Widget Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Widget Type
            </label>
            <select
              value={type}
              onChange={(e) => setType(e.target.value as DashboardWidgetConfig['type'])}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="stat">Statistic</option>
              <option value="pie-chart">Pie Chart</option>
              <option value="line-chart">Line Chart</option>
              <option value="bar-chart">Bar Chart</option>
              <option value="area-chart">Area Chart</option>
              <option value="list">List</option>
            </select>
          </div>
          
          {/* Data Source */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Data Source
            </label>
            <select
              value={dataSource}
              onChange={(e) => setDataSource(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <optgroup label="Statistics">
                <option value="stats.totalAppointments">Total Appointments</option>
                <option value="stats.activeClients">Active Clients</option>
                <option value="stats.messagesHandled">Messages Handled</option>
                <option value="stats.timeSaved">Time Saved</option>
              </optgroup>
              <optgroup label="Charts">
                <option value="appointmentsByStatus">Appointment Status</option>
                <option value="appointmentsByMonth">Appointment Trends</option>
                <option value="clientsByMonth">Client Growth</option>
                <option value="messagesByDay">Message Activity</option>
              </optgroup>
              <optgroup label="Lists">
                <option value="upcomingAppointments">Upcoming Appointments</option>
                <option value="recentMessages">Recent Messages</option>
              </optgroup>
            </select>
          </div>
          
          {/* Size */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Width (1-12)
              </label>
              <input
                type="number"
                min="1"
                max="12"
                value={width}
                onChange={(e) => setWidth(Math.min(12, Math.max(1, parseInt(e.target.value))))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Height
              </label>
              <input
                type="number"
                min="1"
                max="4"
                value={height}
                onChange={(e) => setHeight(Math.min(4, Math.max(1, parseInt(e.target.value))))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
        
        <div className="px-6 py-4 border-t bg-gray-50 flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};
