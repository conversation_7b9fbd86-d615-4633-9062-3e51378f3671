import { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../lib/auth';
import { userService } from '../../services';
import { ThemeToggle } from '../ui';
import {
  Calendar,
  MessageSquare,
  Users,
  Settings,
  LogOut,
  Menu,
  X,
  ChevronDown,
  BrainCircuit,
  LayoutTemplate
} from 'lucide-react';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuthStore();

  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  // Initialize sidebar collapsed state from localStorage or default to false
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
    const saved = localStorage.getItem('sidebarCollapsed');
    return saved ? JSON.parse(saved) : false;
  });

  // Track if the sidebar is being hovered
  const [isSidebarHovered, setIsSidebarHovered] = useState(false);

  // Force sidebar to be expanded for a short time after page load/navigation
  const [forceExpanded, setForceExpanded] = useState(true);
  const expandTimerRef = useRef<number | null>(null);
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);

  // Get user logo URL
  const [userLogoUrl, setUserLogoUrl] = useState<string | null>(null);

  // Load user logo on mount
  useEffect(() => {
    const logoUrl = userService.getUserLogoUrl();
    setUserLogoUrl(logoUrl);

    // Set up an interval to check for logo updates
    const intervalId = setInterval(() => {
      const updatedLogoUrl = userService.getUserLogoUrl();
      setUserLogoUrl(prevLogoUrl => {
        if (updatedLogoUrl !== prevLogoUrl) {
          return updatedLogoUrl;
        }
        return prevLogoUrl;
      });
    }, 5000); // Check every 5 seconds

    return () => clearInterval(intervalId);
  }, []);

  // On initial render, set a timer to collapse the sidebar after 1 second
  useEffect(() => {
    // Clear any existing timer
    if (expandTimerRef.current) {
      clearTimeout(expandTimerRef.current);
    }

    // Force sidebar to be expanded
    setForceExpanded(true);

    // Set timer to allow sidebar to collapse after delay
    expandTimerRef.current = window.setTimeout(() => {
      setForceExpanded(false);
    }, 1000); // Keep expanded for 1 second

    // Cleanup timer on unmount
    return () => {
      if (expandTimerRef.current) {
        clearTimeout(expandTimerRef.current);
      }
    };
  }, [location.pathname]); // Re-run when route changes

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const menuItems = [
    { icon: Calendar, label: 'Calendar', path: '/dashboard/calendar' },
    { icon: MessageSquare, label: 'Messages', path: '/dashboard/messages' },
    { icon: Users, label: 'Clients', path: '/dashboard/clients' },
    { icon: BrainCircuit, label: 'AI Configuration', path: '/dashboard/ai-config' },
    { icon: Settings, label: 'Settings', path: '/dashboard/settings' }
  ];

  // Add dashboard to menu items
  const allMenuItems = [
    { icon: LayoutTemplate, label: 'Dashboard', path: '/dashboard' },
    ...menuItems
  ];

  // Function to check if a menu item is active
  const isActive = (path: string) => {
    if (path === '/dashboard' && location.pathname === '/dashboard') {
      return true;
    }
    return location.pathname.startsWith(path) && path !== '/dashboard';
  };

  // Save sidebar collapsed state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', JSON.stringify(isSidebarCollapsed));
  }, [isSidebarCollapsed]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <aside
        className={`fixed top-0 left-0 z-40 h-screen transition-all duration-300 ease-in-out ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } md:translate-x-0 ${isSidebarCollapsed && !forceExpanded && !isSidebarHovered ? 'md:w-16' : 'md:w-64'}`}
        onMouseEnter={() => setIsSidebarHovered(true)}
        onMouseLeave={() => setIsSidebarHovered(false)}
        style={{ transitionDelay: '0ms' }} // Ensure no delay in the transition
      >
        <div
          className={`flex flex-col h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 ease-in-out ${isSidebarCollapsed && !forceExpanded && !isSidebarHovered ? 'w-16' : 'w-64'}`}
          style={{ transitionDelay: '0ms' }} // Ensure no delay in the transition
        >
          <div
            className="flex items-center p-4 border-b border-gray-200 dark:border-gray-700 h-16 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
            onClick={() => {
              // Simply toggle the collapsed state
              setIsSidebarCollapsed(!isSidebarCollapsed);
            }}
            title="Toggle sidebar"
          >
            <div className="w-8 h-8 flex-shrink-0 flex justify-center">
              <img src="/images/Logo.png" alt="FixMyCal Logo" className="h-8 w-8" />
            </div>
            <span className={`text-xl font-bold text-gray-900 dark:text-white ml-2 transition-all duration-300 ${isSidebarCollapsed && !forceExpanded && !isSidebarHovered ? 'opacity-0 max-w-0 overflow-hidden' : 'opacity-100 max-w-full'}`}>FixMyCal</span>
          </div>

          <nav className="flex-1 p-2 space-y-2 overflow-y-auto">
            {allMenuItems.map((item) => (
              <button
                key={item.label}
                onClick={() => {
                  navigate(item.path);
                  // Navigation will trigger the useEffect that forces the sidebar to expand
                }}
                className={`flex items-center w-full p-3 rounded-lg transition-all duration-200 h-11 ${
                  isActive(item.path)
                    ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                title={isSidebarCollapsed && !forceExpanded && !isSidebarHovered ? item.label : ''}
              >
                <div className="w-5 flex-shrink-0 flex justify-center">
                  <item.icon className="h-5 w-5" />
                </div>
                <span className={`transition-all duration-300 whitespace-nowrap overflow-hidden text-ellipsis ${isSidebarCollapsed && !forceExpanded && !isSidebarHovered ? 'opacity-0 max-w-0' : 'opacity-100 max-w-full ml-3'}`}>{item.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </aside>

      {/* Main Content */}
      <div
        className={`transition-all duration-300 ease-in-out ${isSidebarOpen ? (isSidebarCollapsed && !forceExpanded && !isSidebarHovered ? 'md:ml-16' : 'md:ml-64') : ''}`}
        style={{ transitionDelay: '0ms' }} // Ensure no delay in the transition
      >
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between px-4 py-3">
            <button
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
              className="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              aria-label={isSidebarOpen ? "Close sidebar" : "Open sidebar"}
            >
              {isSidebarOpen ? (
                <X className="h-6 w-6 text-gray-600 dark:text-gray-300" />
              ) : (
                <Menu className="h-6 w-6 text-gray-600 dark:text-gray-300" />
              )}
            </button>

            <div className="flex items-center ml-auto gap-4">
              {/* Theme Toggle */}
              <ThemeToggle />

              <div className="relative">
                <button
                  onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
                  className="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  {userLogoUrl && typeof userLogoUrl === 'string' && userLogoUrl.startsWith('data:') ? (
                    <img
                      src={userLogoUrl}
                      alt="User logo"
                      className="w-8 h-8 rounded-full object-cover border border-gray-200 dark:border-gray-600"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center">
                      {user?.email?.charAt(0).toUpperCase()}
                    </div>
                  )}
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {user?.businessName || user?.email}
                  </span>
                  <ChevronDown className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                </button>

                {isProfileMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                    <button
                      onClick={handleLogout}
                      className="flex items-center gap-2 w-full p-3 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                    >
                      <LogOut className="h-5 w-5" />
                      <span>Logout</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="p-4 dark:text-white">{children}</main>
      </div>
    </div>
  );
}
