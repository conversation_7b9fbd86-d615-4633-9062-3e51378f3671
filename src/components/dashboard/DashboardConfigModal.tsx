import React, { useState, useEffect } from 'react';
import { DashboardConfig } from '../../types/dashboard';
import { X, Plus, Trash2 } from 'lucide-react';

interface DashboardConfigModalProps {
  configs: DashboardConfig[];
  activeConfigId: string;
  isOpen: boolean;
  onClose: () => void;
  onSave: (configId: string) => void;
  onCreate: (name: string) => void;
  onDelete: (configId: string) => void;
  onReset: () => void;
}

export const DashboardConfigModal: React.FC<DashboardConfigModalProps> = ({
  configs,
  activeConfigId,
  isOpen,
  onClose,
  onSave,
  onCreate,
  onDelete,
  onReset
}) => {
  const [selectedConfigId, setSelectedConfigId] = useState(activeConfigId);
  const [newDashboardName, setNewDashboardName] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  
  // Reset selected config when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedConfigId(activeConfigId);
      setNewDashboardName('');
      setShowCreateForm(false);
    }
  }, [isOpen, activeConfigId]);
  
  const handleSave = () => {
    onSave(selectedConfigId);
    onClose();
  };
  
  const handleCreate = () => {
    if (newDashboardName.trim()) {
      onCreate(newDashboardName.trim());
      setNewDashboardName('');
      setShowCreateForm(false);
    }
  };
  
  const handleDelete = (configId: string) => {
    if (configs.length <= 1) {
      alert('You cannot delete the only dashboard configuration.');
      return;
    }
    
    if (confirm('Are you sure you want to delete this dashboard configuration?')) {
      onDelete(configId);
      
      // If the deleted config was selected, select another one
      if (selectedConfigId === configId) {
        const remainingConfigs = configs.filter(c => c.id !== configId);
        if (remainingConfigs.length > 0) {
          setSelectedConfigId(remainingConfigs[0].id);
        }
      }
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
        <div className="flex items-center justify-between px-6 py-4 border-b">
          <h2 className="text-lg font-semibold">Dashboard Configurations</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-6 space-y-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Select Dashboard
            </label>
            
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {configs.map(config => (
                <div 
                  key={config.id}
                  className={`flex items-center justify-between p-3 rounded-md border ${
                    selectedConfigId === config.id 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <div 
                    className="flex-1 cursor-pointer"
                    onClick={() => setSelectedConfigId(config.id)}
                  >
                    <div className="font-medium">{config.name}</div>
                    <div className="text-xs text-gray-500">
                      {config.widgets.length} widgets • Last updated: {new Date(config.updatedAt).toLocaleDateString()}
                    </div>
                  </div>
                  
                  {!config.isDefault && (
                    <button
                      onClick={() => handleDelete(config.id)}
                      className="p-1 text-gray-500 hover:text-red-600"
                      title="Delete dashboard"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
          
          {showCreateForm ? (
            <div className="mt-4 p-4 border border-gray-200 rounded-md bg-gray-50">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                New Dashboard Name
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newDashboardName}
                  onChange={(e) => setNewDashboardName(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="My Dashboard"
                />
                <button
                  onClick={handleCreate}
                  disabled={!newDashboardName.trim()}
                  className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Create
                </button>
              </div>
            </div>
          ) : (
            <button
              onClick={() => setShowCreateForm(true)}
              className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
            >
              <Plus className="h-4 w-4" />
              <span>Create New Dashboard</span>
            </button>
          )}
          
          <div className="mt-4">
            <button
              onClick={onReset}
              className="text-gray-600 hover:text-gray-800 text-sm"
            >
              Reset to Default Dashboard
            </button>
          </div>
        </div>
        
        <div className="px-6 py-4 border-t bg-gray-50 flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Apply Dashboard
          </button>
        </div>
      </div>
    </div>
  );
};
