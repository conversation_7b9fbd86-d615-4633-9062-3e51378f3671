import React from 'react';
import { DashboardWidgetConfig } from '../../types/dashboard';
import { StatsCard } from './StatsCard';
import {
  AppointmentStatusChart,
  AppointmentTrendChart,
  ClientGrowthChart,
  MessageActivityChart
} from './charts';
import { AppointmentsList, MessagesList } from '.';
import { Grip, Settings, X } from 'lucide-react';

interface DashboardWidgetProps {
  config: DashboardWidgetConfig;
  data: unknown;
  isEditMode: boolean;
  onRemove: (widgetId: string) => void;
  onConfigure: (widgetId: string) => void;
}

export const DashboardWidget: React.FC<DashboardWidgetProps> = ({
  config,
  data,
  isEditMode,
  onRemove,
  onConfigure
}) => {
  // Function to render the appropriate widget content based on type
  const renderWidgetContent = () => {
    const { type, dataSource } = config;

    // Extract data based on dataSource path
    const getNestedData = (path: string) => {
      return path.split('.').reduce((obj, key) => (obj && obj[key] !== undefined) ? obj[key] : null, data);
    };

    const widgetData = getNestedData(dataSource);

    if (!widgetData && type !== 'list') {
      return (
        <div className="flex items-center justify-center h-full bg-gray-50 rounded-lg">
          <p className="text-gray-500">No data available</p>
        </div>
      );
    }

    switch (type) {
      case 'stat':
        // For stat widgets, we need to find the corresponding stat
        if (dataSource === 'stats.totalAppointments') {
          return (
            <StatsCard
              icon={data.statIcons?.totalAppointments}
              label="Total Appointments"
              value={widgetData?.toString() || '0'}
              change={data.statTrends?.appointmentTrend || '0%'}
              positive={data.statPositive?.appointmentTrend || true}
            />
          );
        } else if (dataSource === 'stats.activeClients') {
          return (
            <StatsCard
              icon={data.statIcons?.activeClients}
              label="Active Clients"
              value={widgetData?.toString() || '0'}
              change={data.statTrends?.clientTrend || '0%'}
              positive={data.statPositive?.clientTrend || true}
            />
          );
        } else if (dataSource === 'stats.messagesHandled') {
          return (
            <StatsCard
              icon={data.statIcons?.messagesHandled}
              label="Messages Handled"
              value={widgetData?.toString() || '0'}
              change={data.statTrends?.messageTrend || '0%'}
              positive={data.statPositive?.messageTrend || true}
            />
          );
        } else if (dataSource === 'stats.timeSaved') {
          return (
            <StatsCard
              icon={data.statIcons?.timeSaved}
              label="Time Saved"
              value={`${widgetData || '0'}h`}
              change={data.statTrends?.timeTrend || '0%'}
              positive={data.statPositive?.timeTrend || true}
            />
          );
        }
        return null;

      case 'pie-chart':
        return <AppointmentStatusChart data={widgetData} />;

      case 'line-chart':
        return <AppointmentTrendChart data={widgetData} />;

      case 'bar-chart':
        return <ClientGrowthChart data={widgetData} />;

      case 'area-chart':
        return <MessageActivityChart data={widgetData} />;

      case 'list':
        if (dataSource === 'upcomingAppointments') {
          return <AppointmentsList appointments={data.formattedAppointments || []} />;
        } else if (dataSource === 'recentMessages') {
          return data.recentMessages?.length > 0 ? (
            <MessagesList messages={data.recentMessages} />
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>No recent messages</p>
            </div>
          );
        }
        return null;

      default:
        return (
          <div className="flex items-center justify-center h-full bg-gray-50 rounded-lg">
            <p className="text-gray-500">Unknown widget type</p>
          </div>
        );
    }
  };

  return (
    <div
      className={`bg-white rounded-xl shadow-sm overflow-hidden ${
        isEditMode ? 'border-2 border-dashed border-blue-300 cursor-move' : ''
      }`}
      style={{
        gridColumn: `span ${config.width}`,
        gridRow: `span ${config.height}`,
      }}
    >
      {/* Widget Header */}
      <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">{config.title}</h2>

        {isEditMode && (
          <div className="flex items-center gap-2">
            <button
              onClick={() => onConfigure(config.id)}
              className="p-1 text-gray-500 hover:text-blue-600 transition-colors"
              title="Configure widget"
            >
              <Settings className="h-4 w-4" />
            </button>
            <button
              onClick={() => onRemove(config.id)}
              className="p-1 text-gray-500 hover:text-red-600 transition-colors"
              title="Remove widget"
            >
              <X className="h-4 w-4" />
            </button>
            <div className="p-1 text-gray-400 cursor-move" title="Drag to move">
              <Grip className="h-4 w-4" />
            </div>
          </div>
        )}
      </div>

      {/* Widget Content */}
      <div className="p-6">
        {renderWidgetContent()}
      </div>
    </div>
  );
};
