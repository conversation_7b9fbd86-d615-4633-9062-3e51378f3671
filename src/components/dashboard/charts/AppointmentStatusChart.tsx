import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';
import { AppointmentsByStatus } from '../../../services/dashboardService';

interface AppointmentStatusChartProps {
  data: AppointmentsByStatus;
}

export const AppointmentStatusChart: React.FC<AppointmentStatusChartProps> = ({ data }) => {
  const chartData = [
    { name: 'Confirmed', value: data.confirmed, color: '#10B981' },
    { name: 'Pending', value: data.pending, color: '#F59E0B' },
    { name: 'Cancelled', value: data.cancelled, color: '#EF4444' }
  ].filter(item => item.value > 0);

  if (chartData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
        <p className="text-gray-500">No appointment data available</p>
      </div>
    );
  }

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip formatter={(value) => [`${value} appointments`, 'Count']} />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};
