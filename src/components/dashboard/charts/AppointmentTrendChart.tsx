import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { AppointmentsByMonth } from '../../../services/dashboardService';

interface AppointmentTrendChartProps {
  data: AppointmentsByMonth[];
}

export const AppointmentTrendChart: React.FC<AppointmentTrendChartProps> = ({ data }) => {
  if (data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
        <p className="text-gray-500">No appointment data available</p>
      </div>
    );
  }

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Line type="monotone" dataKey="confirmed" stroke="#10B981" activeDot={{ r: 8 }} />
          <Line type="monotone" dataKey="pending" stroke="#F59E0B" />
          <Line type="monotone" dataKey="cancelled" stroke="#EF4444" />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};
