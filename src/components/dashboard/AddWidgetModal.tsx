import React, { useState } from 'react';
import { DashboardWidget } from '../../types/dashboard';
import { X, Check } from 'lucide-react';

interface WidgetTemplate {
  id: string;
  name: string;
  description: string;
  type: DashboardWidget['type'];
  dataSource: string;
  icon: React.ReactNode;
}

interface AddWidgetModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (widget: DashboardWidget) => void;
  existingWidgetIds?: string[];
}

export const AddWidgetModal: React.FC<AddWidgetModalProps> = ({
  isOpen,
  onClose,
  onAdd,
  existingWidgetIds = []
}) => {
  const [selectedTemplates, setSelectedTemplates] = useState<string[]>([]);

  // Widget templates
  const widgetTemplates: WidgetTemplate[] = [
    {
      id: 'total-appointments',
      name: 'Total Appointments',
      description: 'Shows the total number of appointments',
      type: 'stat',
      dataSource: 'stats.totalAppointments',
      icon: <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center">📅</div>
    },
    {
      id: 'active-clients',
      name: 'Active Clients',
      description: 'Shows the number of active clients',
      type: 'stat',
      dataSource: 'stats.activeClients',
      icon: <div className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center">👥</div>
    },
    {
      id: 'messages-handled',
      name: 'Messages Handled',
      description: 'Shows the number of messages handled',
      type: 'stat',
      dataSource: 'stats.messagesHandled',
      icon: <div className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center">💬</div>
    },
    {
      id: 'time-saved',
      name: 'Time Saved',
      description: 'Shows the amount of time saved',
      type: 'stat',
      dataSource: 'stats.timeSaved',
      icon: <div className="w-8 h-8 bg-yellow-100 text-yellow-600 rounded-full flex items-center justify-center">⏱️</div>
    },
    {
      id: 'appointment-status',
      name: 'Appointment Status',
      description: 'Shows the distribution of appointment statuses',
      type: 'pie-chart',
      dataSource: 'appointmentsByStatus',
      icon: <div className="w-8 h-8 bg-red-100 text-red-600 rounded-full flex items-center justify-center">📊</div>
    },
    {
      id: 'appointment-trends',
      name: 'Appointment Trends',
      description: 'Shows appointment trends over time',
      type: 'line-chart',
      dataSource: 'appointmentsByMonth',
      icon: <div className="w-8 h-8 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center">📈</div>
    },
    {
      id: 'client-growth',
      name: 'Client Growth',
      description: 'Shows client growth over time',
      type: 'bar-chart',
      dataSource: 'clientsByMonth',
      icon: <div className="w-8 h-8 bg-pink-100 text-pink-600 rounded-full flex items-center justify-center">📊</div>
    },
    {
      id: 'message-activity',
      name: 'Message Activity',
      description: 'Shows message activity over time',
      type: 'area-chart',
      dataSource: 'messagesByDay',
      icon: <div className="w-8 h-8 bg-teal-100 text-teal-600 rounded-full flex items-center justify-center">📊</div>
    },
    {
      id: 'upcoming-appointments',
      name: 'Upcoming Appointments',
      description: 'Shows a list of upcoming appointments',
      type: 'list',
      dataSource: 'upcomingAppointments',
      icon: <div className="w-8 h-8 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center">📋</div>
    },
    {
      id: 'recent-messages',
      name: 'Recent Messages',
      description: 'Shows a list of recent messages',
      type: 'list',
      dataSource: 'recentMessages',
      icon: <div className="w-8 h-8 bg-cyan-100 text-cyan-600 rounded-full flex items-center justify-center">💬</div>
    },
    {
      id: 'client-feedback',
      name: 'Client Feedback',
      description: 'Shows recent client feedback and ratings',
      type: 'feedback',
      dataSource: 'feedback',
      icon: <div className="w-8 h-8 bg-yellow-100 text-yellow-600 rounded-full flex items-center justify-center">⭐</div>
    }
  ];

  const handleAddWidgets = () => {
    if (selectedTemplates.length === 0) return;

    // Create an array of widgets to add
    const widgetsToAdd = selectedTemplates
      .map(templateId => {
        const template = widgetTemplates.find(t => t.id === templateId);
        if (!template) return null;

        // Create a new widget based on the template
        return {
          id: template.id, // Use the template ID to match with ALL_WIDGET_TEMPLATES
          title: template.name,
          type: template.type,
          dataSource: template.dataSource,
          w: template.type === 'stat' ? 3 : 6,
          h: template.type === 'stat' ? 2 : 8,
          x: 0,
          y: 0, // Position will be adjusted when added to the dashboard
          minW: template.type === 'stat' ? 2 : 3,
          minH: template.type === 'stat' ? 2 : 6
        };
      })
      .filter(widget => widget !== null) as DashboardWidget[];

    // Pass all widgets to add at once
    if (widgetsToAdd.length > 0) {
      onAdd(widgetsToAdd);
    }

    onClose();
  };

  const toggleTemplateSelection = (templateId: string) => {
    setSelectedTemplates(prev => {
      if (prev.includes(templateId)) {
        return prev.filter(id => id !== templateId);
      } else {
        return [...prev, templateId];
      }
    });
  };

  const selectAllAvailableWidgets = () => {
    const availableWidgetIds = widgetTemplates
      .filter(template => !existingWidgetIds.includes(template.id))
      .map(template => template.id);

    setSelectedTemplates(availableWidgetIds);
  };

  const clearSelection = () => {
    setSelectedTemplates([]);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 transition-opacity duration-200 ease-out">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-2xl confirmation-dialog transform transition-all duration-200 ease-out scale-100 opacity-100">
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 confirmation-dialog-header">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Add Widget</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-6">
          <div className="flex justify-between mb-4">
            <div className="text-sm text-gray-600 dark:text-gray-300">
              {selectedTemplates.length} widget(s) selected
            </div>
            <div className="flex gap-2">
              <button
                onClick={selectAllAvailableWidgets}
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Select All
              </button>
              {selectedTemplates.length > 0 && (
                <button
                  onClick={clearSelection}
                  className="text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 ml-4"
                >
                  Clear
                </button>
              )}
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-80 overflow-y-auto">
            {widgetTemplates.map(template => {
              const isAlreadyAdded = existingWidgetIds.includes(template.id);
              const isSelected = selectedTemplates.includes(template.id);
              return (
                <div
                  key={template.id}
                  className={`p-4 border rounded-lg transition-colors relative ${
                    isAlreadyAdded
                      ? 'border-gray-200 bg-gray-100 dark:border-gray-700 dark:bg-gray-800 opacity-50 cursor-not-allowed'
                      : isSelected
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 cursor-pointer'
                        : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer'
                  }`}
                  onClick={() => !isAlreadyAdded && toggleTemplateSelection(template.id)}
                >
                  {isAlreadyAdded && (
                    <div className="absolute top-2 right-2 text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
                      Already Added
                    </div>
                  )}
                  {!isAlreadyAdded && isSelected && (
                    <div className="absolute top-2 right-2 text-xs font-medium text-white bg-blue-500 dark:bg-blue-600 p-1 rounded-full">
                      <Check className="h-3 w-3" />
                    </div>
                  )}
                <div className="flex items-center gap-3">
                  {template.icon}
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">{template.name}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{template.description}</p>
                  </div>
                </div>
                </div>
              );
            })}
          </div>
        </div>

        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 flex justify-end gap-2 confirmation-dialog-footer rounded-b-lg">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            Cancel
          </button>
          <button
            onClick={handleAddWidgets}
            disabled={selectedTemplates.length === 0}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Add {selectedTemplates.length > 0 ? `${selectedTemplates.length} Widget${selectedTemplates.length > 1 ? 's' : ''}` : 'Widgets'}
          </button>
        </div>
      </div>
    </div>
  );
};
