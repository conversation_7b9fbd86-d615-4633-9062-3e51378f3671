import React from 'react';

interface Appointment {
  id: string;
  clientName: string;
  service: string;
  date: string;
  time: string;
  status: 'confirmed' | 'pending' | 'cancelled';
}

interface AppointmentsListProps {
  appointments: Appointment[];
}

interface AppointmentsListState {
  loading: boolean;
  error: string | null;
}

export class AppointmentsList extends React.Component<AppointmentsListProps, AppointmentsListState> {
  state = {
    loading: false,
    error: null
  };

  renderAppointment(appointment: Appointment) {
    const statusColors = {
      confirmed: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
      cancelled: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
    };

    return (
      <div key={appointment.id} className="flex items-center justify-between p-4 border-b border-gray-100 dark:border-gray-700">
        <div>
          <p className="font-medium text-gray-900 dark:text-white">{appointment.clientName}</p>
          <p className="text-sm text-gray-600 dark:text-gray-300">{appointment.service}</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-900 dark:text-white">{appointment.date}</p>
          <p className="text-sm text-gray-600 dark:text-gray-300">{appointment.time}</p>
        </div>
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[appointment.status]}`}>
          {appointment.status}
        </span>
      </div>
    );
  }

  render() {
    const { appointments } = this.props;
    const { loading, error } = this.state;

    if (loading) {
      return <div className="text-center py-8">Loading appointments...</div>;
    }

    if (error) {
      return <div className="text-center py-8 text-red-600">{error}</div>;
    }

    if (!appointments.length) {
      return (
        <p className="text-gray-600 text-center py-8">
          No appointments scheduled for today
        </p>
      );
    }

    return (
      <div className="divide-y divide-gray-100">
        {appointments.map(appointment => this.renderAppointment(appointment))}
      </div>
    );
  }
}
