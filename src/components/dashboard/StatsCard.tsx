import React from 'react';
import { DivideIcon as LucideIcon } from 'lucide-react';

interface StatsCardProps {
  icon: LucideIcon;
  label: string;
  value: string;
  change: string;
  positive: boolean;
}

export const StatsCard: React.FC<StatsCardProps> = ({ icon: Icon, label, value, change, positive }) => {
  return (
    <div className="stats-card bg-white dark:bg-gray-800 h-full w-full flex flex-col justify-center overflow-hidden">
      <div className="flex items-center mb-3 pl-1">
        <div className="p-2 bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 rounded-lg mr-3">
          <Icon className="h-5 w-5" />
        </div>
        <h3 className="font-medium text-gray-700 dark:text-gray-300">{label}</h3>
      </div>
      <div className="flex flex-col pl-1">
        <div className="flex items-baseline">
          <span className="text-2xl font-bold text-gray-900 dark:text-white mr-2">{value}</span>
          <span className={`text-sm font-medium px-1.5 py-0.5 rounded ${
            positive
              ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
              : 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
          }`}>
            {change}
          </span>
        </div>
      </div>
    </div>
  );
};
