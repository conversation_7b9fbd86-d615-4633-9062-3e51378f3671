import React, { useState, useEffect } from 'react';
import { Star, MessageSquare, TrendingUp, Reply, ChevronRight, Filter, MoreHorizontal } from 'lucide-react';
import { feedbackService, Feedback, FeedbackAnalytics } from '../../services/feedbackService';
import { addToast } from '../ui';

interface FeedbackWidgetProps {
  className?: string;
}

export const FeedbackWidget: React.FC<FeedbackWidgetProps> = ({ className = '' }) => {
  const [feedback, setFeedback] = useState<Feedback[]>([]);
  const [analytics, setAnalytics] = useState<FeedbackAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null);
  const [responseText, setResponseText] = useState('');
  const [responding, setResponding] = useState(false);
  const [showAll, setShowAll] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [feedbackResponse, analyticsData] = await Promise.all([
        feedbackService.getFeedback(1, 5), // Load only 5 recent items for widget
        feedbackService.getAnalytics(30)
      ]);
      setFeedback(feedbackResponse.items);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Error loading feedback data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRespond = async () => {
    if (!selectedFeedback || !responseText.trim()) return;

    setResponding(true);
    try {
      await feedbackService.respondToFeedback(selectedFeedback.id, responseText);
      addToast('Response sent successfully', 'success');
      setSelectedFeedback(null);
      setResponseText('');
      loadData(); // Reload data
    } catch (error) {
      console.error('Error sending response:', error);
      addToast('Failed to send response', 'error');
    } finally {
      setResponding(false);
    }
  };

  const renderStars = (rating: number, size: 'xs' | 'sm' = 'xs') => {
    const starSize = size === 'xs' ? 'h-3 w-3' : 'h-4 w-4';
    return (
      <div className="flex items-center gap-0.5">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${starSize} ${
              star <= rating 
                ? 'text-yellow-400 fill-current' 
                : 'text-gray-300 dark:text-gray-600'
            }`}
          />
        ))}
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 48) return 'Yesterday';
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted': return 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200';
      case 'responded': return 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-200';
      case 'pending': return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-200';
      default: return 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm ${className}`}>
        {/* Header */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Client Feedback</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">Recent reviews and ratings</p>
            </div>
            <button
              onClick={() => setShowAll(true)}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium flex items-center gap-1"
            >
              View All
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Analytics Summary */}
        {analytics && (
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-lg font-bold text-gray-900 dark:text-white">
                    {analytics.averageRating}
                  </span>
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-300">Avg Rating</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <MessageSquare className="h-4 w-4 text-blue-500" />
                  <span className="text-lg font-bold text-gray-900 dark:text-white">
                    {analytics.totalFeedback}
                  </span>
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-300">Total Reviews</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  <span className="text-lg font-bold text-gray-900 dark:text-white">
                    {analytics.recentFeedbackCount}
                  </span>
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-300">This Month</p>
              </div>
            </div>
          </div>
        )}

        {/* Recent Feedback */}
        <div className="p-6">
          {feedback.length === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-600 dark:text-gray-300 text-sm">No feedback yet</p>
              <p className="text-gray-500 dark:text-gray-400 text-xs">
                Reviews will appear here once clients start providing feedback
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {feedback.map((item) => (
                <div key={item.id} className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-blue-600 dark:text-blue-300">
                        {item.clientName ? item.clientName[0].toUpperCase() : 'A'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-2">
                        {renderStars(item.rating)}
                        <span className="text-xs font-medium text-gray-900 dark:text-white">
                          {item.clientName || 'Anonymous'}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {formatDate(item.submittedAt)}
                      </span>
                    </div>
                    
                    {item.comment && (
                      <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2 mb-2">
                        "{item.comment}"
                      </p>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                          {item.status}
                        </span>
                        {item.serviceName && (
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {item.serviceName}
                          </span>
                        )}
                      </div>
                      
                      {!item.businessResponse && (
                        <button
                          onClick={() => setSelectedFeedback(item)}
                          className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium flex items-center gap-1"
                        >
                          <Reply className="h-3 w-3" />
                          Reply
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Response Modal */}
      {selectedFeedback && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-lg w-full">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Respond to Feedback
              </h3>
              
              {/* Original Feedback */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                <div className="flex items-center gap-2 mb-2">
                  {renderStars(selectedFeedback.rating, 'sm')}
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {selectedFeedback.clientName || 'Anonymous'}
                  </span>
                </div>
                {selectedFeedback.comment && (
                  <p className="text-sm text-gray-700 dark:text-gray-300">"{selectedFeedback.comment}"</p>
                )}
              </div>

              {/* Response Input */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Your Response
                </label>
                <textarea
                  value={responseText}
                  onChange={(e) => setResponseText(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  placeholder="Thank you for your feedback..."
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end gap-3">
                <button
                  onClick={() => {
                    setSelectedFeedback(null);
                    setResponseText('');
                  }}
                  className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleRespond}
                  disabled={!responseText.trim() || responding}
                  className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {responding ? (
                    <>
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                      Sending...
                    </>
                  ) : (
                    <>
                      <Reply className="h-3 w-3" />
                      Send
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Full View Modal */}
      {showAll && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">All Feedback</h3>
                <button
                  onClick={() => setShowAll(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  ✕
                </button>
              </div>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <p className="text-gray-600 dark:text-gray-300 text-center py-8">
                Full feedback management coming soon...
                <br />
                <span className="text-sm">For now, use the settings to configure feedback collection.</span>
              </p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
