import { useEffect, useState } from 'react';
import { useAuthStore } from '../lib/auth';
import { Outlet } from 'react-router-dom';

/**
 * AuthProvider ensures the auth state is properly loaded before rendering children
 * This helps prevent 401 errors when refreshing the page
 */
export function AuthProvider() {
  const [isLoading, setIsLoading] = useState(true);
  // We don't use isAuthenticated directly, but we might need it in the future
  // const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const user = useAuthStore(state => state.user);

  useEffect(() => {
    // Check if we have a token in localStorage but no user in state
    const accessToken = localStorage.getItem('accessToken');

    if (accessToken && !user) {
      // Force a refresh of the auth state
      const refreshToken = localStorage.getItem('refreshToken');
      if (refreshToken) {
        useAuthStore.getState().setTokens({
          accessToken,
          refreshToken
        });
      }
    }

    // Mark loading as complete
    setIsLoading(false);
  }, [user]);

  // Show nothing while loading
  if (isLoading) {
    return null;
  }

  // Render children once auth is initialized
  return <Outlet />;
}
