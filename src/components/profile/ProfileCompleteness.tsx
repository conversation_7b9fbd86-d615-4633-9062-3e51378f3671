import React from 'react';
import { UserProfile } from '../../services/userService';
import {
  calculateProfileCompleteness,
  getCompletenessColor,
  // getCompletenessStatus is imported but not used in this component
  // keeping it commented for future use
  // getCompletenessStatus
} from '../../utils/profileCompleteness';
import { Info } from 'lucide-react';

interface ProfileCompletenessProps {
  profile: Partial<UserProfile>;
  className?: string;
}

export function ProfileCompleteness({ profile, className = '' }: ProfileCompletenessProps) {
  const { percentage, missingFields, nextSuggestion } = calculateProfileCompleteness(profile);
  const colorClass = getCompletenessColor(percentage);

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-2 ${className}`}>
      {/* Ultra compact layout with progress bar and percentage */}
      <div className="flex items-center gap-2">

        <div className="flex-1 relative">
          {/* Background track */}
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
            {/* Progress bar - using absolute positioning to ensure it's always visible */}
            <div
              className="absolute top-0 left-0 h-1.5 rounded-full transition-all duration-300"
              style={{
                width: `${Math.max(percentage, 1)}%`,
                backgroundColor: percentage < 40 ? '#ef4444' : // red-500
                               percentage < 70 ? '#eab308' : // yellow-500
                               percentage < 100 ? '#3b82f6' : // blue-500
                               '#22c55e', // green-500
              }}
            ></div>
          </div>
        </div>
        <span className={`text-xs font-semibold ${colorClass} whitespace-nowrap`}>{percentage}%</span>

        {/* Tooltip */}
        <div className="relative group">
          <button
            className="p-0.5 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
            aria-label="Profile completeness information"
          >
            <Info size={14} />
          </button>

          <div className="absolute right-0 w-64 p-3 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
            <p className="text-sm font-medium mb-1 dark:text-white">Profile Completeness</p>
            {nextSuggestion && (
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">Suggestion: <span className="font-medium">{nextSuggestion}</span></p>
            )}
            <p className="text-xs font-medium dark:text-white">Missing information:</p>
            {missingFields.length > 0 ? (
              <ul className="text-xs text-gray-600 dark:text-gray-400 list-disc pl-4 mt-1">
                {missingFields.map((field) => (
                  <li key={field}>{field}</li>
                ))}
              </ul>
            ) : (
              <p className="text-xs text-green-500 dark:text-green-400 mt-1">Your profile is complete!</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
