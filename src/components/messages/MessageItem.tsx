import React, { useState, useEffect, useRef } from 'react';
import { Message } from '../../types/whatsapp';
import { formatExactTime, formatAudioDuration } from '../../utils/formatters';
import { getMediaBlobUrl } from '../../services/whatsapp';
import { Play, Pause, Calendar, Bot } from 'lucide-react';
import ImageModal from '../ui/ImageModal';

interface MessageItemProps {
  message: Message;
}

const MessageItem: React.FC<MessageItemProps> = ({ message }) => {
  const isSentByMe = message.sender === 'me';
  const [mediaBlobUrl, setMediaBlobUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [loadError, setLoadError] = useState(false);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioDuration, setAudioDuration] = useState<number>(0);
  const [audioProgress, setAudioProgress] = useState<number>(0);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Load media content if message has media
  useEffect(() => {
    // Skip if this is a text message or has no ID
    if (!message.type || message.type === 'text' || !message.id) {
      return;
    }

    // Skip if we already have a blob URL that's not an encrypted URL
    if (mediaBlobUrl && !mediaBlobUrl.includes('.enc')) {
      return;
    }

    // Set loading state
    setIsLoading(true);
    setLoadError(false);

    // Try to load media from Evolution API
    const loadMedia = async () => {
      try {

        // If we have a direct media URL, try to process it
        if (message.mediaUrl) {
          // For encrypted media, don't set the URL directly, wait for processing
          const isEncryptedMedia = message.mediaUrl.includes('.enc') ||
                                 (message.mediaUrl.includes('mmg.whatsapp.net') &&
                                  message.mediaUrl.endsWith('mms3=true'));

          if (!isEncryptedMedia) {
            // For non-encrypted media, set it immediately
            setMediaBlobUrl(message.mediaUrl);
            setIsLoading(false);
          }

          // Process the media URL (this will handle decryption if needed)
          getMediaBlobUrl(message.id, message.mediaUrl).then(blobUrl => {
            if (blobUrl) {
              // Got better URL for media
              setMediaBlobUrl(blobUrl);
              setIsLoading(false);
              setLoadError(false);
            } else if (!mediaBlobUrl) {
              // Only set error if we don't already have a URL
              setLoadError(true);
              setIsLoading(false);
            }
          }).catch(error => {
            // Failed to get better URL for media
            console.warn(`Failed to load media for message ${message.id}:`, error);
            if (!mediaBlobUrl) {
              // Only set error if we don't already have a URL
              setLoadError(true);
              setIsLoading(false);
            }
          });
        } else {
          // No direct URL, try to get one
          const blobUrl = await getMediaBlobUrl(message.id, message.mediaUrl);
          if (blobUrl) {
            // Successfully loaded media for message
            setMediaBlobUrl(blobUrl);
            setIsLoading(false);
          } else {
            console.error(`Failed to get blob URL for message ${message.id}`);
            setLoadError(true);
            setIsLoading(false);
          }
        }
      } catch (error) {
        console.error(`Error loading media for message ${message.id}:`, error);
        setLoadError(true);
        setIsLoading(false);
      }
    };

    loadMedia();

    // Cleanup function to revoke blob URL when component unmounts
    return () => {
      if (mediaBlobUrl && mediaBlobUrl.startsWith('blob:')) {
        URL.revokeObjectURL(mediaBlobUrl);
      }
    };
  }, [message.id, message.type, message.mediaUrl, mediaBlobUrl]);

  // Reset load error when message changes
  useEffect(() => {
    setLoadError(false);
  }, [message.id]);

  // Render status icon based on message status
  const renderStatusIcon = () => {
    if (!isSentByMe) return null;

    switch (message.status) {
      case 'sending':
        return <span>⏱️</span>;
      case 'sent':
        return <span>✓</span>;
      case 'delivered':
        return <span>✓✓</span>;
      case 'read':
        return <span className="text-blue-300">✓✓</span>;
      case 'failed':
        return <span className="text-red-500">!</span>;
      default:
        return <span>✓</span>;
    }
  };

  // Render message content based on message type
  const renderMessageContent = () => {
    switch (message.type) {
      case 'image':
        return renderImageContent();
      case 'audio':
        return renderAudioContent();
      case 'video':
        return renderVideoContent();
      case 'document':
        return renderDocumentContent();
      case 'text':
      default:
        return renderTextContent();
    }
  };

  // Render text message content
  const renderTextContent = () => {
    // Check if this is an AI-generated message
    const isAIMessage = message.source === 'ai';
    const isAppointmentMessage = message.text.includes('appointment') &&
      (message.text.includes('booked') || message.text.includes('scheduled'));

    return (
      <>
        {/* Show AI indicator for AI-generated messages */}
        {isAIMessage && (
          <div className="flex items-center text-xs text-blue-500 mb-1">
            <Bot size={12} className="mr-1" />
            <span>AI Assistant</span>
          </div>
        )}

        {/* Show appointment indicator for appointment-related messages */}
        {isAppointmentMessage && (
          <div className="flex items-center text-xs text-green-500 mb-1">
            <Calendar size={12} className="mr-1" />
            <span>Appointment</span>
          </div>
        )}

        <p>{message.text}</p>
        <div className={`flex items-center justify-end text-[10px] mt-0.5 ${isSentByMe ? 'text-blue-100' : 'text-gray-500'}`}>
          <span>{formatExactTime(message.timestamp)}</span>
          <span className="ml-1">{renderStatusIcon()}</span>
        </div>
      </>
    );
  };

  // Render image message content
  const renderImageContent = () => {
    return (
      <div className="message-content">
        <div className="image-container relative rounded overflow-hidden mb-1">
          {isLoading ? (
            <div className="flex items-center justify-center bg-gray-100 h-40 w-full">
              <div className="animate-pulse flex flex-col items-center">
                <svg className="h-8 w-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className="text-xs text-gray-500 mt-2">Loading image...</p>
              </div>
            </div>
          ) : loadError ? (
            <div className="flex items-center justify-center bg-gray-100 h-40 w-full">
              <div className="flex flex-col items-center">
                <svg className="h-8 w-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <p className="text-xs text-gray-500 mt-2">Could not load image</p>
                {message.text && message.text !== '[Image]' && (
                  <p className="text-xs text-gray-500 mt-1">{message.text}</p>
                )}
                <div className="flex flex-col mt-2">
                  <button
                    onClick={() => {
                      setIsLoading(true);
                      setLoadError(false);
                      // Try to load media again
                      getMediaBlobUrl(message.id, message.mediaUrl).then(url => {
                        if (url) {
                          setMediaBlobUrl(url);
                          setIsLoading(false);
                        } else {
                          setLoadError(true);
                          setIsLoading(false);
                        }
                      }).catch(() => {
                        setLoadError(true);
                        setIsLoading(false);
                      });
                    }}
                    className="text-xs text-blue-500 underline"
                  >
                    Retry
                  </button>
                  {message.mediaUrl && (
                    <a
                      href={message.mediaUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-blue-500 underline mt-1"
                    >
                      View image
                    </a>
                  )}
                </div>
              </div>
            </div>
          ) : (
            mediaBlobUrl && (
              <div className="relative">
                {!loadError ? (
                  <>
                    <div className="relative group">
                      <img
                        src={mediaBlobUrl}
                        alt={message.text || 'Image'}
                        className="max-w-[250px] max-h-[250px] object-contain cursor-pointer hover:opacity-90 transition-opacity"
                        loading="lazy"
                        onClick={() => setIsImageModalOpen(true)}
                        onError={(e) => {
                          // Image failed to load
                          console.error('Image failed to load:', mediaBlobUrl);
                          // Set fallback image
                          e.currentTarget.src = '/whatsapp-media-placeholder.svg';
                          e.currentTarget.classList.add('bg-gray-100', 'p-2', 'rounded');
                          setLoadError(true);

                          // Clear the media URL from cache
                          if (typeof mediaBlobUrl === 'string' && mediaBlobUrl.includes('mmg.whatsapp.net')) {
                            // This is a direct WhatsApp URL that failed to load
                            // We should clear it from the cache
                            localStorage.removeItem(`media_${message.id}`);
                          }
                        }}
                      />
                      <div className="absolute top-2 right-2 bg-black/50 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="11" cy="11" r="8"></circle>
                          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                          <line x1="11" y1="8" x2="11" y2="14"></line>
                          <line x1="8" y1="11" x2="14" y2="11"></line>
                        </svg>
                      </div>
                    </div>
                    {isImageModalOpen && (
                      <ImageModal
                        imageUrl={mediaBlobUrl}
                        alt={message.text || 'Image'}
                        onClose={() => setIsImageModalOpen(false)}
                      />
                    )}
                  </>
                ) : (
                  <div className="p-4 bg-gray-100 rounded text-center">
                    <svg className="w-12 h-12 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <p className="text-sm font-medium mt-2">Image Unavailable</p>
                  </div>
                )}
                {message.text && message.text !== '[Image]' && (
                  <p className="text-xs text-gray-500 mt-1">{message.text}</p>
                )}
              </div>
            )
          )}
        </div>

        {message.text && <p className="mt-1">{message.text}</p>}

        <div className={`flex items-center justify-end text-[10px] mt-0.5 ${isSentByMe ? 'text-blue-100' : 'text-gray-500'}`}>
          <span>{formatExactTime(message.timestamp)}</span>
          <span className="ml-1">{renderStatusIcon()}</span>
        </div>
      </div>
    );
  };

  // Render audio message content
  const renderAudioContent = () => {
    return (
      <div className="audio-message">
        <div className="flex items-center space-x-2">
          {isLoading ? (
            <div className="flex items-center space-x-2">
              <div className="animate-pulse flex items-center">
                <svg className="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
                <span className="text-sm ml-2">Loading audio...</span>
              </div>
            </div>
          ) : loadError ? (
            <div className="flex items-center space-x-2 text-red-500">
              <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span className="text-sm">Failed to load audio</span>
              <div className="flex flex-col">
                <p className="text-xs text-gray-500 mt-1">
                  {message.text && message.text !== '[Audio]' ? message.text : 'Voice message'}
                </p>
                <button
                  onClick={() => {
                    setIsLoading(true);
                    setLoadError(false);
                    // Try to load media again
                    getMediaBlobUrl(message.id, message.mediaUrl).then(url => {
                      if (url) {
                        setMediaBlobUrl(url);
                        setIsLoading(false);
                      } else {
                        setLoadError(true);
                        setIsLoading(false);
                      }
                    }).catch(() => {
                      setLoadError(true);
                      setIsLoading(false);
                    });
                  }}
                  className="text-xs text-blue-500 underline mt-1"
                >
                  Retry
                </button>
                {message.mediaUrl && (
                  <a
                    href={message.mediaUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-blue-500 underline mt-1"
                  >
                    Listen to audio
                  </a>
                )}
              </div>
            </div>
          ) : (
            mediaBlobUrl && (
              <>
                <div className="audio-player-container">
                  {!loadError ? (
                    <div className={`flex flex-col p-3 rounded-lg ${isSentByMe ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-800'}`}>
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => {
                            if (audioRef.current) {
                              if (isPlaying) {
                                audioRef.current.pause();
                              } else {
                                audioRef.current.play();
                              }
                              setIsPlaying(!isPlaying);
                            }
                          }}
                          className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center transition-colors ${isSentByMe ? 'bg-white text-blue-500 hover:bg-gray-100' : 'bg-blue-500 text-white hover:bg-blue-600'}`}
                        >
                          {isPlaying ? <Pause size={16} /> : <Play size={16} />}
                        </button>

                        <div className="flex-grow">
                          <div className={`w-full h-6 rounded-full overflow-hidden relative ${isSentByMe ? 'bg-blue-600' : 'bg-gray-200'}`}>
                            {/* Simplified waveform visualization */}
                            <div className="flex h-full items-center justify-around px-1">
                              {Array.from({ length: 15 }).map((_, i) => (
                                <div
                                  key={i}
                                  className={`w-1 ${isSentByMe ? 'bg-blue-300' : 'bg-blue-400'} opacity-70`}
                                  style={{
                                    height: `${Math.max(20, Math.min(80, 40 + (i % 3) * 20))}%`,
                                  }}
                                ></div>
                              ))}
                            </div>

                            {/* Progress indicator */}
                            <div
                              className="absolute top-0 left-0 h-full w-1 bg-white rounded-full"
                              style={{ left: `${(audioProgress / audioDuration) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>

                      <div className="flex justify-between mt-1 text-xs">
                        <div>{formatAudioDuration(audioDuration)}</div>
                      </div>

                      <audio
                        ref={audioRef}
                        className="hidden"
                        src={mediaBlobUrl}
                        preload="metadata"
                        onLoadedMetadata={() => {
                          if (audioRef.current) {
                            setAudioDuration(audioRef.current.duration);
                          }
                        }}
                        onTimeUpdate={() => {
                          if (audioRef.current) {
                            setAudioProgress(audioRef.current.currentTime);
                          }
                        }}
                        onEnded={() => setIsPlaying(false)}
                        onError={() => {
                          // Audio failed to load
                          console.error('Audio failed to load:', mediaBlobUrl);
                          setLoadError(true);
                          // Clear the media URL from cache
                          if (typeof mediaBlobUrl === 'string' && mediaBlobUrl.includes('mmg.whatsapp.net')) {
                            // This is a direct WhatsApp URL that failed to load
                            // We should clear it from the cache
                            localStorage.removeItem(`media_${message.id}`);
                          }
                        }}
                      ></audio>
                    </div>
                  ) : (
                    <div className="p-3 bg-gray-100 rounded text-center">
                      <svg className="w-8 h-8 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                      </svg>
                      <p className="text-sm font-medium mt-2">Audio Unavailable</p>
                    </div>
                  )}

                </div>
              </>
            )
          )}
        </div>

        {/* Message timestamp and status */}
        <div className={`flex items-center justify-end text-[10px] mt-0.5 ${isSentByMe ? 'text-blue-100' : 'text-gray-500'}`}>
          <span>{formatExactTime(message.timestamp)}</span>
          <span className="ml-1">{renderStatusIcon()}</span>
        </div>
      </div>
    );
  };

  // Render video message content
  const renderVideoContent = () => {
    return (
      <div className="video-message">
        <div className="video-container relative rounded overflow-hidden mb-1">
          {isLoading ? (
            <div className="flex items-center justify-center bg-gray-100 h-40 w-full">
              <div className="animate-pulse flex flex-col items-center">
                <svg className="h-8 w-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                <p className="text-xs text-gray-500 mt-2">Loading video...</p>
              </div>
            </div>
          ) : loadError ? (
            <div className="flex items-center justify-center bg-gray-100 h-40 w-full">
              <div className="flex flex-col items-center">
                <svg className="h-8 w-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <p className="text-xs text-gray-500 mt-2">Could not load video</p>
                {message.text && message.text !== '[Video]' && (
                  <p className="text-xs text-gray-500 mt-1">{message.text}</p>
                )}
                <div className="flex flex-col mt-2">
                  <button
                    onClick={() => {
                      setIsLoading(true);
                      setLoadError(false);
                      // Try to load media again
                      getMediaBlobUrl(message.id, message.mediaUrl).then(url => {
                        if (url) {
                          setMediaBlobUrl(url);
                          setIsLoading(false);
                        } else {
                          setLoadError(true);
                          setIsLoading(false);
                        }
                      }).catch(() => {
                        setLoadError(true);
                        setIsLoading(false);
                      });
                    }}
                    className="text-xs text-blue-500 underline"
                  >
                    Retry
                  </button>
                  {message.mediaUrl && (
                    <a
                      href={message.mediaUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-blue-500 underline mt-1"
                    >
                      Watch video
                    </a>
                  )}
                </div>
              </div>
            </div>
          ) : (
            mediaBlobUrl && (
              <div className="video-container relative">
                <video
                  controls
                  className="w-full h-auto"
                  src={mediaBlobUrl}
                  preload="metadata"
                  onError={() => {
                    console.error('Video failed to load:', mediaBlobUrl);
                    setLoadError(true);
                    // Clear the media URL from cache
                    if (typeof mediaBlobUrl === 'string' && mediaBlobUrl.includes('mmg.whatsapp.net')) {
                      // This is a direct WhatsApp URL that failed to load
                      // We should clear it from the cache
                      localStorage.removeItem(`media_${message.id}`);
                    }
                  }}
                ></video>
                <div className="text-xs text-gray-500 mt-1">
                  {message.text && message.text !== '[Video]' ? message.text : 'Video message'}
                </div>
              </div>
            )
          )}
        </div>

        {message.text && <p className="mt-1">{message.text}</p>}

        {/* Message timestamp and status */}
        <div className={`flex items-center justify-end text-[10px] mt-0.5 ${isSentByMe ? 'text-blue-100' : 'text-gray-500'}`}>
          <span>{formatExactTime(message.timestamp)}</span>
          <span className="ml-1">{renderStatusIcon()}</span>
        </div>
      </div>
    );
  };

  // Render document message content
  const renderDocumentContent = () => {
    return (
      <div className="document-message">
        <div className="flex items-center p-2 rounded bg-gray-100">
          <svg className="mr-2 text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>

          <div className="flex-grow overflow-hidden">
            <div className="text-sm font-medium truncate">
              {message.fileName || 'Document'}
            </div>
            {message.fileSize && (
              <div className="text-xs text-gray-500">
                {typeof message.fileSize === 'number'
                  ? `${Math.round(message.fileSize / 1024)} KB`
                  : message.fileSize}
              </div>
            )}
          </div>

          {mediaBlobUrl ? (
            <a
              href={mediaBlobUrl}
              download={message.fileName || 'document'}
              target="_blank"
              rel="noopener noreferrer"
              className="ml-2 p-1 rounded-full bg-blue-500 text-white"
              onClick={(e) => {
                // Check if the URL is valid before allowing the download
                if (!mediaBlobUrl || mediaBlobUrl === 'undefined') {
                  e.preventDefault();
                  console.error('Document URL is invalid:', mediaBlobUrl);
                  setLoadError(true);
                }
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
            </a>
          ) : loadError ? (
            <button
              onClick={() => {
                setIsLoading(true);
                setLoadError(false);
                // Try to load media again
                getMediaBlobUrl(message.id, message.mediaUrl).then(url => {
                  if (url) {
                    setMediaBlobUrl(url);
                    setIsLoading(false);
                  } else {
                    setLoadError(true);
                    setIsLoading(false);
                  }
                }).catch(() => {
                  setLoadError(true);
                  setIsLoading(false);
                });
              }}
              className="text-xs text-blue-500 underline"
            >
              Retry
            </button>
          ) : isLoading ? (
            <div className="ml-2 p-1 rounded-full bg-gray-300">
              <svg className="animate-spin h-4 w-4 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
          ) : null}
        </div>

        {/* Message timestamp and status */}
        <div className={`flex items-center justify-end text-[10px] mt-0.5 ${isSentByMe ? 'text-blue-100' : 'text-gray-500'}`}>
          <span>{formatExactTime(message.timestamp)}</span>
          <span className="ml-1">{renderStatusIcon()}</span>
        </div>
      </div>
    );
  };

  return (
    <div
      className={`flex ${isSentByMe ? 'justify-end' : 'justify-start'} mb-1`}
    >
      <div
        className={`max-w-[75%] rounded-lg px-2 py-1.5 ${
          isSentByMe
            ? 'bg-blue-500 text-white rounded-br-none'
            : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-bl-none'
        }`}
      >
        {renderMessageContent()}
      </div>
    </div>
  );
};

export default MessageItem;
