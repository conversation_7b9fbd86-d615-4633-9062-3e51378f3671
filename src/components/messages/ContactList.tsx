import React, { useState, useEffect } from 'react';
import { Search, RefreshCw, Loader2 } from 'lucide-react';
import { Contact } from '../../types/whatsapp';
import ContactItem from './ContactItem';

interface ContactListProps {
  contacts: Contact[];
  selectedContact: Contact | null;
  onSelectContact: (contact: Contact) => void;
  onRefreshContacts: () => Promise<Contact[] | void>;
  isLoading: boolean;
  hasMoreContacts: boolean;
  onLoadMoreContacts: () => void;
  clientContacts?: Set<string>; // Set of client phone numbers to identify existing clients
  onAddToClients?: (contact: Contact) => void; // Function to add a WhatsApp contact to clients
  allContacts?: Contact[]; // All contacts for global search
}

const ContactList: React.FC<ContactListProps> = ({
  contacts,
  selectedContact,
  onSelectContact,
  onRefreshContacts,
  isLoading,
  hasMoreContacts,
  onLoadMoreContacts,
  clientContacts = new Set(),
  onAddToClients,
  allContacts = []
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>(contacts);

  // Helper function to check if a contact is already a client
  const isContactClient = (contact: Contact, clientPhones: Set<string>): boolean => {
    if (!contact.phone || !clientPhones.size) return false;

    // Try direct match first
    if (clientPhones.has(contact.phone)) return true;

    // Try normalized phone (digits only)
    const normalizedPhone = contact.phone.replace(/\D/g, '');
    if (clientPhones.has(normalizedPhone)) return true;

    // Try last 9 digits
    if (normalizedPhone.length >= 9) {
      const last9Digits = normalizedPhone.slice(-9);
      if (clientPhones.has(last9Digits)) return true;
    }

    return false;
  };

  // Update filtered contacts when search query changes
  useEffect(() => {
    if (!searchQuery.trim()) {
      // If no search query, just use the current contacts
      setFilteredContacts(contacts);
      return;
    }

    // Use all contacts for search if available, otherwise use loaded contacts
    const contactsToSearch = allContacts.length > 0 ? allContacts : contacts;

    // Filter contacts based on search query
    const filtered = contactsToSearch
      .filter(contact => {
        const nameMatch = contact.name.toLowerCase().includes(searchQuery.toLowerCase());
        const phoneMatch = contact.phone.includes(searchQuery);
        return nameMatch || phoneMatch;
      })
      .sort((a, b) => {
        // If we're searching, prioritize exact matches
        const aNameMatch = a.name.toLowerCase().includes(searchQuery.toLowerCase());
        const bNameMatch = b.name.toLowerCase().includes(searchQuery.toLowerCase());

        // If one matches and the other doesn't, prioritize the match
        if (aNameMatch && !bNameMatch) return -1;
        if (!aNameMatch && bNameMatch) return 1;

        // Otherwise sort by timestamp (most recent first)
        const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
        const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
        return timeB - timeA;
      });

    setFilteredContacts(filtered);
  }, [searchQuery, contacts, allContacts]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onRefreshContacts();
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <div className="w-full md:w-80 lg:w-96 border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 flex flex-col">
      {/* Search and refresh */}
      <div className="p-3 border-b border-gray-200 dark:border-gray-700 flex items-center gap-2">
        <div className="relative flex-1">
          <input
            type="text"
            placeholder="Search contacts..."
            className="w-full pl-9 pr-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400 dark:text-gray-500" />
        </div>
        <button
          className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
          onClick={handleRefresh}
          disabled={isRefreshing}
        >
          {isRefreshing ? (
            <Loader2 className="h-5 w-5 text-gray-500 dark:text-gray-400 animate-spin" />
          ) : (
            <RefreshCw className="h-5 w-5 text-gray-500 dark:text-gray-400" />
          )}
        </button>
      </div>

      {/* Contacts list */}
      <div className="flex-1 overflow-y-auto">
        {isLoading && contacts.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <Loader2 className="h-8 w-8 text-blue-500 dark:text-blue-400 animate-spin" />
          </div>
        ) : filteredContacts.length === 0 ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            {searchQuery ? 'No contacts match your search' : 'No contacts found'}
          </div>
        ) : (
          <>
            {filteredContacts.map(contact => (
              <ContactItem
                key={contact.id}
                contact={contact}
                isSelected={selectedContact?.id === contact.id}
                onSelect={() => onSelectContact(contact)}
                isClient={isContactClient(contact, clientContacts)}
                onAddToClients={onAddToClients}
              />
            ))}
            {hasMoreContacts && (
              <div className="p-3 text-center">
                <button
                  className="px-4 py-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                  onClick={onLoadMoreContacts}
                >
                  Load More
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ContactList;
