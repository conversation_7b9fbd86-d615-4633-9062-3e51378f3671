import React from 'react';
import { User, Check, UserPlus } from 'lucide-react';
import { Contact } from '../../types/whatsapp';
import { formatTimestamp } from '../../utils/formatters';

interface ContactItemProps {
  contact: Contact;
  isSelected: boolean;
  onSelect: () => void;
  isClient?: boolean;
  onAddToClients?: (contact: Contact) => void;
}

const ContactItem: React.FC<ContactItemProps> = ({
  contact,
  isSelected,
  onSelect,
  isClient = false,
  onAddToClients
}) => {
  return (
    <div
      className={`p-3 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center ${
        isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''
      }`}
      onClick={onSelect}
    >
      {/* Avatar with client indicator */}
      <div className="relative">
        {contact.avatar ? (
          <img
            src={contact.avatar}
            alt={contact.name}
            className="h-12 w-12 rounded-full object-cover"
          />
        ) : (
          <div className="h-12 w-12 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
            <User className="h-6 w-6 text-gray-600 dark:text-gray-300" />
          </div>
        )}

        {/* Client indicator */}
        {isClient && (
          <div className="absolute -top-1 -right-1 bg-green-500 rounded-full p-1" title="This contact is already a client">
            <Check className="h-3 w-3 text-white" />
          </div>
        )}
      </div>

      <div className="flex-1 min-w-0 ml-3">
        <div className="flex justify-between items-baseline">
          <h3 className="font-medium truncate text-gray-900 dark:text-white">{contact.name}</h3>
          {contact.timestamp && (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {formatTimestamp(contact.timestamp)}
            </span>
          )}
        </div>
        <div className="flex justify-between items-center">
          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
            {contact.lastMessage || `+${contact.phone}`}
          </p>

          {/* Action buttons */}
          <div className="flex items-center space-x-1">
            {/* Add to clients button (only show if not a client and handler provided) */}
            {!isClient && onAddToClients && (
              <button
                onClick={(e) => {
                  e.stopPropagation(); // Prevent triggering the parent onClick
                  onAddToClients(contact);
                }}
                className="p-1 text-blue-500 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-full"
                title="Add this WhatsApp contact to your clients database"
              >
                <UserPlus className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactItem;
