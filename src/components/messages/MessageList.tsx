import React, { useRef, useEffect } from 'react';
import { Message } from '../../types/whatsapp';
import MessageItem from './MessageItem';
import { formatMessageDate } from '../../utils/formatters';

interface MessageListProps {
  messages: Message[];
  isLoading: boolean;
}

const MessageList: React.FC<MessageListProps> = ({ messages, isLoading }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Group messages by date
  const groupedMessages = React.useMemo(() => {
    const groups: Record<string, Message[]> = {};

    messages.forEach(message => {
      const date = formatMessageDate(message.timestamp);
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);
    });

    return Object.entries(groups).map(([date, messages]) => ({
      date,
      messages
    }));
  }, [messages]);

  // Scroll to bottom only when new messages are added, not on every message change
  const prevMessagesLengthRef = useRef(messages.length);

  useEffect(() => {
    // Only auto-scroll if new messages were added (not when just refreshing existing ones)
    const shouldScrollToBottom = messages.length > prevMessagesLengthRef.current;

    // Update the previous length reference
    prevMessagesLengthRef.current = messages.length;

    // Only scroll if new messages were added
    if (shouldScrollToBottom && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  return (
    <div className="flex-1 overflow-y-auto p-2 space-y-3">
      {isLoading ? (
        <div className="flex justify-center items-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      ) : messages.length === 0 ? (
        <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
          No messages yet
        </div>
      ) : (
        groupedMessages.map(group => (
          <div key={group.date} className="space-y-2">
            <div className="flex justify-center">
              <span className="text-xs bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full sticky top-0">
                {group.date}
              </span>
            </div>
            {group.messages.map(message => (
              <MessageItem key={message.id} message={message} />
            ))}
          </div>
        ))
      )}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
