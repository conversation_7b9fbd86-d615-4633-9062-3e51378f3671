import { CheckCircle, Info } from 'lucide-react';
import { useState } from 'react';

// Feature interface is defined but not directly used in this file
// Keeping it for documentation purposes
// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface Feature {
  text: string;
  highlight?: boolean;
}

interface PricingProps {
  t: (key: string) => string;
}

export function Pricing({ t }: PricingProps) {
  const [isAnnual, setIsAnnual] = useState(true);

  const getStripeLink = (planName: string) => {
    if (planName === t('pricing.plans.enterprise.name')) {
      return null; // For Enterprise plan, we'll handle it differently
    } else {
      // For now, redirect to login page instead of Stripe
      return '/login';
    }
  };

  const getPrice = (monthly: number, planName: string) => {
    if (isAnnual) {
      // Apply 20% discount for annual plans
      return Math.round(monthly * 0.8);
    }
    return monthly;
  };

  const getAnnualSavings = (planName: string) => {
    // Calculate 20% savings on annual plans
    if (planName === t('pricing.plans.starter.name')) {
      return Math.round(50 * 12 * 0.2); // 20% of annual price
    } else if (planName === t('pricing.plans.professional.name')) {
      return Math.round(100 * 12 * 0.2); // 20% of annual price
    }
    return 0;
  };

  const plans = [
    {
      name: t('pricing.plans.starter.name'),
      subtitle: t('pricing.plans.starter.subtitle'),
      price: getPrice(50, 'starter'),
      monthlyPrice: 50,
      period: isAnnual ? t('pricing.period.annual') : t('pricing.period.monthly'),
      setupFee: isAnnual ? 0 : 100,
      features: [{
        text: t('pricing.plans.starter.features.f1'),
        highlight: true
      }, {
        text: t('pricing.plans.starter.features.f2'),
        highlight: true
      }, {
        text: t('pricing.plans.starter.features.f3'),
        highlight: true
      }, {
        text: t('pricing.plans.starter.features.f4'),
        highlight: true
      }],
      note: t('pricing.plans.starter.note')
    },
    {
      name: t('pricing.plans.professional.name'),
      subtitle: t('pricing.plans.professional.subtitle'),
      price: getPrice(100, 'professional'),
      monthlyPrice: 100,
      period: isAnnual ? t('pricing.period.annual') : t('pricing.period.monthly'),
      setupFee: isAnnual ? 0 : 100,
      features: [{
        text: t('pricing.plans.professional.features.f1'),
        highlight: true
      }, {
        text: t('pricing.plans.professional.features.f2'),
        highlight: true
      }, {
        text: t('pricing.plans.professional.features.f3'),
        highlight: true
      }, {
        text: t('pricing.plans.professional.features.f4'),
        highlight: true
      }],
      note: t('pricing.plans.professional.note'),
      popular: true
    },
    {
      name: t('pricing.plans.enterprise.name'),
      subtitle: t('pricing.plans.enterprise.subtitle'),
      price: 'Contact Us',
      isCustomPrice: true,
      features: [{
        text: t('pricing.plans.enterprise.features.f1'),
        highlight: true
      }, {
        text: t('pricing.plans.enterprise.features.f2'),
        highlight: true
      }, {
        text: t('pricing.plans.enterprise.features.f3'),
        highlight: true
      }, {
        text: t('pricing.plans.enterprise.features.f4'),
        highlight: true
      }, {
        text: t('pricing.plans.enterprise.features.f5'),
        highlight: true
      }, {
        text: t('pricing.plans.enterprise.features.f6'),
        highlight: true
      }],
      note: t('pricing.plans.enterprise.note')
    }
  ];

  return (
    <section id="pricing" className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-8">
          <span className="inline-block px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm font-semibold mb-4">
            {t('nav.pricing')}
          </span>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 dark:text-white">{t('pricing.title')}</h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            {t('pricing.subtitle')}
          </p>
        </div>

        <div className="flex justify-center mb-8">
          <div className="bg-gray-100 dark:bg-gray-800 p-1 rounded-lg inline-flex items-center">
            <button
              onClick={() => setIsAnnual(false)}
              className={`px-4 py-2 rounded-md transition-all ${
                !isAnnual ? 'bg-white dark:bg-gray-700 shadow-sm text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400'
              }`}
            >
              {t('pricing.billing.monthly')}
            </button>
            <button
              onClick={() => setIsAnnual(true)}
              className={`px-4 py-2 rounded-md transition-all ${
                isAnnual ? 'bg-white dark:bg-gray-700 shadow-sm text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400'
              }`}
            >
              {t('pricing.billing.annually')}
              <span className="ml-1 text-sm text-green-500 dark:text-green-400">{t('pricing.billing.saveText')}</span>
            </button>
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-4">
          {plans.map((plan, index) => (
            <div
              key={index}
              className={`relative p-4 sm:p-6 rounded-xl ${
                plan.popular
                  ? 'bg-blue-600 dark:bg-blue-700 text-white shadow-xl scale-105 min-h-[650px]'
                  : 'bg-white dark:bg-gray-800 border-2 border-gray-100 dark:border-gray-700 min-h-[620px]'
              }`}
            >
              {plan.popular && (
                <span className="absolute -top-4 left-1/2 -translate-x-1/2 bg-green-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                  {t('pricing.mostPopular')}
                </span>
              )}
              <h3 className={`text-lg sm:text-xl font-bold mb-2 ${plan.popular ? 'text-white' : 'text-gray-900 dark:text-white'}`}>
                {plan.name} – {plan.subtitle}
              </h3>
              {plan.isCustomPrice ? (
                <div className="mb-6">
                  <div className="flex items-baseline gap-2">
                    <span className={`text-4xl font-bold ${plan.popular ? 'text-white' : 'text-gray-900 dark:text-white'}`}>
                      {plan.price}
                    </span>
                  </div>
                  <div className="mt-2">
                    <span className={`text-sm ${plan.popular ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'}`}>
                      Custom pricing for your specific needs
                    </span>
                  </div>
                </div>
              ) : (
                <div className="mb-6">
                  <div className="flex items-baseline gap-2">
                    <span className={`text-4xl font-bold ${plan.popular ? 'text-white' : 'text-gray-900 dark:text-white'}`}>
                      €{plan.price}*
                    </span>
                    <span className={`text-sm ${plan.popular ? 'text-blue-100' : 'text-gray-600 dark:text-gray-300'}`}>
                      {t('pricing.period.perMonth')}
                    </span>
                  </div>
                  <div className="mt-1">
                    <span className={`text-sm ${plan.popular ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'}`}>
                      +21% {t('pricing.tax.name')} = €{(Number(plan.price) * 1.21).toFixed(2)} total
                    </span>
                  </div>
                  {isAnnual && (
                    <div className="mt-1">
                      <span className={`text-sm ${plan.popular ? 'text-green-300' : 'text-green-600 dark:text-green-400'}`}>
                        {t('pricing.savings', { amount: getAnnualSavings(plan.name) })}
                      </span>
                    </div>
                  )}
                  {isAnnual && (
                    <div className="mt-2 flex items-center gap-1">
                      <Info className={`h-4 w-4 ${plan.popular ? 'text-blue-200' : 'text-green-500 dark:text-green-400'}`} />
                      <span className={`text-sm ${plan.popular ? 'text-blue-100' : 'text-green-600 dark:text-green-400'}`}>
                        {t('pricing.billing.saveText')}
                      </span>
                    </div>
                  )}
                </div>
              )}

              <ul className="mb-4 space-y-2">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center gap-3">
                    <CheckCircle className={`h-4 w-4 flex-shrink-0 ${plan.popular ? 'text-blue-200' : 'text-blue-600 dark:text-blue-400'}`} />
                    <span className={`text-sm ${plan.popular ? 'text-blue-100' : 'text-gray-600 dark:text-gray-300'}`}>{feature.text}</span>
                  </li>
                ))}
              </ul>
              {plan.note && (
                <p className={`text-sm ${plan.popular ? 'text-blue-100' : 'text-gray-600 dark:text-gray-300'}`}>
                  💎 {plan.note}
                </p>
              )}
              <div className="absolute bottom-6 left-6 right-6">
              <button
                onClick={(e) => {
                  const planName = plan.name; // Use current plan's name instead of hardcoding Enterprise
                  const link = getStripeLink(planName);

                  if (!link) {
                    e.preventDefault();
                    document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' });
                  } else {
                    window.location.href = link;
                  }
                }}
                className={`w-full py-3 px-4 rounded-lg text-sm font-semibold transition-all ${
                  plan.name === t('pricing.plans.enterprise.name')
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : plan.popular
                      ? 'bg-white text-blue-600 hover:bg-blue-50'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {plan.name === t('pricing.plans.enterprise.name')
                  ? t('pricing.cta.contact')
                  : t('pricing.cta.start')}
              </button>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-8 text-sm text-gray-500 dark:text-gray-400">
          * {t('pricing.tax.note')}
        </div>
      </div>
    </section>
  );
}
