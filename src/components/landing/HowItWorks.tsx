import { CalendarCheck, UserPlus, Settings } from 'lucide-react';

interface HowItWorksProps {
  t: (key: string) => string;
}

export function HowItWorks({ t }: HowItWorksProps) {
  return (
    <section id="how-it-works" className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm font-semibold mb-4">
            {t('nav.howItWorks')}
          </span>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 dark:text-white">{t('howItWorks.title')}</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            {t('howItWorks.subtitle')}
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {[
            {
              icon: <UserPlus />,
              title: t('howItWorks.steps.s1.title'),
              description: t('howItWorks.steps.s1.description')
            },
            {
              icon: <Settings />,
              title: t('howItWorks.steps.s2.title'),
              description: t('howItWorks.steps.s2.description')
            },
            {
              icon: <CalendarCheck />,
              title: t('howItWorks.steps.s3.title'),
              description: t('howItWorks.steps.s3.description')
            }
          ].map((step, index) => (
            <div key={index} className="relative p-8 bg-white dark:bg-gray-700 rounded-xl shadow-lg">
              <div className="absolute -top-4 left-8 w-8 h-8 bg-blue-600 dark:bg-blue-700 text-white rounded-full flex items-center justify-center font-bold">
                {index + 1}
              </div>
              <div className="text-blue-600 dark:text-blue-400 mb-4">
                {step.icon}
              </div>
              <h3 className="text-xl font-bold mb-4 dark:text-white">{step.title}</h3>
              <p className="text-gray-600 dark:text-gray-300">{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
