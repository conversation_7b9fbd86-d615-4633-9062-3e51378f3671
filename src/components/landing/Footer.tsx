import { Facebook, Twitter, Linkedin, Instagram } from 'lucide-react';
import { useState } from 'react';
import { PolicyModal } from './PolicyModal';

interface FooterProps {
  t: (key: string) => string;
}

export function Footer({ t }: FooterProps) {
  const currentYear = new Date().getFullYear();
  const [activePolicy, setActivePolicy] = useState<'privacy' | 'terms' | 'cookies' | null>(null);
  
  const policies = {
    privacy: {
      title: t('policies.privacy.title'),
      content: t('policies.privacy.content')
    },
    terms: {
      title: t('policies.terms.title'),
      content: t('policies.terms.content')
    },
    cookies: {
      title: t('policies.cookies.title'),
      content: t('policies.cookies.content')
    }
  };

  return (
    <footer className="bg-gray-900 text-gray-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col items-center gap-8">
          <div className="flex space-x-6">
            <a href="#" className="hover:text-white transition-colors">
              <Facebook className="h-6 w-6" />
            </a>
            <a href="#" className="hover:text-white transition-colors">
              <Twitter className="h-6 w-6" />
            </a>
            <a href="#" className="hover:text-white transition-colors">
              <Linkedin className="h-6 w-6" />
            </a>
            <a href="#" className="hover:text-white transition-colors">
              <Instagram className="h-6 w-6" />
            </a>
          </div>
          
          <div className="flex flex-wrap justify-center gap-6">
            <button 
              onClick={() => setActivePolicy('privacy')} 
              className="text-gray-400 hover:text-white transition-colors"
            >
              {t('footer.policies.privacy')}
            </button>
            <button 
              onClick={() => setActivePolicy('terms')} 
              className="text-gray-400 hover:text-white transition-colors"
            >
              {t('footer.policies.terms')}
            </button>
            <button 
              onClick={() => setActivePolicy('cookies')} 
              className="text-gray-400 hover:text-white transition-colors"
            >
              {t('footer.policies.cookies')}
            </button>
          </div>
          
          <div className="text-center">
            <p className="text-gray-400">
              &copy; {currentYear} FixMyCal. {t('footer.rights')}
            </p>
          </div>
        </div>
      </div>

      <PolicyModal
        isOpen={activePolicy === 'privacy'}
        onClose={() => setActivePolicy(null)}
        title={policies.privacy.title}
        content={policies.privacy.content}
      />
      <PolicyModal
        isOpen={activePolicy === 'terms'}
        onClose={() => setActivePolicy(null)}
        title={policies.terms.title}
        content={policies.terms.content}
      />
      <PolicyModal
        isOpen={activePolicy === 'cookies'}
        onClose={() => setActivePolicy(null)}
        title={policies.cookies.title}
        content={policies.cookies.content}
      />
    </footer>
  );
}
