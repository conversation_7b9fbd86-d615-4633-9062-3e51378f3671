import { Calendar, MessageSquare, CheckCircle } from 'lucide-react';

interface FeaturesProps {
  t: (key: string) => string;
}

export function Features({ t }: FeaturesProps) {
  return (
    <section id="features" className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm font-semibold mb-4">
            {t('nav.features')}
          </span>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 dark:text-white">{t('features.title')}</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            {t('features.subtitle')}
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          <div className="p-8 rounded-2xl bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all">
            <div className="inline-block p-3 rounded-lg bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 mb-4">
              <Calendar className="h-6 w-6" />
            </div>
            <h3 className="text-xl font-bold mb-4 dark:text-white">{t('features.calendar.title')}</h3>
            <ul className="space-y-3">
              <li className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-1" />
                <span className="dark:text-gray-300">{t('features.calendar.f1')}</span>
              </li>
              <li className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-1" />
                <span className="dark:text-gray-300">{t('features.calendar.f2')}</span>
              </li>
              <li className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-1" />
                <span className="dark:text-gray-300">{t('features.calendar.f3')}</span>
              </li>
              <li className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-1" />
                <span className="dark:text-gray-300">{t('features.calendar.f4')}</span>
              </li>
            </ul>
          </div>

          <div className="p-8 rounded-2xl bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all">
            <div className="inline-block p-3 rounded-lg bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 mb-4">
              <MessageSquare className="h-6 w-6" />
            </div>
            <h3 className="text-xl font-bold mb-4 dark:text-white">{t('features.support.title')}</h3>
            <ul className="space-y-3">
              <li className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-1" />
                <span className="dark:text-gray-300">{t('features.support.f1')}</span>
              </li>
              <li className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-1" />
                <span className="dark:text-gray-300">{t('features.support.f2')}</span>
              </li>
              <li className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-1" />
                <span className="dark:text-gray-300">{t('features.support.f3')}</span>
              </li>
              <li className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-1" />
                <span className="dark:text-gray-300">{t('features.support.f4')}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}
