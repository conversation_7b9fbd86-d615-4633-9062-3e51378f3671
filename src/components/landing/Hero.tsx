import { Clock, CheckCircle, ArrowR<PERSON>, Play } from 'lucide-react';

interface HeroProps {
  t: (key: string) => string;
}

export function Hero({ t }: HeroProps) {
  return (
    <section id="top" className="relative min-h-[85vh] flex items-center bg-gradient-to-r from-gray-900 to-blue-900 text-white overflow-hidden">
      <div className="absolute inset-0">
        <img
          src="/images/ai-robot.jpg"
          alt="AI Assistant"
          className="w-full h-full object-cover opacity-30 object-center"
        />
      </div>
      <div className="relative z-10 max-w-6xl mx-auto px-4 py-20 md:py-28">
        <div className="max-w-3xl">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-blue-600/20 text-blue-400 mb-6">
            <Clock className="w-5 h-5" />
            <span>{t('hero.tagline')}</span>
          </div>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            {t('hero.title')}
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-2xl leading-relaxed">
            {t('hero.subtitle')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 items-start">
            <a href="#pricing" className="btn-secondary inline-flex items-center text-base md:text-lg px-8 py-4">
              {t('hero.cta.primary')}
              <ArrowRight className="ml-2 h-5 w-5" />
            </a>
            <a
              href="https://wa.me/34604459063?text=I%20would%20like%20to%20schedule%20a%20demo%20of%20FixMyCal"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-outline inline-flex items-center text-base md:text-lg px-8 py-4"
            >
              {t('hero.cta.secondary')}
              <Play className="ml-2 h-5 w-5" />
            </a>
          </div>
          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-6 w-6 text-green-400" />
              <span className="text-gray-200">{t('hero.features.f1')}</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-6 w-6 text-green-400" />
              <span className="text-gray-200">{t('hero.features.f2')}</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-6 w-6 text-green-400" />
              <span className="text-gray-200">{t('hero.features.f3')}</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
