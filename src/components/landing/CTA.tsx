interface CTAProps {
  t: (key: string) => string;
}

export function CTA({ t }: CTAProps) {
  return (
    <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
      <div className="max-w-4xl mx-auto px-4 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-8">{t('cta.title')}</h2>
        <p className="text-xl text-blue-100 mb-8">
          {t('cta.subtitle')}
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="https://wa.me/34604459063?text=I%20would%20like%20to%20schedule%20a%20demo%20of%20FixMyCal"
            target="_blank"
            rel="noopener noreferrer"
            className="btn-outline border-white text-white hover:bg-white hover:text-blue-600 inline-block text-center"
          >
            {t('cta.demo')}
          </a>
        </div>
      </div>
    </section>
  );
}
