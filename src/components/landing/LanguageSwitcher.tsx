import { useTranslation } from 'react-i18next';
import { Globe, Check } from 'lucide-react';
import { useState } from 'react';

const languages = [
  { code: 'en', name: 'English' },
  { code: 'es', name: '<PERSON>spa<PERSON><PERSON>' },
  { code: 'ca', name: 'Català' }
];

export function LanguageSwitcher() {
  const { i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const isMobile = window.innerWidth < 768;

  // Find current language for future use
  // const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0];

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
          isMobile ? 'hover:bg-gray-50 dark:hover:bg-gray-700 w-full justify-center' : 'hover:bg-gray-100 dark:hover:bg-gray-700'
        }`}
      >
        <Globe className="h-5 w-5 text-gray-600 dark:text-gray-300" strokeWidth={1.5} />
      </button>

      {isOpen && (
        <div className={`${
          isMobile
            ? 'fixed inset-x-0 bottom-0 rounded-t-xl'
            : 'absolute right-0 mt-2 w-48 rounded-lg'
        } bg-white dark:bg-gray-800 shadow-lg py-2 z-50`}>
          <div className="px-4 py-2 border-b border-gray-100 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">Select Language</h3>
          </div>
          {languages.map((language) => (
            <button
              key={language.code}
              onClick={() => {
                i18n.changeLanguage(language.code);
                setIsOpen(false);
              }}
              className={`w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 ${
                language.code === i18n.language ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-700 dark:text-gray-300'
              }`}
            >
              <span>{language.name}</span>
              {language.code === i18n.language && (
                <Check className="h-4 w-4 ml-auto" />
              )}
            </button>
          ))}
          {isMobile && (
            <div className="px-4 py-2 border-t border-gray-100 dark:border-gray-700">
              <button
                onClick={() => setIsOpen(false)}
                className="w-full py-2 text-center text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
              >
                Cancel
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
