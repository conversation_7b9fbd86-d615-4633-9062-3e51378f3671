import { Clock, DollarSign, X } from 'lucide-react';
import { ChangeEvent, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

interface CalculatorData {
  appointmentsPerDay: number;
  averageTimePerAppointment: number;
  hourlyRate: number;
}

interface Savings {
  timePerDay: number;
  hoursPerMonth: number;
  monthlySavings: number;
}

interface SavingsCalculatorProps {
  isOpen: boolean;
  onClose: () => void;
  calculatorData: CalculatorData;
  savings: Savings;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
}

export function SavingsCalculator({
  isOpen,
  onClose,
  calculatorData,
  savings,
  onChange
}: SavingsCalculatorProps) {
  const { t } = useTranslation();

  useEffect(() => {
    if (!isOpen) {
      const timer = setTimeout(() => {
        // Logic to handle after the popup is closed
      }, 300); // Match the duration of the exit animation
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  return (
    <div className={`fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-3 transition-opacity duration-300 ${isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}>
      <div className={`bg-white dark:bg-gray-800 rounded-xl max-w-sm w-full transition-transform duration-300 transform ${isOpen ? 'translate-y-0 scale-100' : '-translate-y-4 scale-95'}`}>
        <div className="p-3 border-b border-gray-100 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white">{t('calculator.title')}</h3>
            <button
              onClick={onClose}
              className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <X className="h-5 w-5 text-gray-500 dark:text-gray-400" />
            </button>
          </div>
        </div>

        <div className="p-3 space-y-3">
          <div className="space-y-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-0.5">
                {t('calculator.fields.appointments')}
              </label>
              <input
                type="number"
                name="appointmentsPerDay"
                value={calculatorData.appointmentsPerDay}
                onChange={onChange}
                min="1"
                max="100"
                className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-0.5">
                {t('calculator.fields.minutes')}
              </label>
              <input
                type="number"
                name="averageTimePerAppointment"
                value={calculatorData.averageTimePerAppointment}
                onChange={onChange}
                min="1"
                max="60"
                className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-0.5">
                {t('calculator.fields.rate')}
              </label>
              <input
                type="number"
                name="hourlyRate"
                value={calculatorData.hourlyRate}
                onChange={onChange}
                min="1"
                max="1000"
                className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3 space-y-2">
            <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-300">{t('calculator.results.title')}</h4>

            <div className="grid grid-cols-2 gap-2">
              <div className="bg-white dark:bg-gray-700 p-2 rounded-md shadow-sm">
                <div className="flex items-center gap-1.5 text-blue-600 dark:text-blue-400 mb-1">
                  <Clock className="h-4 w-4" />
                  <span className="font-medium">{t('calculator.results.timeSavings')}</span>
                </div>
                <p className="text-lg font-bold text-gray-900 dark:text-white">{Math.round(savings.timePerDay)} min/day</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">{Math.ceil(savings.hoursPerMonth)} hours/month</p>
              </div>

              <div className="bg-white dark:bg-gray-700 p-2 rounded-md shadow-sm">
                <div className="flex items-center gap-1.5 text-green-600 dark:text-green-400 mb-1">
                  <DollarSign className="h-4 w-4" />
                  <span className="font-medium">{t('calculator.results.moneySavings')}</span>
                </div>
                <p className="text-lg font-bold text-gray-900 dark:text-white">€{savings.monthlySavings}/month</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">€{savings.monthlySavings * 12}/year</p>
              </div>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 p-2 rounded-md">
              <p className="text-green-800 dark:text-green-300 text-xs">
                {t('calculator.results.summary', { hours: Math.ceil(savings.hoursPerMonth), money: savings.monthlySavings,
                  yearlyHours: Math.ceil(savings.hoursPerMonth * 12), yearlyMoney: savings.monthlySavings * 12 })}
              </p>
            </div>
          </div>
        </div>

        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-b-xl mb-4">
          <a
            href="#pricing"
            onClick={() => {
              onClose();
            }}
            className="w-full btn-primary text-sm py-2 mb-4"
          >
            {t('calculator.cta')}
          </a>
        </div>
      </div>
    </div>
  );
}
