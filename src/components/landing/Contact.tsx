import React, { useState } from 'react';
import { Mail, MessageSquareMore, Phone } from 'lucide-react';
import { TFunction } from 'i18next';

interface ContactProps {
  id: string;
  formData: {
    name: string;
    email: string;
    phone: string;
    countryCode: string;
    businessType: string;
    needs: string;
  };
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  onSubmit: (e: React.FormEvent) => void;
  t: TFunction;
}

export function Contact({ t }: ContactProps) {
  const [result, setResult] = useState("");

  const onSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setResult(t('contact.form.sending'));
    const formData = new FormData(event.target as HTMLFormElement);

    formData.append("access_key", "15cf5583-c647-41f9-9a48-87a89fc54c33");

    try {
      const response = await fetch("https://api.web3forms.com/submit", {
        method: "POST",
        body: formData
      });

      const data = await response.json();

      if (data.success) {
        setResult(t('contact.form.submittedSuccessfully'));
        (event.target as HTMLFormElement).reset();
      } else {
        console.error("Error", data);
        setResult(data.message ? t(data.message) : t('contact.form.errorOccurred'));
      }
    } catch (error) {
      console.error("Error:", error);
      setResult(t('contact.form.failedToSubmit'));
    }
  };

  return (
    <section id="contact" className="py-12 sm:py-20 bg-gray-50 dark:bg-gray-800">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm font-semibold mb-4">
            {t('nav.contact')}
          </span>
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 dark:text-white">{t('contact.title')}</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            {t('contact.subtitle')}
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12">
          <div className="bg-white dark:bg-gray-700 p-4 sm:p-6 md:p-8 rounded-2xl shadow-lg">
            <form onSubmit={onSubmit} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('contact.form.name')}
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  required
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('contact.form.email')}
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  required
                />
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('contact.form.phone')}
                </label>
                <div className="flex gap-2">
                  <select
                    id="countryCode"
                    name="countryCode"
                    defaultValue="+34"
                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  >
                    <option value="+93">🇦🇫 +93</option>
                    <option value="+355">🇦🇱 +355</option>
                    <option value="+213">🇩🇿 +213</option>
                    <option value="+376">🇦🇩 +376</option>
                    <option value="+244">🇦🇴 +244</option>
                    <option value="+54">🇦🇷 +54</option>
                    <option value="+374">🇦🇲 +374</option>
                    <option value="+61">🇦🇺 +61</option>
                    <option value="+43">🇦🇹 +43</option>
                    <option value="+994">🇦🇿 +994</option>
                    <option value="+973">🇧🇭 +973</option>
                    <option value="+880">🇧🇩 +880</option>
                    <option value="+375">🇧🇾 +375</option>
                    <option value="+32">🇧🇪 +32</option>
                    <option value="+501">🇧🇿 +501</option>
                    <option value="+229">🇧🇯 +229</option>
                    <option value="+975">🇧🇹 +975</option>
                    <option value="+591">🇧🇴 +591</option>
                    <option value="+387">🇧🇦 +387</option>
                    <option value="+267">🇧🇼 +267</option>
                    <option value="+55">🇧🇷 +55</option>
                    <option value="+359">🇧🇬 +359</option>
                    <option value="+226">🇧🇫 +226</option>
                    <option value="+257">🇧🇮 +257</option>
                    <option value="+855">🇰🇭 +855</option>
                    <option value="+237">🇨🇲 +237</option>
                    <option value="+1">🇨🇦 +1</option>
                    <option value="+236">🇨🇫 +236</option>
                    <option value="+235">🇹🇩 +235</option>
                    <option value="+56">🇨🇱 +56</option>
                    <option value="+86">🇨🇳 +86</option>
                    <option value="+57">🇨🇴 +57</option>
                    <option value="+269">🇰🇲 +269</option>
                    <option value="+242">🇨🇬 +242</option>
                    <option value="+506">🇨🇷 +506</option>
                    <option value="+385">🇭🇷 +385</option>
                    <option value="+53">🇨🇺 +53</option>
                    <option value="+357">🇨🇾 +357</option>
                    <option value="+420">🇨🇿 +420</option>
                    <option value="+45">🇩🇰 +45</option>
                    <option value="+253">🇩🇯 +253</option>
                    <option value="+593">🇪🇨 +593</option>
                    <option value="+20">🇪🇬 +20</option>
                    <option value="+503">🇸🇻 +503</option>
                    <option value="+240">🇬🇶 +240</option>
                    <option value="+372">🇪🇪 +372</option>
                    <option value="+251">🇪🇹 +251</option>
                    <option value="+679">🇫🇯 +679</option>
                    <option value="+358">🇫🇮 +358</option>
                    <option value="+33">🇫🇷 +33</option>
                    <option value="+241">🇬🇦 +241</option>
                    <option value="+220">🇬🇲 +220</option>
                    <option value="+995">🇬🇪 +995</option>
                    <option value="+49">🇩🇪 +49</option>
                    <option value="+233">🇬🇭 +233</option>
                    <option value="+30">🇬🇷 +30</option>
                    <option value="+502">🇬🇹 +502</option>
                    <option value="+224">🇬🇳 +224</option>
                    <option value="+245">🇬🇼 +245</option>
                    <option value="+592">🇬🇾 +592</option>
                    <option value="+509">🇭🇹 +509</option>
                    <option value="+504">🇭🇳 +504</option>
                    <option value="+852">🇭🇰 +852</option>
                    <option value="+36">🇭🇺 +36</option>
                    <option value="+354">🇮🇸 +354</option>
                    <option value="+91">🇮🇳 +91</option>
                    <option value="+62">🇮🇩 +62</option>
                    <option value="+98">🇮🇷 +98</option>
                    <option value="+964">🇮🇶 +964</option>
                    <option value="+353">🇮🇪 +353</option>
                    <option value="+972">🇮🇱 +972</option>
                    <option value="+39">🇮🇹 +39</option>
                    <option value="+81">🇯🇵 +81</option>
                    <option value="+962">🇯🇴 +962</option>
                    <option value="+7">🇰🇿 +7</option>
                    <option value="+254">🇰🇪 +254</option>
                    <option value="+82">🇰🇷 +82</option>
                    <option value="+965">🇰🇼 +965</option>
                    <option value="+996">🇰🇬 +996</option>
                    <option value="+856">🇱🇦 +856</option>
                    <option value="+371">🇱🇻 +371</option>
                    <option value="+961">🇱🇧 +961</option>
                    <option value="+266">🇱🇸 +266</option>
                    <option value="+231">🇱🇷 +231</option>
                    <option value="+218">🇱🇾 +218</option>
                    <option value="+423">🇱🇮 +423</option>
                    <option value="+370">🇱🇹 +370</option>
                    <option value="+352">🇱🇺 +352</option>
                    <option value="+853">🇲🇴 +853</option>
                    <option value="+389">🇲🇰 +389</option>
                    <option value="+261">🇲🇬 +261</option>
                    <option value="+265">🇲🇼 +265</option>
                    <option value="+60">🇲🇾 +60</option>
                    <option value="+960">🇲🇻 +960</option>
                    <option value="+223">🇲🇱 +223</option>
                    <option value="+356">🇲🇹 +356</option>
                    <option value="+222">🇲🇷 +222</option>
                    <option value="+230">🇲🇺 +230</option>
                    <option value="+52">🇲🇽 +52</option>
                    <option value="+373">🇲🇩 +373</option>
                    <option value="+377">🇲🇨 +377</option>
                    <option value="+976">🇲🇳 +976</option>
                    <option value="+382">🇲🇪 +382</option>
                    <option value="+212">🇲🇦 +212</option>
                    <option value="+258">🇲🇿 +258</option>
                    <option value="+95">🇲🇲 +95</option>
                    <option value="+264">🇳🇦 +264</option>
                    <option value="+977">🇳🇵 +977</option>
                    <option value="+31">🇳🇱 +31</option>
                    <option value="+64">🇳🇿 +64</option>
                    <option value="+505">🇳🇮 +505</option>
                    <option value="+227">🇳🇪 +227</option>
                    <option value="+234">🇳🇬 +234</option>
                    <option value="+47">🇳🇴 +47</option>
                    <option value="+968">🇴🇲 +968</option>
                    <option value="+92">🇵🇰 +92</option>
                    <option value="+507">🇵🇦 +507</option>
                    <option value="+675">🇵🇬 +675</option>
                    <option value="+595">🇵🇾 +595</option>
                    <option value="+51">🇵🇪 +51</option>
                    <option value="+63">🇵🇭 +63</option>
                    <option value="+48">🇵🇱 +48</option>
                    <option value="+351">🇵🇹 +351</option>
                    <option value="+974">🇶🇦 +974</option>
                    <option value="+40">🇷🇴 +40</option>
                    <option value="+7">🇷🇺 +7</option>
                    <option value="+250">🇷🇼 +250</option>
                    <option value="+966">🇸🇦 +966</option>
                    <option value="+221">🇸🇳 +221</option>
                    <option value="+381">🇷🇸 +381</option>
                    <option value="+248">🇸🇨 +248</option>
                    <option value="+232">🇸🇱 +232</option>
                    <option value="+65">🇸🇬 +65</option>
                    <option value="+421">🇸🇰 +421</option>
                    <option value="+386">🇸🇮 +386</option>
                    <option value="+252">🇸🇴 +252</option>
                    <option value="+27">🇿🇦 +27</option>
                    <option value="+34">🇪🇸 +34</option>
                    <option value="+94">🇱🇰 +94</option>
                    <option value="+249">🇸🇩 +249</option>
                    <option value="+597">🇸🇷 +597</option>
                    <option value="+268">🇸🇿 +268</option>
                    <option value="+46">🇸🇪 +46</option>
                    <option value="+41">🇨🇭 +41</option>
                    <option value="+963">🇸🇾 +963</option>
                    <option value="+886">🇹🇼 +886</option>
                    <option value="+992">🇹🇯 +992</option>
                    <option value="+255">🇹🇿 +255</option>
                    <option value="+66">🇹🇭 +66</option>
                    <option value="+228">🇹🇬 +228</option>
                    <option value="+216">🇹🇳 +216</option>
                    <option value="+90">🇹🇷 +90</option>
                    <option value="+993">🇹🇲 +993</option>
                    <option value="+256">🇺🇬 +256</option>
                    <option value="+380">🇺🇦 +380</option>
                    <option value="+971">🇦🇪 +971</option>
                    <option value="+44">🇬🇧 +44</option>
                    <option value="+1">🇺🇸 +1</option>
                    <option value="+598">🇺🇾 +598</option>
                    <option value="+998">🇺🇿 +998</option>
                    <option value="+58">🇻🇪 +58</option>
                    <option value="+84">🇻🇳 +84</option>
                    <option value="+967">🇾🇪 +967</option>
                    <option value="+260">🇿🇲 +260</option>
                    <option value="+263">🇿🇼 +263</option>
                  </select>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    placeholder="123 456 789"
                    className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    required
                  />
                </div>
              </div>

              <div>
                <label htmlFor="businessType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('contact.form.businessType')}
                </label>
                <div className="grid sm:grid-cols-1 gap-4">
                  <select
                    id="businessType"
                    name="businessType"
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  >
                    <option value="">{t('contact.form.businessTypes.placeholder')}</option>
                    <option value="retail">{t('contact.form.businessTypes.retail')}</option>
                    <option value="healthcare">{t('contact.form.businessTypes.healthcare')}</option>
                    <option value="professional">{t('contact.form.businessTypes.professional')}</option>
                    <option value="technology">{t('contact.form.businessTypes.technology')}</option>
                    <option value="other">{t('contact.form.businessTypes.other')}</option>
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="needs" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('contact.form.needs')}
                </label>
                <textarea
                  id="needs"
                  name="needs"
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  required
                ></textarea>
              </div>

              <button type="submit" className="w-full btn-primary">
                {t('contact.form.submit')}
              </button>
              {result && <p className="mt-4 text-center text-green-600 dark:text-green-400">{result}</p>}
            </form>
          </div>

          <div className="lg:pl-6 xl:pl-12">
            <div className="bg-white dark:bg-gray-700 p-8 rounded-2xl shadow-lg">
              <h3 className="text-xl font-bold mb-6 dark:text-white">{t('contact.options.title')}</h3>

              <div className="grid gap-4 sm:gap-6">
                <div className="flex items-start gap-3 sm:gap-4">
                  <div className="p-3 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-lg">
                    <Phone className="h-6 w-6" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-gray-600 dark:text-gray-300">+34 604 45 90 63</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{t('contact.options.hours')}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3 sm:gap-4">
                  <div className="p-3 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-lg">
                    <MessageSquareMore className="h-6 w-6" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-gray-600 dark:text-gray-300">+34 604 45 90 63</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{t('contact.options.whatsapp')}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3 sm:gap-4">
                  <div className="p-3 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-lg">
                    <Mail className="h-6 w-6" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-gray-600 dark:text-gray-300"><EMAIL></p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{t('contact.options.email')}</p>
                  </div>
                </div>
              </div>

              <div className="mt-6 sm:mt-8 p-4 sm:p-6 bg-blue-600 dark:bg-blue-700 text-white rounded-xl">
                <h4 className="text-lg font-semibold mb-2">{t('contact.options.demo.title')}</h4>
                <p className="text-blue-100 dark:text-blue-200 mb-4">{t('contact.options.demo.subtitle')}</p>
                <a
                  href="https://wa.me/34604459063?text=I%20would%20like%20to%20schedule%20a%20demo%20of%20FixMyCal"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn-secondary inline-block text-center"
                >
                  {t('contact.options.demo.button')}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
