import { useState } from 'react';
import { Clock, MessageSquare, ArrowRight } from 'lucide-react';
import { SavingsCalculator } from './SavingsCalculator';

interface CalculatorData {
  appointmentsPerDay: number;
  averageTimePerAppointment: number;
  hourlyRate: number;
}

interface TimeSavingsProps {
  t: (key: string) => string;
}

export function TimeSavings({ t }: TimeSavingsProps) {
  const [isCalculatorOpen, setIsCalculatorOpen] = useState(false);
  const [calculatorData, setCalculatorData] = useState<CalculatorData>({
    appointmentsPerDay: 10,
    averageTimePerAppointment: 15,
    hourlyRate: 50
  });

  const calculateSavings = () => {
    const timePerDay = calculatorData.appointmentsPerDay * calculatorData.averageTimePerAppointment;
    const hoursPerMonth = Math.ceil((timePerDay / 60) * 22); // Assuming 22 working days, rounded up
    const monthlySavings = Math.round(hoursPerMonth * calculatorData.hourlyRate);

    return {
      timePerDay,
      hoursPerMonth,
      monthlySavings
    };
  };

  const handleCalculatorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCalculatorData(prev => ({
      ...prev,
      [name]: parseInt(value) || 0
    }));
  };

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm font-semibold mb-4">
            {t('timeSavings.title')}
          </span>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 dark:text-white">
            {t('timeSavings.heading')}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            {t('timeSavings.subtitle')}
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <div className="bg-white dark:bg-gray-700 p-8 rounded-2xl shadow-lg">
            <h3 className="text-xl font-bold mb-6 text-gray-900 dark:text-white">{t('timeSavings.timeSpent.title')}</h3>

            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-3">{t('timeSavings.timeSpent.scheduling.title')}</h4>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    {t('timeSavings.timeSpent.scheduling.items.availability')}
                  </li>
                  <li className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    {t('timeSavings.timeSpent.scheduling.items.discussing')}
                  </li>
                  <li className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    {t('timeSavings.timeSpent.scheduling.items.confirming')}
                  </li>
                  <li className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    {t('timeSavings.timeSpent.scheduling.items.reminders')}
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-3">{t('timeSavings.timeSpent.customerService.title')}</h4>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    {t('timeSavings.timeSpent.customerService.items.questions')}
                  </li>
                  <li className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    {t('timeSavings.timeSpent.customerService.items.rescheduling')}
                  </li>
                  <li className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    {t('timeSavings.timeSpent.customerService.items.cancellations')}
                  </li>
                  <li className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    {t('timeSavings.timeSpent.customerService.items.followups')}
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-700 p-8 rounded-2xl shadow-lg">
            <h3 className="text-xl font-bold mb-6 text-gray-900 dark:text-white">{t('timeSavings.investment.title')}</h3>

            <div className="space-y-6">
              <div className="p-4 bg-gray-50 dark:bg-gray-600 rounded-xl">
                <h4 className="font-semibold text-gray-900 dark:text-white">{t('timeSavings.investment.small.title')}</h4>
                <p className="text-gray-600 dark:text-gray-300 mb-2">{t('timeSavings.investment.small.appointments')}</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-300">{t('timeSavings.investment.current')}:</span>
                  <span className="font-semibold text-red-600 dark:text-red-400">{t('timeSavings.investment.small.current')}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-300">{t('timeSavings.investment.withFixMyCal')}:</span>
                  <span className="font-semibold text-green-600 dark:text-green-400">{t('timeSavings.investment.withApp')}</span>
                </div>
                <p className="text-sm text-green-600 dark:text-green-400 mt-2 italic">{t('timeSavings.investment.note')}</p>
              </div>

              <div className="p-4 bg-gray-50 dark:bg-gray-600 rounded-xl">
                <h4 className="font-semibold text-gray-900 dark:text-white">{t('timeSavings.investment.medium.title')}</h4>
                <p className="text-gray-600 dark:text-gray-300 mb-2">{t('timeSavings.investment.medium.appointments')}</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-300">{t('timeSavings.investment.current')}:</span>
                  <span className="font-semibold text-red-600 dark:text-red-400">{t('timeSavings.investment.medium.current')}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-300">{t('timeSavings.investment.withFixMyCal')}:</span>
                  <span className="font-semibold text-green-600 dark:text-green-400">{t('timeSavings.investment.withApp')}</span>
                </div>
                <p className="text-sm text-green-600 dark:text-green-400 mt-2 italic">{t('timeSavings.investment.note')}</p>
              </div>

              <div className="p-4 bg-gray-50 dark:bg-gray-600 rounded-xl">
                <h4 className="font-semibold text-gray-900 dark:text-white">{t('timeSavings.investment.large.title')}</h4>
                <p className="text-gray-600 dark:text-gray-300 mb-2">{t('timeSavings.investment.large.appointments')}</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-300">{t('timeSavings.investment.current')}:</span>
                  <span className="font-semibold text-red-600 dark:text-red-400">{t('timeSavings.investment.large.current')}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-300">{t('timeSavings.investment.withFixMyCal')}:</span>
                  <span className="font-semibold text-green-600 dark:text-green-400">{t('timeSavings.investment.withApp')}</span>
                </div>
                <p className="text-sm text-green-600 dark:text-green-400 mt-2 italic">{t('timeSavings.investment.note')}</p>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-xl">
              <h4 className="font-semibold text-blue-900 dark:text-blue-300 mb-2">{t('timeSavings.why.title')}</h4>
              <p className="text-blue-800 dark:text-blue-200">
                {t('timeSavings.why.description')}
              </p>
            </div>
          </div>
        </div>

        <div className="text-center bg-blue-50 dark:bg-blue-900/30 p-8 rounded-2xl">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">{t('timeSavings.cta.title')}</h3>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-6">
            {t('timeSavings.cta.subtitle')}
          </p>
          <button
            onClick={() => setIsCalculatorOpen(true)}
            className="btn-primary inline-flex items-center"
          >
            {t('timeSavings.cta.button')}
            <ArrowRight className="ml-2 h-5 w-5" />
          </button>
          <SavingsCalculator
            isOpen={isCalculatorOpen}
            onClose={() => setIsCalculatorOpen(false)}
            calculatorData={calculatorData}
            savings={calculateSavings()}
            onChange={handleCalculatorChange}
          />
        </div>
      </div>
    </section>
  );
}
