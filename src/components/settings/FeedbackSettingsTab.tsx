import React, { useState, useEffect } from 'react';
import { Star, Clock, MessageSquare, Save, RefreshCw, TestTube } from 'lucide-react';
import { addToast } from '../../components/ui';
import { feedbackService, FeedbackSettings } from '../../services/feedbackService';

export const FeedbackSettingsTab: React.FC = () => {
  const [settings, setSettings] = useState<FeedbackSettings>({
    enabled: true,
    autoRequestAfterAppointment: true,
    requestDelayHours: 2,
    allowAnonymousFeedback: true,
    requireRating: true,
    requireComment: false,
    showPublicReviews: true,
    minimumRatingForPublic: 3,
    requestTemplate: 'Hi {{clientName}}! We hope you enjoyed your {{serviceName}} appointment. We\'d love to hear your feedback! Please rate your experience: {{feedbackLink}}'
  });
  const [isInitialized, setIsInitialized] = useState(false);

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testPhone, setTestPhone] = useState('');
  const [sendingTest, setSendingTest] = useState(false);

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setLoading(true);
    try {
      const response = await feedbackService.getSettings();
      setSettings(response);
      setIsInitialized(true);
    } catch (error) {
      console.error('Error loading feedback settings:', error);
      addToast('Failed to load feedback settings', 'error');
      setIsInitialized(true); // Still mark as initialized to show form
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      await feedbackService.updateSettings(settings);
      addToast('Feedback settings saved successfully', 'success');
    } catch (error) {
      console.error('Error saving feedback settings:', error);
      addToast('Failed to save feedback settings', 'error');
    } finally {
      setSaving(false);
    }
  };

  const sendTestFeedbackRequest = async () => {
    if (!testPhone.trim()) {
      addToast('Please enter a phone number', 'error');
      return;
    }

    setSendingTest(true);
    try {
      await feedbackService.sendTestRequest(testPhone);
      addToast('Test feedback request sent successfully', 'success');
      setTestPhone('');
    } catch (error) {
      console.error('Error sending test feedback request:', error);
      addToast('Failed to send test feedback request', 'error');
    } finally {
      setSendingTest(false);
    }
  };

  const updateDelayHours = (hours: number) => {
    if (hours >= 0 && hours <= 72) {
      setSettings(prev => ({ ...prev, requestDelayHours: hours }));
    }
  };

  const updateMinimumRating = (rating: number) => {
    if (rating >= 1 && rating <= 5) {
      setSettings(prev => ({ ...prev, minimumRatingForPublic: rating }));
    }
  };

  if (loading || !isInitialized) {
    return (
      <div className="flex items-center justify-center py-8">
        <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600 dark:text-gray-300">Loading feedback settings...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Client Feedback System</h2>

      {/* Main Enable/Disable Toggle */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-md font-semibold dark:text-white">Enable Feedback System</h3>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={settings.enabled}
              onChange={e => setSettings(prev => ({ ...prev, enabled: e.target.checked }))}
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>
        <p className="text-gray-600 dark:text-gray-300 text-sm">
          When enabled, clients will be able to provide feedback after their appointments. This helps you improve your services and build trust with potential clients.
        </p>
      </div>

      {/* Automatic Request Settings */}
      {settings.enabled && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-md font-semibold dark:text-white flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Automatic Feedback Requests
            </h3>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.autoRequestAfterAppointment}
                onChange={e => setSettings(prev => ({ ...prev, autoRequestAfterAppointment: e.target.checked }))}
                disabled={!settings.enabled}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {settings.autoRequestAfterAppointment && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <Clock className="h-4 w-4 inline mr-1" />
                  Send request after appointment completion
                </label>
                <div className="flex items-center gap-3">
                  <input
                    type="number"
                    min="0"
                    max="72"
                    value={settings.requestDelayHours}
                    onChange={e => updateDelayHours(parseInt(e.target.value) || 0)}
                    className="w-20 px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={!settings.enabled || !settings.autoRequestAfterAppointment}
                  />
                  <span className="text-gray-600 dark:text-gray-300">hours after appointment</span>
                </div>
              </div>

              {/* Message Template */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  WhatsApp Message Template
                </label>
                <textarea
                  value={settings.requestTemplate}
                  onChange={e => setSettings(prev => ({ ...prev, requestTemplate: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  disabled={!settings.enabled || !settings.autoRequestAfterAppointment}
                  placeholder="Enter your feedback request message template..."
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Available variables: {`{{clientName}}, {{serviceName}}, {{feedbackLink}}`}
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Feedback Requirements */}
      {settings.enabled && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <h3 className="text-md font-semibold dark:text-white mb-4">Feedback Requirements</h3>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Allow Anonymous Feedback</label>
                <p className="text-xs text-gray-500 dark:text-gray-400">Clients can submit feedback without revealing their identity</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={settings.allowAnonymousFeedback}
                  onChange={e => setSettings(prev => ({ ...prev, allowAnonymousFeedback: e.target.checked }))}
                  disabled={!settings.enabled}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Require Rating</label>
                <p className="text-xs text-gray-500 dark:text-gray-400">Clients must provide a star rating</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={settings.requireRating}
                  onChange={e => setSettings(prev => ({ ...prev, requireRating: e.target.checked }))}
                  disabled={!settings.enabled}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Require Comment</label>
                <p className="text-xs text-gray-500 dark:text-gray-400">Clients must provide written feedback</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={settings.requireComment}
                  onChange={e => setSettings(prev => ({ ...prev, requireComment: e.target.checked }))}
                  disabled={!settings.enabled}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>
      )}



      {/* Test Feedback Request */}
      {settings.enabled && settings.autoRequestAfterAppointment && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <h3 className="text-md font-semibold dark:text-white mb-4 flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Test Feedback Request
          </h3>
          <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
            Send a test feedback request to verify your message template and settings.
          </p>

          <div className="flex gap-3">
            <input
              type="tel"
              value={testPhone}
              onChange={e => setTestPhone(e.target.value)}
              placeholder="Enter phone number (e.g., +34123456789)"
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={sendTestFeedbackRequest}
              disabled={sendingTest || !testPhone.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {sendingTest ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <MessageSquare className="h-4 w-4" />
              )}
              Send Test
            </button>
          </div>
        </div>
      )}

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={saveSettings}
          disabled={saving || !settings.enabled}
          className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
        >
          {saving ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <Save className="h-4 w-4" />
          )}
          Save Settings
        </button>
      </div>
    </div>
  );
};
