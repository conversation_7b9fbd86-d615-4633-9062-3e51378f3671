import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CreditCard, ExternalLink, Loader, AlertCircle, Download, CheckCircle } from 'lucide-react';
import { billingService, PricingPlan, BillingInfo, Invoice } from '../../services/billingService';
import { loadStripe, Stripe } from '@stripe/stripe-js';
import { getStripePublishableKey, isStripeConfigured } from '../../utils/env';
import { SubscriptionModal } from './SubscriptionModal';
import { ManageSubscriptionModal } from './ManageSubscriptionModal';
import { useSearchParams } from 'react-router-dom';

// Initialize Stripe only if configured
let stripePromise: Promise<Stripe | null> | null = null;

// Wrap in a function to avoid blocking the component rendering
const initStripe = () => {
  try {
    const stripeKey = getStripePublishableKey();
    if (stripeKey && stripeKey.trim() !== '') {
      return loadStripe(stripeKey);
    } else {
      // Stripe not configured: No publishable key found
      return null;
    }
  } catch (error) {
    console.error('Error initializing Stripe:', error);
    return null;
  }
};

// Initialize Stripe in the background
stripePromise = initStripe();

export const BillingTab: React.FC = () => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [plans, setPlans] = useState<PricingPlan[]>([]);
  const [billingInfo, setBillingInfo] = useState<BillingInfo | null>(null);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [changingPlan] = useState<boolean>(false);
  const [updatingPayment, setUpdatingPayment] = useState<boolean>(false);
  const [downloadingInvoice, setDownloadingInvoice] = useState<string | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<PricingPlan | null>(null);
  const [isAnnual, setIsAnnual] = useState<boolean>(false);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState<boolean>(false);
  const [showManageModal, setShowManageModal] = useState<boolean>(false);

  // Update AI response limit based on the current plan
  useEffect(() => {
    if (billingInfo && billingInfo.currentPlan) {
      const planName = billingInfo.currentPlan.name;
      let aiResponsesLimit = 500; // Default to Starter plan
      let aiResponsesUsed = 345; // Adjust usage to be proportional

      if (planName === 'Professional') {
        aiResponsesLimit = 5000;
        aiResponsesUsed = 3245;
      } else if (planName === 'Enterprise') {
        aiResponsesLimit = 999999; // Unlimited
        aiResponsesUsed = 5000;
      }

      // Only update if the values are different to prevent infinite loops
      if (billingInfo &&
          (billingInfo.aiResponsesLimit !== aiResponsesLimit ||
           billingInfo.aiResponsesUsed !== aiResponsesUsed)) {
        setBillingInfo({
          ...billingInfo,
          aiResponsesLimit,
          aiResponsesUsed
        });
      }
    }
  }, [billingInfo]);

  // Check URL parameters for success/error messages
  useEffect(() => {
    const successParam = searchParams.get('success');
    const canceledParam = searchParams.get('canceled');

    // Only set messages if they're not already set
    if (successParam === 'true' && !success) {
      setSuccess('Your subscription has been updated successfully!');
      // Clear the URL parameter after 5 seconds
      setTimeout(() => {
        setSuccess(null);
        // Use window.location.search to avoid dependency on searchParams
        const newParams = new URLSearchParams(window.location.search);
        newParams.delete('success');
        window.history.replaceState({}, '', `${window.location.pathname}${newParams.toString() ? `?${newParams.toString()}` : ''}`);
      }, 5000);
    } else if (canceledParam === 'true' && !error) {
      setError('Subscription update was canceled.');
      // Clear the URL parameter after 5 seconds
      setTimeout(() => {
        setError(null);
        // Use window.location.search to avoid dependency on searchParams
        const newParams = new URLSearchParams(window.location.search);
        newParams.delete('canceled');
        window.history.replaceState({}, '', `${window.location.pathname}${newParams.toString() ? `?${newParams.toString()}` : ''}`);
      }, 5000);
    }
  }, [searchParams, success, error]);

  // Load billing data
  useEffect(() => {
    const loadBillingData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch all billing data in parallel
        const [plansData, billingInfoData, invoicesData] = await Promise.all([
          billingService.getPlans(),
          billingService.getBillingInfo(),
          billingService.getInvoices()
        ]);

        setPlans(plansData);
        setBillingInfo(billingInfoData);
        setInvoices(invoicesData);
      } catch (err) {
        console.error('Error loading billing data:', err);
        setError('Failed to load billing information. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    // Wrap in a try-catch to prevent unhandled errors from breaking the app
    try {
      loadBillingData();
    } catch (error) {
      console.error('Unhandled error in BillingTab:', error);
      setError('An unexpected error occurred. Please try refreshing the page.');
      setLoading(false);
    }
  }, []);

  // This useEffect was removed to prevent conflicts with the other useEffect that handles URL parameters



  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // Calculate usage percentage
  const calculateUsagePercentage = () => {
    if (!billingInfo) return 0;
    return Math.round((billingInfo.aiResponsesUsed / billingInfo.aiResponsesLimit) * 100);
  };

  // If loading, show loading spinner
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Loader className="h-8 w-8 text-blue-500 animate-spin mb-4" />
        <p className="text-gray-600 dark:text-gray-400">Loading billing information...</p>
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertCircle className="h-8 w-8 text-red-500 mb-4" />
        <p className="text-red-600 dark:text-red-400 mb-2">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
        >
          Retry
        </button>
      </div>
    );
  }

  // If no billing info, show message
  if (!billingInfo) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertCircle className="h-8 w-8 text-yellow-500 mb-4" />
        <p className="text-gray-600 dark:text-gray-400 mb-2">No billing information available.</p>
        <button
          onClick={() => window.location.reload()}
          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
        >
          Refresh
        </button>
      </div>
    );
  }

  return (
    <div>
      {/* Success and error notifications */}
      {success && (
        <div className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 p-3 rounded-md flex items-center gap-2 mb-4">
          <CheckCircle className="h-5 w-5 flex-shrink-0" />
          <span>{success}</span>
        </div>
      )}

      {error && (
        <div className="bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 p-3 rounded-md flex items-center gap-2 mb-4">
          <AlertCircle className="h-5 w-5 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}

      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Billing & Plans</h2>

      <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mb-6">
        <div className="flex items-start gap-3">
          <div className="bg-blue-100 dark:bg-blue-800 p-2 rounded-full">
            <CreditCard className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-blue-900 dark:text-blue-300">Current Plan: {billingInfo.currentPlan.name}</h3>
            <p className="text-blue-700 dark:text-blue-400 text-sm">Your subscription renews on {formatDate(billingInfo.renewalDate)}</p>
            <p className="text-blue-700 dark:text-blue-400 text-sm mt-1">
              {billingInfo.currentPlan.name === 'Enterprise' ? (
                'Custom Pricing Plan'
              ) : (
                <>Monthly billing: €{billingInfo.currentPlan.monthlyFee}/month <span className="text-xs">(+21% {t('pricing.tax.name')} = €{(billingInfo.currentPlan.monthlyFee * 1.21).toFixed(2)} total)</span></>
              )}
            </p>
            <p className="text-blue-700 dark:text-blue-400 text-sm mt-1">AI Responses: {billingInfo.aiResponsesUsed.toLocaleString()} / {billingInfo.aiResponsesLimit.toLocaleString()} used this month</p>
            <div className="mt-2 flex items-center gap-2">
              <button
                onClick={() => setShowManageModal(true)}
                disabled={loading}
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium flex items-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <Loader className="h-3 w-3 animate-spin mr-1" />
                    Loading...
                  </>
                ) : (
                  <>
                    Manage Subscription & Billing
                    <ExternalLink className="h-3 w-3" />
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-6">
        <div>
          <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">Subscription Plans</h3>
          <div className="bg-white dark:bg-gray-800 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Sort plans in a specific order: Starter, Professional, Enterprise */}
              {[...plans].sort((a, b) => {
                // Custom sort order
                const order: Record<string, number> = { 'Starter': 1, 'Professional': 2, 'Enterprise': 3 };
                return (order[a.name] || 99) - (order[b.name] || 99);
              }).map((plan) => {
                const isCurrentPlan = billingInfo?.currentPlan.id === plan.id;
                return (
                  <div
                    key={plan.id}
                    className={`flex-1 p-4 rounded-lg ${isCurrentPlan
                      ? 'border-2 border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                      : 'border border-gray-200 dark:border-gray-700'}`}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className={`font-medium ${isCurrentPlan
                          ? 'text-blue-800 dark:text-blue-300'
                          : 'dark:text-white'}`}>
                          {plan.name}
                        </h4>
                        <p className={`text-sm mt-1 ${isCurrentPlan
                          ? 'text-blue-700 dark:text-blue-400'
                          : 'text-gray-600 dark:text-gray-400'}`}>
                          {plan.name === 'Enterprise' ? (
                            <span>Contact Us for Custom Pricing</span>
                          ) : (
                            <>
                              €{plan.monthlyFee}/month <span className="text-xs">(+21% {t('pricing.tax.name')} = €{(plan.monthlyFee * 1.21).toFixed(2)})</span>
                            </>
                          )}
                        </p>
                        {plan.features.map((feature, index) => (
                          <p
                            key={index}
                            className={`text-sm mt-1 ${isCurrentPlan
                              ? 'text-blue-700 dark:text-blue-400'
                              : 'text-gray-600 dark:text-gray-400'}`}
                          >
                            {feature}
                          </p>
                        ))}
                      </div>
                      {isCurrentPlan && (
                        <span className="bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-300 text-xs font-medium px-2 py-1 rounded">
                          Current Plan
                        </span>
                      )}
                    </div>
                    {!isCurrentPlan && (
                      <button
                        onClick={() => {
                          // For Enterprise plan, open email client
                          if (plan.name === 'Enterprise') {
                            window.location.href = 'mailto:<EMAIL>?subject=Enterprise%20Plan%20Inquiry&body=I%20am%20interested%20in%20the%20Enterprise%20plan.%20Please%20provide%20more%20information.';
                            return;
                          }

                          // Open subscription modal
                          setSelectedPlan(plan);
                          setIsAnnual(false); // Default to monthly
                          setShowSubscriptionModal(true);
                        }}
                        disabled={changingPlan || loading}
                        className="mt-3 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
                      >
                        {changingPlan ? (
                          <>
                            <Loader className="h-3 w-3 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          plan.name === 'Enterprise' ? 'Contact Us' : (plan.monthlyFee < billingInfo.currentPlan.monthlyFee ? 'Downgrade' : 'Upgrade')
                        )}
                      </button>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
        <div>
          <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">AI Response Usage</h3>
          <div className="bg-white dark:bg-gray-800 p-4 border border-gray-200 dark:border-gray-700 rounded-lg mb-6">
            <div className="mb-2 flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Monthly usage ({billingInfo.aiResponsesUsed.toLocaleString()} / {billingInfo.aiResponsesLimit.toLocaleString()})
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">{calculateUsagePercentage()}%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
              <div
                className="bg-blue-600 dark:bg-blue-500 h-2.5 rounded-full"
                style={{ width: `${calculateUsagePercentage()}%` }}
              ></div>
            </div>
            <p className="mt-3 text-sm text-gray-600 dark:text-gray-400">
              Additional responses will be charged at €0.01 each. Your plan resets on {formatDate(billingInfo.renewalDate)}.
            </p>
            <div className="mt-3 flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Usage History</span>
              <button
                onClick={() => alert('Usage history details coming soon!')}
                className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium"
              >
                View Details
              </button>
            </div>
          </div>

          <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">Payment Method</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">Your subscription is automatically charged to this payment method.</p>
          <div className="bg-white dark:bg-gray-800 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded">
                  <CreditCard className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                </div>
                <div>
                  <p className="font-medium dark:text-white">
                    {billingInfo.paymentMethod.type.charAt(0).toUpperCase() + billingInfo.paymentMethod.type.slice(1)} ending in {billingInfo.paymentMethod.last4}
                  </p>
                  <p className="text-gray-500 dark:text-gray-400 text-sm">
                    Expires {billingInfo.paymentMethod.expiryMonth}/{billingInfo.paymentMethod.expiryYear}
                  </p>
                </div>
              </div>
              <button
                onClick={async () => {
                  try {
                    // Check if Stripe is configured
                    if (!isStripeConfigured()) {
                      alert('Stripe is not configured. Please add your Stripe publishable key to the .env file and restart the application.');
                      return;
                    }

                    setUpdatingPayment(true);

                    // Check if Stripe is available
                    const stripe = await stripePromise;
                    if (!stripe) {
                      throw new Error('Stripe is not available. Please check your configuration.');
                    }

                    // Create a portal session to update payment method
                    const response = await billingService.createCustomerPortalSession(window.location.origin + '/dashboard/settings');
                    window.location.href = response.url;
                  } catch (error) {
                    console.error('Error updating payment method:', error);
                    alert('Failed to update payment method: ' + (error instanceof Error ? error.message : 'Unknown error'));
                    setUpdatingPayment(false);
                  }
                }}
                disabled={updatingPayment || loading}
                className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
              >
                {updatingPayment ? (
                  <>
                    <Loader className="h-3 w-3 animate-spin" />
                    Processing...
                  </>
                ) : 'Update'}
              </button>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">Billing History</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
            View and download your past invoices. For real Stripe invoices, you can download a PDF or view the invoice online.
            {!isStripeConfigured() && (
              <span className="text-yellow-600 dark:text-yellow-400 ml-1">
                Note: Stripe is not configured. Invoice downloads will be unavailable until Stripe is set up.
              </span>
            )}
          </p>
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden mb-6">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Amount
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Invoice
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {invoices.length === 0 ? (
                  <tr>
                    <td colSpan={4} className="px-6 py-8 text-center text-sm text-gray-500 dark:text-gray-400">
                      No invoices found
                    </td>
                  </tr>
                ) : (
                  invoices.map((invoice) => (
                    <tr key={invoice.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
                        {formatDate(invoice.date)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
                        €{invoice.amount.toFixed(2)}
                        <span className="text-xs text-gray-500 dark:text-gray-400 block">(Includes 21% {t('pricing.tax.name')})</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${invoice.status === 'paid'
                          ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300'
                          : invoice.status === 'pending'
                            ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-300'
                            : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300'}`}
                        >
                          {invoice.status === 'paid' ? 'Paid' : invoice.status === 'pending' ? 'Pending' : 'Failed'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                        <button
                          onClick={async () => {
                            try {
                              // Set the downloading state for this invoice
                              setDownloadingInvoice(invoice.id);

                              // Check if we already have the URLs directly in the invoice object
                              if (invoice.hosted_invoice_url) {
                                // Open the hosted invoice URL in a new tab (preferred)
                                window.open(invoice.hosted_invoice_url, '_blank');
                                setDownloadingInvoice(null);
                                return;
                              } else if (invoice.invoice_pdf) {
                                // Open the PDF URL in a new tab as fallback
                                window.open(invoice.invoice_pdf, '_blank');
                                setDownloadingInvoice(null);
                                return;
                              }

                              // If we don't have the URLs, call the billing service to download the invoice
                              const response = await billingService.downloadInvoice(invoice.id);

                              // Handle the response
                              if (response.message) {
                                // This is a mock invoice or there's a message to display
                                alert(response.message);
                                return;
                              }

                              // For real Stripe invoices with URLs
                              if (response.hosted_invoice_url) {
                                // Open the hosted invoice URL in a new tab (preferred)
                                window.open(response.hosted_invoice_url, '_blank');
                              } else if (response.invoice_pdf) {
                                // Open the PDF URL in a new tab as fallback
                                window.open(response.invoice_pdf, '_blank');
                              } else {
                                // No URLs available
                                alert('No invoice download URL available. Please contact support.');
                              }
                            } catch (error) {
                              console.error('Error downloading invoice:', error);
                              alert('Failed to download invoice: ' + (error instanceof Error ? error.message : 'Unknown error'));
                            } finally {
                              // Clear the downloading state
                              setDownloadingInvoice(null);
                            }
                          }}
                          disabled={downloadingInvoice === invoice.id}
                          className="flex items-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {downloadingInvoice === invoice.id ? (
                            <>
                              <Loader className="h-3 w-3 animate-spin mr-1" />
                              Processing...
                            </>
                          ) : (
                            <>
                              Download
                              <Download className="h-3 w-3 ml-1" />
                            </>
                          )}
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Subscription Modal */}
      {selectedPlan && (
        <SubscriptionModal
          isOpen={showSubscriptionModal}
          onClose={() => setShowSubscriptionModal(false)}
          currentPlan={billingInfo.currentPlan}
          selectedPlan={selectedPlan}
          billingInfo={billingInfo}
          isAnnual={isAnnual}
          onSuccess={(updatedBillingInfo) => {
            setBillingInfo(updatedBillingInfo);
            setShowSubscriptionModal(false);
            setSuccess('Your subscription has been updated successfully!');
          }}
        />
      )}

      {/* Manage Subscription Modal */}
      <ManageSubscriptionModal
        isOpen={showManageModal}
        onClose={() => setShowManageModal(false)}
      />
    </div>
  );
};
