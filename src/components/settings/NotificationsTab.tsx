import React, { useState } from 'react';
import { Mail, Smartphone, RefreshCw, Save } from 'lucide-react';
import { addToast } from '../../components/ui';

interface NotificationSettings {
  emailAppointmentConfirmation: boolean;
  emailAppointmentReminder: boolean;
  emailCancellation: boolean;
  emailMarketing: boolean;
  smsAppointmentConfirmation: boolean;
  smsAppointmentReminder: boolean;
  smsCancellation: boolean;
  smsMarketing: boolean;
}

export const NotificationsTab: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    emailAppointmentConfirmation: true,
    emailAppointmentReminder: true,
    emailCancellation: true,
    emailMarketing: false,
    smsAppointmentConfirmation: true,
    smsAppointmentReminder: true,
    smsCancellation: true,
    smsMarketing: false
  });

  const handleNotificationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setNotificationSettings(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleSaveNotifications = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsSubmitting(true);
      // In a real app, this would call an API
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
      addToast('Notification settings saved successfully', 'success');
    } catch (error) {
      console.error('Error saving notification settings:', error);
      addToast('Failed to save notification settings', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Notification Settings</h2>

      <form onSubmit={handleSaveNotifications}>
        <div className="space-y-6">
          {/* Email Notifications */}
          <div>
            <h3 className="flex items-center gap-2 text-md font-medium text-gray-700 dark:text-gray-300 mb-3">
              <Mail className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              Email Notifications
            </h3>
            <div className="space-y-3 ml-6">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="emailAppointmentConfirmation"
                  name="emailAppointmentConfirmation"
                  checked={notificationSettings.emailAppointmentConfirmation}
                  onChange={handleNotificationChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
                />
                <label htmlFor="emailAppointmentConfirmation" className="text-sm text-gray-700 dark:text-gray-300">
                  Appointment Confirmations
                </label>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="emailAppointmentReminder"
                  name="emailAppointmentReminder"
                  checked={notificationSettings.emailAppointmentReminder}
                  onChange={handleNotificationChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
                />
                <label htmlFor="emailAppointmentReminder" className="text-sm text-gray-700 dark:text-gray-300">
                  Appointment Reminders
                </label>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="emailCancellation"
                  name="emailCancellation"
                  checked={notificationSettings.emailCancellation}
                  onChange={handleNotificationChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
                />
                <label htmlFor="emailCancellation" className="text-sm text-gray-700 dark:text-gray-300">
                  Cancellations and Reschedules
                </label>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="emailMarketing"
                  name="emailMarketing"
                  checked={notificationSettings.emailMarketing}
                  onChange={handleNotificationChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
                />
                <label htmlFor="emailMarketing" className="text-sm text-gray-700 dark:text-gray-300">
                  Marketing Updates and Promotions
                </label>
              </div>
            </div>
          </div>

          {/* SMS Notifications */}
          <div>
            <h3 className="flex items-center gap-2 text-md font-medium text-gray-700 dark:text-gray-300 mb-3">
              <Smartphone className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              SMS Notifications
            </h3>
            <div className="space-y-3 ml-6">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="smsAppointmentConfirmation"
                  name="smsAppointmentConfirmation"
                  checked={notificationSettings.smsAppointmentConfirmation}
                  onChange={handleNotificationChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
                />
                <label htmlFor="smsAppointmentConfirmation" className="text-sm text-gray-700 dark:text-gray-300">
                  Appointment Confirmations
                </label>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="smsAppointmentReminder"
                  name="smsAppointmentReminder"
                  checked={notificationSettings.smsAppointmentReminder}
                  onChange={handleNotificationChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
                />
                <label htmlFor="smsAppointmentReminder" className="text-sm text-gray-700 dark:text-gray-300">
                  Appointment Reminders
                </label>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="smsCancellation"
                  name="smsCancellation"
                  checked={notificationSettings.smsCancellation}
                  onChange={handleNotificationChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
                />
                <label htmlFor="smsCancellation" className="text-sm text-gray-700 dark:text-gray-300">
                  Cancellations and Reschedules
                </label>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="smsMarketing"
                  name="smsMarketing"
                  checked={notificationSettings.smsMarketing}
                  onChange={handleNotificationChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
                />
                <label htmlFor="smsMarketing" className="text-sm text-gray-700 dark:text-gray-300">
                  Marketing Updates and Promotions
                </label>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="border-t dark:border-gray-700 pt-4 flex justify-end">
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-800 flex items-center gap-2 disabled:opacity-70 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};
