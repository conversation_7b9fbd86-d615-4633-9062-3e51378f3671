import React, { useState } from 'react';
import { Plus, Edit, Trash2, Save, X, AlertTriangle } from 'lucide-react';
import { serviceService, ServiceType } from '../../services/serviceService';
import { addToast, ConfirmationDialog } from '../ui';

interface ServiceTypesManagerProps {
  serviceTypes: ServiceType[];
  onServiceTypesChange: (serviceTypes: ServiceType[]) => void;
}

export const ServiceTypesManager: React.FC<ServiceTypesManagerProps> = ({
  serviceTypes,
  onServiceTypesChange
}) => {
  const [isCreating, setIsCreating] = useState(false);
  const [editingTypeId, setEditingTypeId] = useState<string | null>(null);
  const [newTypeName, setNewTypeName] = useState('');
  const [editTypeName, setEditTypeName] = useState('');
  const [loading, setLoading] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [typeToDelete, setTypeToDelete] = useState<string | null>(null);

  const handleCreateServiceType = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newTypeName.trim()) {
      addToast('Please enter a service type name', 'error');
      return;
    }

    setLoading(true);

    try {
      const newType = await serviceService.createServiceType(newTypeName);

      // Update the service types list
      onServiceTypesChange([...serviceTypes, newType]);

      // Reset form
      setNewTypeName('');
      setIsCreating(false);

      // Show success message
      addToast('Service type created successfully', 'success');
    } catch (error) {
      console.error('Error creating service type:', error);

      // Show error message
      const errorMessage = error instanceof Error ? error.message : 'Failed to create service type';
      addToast(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateServiceType = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!editingTypeId || !editTypeName.trim()) {
      addToast('Please enter a service type name', 'error');
      return;
    }

    setLoading(true);

    try {
      const updatedType = await serviceService.updateServiceType(editingTypeId, editTypeName);

      // Update the service types list
      onServiceTypesChange(serviceTypes.map(type =>
        type.id === editingTypeId ? updatedType : type
      ));

      // Reset form
      setEditTypeName('');
      setEditingTypeId(null);

      // Show success message
      addToast('Service type updated successfully', 'success');
    } catch (error) {
      console.error('Error updating service type:', error);

      // Show error message
      const errorMessage = error instanceof Error ? error.message : 'Failed to update service type';
      addToast(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const confirmDeleteServiceType = (id: string) => {
    setTypeToDelete(id);
    setShowDeleteConfirmation(true);
  };

  const handleDeleteServiceType = async () => {
    if (!typeToDelete) return;

    setLoading(true);
    setShowDeleteConfirmation(false);

    try {
      const result = await serviceService.deleteServiceType(typeToDelete);

      // Update the service types list
      onServiceTypesChange(serviceTypes.filter(type => type.id !== typeToDelete));

      // Show success message
      addToast(result.message || 'Service type deleted successfully', 'success');
    } catch (error) {
      console.error('Error deleting service type:', error);

      // Show error message
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete service type';
      addToast(errorMessage, 'error');
    } finally {
      setLoading(false);
      setTypeToDelete(null);
    }
  };

  const startEditing = (type: ServiceType) => {
    setEditingTypeId(type.id);
    setEditTypeName(type.name);
  };

  const cancelEditing = () => {
    setEditingTypeId(null);
    setEditTypeName('');
  };

  const cancelCreating = () => {
    setIsCreating(false);
    setNewTypeName('');
  };

  return (
    <div className="mt-8 space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-md font-semibold">Service Types</h3>
        {!isCreating && (
          <button
            onClick={() => setIsCreating(true)}
            className="flex items-center gap-2 px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
            disabled={loading}
          >
            <Plus className="h-3 w-3" />
            Add Type
          </button>
        )}
      </div>

      {/* Create Form */}
      {isCreating && (
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <form onSubmit={handleCreateServiceType} className="flex items-center gap-2">
            <input
              type="text"
              value={newTypeName}
              onChange={(e) => setNewTypeName(e.target.value)}
              placeholder="Type name"
              className="flex-1 rounded-md border border-gray-300 dark:border-gray-600 px-3 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
              disabled={loading}
            />
            <button
              type="submit"
              className="p-1 bg-green-600 text-white rounded-lg hover:bg-green-700"
              disabled={loading}
            >
              <Save className="h-4 w-4" />
            </button>
            <button
              type="button"
              onClick={cancelCreating}
              className="p-1 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              disabled={loading}
            >
              <X className="h-4 w-4" />
            </button>
          </form>
        </div>
      )}

      {/* Service Types List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-900">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {serviceTypes.map((type) => (
              <tr key={type.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  {editingTypeId === type.id ? (
                    <form onSubmit={handleUpdateServiceType} className="flex items-center gap-2">
                      <input
                        type="text"
                        value={editTypeName}
                        onChange={(e) => setEditTypeName(e.target.value)}
                        className="flex-1 rounded-md border border-gray-300 dark:border-gray-600 px-3 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                        disabled={loading}
                      />
                      <button
                        type="submit"
                        className="p-1 bg-green-600 text-white rounded-lg hover:bg-green-700"
                        disabled={loading}
                      >
                        <Save className="h-4 w-4" />
                      </button>
                      <button
                        type="button"
                        onClick={cancelEditing}
                        className="p-1 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                        disabled={loading}
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </form>
                  ) : (
                    <span className="text-gray-900 dark:text-white">{type.name}</span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  {editingTypeId !== type.id && (
                    <div className="flex justify-end gap-2">
                      <button
                        onClick={() => startEditing(type)}
                        className="p-1 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                        disabled={loading}
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => confirmDeleteServiceType(type.id)}
                        className="p-1 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        disabled={loading}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </td>
              </tr>
            ))}
            {serviceTypes.length === 0 && (
              <tr>
                <td colSpan={2} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                  No service types found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showDeleteConfirmation}
        title="Delete Service Type"
        message="Are you sure you want to delete this service type? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDeleteServiceType}
        onCancel={() => setShowDeleteConfirmation(false)}
        icon={<AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />}
      />
    </div>
  );
};
