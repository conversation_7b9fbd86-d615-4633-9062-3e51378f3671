import React, { useState, useEffect } from 'react';
import { RefreshCw, Save, RotateCw } from 'lucide-react';
import { UserProfile, userService } from '../../services/userService';
import { FileUpload, FormField, addToast } from '../../components/ui';
import { ProfileCompleteness } from '../../components/profile/ProfileCompleteness';
import { validateBusinessName, validateEmail, validatePhone, validateUrl, validateAddress, validateCurrency } from '../../utils/validation';

interface BusinessProfileTabProps {
  initialProfile: UserProfile;
}

export const BusinessProfileTab: React.FC<BusinessProfileTabProps> = ({ initialProfile }) => {
  const [profileForm, setProfileForm] = useState<UserProfile>(initialProfile);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all required fields
    const businessNameValidation = validateBusinessName(profileForm.businessName || '');
    const emailValidation = validateEmail(profileForm.email || '');

    // Check if any required validations fail
    if (!businessNameValidation.isValid || !emailValidation.isValid) {
      // Show error messages
      if (!businessNameValidation.isValid) {
        addToast(businessNameValidation.message || 'Business name is required', 'error');
      }
      if (!emailValidation.isValid) {
        addToast(emailValidation.message || 'Valid email is required', 'error');
      }
      return;
    }

    // Validate optional fields if they have values
    if (profileForm.phone && !validatePhone(profileForm.phone).isValid) {
      addToast('Please enter a valid phone number', 'error');
      return;
    }

    if (profileForm.website && !validateUrl(profileForm.website).isValid) {
      addToast('Please enter a valid website URL', 'error');
      return;
    }

    if (profileForm.address && !validateAddress(profileForm.address).isValid) {
      addToast('Please enter a valid address', 'error');
      return;
    }

    setIsSubmitting(true);

    try {
      // Log what we're sending to the backend (sanitize logo data)
      const sanitizedProfileForm = { ...profileForm };
      if (typeof sanitizedProfileForm.logo === 'string' && sanitizedProfileForm.logo.startsWith('data:')) {
        sanitizedProfileForm.logo = '<base64_image_data_omitted>';
      }
      // Submit sanitized profile form

      // Update user profile using the user service
      await userService.updateProfile(profileForm);
      addToast('Profile updated successfully', 'success');

      // We don't need to reload the profile data and overwrite the form
      // Trust that the updated values are now live
      // This avoids the issue where the form reverts to old values
    } catch (error) {
      console.error('Error updating profile:', error);
      addToast('Failed to update profile. Please try again.', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to load user profile data
  const loadUserProfile = async () => {
    setIsLoading(true);
    try {
      const userData = await userService.getCurrentUser();

      // Sanitize logo data for logging
      const sanitizedUserData = { ...userData };
      if (sanitizedUserData.logoUrl && sanitizedUserData.logoUrl.startsWith('data:')) {
        sanitizedUserData.logoUrl = '<base64_image_data_omitted>';
      }
      // User profile data loaded

      // Use logo URL if it exists
      let logoFile: string | null = null;
      if (userData.logoUrl) {
        // For demo purposes, we'll just use the URL directly
        logoFile = userData.logoUrl;
      }

      setProfileForm({
        businessName: userData.business_name || '',
        email: userData.email || '',
        phone: userData.phone || '',
        address: userData.address || '',
        website: userData.website || '',
        currency: userData.currency || '',
        logo: logoFile
      });
    } catch (error) {
      console.error('Error loading user profile:', error);
      addToast('Failed to load profile data', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Load user profile data on component mount
  useEffect(() => {
    loadUserProfile();
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-10">
        <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
        <span className="ml-2 text-gray-600 dark:text-gray-300">Loading profile data...</span>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit}>
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-bold text-gray-900 dark:text-white">Business Profile</h2>
          <div className="w-48">
            <ProfileCompleteness profile={profileForm} />
          </div>
        </div>
      </div>

      <div className="space-y-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start">
          <FileUpload
            value={profileForm.logo || null}
            onChange={(file) => setProfileForm(prev => ({ ...prev, logo: file }))}
            className="w-full sm:w-32 h-32"
            maxSizeMB={2}
          />

          <div className="space-y-4 flex-1">
            <FormField
              id="businessName"
              label="Business Name"
              value={profileForm.businessName || ''}
              onChange={handleProfileChange}
              required
              validate={validateBusinessName}
            />

            <FormField
              id="currency"
              label="Currency"
              type="select"
              value={profileForm.currency || ''}
              onChange={handleProfileChange}
              validate={validateCurrency}
              options={[
                { value: '', label: 'Select a currency' },
                { value: 'USD', label: 'USD - US Dollar' },
                { value: 'EUR', label: 'EUR - Euro' },
                { value: 'GBP', label: 'GBP - British Pound' },
                { value: 'CAD', label: 'CAD - Canadian Dollar' },
                { value: 'AUD', label: 'AUD - Australian Dollar' }
              ]}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <FormField
            id="email"
            label="Email"
            type="email"
            value={profileForm.email || ''}
            onChange={handleProfileChange}
            required
            validate={validateEmail}
          />

          <FormField
            id="phone"
            label="Phone"
            type="tel"
            value={profileForm.phone || ''}
            onChange={handleProfileChange}
            placeholder="+****************"
            validate={validatePhone}
          />
        </div>

        <FormField
          id="address"
          label="Address"
          type="textarea"
          value={profileForm.address || ''}
          onChange={handleProfileChange}
          placeholder="123 Business St, City, State, ZIP"
          validate={validateAddress}
        />

        <FormField
          id="website"
          label="Website"
          type="url"
          value={profileForm.website || ''}
          onChange={handleProfileChange}
          placeholder="www.example.com"
          validate={validateUrl}
        />
      </div>

      <div className="border-t pt-4 flex justify-end gap-2">
        <button
          type="button"
          onClick={loadUserProfile}
          className="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 flex items-center gap-2 disabled:opacity-70 disabled:cursor-not-allowed"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <RefreshCw className="h-4 w-4 animate-spin" />
              Loading...
            </>
          ) : (
            <>
              <RotateCw className="h-4 w-4" />
              Refresh
            </>
          )}
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2 disabled:opacity-70 disabled:cursor-not-allowed"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <RefreshCw className="h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              Save Changes
            </>
          )}
        </button>
      </div>
    </form>
  );
};
