import React, { useState, useEffect } from 'react';
import { CalendarClock, RefreshCw, Save } from 'lucide-react';
import { addToast } from '../../components/ui';
import { calendarSettingsService, CalendarSettings, WorkingHours } from '../../services/calendarSettingsService';



export const CalendarSettingsTab: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [calendarSettings, setCalendarSettings] = useState<CalendarSettings>({
    workingHours: {
      monday: { start: '09:00', end: '17:00', enabled: true },
      tuesday: { start: '09:00', end: '17:00', enabled: true },
      wednesday: { start: '09:00', end: '17:00', enabled: true },
      thursday: { start: '09:00', end: '17:00', enabled: true },
      friday: { start: '09:00', end: '17:00', enabled: true },
      saturday: { start: '10:00', end: '15:00', enabled: false },
      sunday: { start: '10:00', end: '15:00', enabled: false }
    },
    defaultDuration: '30min',
    bufferTime: '15min',
    allowWeekends: false
  });

  const timeOptions = [
    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
    '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30',
    '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00'
  ];

  const handleWorkingHoursChange = (day: string, field: 'start' | 'end' | 'enabled', value: string | boolean) => {
    setCalendarSettings(prev => ({
      ...prev,
      workingHours: {
        ...prev.workingHours,
        [day]: {
          ...prev.workingHours[day],
          [field]: value
        }
      }
    }));
  };

  // Load calendar settings on component mount
  useEffect(() => {
    const loadCalendarSettings = async () => {
      setIsSubmitting(true);
      try {
        const settings = await calendarSettingsService.getSettings();
        setCalendarSettings(settings);
      } catch (error) {
        console.error('Error loading calendar settings:', error);
        // Don't show error toast, just use default settings
      } finally {
        setIsSubmitting(false);
      }
    };

    loadCalendarSettings();
  }, []);

  const handleSaveCalendarSettings = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsSubmitting(true);
      await calendarSettingsService.updateSettings(calendarSettings);
      addToast('Calendar settings saved successfully', 'success');
    } catch (error) {
      console.error('Error saving calendar settings:', error);
      addToast('Failed to save calendar settings', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Calendar Settings</h2>

      <form onSubmit={handleSaveCalendarSettings}>
        <div className="space-y-6">
          <div>
            <h3 className="flex items-center gap-2 text-md font-medium text-gray-700 dark:text-gray-300 mb-3">
              <CalendarClock className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              Working Hours
            </h3>

            <div className="space-y-4 ml-6">
              {Object.entries(calendarSettings.workingHours).map(([day, hours]) => (
                <div key={day} className="flex items-center gap-4">
                  <div className="w-24">
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id={`${day}-enabled`}
                        checked={hours.enabled}
                        onChange={(e) => handleWorkingHoursChange(day, 'enabled', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
                      />
                      <label htmlFor={`${day}-enabled`} className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {day.charAt(0).toUpperCase() + day.slice(1)}
                      </label>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <select
                      value={hours.start}
                      onChange={(e) => handleWorkingHoursChange(day, 'start', e.target.value)}
                      disabled={!hours.enabled}
                      className="text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500 disabled:bg-gray-100 dark:disabled:bg-gray-800 disabled:text-gray-500 dark:disabled:text-gray-400"
                    >
                      {timeOptions.map(time => (
                        <option key={`${day}-start-${time}`} value={time}>{time}</option>
                      ))}
                    </select>
                    <span className="text-gray-500 dark:text-gray-400">to</span>
                    <select
                      value={hours.end}
                      onChange={(e) => handleWorkingHoursChange(day, 'end', e.target.value)}
                      disabled={!hours.enabled}
                      className="text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500 disabled:bg-gray-100 dark:disabled:bg-gray-800 disabled:text-gray-500 dark:disabled:text-gray-400"
                    >
                      {timeOptions.map(time => (
                        <option key={`${day}-end-${time}`} value={time}>{time}</option>
                      ))}
                    </select>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h3 className="flex items-center gap-2 text-md font-medium text-gray-700 dark:text-gray-300 mb-3">
              <CalendarClock className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              Appointment Settings
            </h3>

            <div className="space-y-4 ml-6">
              <div className="flex flex-col gap-2">
                <label htmlFor="defaultDuration" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Default Appointment Duration
                </label>
                <select
                  id="defaultDuration"
                  value={calendarSettings.defaultDuration}
                  onChange={(e) => setCalendarSettings(prev => ({ ...prev, defaultDuration: e.target.value }))}
                  className="w-full sm:w-auto text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="15min">15 minutes</option>
                  <option value="30min">30 minutes</option>
                  <option value="45min">45 minutes</option>
                  <option value="60min">1 hour</option>
                  <option value="90min">1.5 hours</option>
                  <option value="120min">2 hours</option>
                </select>
              </div>

              <div className="flex flex-col gap-2">
                <label htmlFor="bufferTime" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Buffer Time Between Appointments
                </label>
                <select
                  id="bufferTime"
                  value={calendarSettings.bufferTime}
                  onChange={(e) => setCalendarSettings(prev => ({ ...prev, bufferTime: e.target.value }))}
                  className="w-full sm:w-auto text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="0min">No buffer</option>
                  <option value="5min">5 minutes</option>
                  <option value="10min">10 minutes</option>
                  <option value="15min">15 minutes</option>
                  <option value="30min">30 minutes</option>
                </select>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="allowWeekends"
                  checked={calendarSettings.allowWeekends}
                  onChange={(e) => setCalendarSettings(prev => ({ ...prev, allowWeekends: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
                />
                <label htmlFor="allowWeekends" className="text-sm text-gray-700 dark:text-gray-300">
                  Allow weekend appointments
                </label>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="border-t dark:border-gray-700 pt-4 flex justify-end">
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-800 flex items-center gap-2 disabled:opacity-70 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};
