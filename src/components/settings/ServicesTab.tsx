import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Save, X, Clock, DollarSign, AlertTriangle } from 'lucide-react';
import { serviceService, Service } from '../../services/serviceService';
import { addToast, ConfirmationDialog } from '../ui';
import { ServiceTypesManager } from './ServiceTypesManager';



export const ServicesTab: React.FC = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<Service>>({
    name: '',
    description: '',
    price: 0,
    duration_minutes: 30,
    active: true,
    type_id: ''
  });
  const [serviceTypes, setServiceTypes] = useState<{ id: string; name: string }[]>([]);

  // Load services and service types on component mount
  useEffect(() => {
    // Load service types first, then services
    const initializeData = async () => {
      await loadServiceTypes();
      await loadServices();
    };

    initializeData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const loadServices = async () => {
    setLoading(true);
    try {
      // Get services from API
      const response = await serviceService.getServices({ limit: 100 });

      if (response && response.items) {
        setServices(response.items);
      } else {
        // If API returns empty array, show empty state
        setServices([]);
      }
    } catch (error) {
      console.error('Error loading services:', error);
      // Show error state if API fails
      addToast('Failed to load services. Please check your connection and try again.', 'error');
      setServices([]);
    } finally {
      setLoading(false);
    }
  };

  const loadServiceTypes = async () => {
    try {
      // Get service types from API
      const types = await serviceService.getServiceTypes();

      if (types && Array.isArray(types) && types.length > 0) {
        setServiceTypes(types);

        // Set default type_id if available
        if (!formData.type_id && types.length > 0) {
          setFormData(prev => ({ ...prev, type_id: types[0].id }));
        }
      } else {
        // If API returns empty array, create a new service type
        try {
          // Create a default service type
          const newType = await serviceService.createServiceType('General');
          setServiceTypes([newType]);

          // Set default type_id
          if (!formData.type_id) {
            setFormData(prev => ({ ...prev, type_id: newType.id }));
          }
        } catch (createError) {
          console.error('Error creating default service type:', createError);
          // Show error message
          addToast('Failed to create default service type. Please try again.', 'error');
          setServiceTypes([]);
        }
      }
    } catch (error) {
      console.error('Error loading service types:', error);
      addToast('Failed to load service types. Please try again later.', 'error');
      setServiceTypes([]);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
      // Handle empty string case to avoid NaN
      const numValue = value === '' ? 0 : parseFloat(value);
      setFormData(prev => ({ ...prev, [name]: numValue }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleCreateService = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form data
    if (!formData.name || !formData.type_id || formData.price === undefined || !formData.duration_minutes) {
      addToast('Please fill in all required fields', 'error');
      return;
    }

    // Show loading state
    setLoading(true);

    try {
      // Create service via API first
      const apiService = await serviceService.createService(formData as Omit<Service, 'id' | 'user_id'>);

      // If successful, update the UI with the new service from the API
      setServices(prev => [...prev, apiService]);

      // Reset form and close create mode
      setIsCreating(false);
      setFormData({
        name: '',
        description: '',
        price: 0,
        duration_minutes: 30,
        active: true,
        type_id: serviceTypes.length > 0 ? serviceTypes[0].id : ''
      });

      // Show success message
      addToast('Service created successfully', 'success');
    } catch (error) {
      console.error('Error creating service:', error);

      // Show error message
      const errorMessage = error instanceof Error ? error.message : 'Failed to create service. Please try again.';
      addToast(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateService = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!editingService) return;

    // Validate form data
    if (!formData.name || !formData.type_id || formData.price === undefined || !formData.duration_minutes) {
      addToast('Please fill in all required fields', 'error');
      return;
    }

    // Show loading state
    setLoading(true);

    try {
      // Update service via API first
      const updatedService = await serviceService.updateService(
        editingService.id,
        formData as Partial<Omit<Service, 'id' | 'user_id'>>
      );

      // If successful, update the UI with the updated service from the API
      setServices(prev => prev.map(service =>
        service.id === editingService.id ? updatedService : service
      ));

      // Close edit mode
      setEditingService(null);

      // Show success message
      addToast('Service updated successfully', 'success');
    } catch (error) {
      console.error('Error updating service:', error);

      // Show error message
      const errorMessage = error instanceof Error ? error.message : 'Failed to update service. Please try again.';
      addToast(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const confirmDeleteService = (id: string) => {
    setServiceToDelete(id);
    setShowDeleteConfirmation(true);
  };

  const handleDeleteService = async () => {
    if (!serviceToDelete) return;

    setShowDeleteConfirmation(false);

    try {
      // Try to delete service via API first
      const result = await serviceService.deleteService(serviceToDelete);

      // If successful, update the UI
      const updatedServices = services.filter(service => service.id !== serviceToDelete);
      setServices(updatedServices);

      // Show success message
      addToast(result.message || 'Service deleted successfully', 'success');
    } catch (error) {
      console.error('Error deleting service:', error);

      // Show error message
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete service. It may be referenced by appointments.';
      addToast(errorMessage, 'error');

      // Don't update the UI if there was an error
    } finally {
      setServiceToDelete(null);
    }
  };

  const startEditing = (service: Service) => {
    setEditingService(service);
    setFormData({
      name: service.name,
      description: service.description,
      price: service.price,
      duration_minutes: service.duration_minutes,
      active: service.active,
      type_id: service.type_id
    });
  };

  const cancelEditing = () => {
    setEditingService(null);
    setFormData({
      name: '',
      description: '',
      price: 0,
      duration_minutes: 30,
      active: true,
      type_id: serviceTypes.length > 0 ? serviceTypes[0].id : ''
    });
  };

  const startCreating = () => {
    setIsCreating(true);
    setFormData({
      name: '',
      description: '',
      price: 0,
      duration_minutes: 30,
      active: true,
      type_id: serviceTypes.length > 0 ? serviceTypes[0].id : ''
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-bold text-gray-900 dark:text-white">Services</h2>
        {!isCreating && !editingService && (
          <button
            onClick={startCreating}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Plus className="h-4 w-4" />
            Add Service
          </button>
        )}
      </div>

      {/* Service Types Manager */}
      <ServiceTypesManager
        serviceTypes={serviceTypes}
        onServiceTypesChange={setServiceTypes}
      />

      {/* Service Form (Create/Edit) */}
      {(isCreating || editingService) && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-md font-semibold mb-4">
            {isCreating ? 'Add New Service' : 'Edit Service'}
          </h3>

          <form onSubmit={isCreating ? handleCreateService : handleUpdateService} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Service Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Service Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              {/* Service Type */}
              <div>
                <label htmlFor="type_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Service Type <span className="text-red-500">*</span>
                </label>
                <select
                  id="type_id"
                  name="type_id"
                  value={formData.type_id}
                  onChange={handleInputChange}
                  required
                  className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  {/* Display available service types */}
                  {serviceTypes.map(type => (
                    <option key={type.id} value={type.id}>{type.name}</option>
                  ))}
                  {serviceTypes.length === 0 && (
                    <option value="" disabled>No service types available</option>
                  )}
                </select>
              </div>

              {/* Price */}
              <div>
                <label htmlFor="price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Price <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type="number"
                    id="price"
                    name="price"
                    value={formData.price}
                    onChange={handleInputChange}
                    min="0"
                    step="0.01"
                    required
                    className="w-full rounded-md border border-gray-300 dark:border-gray-600 pl-10 pr-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              {/* Duration */}
              <div>
                <label htmlFor="duration_minutes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Duration (minutes) <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Clock className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type="number"
                    id="duration_minutes"
                    name="duration_minutes"
                    value={formData.duration_minutes}
                    onChange={handleInputChange}
                    min="1"
                    required
                    className="w-full rounded-md border border-gray-300 dark:border-gray-600 pl-10 pr-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description || ''}
                onChange={handleInputChange}
                rows={3}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            {/* Active Status */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="active"
                name="active"
                checked={formData.active}
                onChange={(e) => setFormData(prev => ({ ...prev, active: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="active" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Active (available for booking)
              </label>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end gap-2 pt-2">
              <button
                type="button"
                onClick={isCreating ? () => setIsCreating(false) : cancelEditing}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {isCreating ? 'Create Service' : 'Update Service'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Services List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        {loading ? (
          <div className="p-6 text-center text-gray-500 dark:text-gray-400">Loading services...</div>
        ) : services.length === 0 ? (
          <div className="p-6 text-center text-gray-500 dark:text-gray-400">
            No services found. Add your first service to get started.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Name & Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Duration
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Price
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {services.map((service) => (
                  <tr key={service.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">{service.name}</div>
                      {service.description && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-xs">
                          {service.description}
                        </div>
                      )}
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Type: {serviceTypes.find(type => type.id === service.type_id)?.name || service.type_id}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">{service.duration_minutes} min</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">${service.price.toFixed(2)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        service.active
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                      }`}>
                        {service.active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end gap-2">
                        <button
                          onClick={() => startEditing(service)}
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => confirmDeleteService(service.id)}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showDeleteConfirmation}
        title="Delete Service"
        message="Are you sure you want to delete this service? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDeleteService}
        onCancel={() => setShowDeleteConfirmation(false)}
        icon={<AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />}
      />
    </div>
  );
};
