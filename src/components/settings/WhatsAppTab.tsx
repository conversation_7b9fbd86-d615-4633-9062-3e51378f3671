import React, { useState, useEffect } from 'react';
import { RefreshCw, X, Send } from 'lucide-react';
import { whatsappService, WhatsAppStatus } from '../../services/whatsappService';

export const WhatsAppTab: React.FC = () => {
  const [status, setStatus] = useState<WhatsAppStatus>({ connected: false });
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [connecting, setConnecting] = useState(false);
  const [loading, setLoading] = useState(false);
  const [testPhone, setTestPhone] = useState('');
  const [testMessage, setTestMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);

  // Load WhatsApp status on component mount
  useEffect(() => {
    loadWhatsAppStatus();
  }, []);

  // Function to load WhatsApp status
  const loadWhatsAppStatus = async () => {
    try {
      setLoading(true);
      const status = await whatsappService.getStatus();
      setStatus(status);
    } catch (error) {
      console.error('Error loading WhatsApp status:', error);
      alert('Failed to load WhatsApp status');
    } finally {
      setLoading(false);
    }
  };

  // Function to connect WhatsApp
  const connectWhatsApp = async () => {
    setLoading(true);
    try {
      const result = await whatsappService.connect();

      if (result.success && result.qrcode) {
        setQrCode(result.qrcode);

        // Start polling for status to detect when the QR code is scanned
        const intervalId = setInterval(async () => {
          const status = await whatsappService.getStatus();
          if (status.connected) {
            clearInterval(intervalId);
            setQrCode(null);
            setConnecting(false);
            setStatus(status);
            alert('WhatsApp connected successfully');
          } else if (qrCode && status.status === 'connecting') {
            // If we have a QR code and the status is connecting, update the state
            setConnecting(true);
          }
        }, 3000);

        // Clear interval after 2 minutes (QR code expires)
        setTimeout(() => {
          clearInterval(intervalId);
          if (!status.connected) {
            alert('QR code expired. Please try again.');
            setQrCode(null);
            setConnecting(false);
          }
        }, 120000);
      } else {
        alert(result.message || 'Failed to get QR code');
      }
    } catch (error) {
      console.error('Error connecting to WhatsApp:', error);
      alert('Failed to connect to WhatsApp');
      setConnecting(false);
    } finally {
      setLoading(false);
    }
  };

  // Function to disconnect WhatsApp
  const disconnectWhatsApp = async () => {
    setLoading(true);
    try {
      const result = await whatsappService.disconnect();

      if (result.success) {
        setStatus({ connected: false });
        alert('WhatsApp disconnected successfully');
      } else {
        alert(result.message || 'Failed to disconnect WhatsApp');
      }
    } catch (error) {
      console.error('Error disconnecting WhatsApp:', error);
      alert('Failed to disconnect WhatsApp');
    } finally {
      setLoading(false);
    }
  };

  // Function to send a test message
  const sendTestMessage = async () => {
    if (!testPhone || !testMessage) {
      alert('Please enter both phone number and message');
      return;
    }

    setSendingMessage(true);
    try {
      const result = await whatsappService.sendMessage({
        phone: testPhone,
        message: testMessage
      });

      if (result.success) {
        alert('Message sent successfully');
        setTestMessage('');
        setTestPhone('');
      } else {
        alert(result.message || 'Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      alert('Failed to send message');
    } finally {
      setSendingMessage(false);
    }
  };

  return (
    <div>
      <h2 className="text-lg font-bold text-gray-900 mb-4">WhatsApp Integration</h2>

      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="p-5 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <img
                src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/WhatsApp.svg/512px-WhatsApp.svg.png"
                alt="WhatsApp"
                className="w-10 h-10"
              />
              <div>
                <h3 className="font-semibold text-lg">WhatsApp</h3>
                <p className="text-gray-600 text-sm">Connect your WhatsApp account to send messages</p>
              </div>
            </div>
            <div className="flex items-center">
              {status.connected ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Connected
                </span>
              ) : (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  Not Connected
                </span>
              )}
            </div>
          </div>
        </div>

        <div className="p-5">
          {status.connected ? (
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                  <p className="text-sm text-gray-600 mb-1">Connected Account</p>
                  <p className="font-medium">{status.phone || 'Unknown'}</p>
                  <p className="text-sm text-gray-600 mt-1">{status.name || 'Unknown'}</p>
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <button
                    className="px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm"
                    onClick={loadWhatsAppStatus}
                    disabled={loading}
                  >
                    <RefreshCw className="w-4 h-4 inline mr-1" /> {loading ? 'Refreshing...' : 'Refresh Status'}
                  </button>
                  <button
                    className="px-3 py-2 border border-red-300 text-red-700 rounded-md hover:bg-red-50 text-sm"
                    onClick={disconnectWhatsApp}
                    disabled={loading}
                  >
                    <X className="w-4 h-4 inline mr-1" /> {loading ? 'Disconnecting...' : 'Disconnect'}
                  </button>
                </div>
              </div>

              <div className="mt-6 border-t pt-6">
                <h4 className="font-medium text-gray-900 mb-3">Send Test Message</h4>
                <div className="space-y-3">
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number (with country code)
                    </label>
                    <input
                      type="text"
                      id="phone"
                      value={testPhone}
                      onChange={(e) => setTestPhone(e.target.value)}
                      placeholder="+1234567890"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                      Message
                    </label>
                    <textarea
                      id="message"
                      value={testMessage}
                      onChange={(e) => setTestMessage(e.target.value)}
                      placeholder="Enter your message here..."
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <button
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      onClick={sendTestMessage}
                      disabled={sendingMessage}
                    >
                      <Send className="w-4 h-4 inline mr-1" />
                      {sendingMessage ? 'Sending...' : 'Send Message'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-6">
              {connecting ? (
                <div className="text-center">
                  <div className="flex flex-col items-center justify-center py-6">
                    <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-green-500 mb-4"></div>
                    <p className="text-gray-600 mb-2">Connecting to WhatsApp...</p>
                    <p className="text-amber-600 text-sm">This may take a few minutes. Please wait.</p>
                  </div>
                </div>
              ) : qrCode ? (
                <div className="text-center">
                  <p className="text-gray-600 mb-4">Scan this QR code with your WhatsApp to connect</p>
                  <p className="text-amber-600 text-sm mb-4">Note: After scanning, it may take a few minutes for the connection to be fully established.</p>
                  <img
                    src={qrCode.startsWith('data:image') ? qrCode : `data:image/png;base64,${qrCode}`}
                    alt="WhatsApp QR Code"
                    className="mx-auto w-64 h-64"
                    onError={(e) => {
                      console.error('Error loading QR code image');
                      e.currentTarget.style.display = 'none';
                      // Show a fallback
                      const fallback = document.createElement('div');
                      fallback.className = 'mx-auto w-64 h-64 bg-gray-100 flex items-center justify-center';
                      fallback.innerHTML = '<p class="text-gray-500">QR Code not available</p>';
                      e.currentTarget.parentNode?.appendChild(fallback);
                    }}
                  />
                  <p className="text-sm text-gray-500 mt-2">QR code will expire in 2 minutes</p>
                </div>
              ) : (
                <div className="text-center">
                  <p className="text-gray-600 mb-4">Connect your WhatsApp account to send messages</p>
                  <button
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                    onClick={connectWhatsApp}
                    disabled={loading}
                  >
                    {loading ? 'Connecting...' : 'Connect WhatsApp'}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
