import React, { useState } from 'react';
import { X, CreditCard, FileText, Calendar, Settings, ExternalLink } from 'lucide-react';
import { billingService } from '../../services/billingService';
import { Button } from '../ui';
import { Loader } from 'lucide-react';

interface ManageSubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ManageSubscriptionModal: React.FC<ManageSubscriptionModalProps> = ({
  isOpen,
  onClose
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (!isOpen) return null;

  const handleOpenStripePortal = async () => {
    try {
      setLoading(true);
      setError(null);

      // Create a customer portal session
      const response = await billingService.createCustomerPortalSession(
        window.location.origin + '/dashboard/settings'
      );

      // Redirect to the Stripe Customer Portal
      window.location.href = response.url;
    } catch (error) {
      console.error('Error opening Stripe portal:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Manage Your Subscription
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-4">
          {error && (
            <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded-md">
              {error}
            </div>
          )}

          <p className="text-gray-700 dark:text-gray-300 mb-4">
            You'll be redirected to the Stripe Customer Portal where you can:
          </p>

          <ul className="space-y-3 mb-6">
            <li className="flex items-center gap-3 text-gray-700 dark:text-gray-300">
              <CreditCard className="h-5 w-5 text-blue-500" />
              <span>Update your payment method</span>
            </li>
            <li className="flex items-center gap-3 text-gray-700 dark:text-gray-300">
              <FileText className="h-5 w-5 text-blue-500" />
              <span>View and download invoices</span>
            </li>
            <li className="flex items-center gap-3 text-gray-700 dark:text-gray-300">
              <Calendar className="h-5 w-5 text-blue-500" />
              <span>Change billing cycle (monthly/annual)</span>
            </li>
            <li className="flex items-center gap-3 text-gray-700 dark:text-gray-300">
              <Settings className="h-5 w-5 text-blue-500" />
              <span>Manage subscription settings</span>
            </li>
          </ul>

          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleOpenStripePortal}
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader className="h-4 w-4 mr-2" />
                  Loading...
                </>
              ) : (
                <>
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open Stripe Portal
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
