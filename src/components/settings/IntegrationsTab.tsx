import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { RefreshCw, Send } from 'lucide-react';
// import { useAuthStore } from '../../lib/auth';
import { googleCalendarService } from '../../services/googleCalendarService';
import { whatsappService } from '../../services/whatsappService';
import { addToast } from '../ui';

interface IntegrationStatus {
  google: {
    connected: boolean;
    email?: string;
    lastSynced?: string;
  };
  whatsapp: {
    connected: boolean;
    phone?: string;
    lastSynced?: string;
  };
}

export const IntegrationsTab: React.FC = () => {
  // const { user } = useAuthStore(); // Not needed
  const location = useLocation();
  const [integrationStatus, setIntegrationStatus] = useState<IntegrationStatus>({
    google: { connected: false },
    whatsapp: { connected: false }
  });
  const [whatsappQR, setWhatsappQR] = useState<string | null>(null);
  const [qrLoading, setQrLoading] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [loading, setLoading] = useState<{ google: boolean; whatsapp: boolean }>({ google: false, whatsapp: false });
  const [testPhone, setTestPhone] = useState('');
  const [testMessage, setTestMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);

  const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;

  // Function to connect Google Calendar
  const connectGoogleCalendar = () => {
    const redirectUri = encodeURIComponent('http://localhost:5173/dashboard/settings/google-callback');
    const scope = encodeURIComponent('https://www.googleapis.com/auth/calendar https://www.googleapis.com/auth/userinfo.email');

    // Get the user's email from localStorage if available
    const userEmail = localStorage.getItem('userEmail');

    // Build the auth URL with login_hint if we have the user's email
    // Include prompt=consent to force the consent screen and get a new refresh token
    let authUrl = `https://accounts.google.com/o/oauth2/v2/auth?scope=${scope}&access_type=offline&include_granted_scopes=true&response_type=code&redirect_uri=${redirectUri}&client_id=${clientId}&prompt=consent`;

    if (userEmail) {
      authUrl += `&login_hint=${encodeURIComponent(userEmail)}`;
    }

    // OAuth URL is ready for redirect

    // Redirect to Google OAuth page
    window.location.href = authUrl;
  };

  // Function to connect WhatsApp
  const connectWhatsApp = async () => {
    try {
      setQrLoading(true);
      const response = await whatsappService.connect();

      if (response.success) {
        setQrLoading(false);
        setWhatsappQR(response.qrcode || null);

        // Start polling for status
        const intervalId = setInterval(async () => {
          try {
            const status = await whatsappService.getStatus();

            if (status.connected) {
              clearInterval(intervalId);
              setWhatsappQR(null);
              setConnecting(false);
              setIntegrationStatus(prev => ({
                ...prev,
                whatsapp: {
                  connected: true,
                  phone: status.phone || 'Unknown',
                  lastSynced: new Date().toISOString()
                }
              }));
              addToast('WhatsApp connected successfully', 'success');
            } else if (whatsappQR && status.status === 'connecting') {
              // If we have a QR code and the status is connecting, update the state
              setConnecting(true);
            }
          } catch (error) {
            console.error('Error checking WhatsApp status:', error);
          }
        }, 5000);

        // Set a timeout to clear the interval if it takes too long
        setTimeout(() => {
          clearInterval(intervalId);
          if (!integrationStatus.whatsapp.connected) {
            addToast('QR code expired. Please try again.', 'error');
            setWhatsappQR(null);
            setConnecting(false);
          }
        }, 120000);
      } else {
        setQrLoading(false);
        addToast(response.message || 'Failed to connect to WhatsApp', 'error');
      }
    } catch (error) {
      console.error('Error connecting to WhatsApp:', error);
      addToast('Failed to connect to WhatsApp', 'error');
      setConnecting(false);
      setQrLoading(false);
    }
  };

  // Function to disconnect Google Calendar
  const disconnectGoogleCalendar = async () => {
    try {
      await googleCalendarService.disconnect();
      setIntegrationStatus(prev => ({
        ...prev,
        google: { connected: false }
      }));
      addToast('Google Calendar disconnected successfully', 'success');
    } catch (error) {
      console.error('Error disconnecting Google Calendar:', error);
      addToast('Failed to disconnect Google Calendar', 'error');
    }
  };

  // Function to disconnect WhatsApp
  const disconnectWhatsApp = async () => {
    try {
      await whatsappService.disconnect();
      setIntegrationStatus(prev => ({
        ...prev,
        whatsapp: { connected: false }
      }));
      setWhatsappQR(null);
      addToast('WhatsApp disconnected successfully', 'success');
    } catch (error) {
      console.error('Error disconnecting WhatsApp:', error);
      addToast('Failed to disconnect WhatsApp', 'error');
    }
  };

  // Function to send a test message
  const sendTestMessage = async () => {
    if (!testPhone || !testMessage) {
      addToast('Please enter a phone number and message', 'error');
      return;
    }

    try {
      setSendingMessage(true);
      const response = await whatsappService.sendMessage({ phone: testPhone, message: testMessage });

      if (response.success) {
        addToast('Test message sent successfully', 'success');
        setTestMessage('');
      } else {
        addToast(response.message || 'Failed to send test message', 'error');
      }
    } catch (error) {
      console.error('Error sending test message:', error);
      addToast('Failed to send test message', 'error');
    } finally {
      setSendingMessage(false);
    }
  };

  // Function to refresh Google Calendar token
  const refreshGoogleCalendar = async () => {
    try {
      setLoading(prev => ({ ...prev, google: true }));

      // Refresh the token
      const result = await googleCalendarService.refreshToken();

      // Update the status
      setIntegrationStatus(prev => ({
        ...prev,
        google: {
          connected: result.connected,
          email: result.email,
          lastSynced: result.last_synced
        }
      }));

      addToast('Google Calendar token refreshed successfully', 'success');
    } catch (error) {
      console.error('Error refreshing Google Calendar token:', error);
      addToast('Failed to refresh Google Calendar token', 'error');
    } finally {
      setLoading(prev => ({ ...prev, google: false }));
    }
  };

  // Function to sync with Google Calendar
  const syncGoogleCalendar = async () => {
    try {
      await googleCalendarService.syncAppointments();

      // Refresh the status
      const status = await googleCalendarService.getStatus();
      setIntegrationStatus(prev => ({
        ...prev,
        google: {
          connected: status.connected,
          email: status.email,
          lastSynced: status.last_synced
        }
      }));

      addToast('Google Calendar synced successfully', 'success');
    } catch (error) {
      console.error('Error syncing with Google Calendar:', error);
      addToast('Failed to sync with Google Calendar', 'error');
    }
  };



  // Load integration status on component mount or when location changes
  useEffect(() => {
    // Load integration status on mount or location change
    const loadIntegrationStatus = async () => {
      try {
        // Load Google Calendar status
        const googleStatus = await googleCalendarService.getStatus();
        // Try to get email from localStorage if not in the API response
        const googleEmail = googleStatus.email || localStorage.getItem('googleEmail') || undefined;

        // If connected, validate the token
        let isTokenValid = false;
        if (googleStatus.connected) {
          isTokenValid = await googleCalendarService.validateToken();
          // Token validation completed

          // If token is invalid but status shows connected, show a warning
          if (!isTokenValid) {
            addToast('Your Google Calendar connection has expired. Please reconnect.', 'warning', 8000);
          }
        }

        // Load WhatsApp status
        const whatsappStatus = await whatsappService.getStatus();
        // WhatsApp status loaded

        setIntegrationStatus(prev => ({
          ...prev,
          google: {
            connected: googleStatus.connected && isTokenValid,
            email: googleEmail,
            lastSynced: googleStatus.last_synced
          },
          whatsapp: {
            connected: whatsappStatus.connected,
            phone: whatsappStatus.phone || 'Unknown',
            lastSynced: whatsappStatus.lastSynced || new Date().toISOString()
          }
        }));

        // Integration status updated
      } catch (error) {
        console.error('Error loading integration status:', error);
      }
    };

    loadIntegrationStatus();
  }, [location]); // Reload when location changes

  // Function to manually refresh integration status
  const refreshIntegrationStatus = async () => {
    try {
      // Load Google Calendar status
      const googleStatus = await googleCalendarService.getStatus();
      // Try to get email from localStorage if not in the API response
      const googleEmail = googleStatus.email || localStorage.getItem('googleEmail') || undefined;

      // Always try to validate the token, regardless of connected status
      const isTokenValid = await googleCalendarService.validateToken();

      // Load WhatsApp status
      const whatsappStatus = await whatsappService.getStatus();
      // WhatsApp status refreshed

      setIntegrationStatus(prev => ({
        ...prev,
        google: {
          connected: googleStatus.connected && isTokenValid,
          email: googleEmail,
          lastSynced: googleStatus.last_synced
        },
        whatsapp: {
          connected: whatsappStatus.connected,
          phone: whatsappStatus.phone || 'Unknown',
          lastSynced: whatsappStatus.lastSynced || new Date().toISOString()
        }
      }));

      addToast('Integration status refreshed', 'success');
    } catch (error) {
      console.error('Error refreshing integration status:', error);
      addToast('Failed to refresh integration status', 'error');
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold text-gray-900 dark:text-white">Integrations</h2>
        <button
          onClick={refreshIntegrationStatus}
          className="flex items-center gap-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
        >
          <RefreshCw className="h-4 w-4" />
          Refresh Status
        </button>
      </div>

      <div className="space-y-6">
        {/* Google Calendar Integration */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-5 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <img
                  src="https://www.gstatic.com/images/branding/product/1x/calendar_48dp.png"
                  alt="Google Calendar"
                  className="w-10 h-10"
                />
                <div>
                  <h3 className="font-semibold text-lg text-gray-900 dark:text-white">Google Calendar</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">Sync your appointments with Google Calendar</p>
                </div>
              </div>
              <div className="flex items-center">
                {integrationStatus.google.connected ? (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                    Connected
                  </span>
                ) : (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                    Not Connected
                  </span>
                )}
              </div>
            </div>
          </div>

          {integrationStatus.google.connected ? (
            <div className="p-5">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-1">Connected Account</p>
                  <p className="font-medium text-gray-900 dark:text-white">{integrationStatus.google.email || localStorage.getItem('googleEmail') || 'Unknown Google Account'}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Last synced: {integrationStatus.google.lastSynced ? new Date(integrationStatus.google.lastSynced).toLocaleString() : 'Never'}
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <button
                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm"
                    onClick={refreshGoogleCalendar}
                    disabled={loading.google}
                  >
                    <RefreshCw className="w-4 h-4 inline mr-1" /> {loading.google ? 'Refreshing...' : 'Refresh Token'}
                  </button>
                  <button
                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm"
                    onClick={syncGoogleCalendar}
                  >
                    <RefreshCw className="w-4 h-4 inline mr-1" /> Sync Now
                  </button>
                  <button
                    className="px-3 py-2 border border-red-300 dark:border-red-700 text-red-700 dark:text-red-400 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 text-sm bg-white dark:bg-gray-800"
                    onClick={disconnectGoogleCalendar}
                  >
                    Disconnect
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="p-5">
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                Connect your Google Calendar to automatically sync appointments and prevent double bookings.
              </p>
              <button
                onClick={connectGoogleCalendar}
                className="px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-800"
              >
                Connect Google Calendar
              </button>
            </div>
          )}
        </div>

        {/* WhatsApp Integration */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-5 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/WhatsApp.svg/479px-WhatsApp.svg.png"
                  alt="WhatsApp"
                  className="w-10 h-10"
                />
                <div>
                  <h3 className="font-semibold text-lg text-gray-900 dark:text-white">WhatsApp</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">Connect WhatsApp to send automated messages to clients</p>
                </div>
              </div>
              <div className="flex items-center">
                {integrationStatus.whatsapp.connected ? (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                    Connected
                  </span>
                ) : (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                    Not Connected
                  </span>
                )}
              </div>
            </div>
          </div>

          {integrationStatus.whatsapp.connected ? (
            <div className="p-5">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-1">Connected Number</p>
                  <p className="font-medium text-gray-900 dark:text-white">{integrationStatus.whatsapp.phone}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Last synced: {new Date(integrationStatus.whatsapp.lastSynced || '').toLocaleString()}
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <button
                    className="px-3 py-2 border border-red-300 dark:border-red-700 text-red-700 dark:text-red-400 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 text-sm bg-white dark:bg-gray-800"
                    onClick={disconnectWhatsApp}
                    disabled={loading.whatsapp}
                  >
                    {loading.whatsapp ? 'Disconnecting...' : 'Disconnect'}
                  </button>
                </div>
              </div>

              <div className="mt-6 border-t pt-6">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">Send Test Message</h4>
                <div className="space-y-3">
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Phone Number (with country code)
                    </label>
                    <input
                      type="text"
                      id="phone"
                      value={testPhone}
                      onChange={(e) => setTestPhone(e.target.value)}
                      placeholder="+1234567890"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600"
                    />
                  </div>
                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Message
                    </label>
                    <textarea
                      id="message"
                      value={testMessage}
                      onChange={(e) => setTestMessage(e.target.value)}
                      placeholder="Enter your message here..."
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600"
                    />
                  </div>
                  <div>
                    <button
                      className="px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                      onClick={sendTestMessage}
                      disabled={sendingMessage}
                    >
                      <Send className="w-4 h-4 inline mr-1" />
                      {sendingMessage ? 'Sending...' : 'Send Message'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="p-5">
              {connecting ? (
                <div className="text-center">
                  <div className="flex flex-col items-center justify-center py-6">
                    <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-green-500 mb-4"></div>
                    <p className="text-gray-600 mb-2">Connecting to WhatsApp...</p>
                    <p className="text-amber-600 text-sm">This may take a few minutes. Please wait.</p>
                  </div>
                </div>
              ) : whatsappQR ? (
                <div className="flex flex-col items-center">
                  <p className="text-gray-600 mb-4 text-center">
                    Scan this QR code with your WhatsApp app to connect your account
                  </p>
                  <p className="text-amber-600 text-sm mb-4 text-center">
                    Note: After scanning, it may take a few minutes for the connection to be fully established.
                  </p>
                  <div className="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm mb-4">
                    {whatsappQR.startsWith('data:image') ? (
                      <img src={whatsappQR} alt="WhatsApp QR Code" className="w-64 h-64" />
                    ) : (
                      <img src={`data:image/png;base64,${whatsappQR}`} alt="WhatsApp QR Code" className="w-64 h-64" />
                    )}
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setWhatsappQR(null)}
                      className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              ) : (
                <>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Connect WhatsApp to send appointment confirmations, reminders, and updates to your clients.
                  </p>
                  <button
                    onClick={connectWhatsApp}
                    className="px-4 py-2 bg-green-600 dark:bg-green-700 text-white rounded-lg hover:bg-green-700 dark:hover:bg-green-800 flex items-center gap-2"
                    disabled={qrLoading}
                  >
                    {qrLoading ? (
                      <>
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        Loading...
                      </>
                    ) : (
                      'Connect WhatsApp'
                    )}
                  </button>
                </>
              )}
            </div>
          )}
        </div>

        {/* Integration Settings */}
        {(integrationStatus.google.connected || integrationStatus.whatsapp.connected) && (
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="p-5 border-b">
              <h3 className="font-semibold text-lg">Integration Settings</h3>
            </div>
            <div className="p-5 space-y-4">
              {integrationStatus.google.connected && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Google Calendar</h4>
                  <div className="space-y-2 ml-4">
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id="sync-two-way"
                        checked={true}
                        onChange={() => {}}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="sync-two-way" className="text-sm text-gray-700">
                        Two-way sync (changes in either calendar will sync to the other)
                      </label>
                    </div>
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id="sync-client-details"
                        checked={true}
                        onChange={() => {}}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="sync-client-details" className="text-sm text-gray-700">
                        Include client details in calendar events
                      </label>
                    </div>
                  </div>
                </div>
              )}

              {integrationStatus.whatsapp.connected && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">WhatsApp</h4>
                  <div className="space-y-2 ml-4">
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id="send-confirmations"
                        checked={true}
                        onChange={() => {}}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="send-confirmations" className="text-sm text-gray-700">
                        Send appointment confirmations
                      </label>
                    </div>
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id="send-reminders"
                        checked={true}
                        onChange={() => {}}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="send-reminders" className="text-sm text-gray-700">
                        Send appointment reminders (24 hours before)
                      </label>
                    </div>
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id="send-follow-ups"
                        checked={false}
                        onChange={() => {}}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="send-follow-ups" className="text-sm text-gray-700">
                        Send follow-up messages after appointments
                      </label>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
