import React, { useState } from 'react';
import { RefreshCw, Shield } from 'lucide-react';
import { userService } from '../../services/userService';
import { addToast } from '../../components/ui';
import TwoFactorAuthTab from './TwoFactorAuthTab';
import { PasswordStrengthMeter } from '../auth';

interface PasswordForm {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export const SecurityTab: React.FC = () => {
  const [passwordForm, setPasswordForm] = useState<PasswordForm>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [passwordError, setPasswordError] = useState<string | null>(null);

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordError(null);

    // Validate passwords
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setPasswordError('New passwords do not match');
      return;
    }

    // Check password strength
    if (passwordForm.newPassword.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      return;
    }

    // Check for character variety
    const hasUppercase = /[A-Z]/.test(passwordForm.newPassword);
    const hasLowercase = /[a-z]/.test(passwordForm.newPassword);
    const hasNumber = /[0-9]/.test(passwordForm.newPassword);
    const hasSpecial = /[^A-Za-z0-9]/.test(passwordForm.newPassword);

    if (!(hasUppercase && hasLowercase && hasNumber)) {
      setPasswordError('Password must contain uppercase letters, lowercase letters, and numbers');
      return;
    }

    if (!hasSpecial) {
      setPasswordError('Password should include at least one special character');
      return;
    }

    setIsChangingPassword(true);

    try {
      await userService.changePassword(
        passwordForm.currentPassword,
        passwordForm.newPassword
      );

      // Reset form
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });

      addToast('Password changed successfully', 'success');
    } catch (error) {
      console.error('Error changing password:', error);
      setPasswordError(
        error instanceof Error
          ? error.message
          : 'Failed to change password. Please try again.'
      );
    } finally {
      setIsChangingPassword(false);
    }
  };

  return (
    <div className="space-y-8">
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Security</h2>

      {/* Two-Factor Authentication */}
      <div className="space-y-4 mb-6">
        <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
          <Shield className="h-4 w-4 dark:text-gray-300" />
          Two-Factor Authentication
        </h3>
        <TwoFactorAuthTab />
      </div>

      {/* Change Password */}
      <div className="space-y-4 mb-6">
        <h3 className="text-md font-medium text-gray-700 dark:text-gray-300">Change Password</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Current Password
            </label>
            <input
              type="password"
              id="currentPassword"
              name="currentPassword"
              value={passwordForm.currentPassword}
              onChange={handlePasswordChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              New Password
            </label>
            <input
              type="password"
              id="newPassword"
              name="newPassword"
              value={passwordForm.newPassword}
              onChange={handlePasswordChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
            <PasswordStrengthMeter password={passwordForm.newPassword} />
          </div>

          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Confirm New Password
            </label>
            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              value={passwordForm.confirmPassword}
              onChange={handlePasswordChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {passwordError && (
            <div className="text-red-500 text-sm">{passwordError}</div>
          )}

          <div className="border-t dark:border-gray-700 pt-4 flex justify-end">
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-800 flex items-center gap-2 disabled:opacity-70 disabled:cursor-not-allowed"
              disabled={isChangingPassword}
            >
              {isChangingPassword ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  Changing Password...
                </>
              ) : (
                'Change Password'
              )}
            </button>
          </div>
        </form>
      </div>
  </div>
  );
};
