import React from 'react';
import { Building, CreditCard, Lock, Bell, Link as LinkI<PERSON>, CalendarClock, MessageSquare, Handshake, Star } from 'lucide-react';

export type SettingsTab = 'profile' | 'integrations' | 'calendar' | 'reminders' | 'notifications' | 'security' | 'billing' | 'services' | 'ai' | 'feedback';

interface SettingsSidebarProps {
  activeTab: SettingsTab;
  setActiveTab: (tab: SettingsTab) => void;
}

export const SettingsSidebar: React.FC<SettingsSidebarProps> = ({ activeTab, setActiveTab }) => {
  const tabs = [
    { id: 'profile' as SettingsTab, label: 'Business Profile', icon: Building },
    { id: 'services' as SettingsTab, label: 'Services', icon: Handshake },
    { id: 'integrations' as SettingsTab, label: 'Integrations', icon: LinkIcon },
    { id: 'calendar' as SettingsTab, label: 'Calendar Settings', icon: CalendarClock },
    { id: 'reminders' as SettingsTab, label: 'Reminders', icon: MessageSquare },
    { id: 'feedback' as SettingsTab, label: 'Client Feedback', icon: Star },
    { id: 'notifications' as SettingsTab, label: 'Notifications', icon: Bell },
    { id: 'security' as SettingsTab, label: 'Security', icon: Lock },
    { id: 'billing' as SettingsTab, label: 'Billing & Plans', icon: CreditCard }
  ];

  return (
    <div className="sm:w-64 bg-gray-50 dark:bg-gray-900 p-4 sm:border-r border-gray-200 dark:border-gray-700">
      <nav className="space-y-1">
        {tabs.map((item) => (
          <button
            key={item.id}
            onClick={() => setActiveTab(item.id)}
            className={`flex items-center gap-3 w-full p-3 rounded-lg transition-colors ${
              activeTab === item.id
                ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
            }`}
          >
            <item.icon className="h-5 w-5" />
            <span>{item.label}</span>
          </button>
        ))}
      </nav>
    </div>
  );
};
