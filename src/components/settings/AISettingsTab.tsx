import React, { useState, useEffect } from 'react';
import { aiConfig } from '../../config/aiConfig';
import { Switch } from '../ui/Switch';
import { Slider } from '../ui/Slider';

export const AISettingsTab: React.FC = () => {
  // State for settings
  const [enableAutoResponses, setEnableAutoResponses] = useState(aiConfig.enableAutoResponses);
  const [responseDelay, setResponseDelay] = useState(aiConfig.responseDelay);
  const [maxResponseLength, setMaxResponseLength] = useState(aiConfig.maxResponseLength);
  const [enableDetailedLogging, setEnableDetailedLogging] = useState(aiConfig.enableDetailedLogging);
  
  // Update the config when settings change
  useEffect(() => {
    aiConfig.enableAutoResponses = enableAutoResponses;
    aiConfig.responseDelay = responseDelay;
    aiConfig.maxResponseLength = maxResponseLength;
    aiConfig.enableDetailedLogging = enableDetailedLogging;
    
    // Save to localStorage for persistence
    localStorage.setItem('aiConfig', JSON.stringify({
      enableAutoResponses,
      responseDelay,
      maxResponseLength,
      enableDetailedLogging
    }));
  }, [enableAutoResponses, responseDelay, maxResponseLength, enableDetailedLogging]);
  
  // Load settings from localStorage on mount
  useEffect(() => {
    const savedConfig = localStorage.getItem('aiConfig');
    if (savedConfig) {
      try {
        const parsedConfig = JSON.parse(savedConfig);
        setEnableAutoResponses(parsedConfig.enableAutoResponses ?? aiConfig.enableAutoResponses);
        setResponseDelay(parsedConfig.responseDelay ?? aiConfig.responseDelay);
        setMaxResponseLength(parsedConfig.maxResponseLength ?? aiConfig.maxResponseLength);
        setEnableDetailedLogging(parsedConfig.enableDetailedLogging ?? aiConfig.enableDetailedLogging);
      } catch (error) {
        console.error('Error parsing saved AI config:', error);
      }
    }
  }, []);
  
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">WhatsApp AI Auto-Responses</h3>
        <p className="text-sm text-gray-500">
          Configure how the AI responds to incoming WhatsApp messages
        </p>
      </div>
      
      <div className="space-y-4">
        {/* Enable Auto-Responses */}
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium">Enable Auto-Responses</h4>
            <p className="text-sm text-gray-500">
              Automatically respond to incoming WhatsApp messages
            </p>
          </div>
          <Switch
            checked={enableAutoResponses}
            onChange={setEnableAutoResponses}
          />
        </div>
        
        {/* Response Delay */}
        <div className="space-y-2">
          <div>
            <h4 className="font-medium">Response Delay</h4>
            <p className="text-sm text-gray-500">
              Delay before sending an AI response (in seconds)
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Slider
              min={0}
              max={10}
              step={0.5}
              value={responseDelay / 1000}
              onChange={(value) => setResponseDelay(value * 1000)}
              disabled={!enableAutoResponses}
            />
            <span className="w-12 text-center">{(responseDelay / 1000).toFixed(1)}s</span>
          </div>
        </div>
        
        {/* Max Response Length */}
        <div className="space-y-2">
          <div>
            <h4 className="font-medium">Maximum Response Length</h4>
            <p className="text-sm text-gray-500">
              Maximum number of characters in AI responses
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Slider
              min={100}
              max={1000}
              step={50}
              value={maxResponseLength}
              onChange={setMaxResponseLength}
              disabled={!enableAutoResponses}
            />
            <span className="w-12 text-center">{maxResponseLength}</span>
          </div>
        </div>
        
        {/* Detailed Logging */}
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium">Detailed Logging</h4>
            <p className="text-sm text-gray-500">
              Log detailed information about AI responses
            </p>
          </div>
          <Switch
            checked={enableDetailedLogging}
            onChange={setEnableDetailedLogging}
            disabled={!enableAutoResponses}
          />
        </div>
      </div>
    </div>
  );
};
