import React, { useState } from 'react';
import { X, Check, AlertCircle, CreditCard } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { BillingInfo, PricingPlan } from '../../types/billing';
import { billingService } from '../../services/billingService';
import { Button } from '../ui';
import { Loader } from 'lucide-react';

interface SubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentPlan: PricingPlan;
  selectedPlan: PricingPlan | null;
  billingInfo: BillingInfo;
  onSuccess: (updatedBillingInfo: BillingInfo) => void;
  isAnnual: boolean;
}

export const SubscriptionModal: React.FC<SubscriptionModalProps> = ({
  isOpen,
  onClose,
  currentPlan,
  selectedPlan,
  billingInfo,
  onSuccess,
  isAnnual
}) => {
  const { t } = useTranslation();
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (!isOpen || !selectedPlan) return null;

  const isDowngrade = selectedPlan.monthlyFee <= currentPlan.monthlyFee;
  const isEnterprise = selectedPlan.name === 'Enterprise';

  const handleConfirm = async () => {
    try {
      setProcessing(true);
      setError(null);

      // For Enterprise plan, open email client
      if (isEnterprise) {
        window.location.href = 'mailto:<EMAIL>?subject=Enterprise%20Plan%20Inquiry&body=I%20am%20interested%20in%20the%20Enterprise%20plan.%20Please%20provide%20more%20information.';
        onClose();
        return;
      }

      // For all plan changes (upgrades, downgrades, or same-tier changes), use Stripe Customer Portal
      const response = await billingService.createCustomerPortalSession(
        window.location.origin + '/dashboard/settings'
      );

      // Redirect to Stripe Customer Portal
      window.location.href = response.url;
    } catch (error) {
      console.error('Error changing plan:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setProcessing(false);
    }
  };

  // Calculate prices
  const monthlyPrice = selectedPlan.monthlyFee;
  const annualPrice = Math.round(selectedPlan.monthlyFee * 12 * 0.8);
  const effectiveMonthlyPrice = isAnnual ? Math.round(selectedPlan.monthlyFee * 0.8) : monthlyPrice;

  // Calculate tax
  const taxRate = 0.21; // 21% VAT/IVA
  const priceWithTax = isAnnual
    ? Math.round(annualPrice * (1 + taxRate))
    : Math.round(monthlyPrice * (1 + taxRate));

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Manage Subscription
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-4">
          {error && (
            <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded-md flex items-start gap-2">
              <AlertCircle className="h-5 w-5 flex-shrink-0 mt-0.5" />
              <span>{error}</span>
            </div>
          )}

          <div className="mb-4">
            <p className="text-gray-700 dark:text-gray-300 mb-2">
              You are about to change your subscription from <span className="font-medium">{currentPlan.name}</span> to <span className="font-medium">{selectedPlan.name}</span>. You will be redirected to the Stripe Customer Portal to complete this action.
            </p>

            {!isEnterprise && (
              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-1">
                  {selectedPlan.name} Plan Details
                </h4>
                <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                  {selectedPlan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>

                <div className="mt-3 pt-3 border-t border-blue-100 dark:border-blue-800">
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {isAnnual ? (
                      <>
                        <span className="font-medium">Annual billing:</span> €{annualPrice}/year (€{effectiveMonthlyPrice}/month)
                        <br />
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          Includes 20% discount compared to monthly billing
                        </span>
                      </>
                    ) : (
                      <>
                        <span className="font-medium">Monthly billing:</span> €{monthlyPrice}/month
                      </>
                    )}
                    <br />
                    <span className="text-xs">
                      +21% {t('pricing.tax.name')} = €{priceWithTax} {isAnnual ? '/year' : '/month'}
                    </span>
                  </p>
                </div>
              </div>
            )}

            {isEnterprise && (
              <div className="mt-4 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-md">
                <h4 className="font-medium text-purple-800 dark:text-purple-300 mb-1">
                  Enterprise Plan
                </h4>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Our Enterprise plan offers custom pricing and features tailored to your business needs.
                  Click confirm to contact our sales team.
                </p>
              </div>
            )}
          </div>

          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={processing}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleConfirm}
              disabled={processing}
            >
              {processing ? (
                <>
                  <Loader className="h-4 w-4 mr-2" />
                  Processing...
                </>
              ) : isEnterprise ? (
                'Contact Sales'
              ) : (
                <>
                  <CreditCard className="h-4 w-4 mr-2" />
                  Manage Subscription
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
