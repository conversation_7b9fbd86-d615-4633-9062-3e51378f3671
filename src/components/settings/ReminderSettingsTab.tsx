import React, { useState, useEffect } from 'react';
import { Clock, Plus, Trash2, Send } from 'lucide-react';
import { reminderService, ReminderSettings } from '../../services/reminderService';
import { addToast } from '../ui';

export const ReminderSettingsTab: React.FC = () => {
  const [settings, setSettings] = useState<ReminderSettings>({
    enabled: true,
    reminderTimes: [24, 1],
    messageTemplate: 'Hi {{clientName}}, this is a reminder for your appointment on {{date}} at {{time}}. Please let us know if you need to reschedule.',
    includeLocation: true,
    includeServiceDetails: true
  });

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testPhone, setTestPhone] = useState('');
  const [sendingTest, setSendingTest] = useState(false);

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setLoading(true);
    try {
      const settings = await reminderService.getSettings();
      setSettings(settings);
    } catch (error) {
      console.error('Error loading reminder settings:', error);
      // Don't show error toast, just use default settings
      // addToast('Failed to load reminder settings', 'error');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      // Ensure there's at least an empty array for reminderTimes
      const settingsToSave = {
        ...settings,
        reminderTimes: settings.reminderTimes || []
      };

      await reminderService.updateSettings(settingsToSave);
      addToast('Reminder settings saved successfully', 'success');
    } catch (error) {
      console.error('Error saving reminder settings:', error);
      addToast('Failed to save reminder settings', 'error');
    } finally {
      setSaving(false);
    }
  };

  const addReminderTime = () => {
    setSettings(prev => ({
      ...prev,
      reminderTimes: [...(prev.reminderTimes || []), 2] // Default to 2 hours
    }));
  };

  const removeReminderTime = (index: number) => {
    setSettings(prev => ({
      ...prev,
      reminderTimes: (prev.reminderTimes || []).filter((_, i) => i !== index)
    }));
  };

  const updateReminderTime = (index: number, value: number) => {
    setSettings(prev => {
      const newTimes = [...(prev.reminderTimes || [])];
      newTimes[index] = value;
      return {
        ...prev,
        reminderTimes: newTimes
      };
    });
  };

  const sendTestReminder = async () => {
    if (!testPhone) {
      addToast('Please enter a phone number', 'error');
      return;
    }

    setSendingTest(true);
    try {
      const result = await reminderService.sendTestReminder(testPhone);

      if (result.success) {
        addToast('Test reminder sent successfully', 'success');
        setTestPhone('');
      } else {
        addToast(result.message || 'Failed to send test reminder', 'error');
      }
    } catch (error) {
      console.error('Error sending test reminder:', error);
      addToast('Failed to send test reminder', 'error');
    } finally {
      setSendingTest(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Appointment Reminders</h2>

      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-md font-semibold dark:text-white">Enable Automatic Reminders</h3>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={settings.enabled}
              onChange={e => setSettings(prev => ({ ...prev, enabled: e.target.checked }))}
            />
            <div className="w-11 h-6 bg-gray-200 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 dark:after:border-gray-600 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 dark:peer-checked:bg-blue-500"></div>
          </label>
        </div>

        <div className="mb-6">
          <p className="text-gray-600 dark:text-gray-300 text-sm mb-2">
            When enabled, appointment reminders will be sent automatically via WhatsApp to your clients.
          </p>
        </div>

        <div className="mb-6">
          <h3 className="text-md font-semibold dark:text-white mb-2">Reminder Times</h3>
          <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
            Set when reminders should be sent before appointments.
          </p>

          <div className="space-y-3">
            {settings.reminderTimes?.map((time, index) => (
              <div key={index} className="flex items-center gap-3">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Clock className="h-5 w-5 text-gray-400 dark:text-gray-300" />
                    <input
                      type="number"
                      min="1"
                      max="72"
                      value={time}
                      onChange={e => updateReminderTime(index, parseInt(e.target.value) || 1)}
                      className="w-20 px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={!settings.enabled}
                    />
                    <span className="text-gray-600 dark:text-gray-300">hours before appointment</span>
                  </div>
                </div>
                <button
                  onClick={() => removeReminderTime(index)}
                  className="p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 focus:outline-none"
                  disabled={!settings.enabled || !settings.reminderTimes}
                >
                  <Trash2 className="h-5 w-5" />
                </button>
              </div>
            ))}

            <button
              onClick={addReminderTime}
              className="flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 focus:outline-none mt-2"
              disabled={!settings.enabled}
            >
              <Plus className="h-5 w-5" />
              <span>Add another reminder time</span>
            </button>
          </div>
        </div>

        <div className="mb-6">
          <h3 className="text-md font-semibold dark:text-white mb-2">Message Template</h3>
          <p className="text-gray-600 dark:text-gray-300 text-sm mb-2">
            Customize the message that will be sent to clients. You can use the following placeholders:
          </p>
          <ul className="list-disc list-inside text-gray-600 dark:text-gray-300 text-sm mb-4">
            <li>"&#123;&#123;clientName&#125;&#125;" - Client's name</li>
            <li>"&#123;&#123;date&#125;&#125;" - Appointment date</li>
            <li>"&#123;&#123;time&#125;&#125;" - Appointment time</li>
            <li>"&#123;&#123;service&#125;&#125;" - Service name</li>
            <li>"&#123;&#123;location&#125;&#125;" - Your business location</li>
          </ul>

          <textarea
            value={settings.messageTemplate}
            onChange={e => setSettings(prev => ({ ...prev, messageTemplate: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={5}
            disabled={!settings.enabled}
          ></textarea>
        </div>

        <div className="mb-6">
          <h3 className="text-md font-semibold dark:text-white mb-2">Additional Information</h3>

          <div className="flex items-center mb-3">
            <input
              type="checkbox"
              id="includeLocation"
              checked={settings.includeLocation}
              onChange={e => setSettings(prev => ({ ...prev, includeLocation: e.target.checked }))}
              className="w-4 h-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded focus:ring-blue-500"
              disabled={!settings.enabled}
            />
            <label htmlFor="includeLocation" className="ml-2 text-gray-700 dark:text-gray-300">
              Include business location in reminders
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="includeServiceDetails"
              checked={settings.includeServiceDetails}
              onChange={e => setSettings(prev => ({ ...prev, includeServiceDetails: e.target.checked }))}
              className="w-4 h-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded focus:ring-blue-500"
              disabled={!settings.enabled}
            />
            <label htmlFor="includeServiceDetails" className="ml-2 text-gray-700 dark:text-gray-300">
              Include service details in reminders
            </label>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            onClick={saveSettings}
            className="px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-800 flex items-center gap-2"
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
        <h3 className="text-md font-semibold dark:text-white mb-4">Send Test Reminder</h3>
        <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
          Send a test reminder to verify your WhatsApp integration is working correctly.
        </p>

        <div className="flex items-end gap-3 mb-4">
          <div className="flex-1">
            <label htmlFor="testPhone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Phone Number
            </label>
            <input
              type="text"
              id="testPhone"
              value={testPhone}
              onChange={e => setTestPhone(e.target.value)}
              placeholder="+34 123 456 789"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <button
            onClick={sendTestReminder}
            className="px-4 py-2 bg-green-600 dark:bg-green-700 text-white rounded-lg hover:bg-green-700 dark:hover:bg-green-800 flex items-center gap-2"
            disabled={sendingTest || !testPhone}
          >
            {sendingTest ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                <span>Sending...</span>
              </>
            ) : (
              <>
                <Send className="h-4 w-4" />
                <span>Send Test</span>
              </>
            )}
          </button>
        </div>

        <p className="text-amber-600 dark:text-amber-400 text-sm">
          Note: Make sure your WhatsApp integration is connected in the Integrations tab before sending a test.
        </p>
      </div>

      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
        <h3 className="text-md font-semibold dark:text-white mb-4">Manual Reminder Check</h3>
        <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
          Check for upcoming appointments and send reminders manually.
        </p>

        <button
          onClick={() => reminderService.checkAndSendReminders()}
          className="px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-800"
        >
          Check and Send Reminders Now
        </button>
      </div>
    </div>
  );
};
