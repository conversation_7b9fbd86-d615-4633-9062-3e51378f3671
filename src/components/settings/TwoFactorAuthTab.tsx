import React, { useState, useEffect } from 'react';
import { Loader2, Shield, AlertCircle, CheckCircle, Copy, CheckCircle2 } from 'lucide-react';
import { useToastContext } from '../ui/ToastProvider';
import { apiClient } from '../../lib/apiClient';
import { QRCodeSVG } from 'qrcode.react';

export default function TwoFactorAuthTab() {
  const [isEnabled, setIsEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSettingUp, setIsSettingUp] = useState(false);
  const [setupData, setSetupData] = useState<{
    secret: string;
    totp_uri: string;
    backup_codes: string[];
  } | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const { addToast } = useToastContext();

  // Fetch 2FA status on component mount
  useEffect(() => {
    fetchStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchStatus = async () => {
    try {
      setIsLoading(true);
      const response = await apiClient.get('/auth/2fa/status');
      setIsEnabled(response.enabled);
    } catch (error) {
      console.error('Error fetching 2FA status:', error);
      addToast('Failed to fetch 2FA status', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetup = async () => {
    try {
      setIsSettingUp(true);
      setError(null);
      const response = await apiClient.post('/auth/2fa/setup');
      // 2FA setup response received

      // Validate the response data
      if (!response || !response.totp_uri) {
        console.error('Invalid 2FA setup response:', response);
        setError('Invalid response from server. Missing TOTP URI.');
        return;
      }

      setSetupData(response);
    } catch (error) {
      console.error('Error setting up 2FA:', error);
      setError('Failed to set up two-factor authentication');
    } finally {
      setIsSettingUp(false);
    }
  };

  const handleVerify = async () => {
    if (!verificationCode) {
      setError('Please enter the verification code');
      return;
    }

    try {
      setVerifying(true);
      setError(null);
      await apiClient.post('/auth/2fa/verify', { token: verificationCode });
      setIsEnabled(true);
      setSetupData(null);
      addToast('Two-factor authentication has been enabled', 'success');
    } catch (error) {
      console.error('Error verifying 2FA:', error);
      // Handle rate limiting errors specifically
      if (error && typeof error === 'object' && 'status' in error && (error as {status: number}).status === 429) {
        setError(`Too many attempts. Please try again later. ${(error as {message?: string}).message || ''}`);
      } else {
        setError('Invalid verification code');
      }
    } finally {
      setVerifying(false);
    }
  };

  const handleDisable = async () => {
    if (!verificationCode) {
      setError('Please enter the verification code');
      return;
    }

    try {
      setVerifying(true);
      setError(null);
      await apiClient.post('/auth/2fa/disable', { token: verificationCode });
      setIsEnabled(false);
      setVerificationCode('');
      addToast('Two-factor authentication has been disabled', 'success');
    } catch (error) {
      console.error('Error disabling 2FA:', error);
      // Handle rate limiting errors specifically
      if (error && typeof error === 'object' && 'status' in error && (error as {status: number}).status === 429) {
        setError(`Too many attempts. Please try again later. ${(error as {message?: string}).message || ''}`);
      } else {
        setError('Invalid verification code');
      }
    } finally {
      setVerifying(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
      <div className="p-6">
        <div className="mb-4">
          <h3 className="text-lg font-medium flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Two-Factor Authentication
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Add an extra layer of security to your account by requiring a verification code in addition to your password.
          </p>
        </div>

        {error && (
          <div className="mb-4 p-4 border border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800 text-red-600 dark:text-red-400 rounded-md">
            <AlertCircle className="h-4 w-4 inline-block mr-2" />
            <span className="font-medium">Error:</span> {error}
          </div>
        )}

        {isEnabled ? (
          <div className="space-y-4">
            <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 rounded-md">
              <CheckCircle className="h-5 w-5" />
              <span>Two-factor authentication is enabled</span>
            </div>

            <div className="space-y-2">
              <label htmlFor="disable-code" className="block text-sm font-medium">Enter verification code to disable</label>
              <div className="flex gap-2">
                <input
                  id="disable-code"
                  type="text"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  placeholder="Enter 6-digit code"
                  maxLength={6}
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                <button
                  onClick={handleDisable}
                  disabled={verifying || !verificationCode}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {verifying ? <Loader2 className="h-4 w-4 animate-spin mr-2 inline" /> : null}
                  Disable
                </button>
              </div>
            </div>
          </div>
        ) : setupData ? (
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium">Scan this QR code with your authenticator app</label>
              <div className="flex justify-center p-4 bg-white dark:bg-gray-700 rounded-md">
                {setupData.totp_uri ? (
                  <QRCodeSVG
                    value={setupData.totp_uri}
                    size={200}
                    bgColor={"#ffffff"}
                    fgColor={"#000000"}
                    level={"M"}
                    includeMargin={true}
                    className="h-48 w-48"
                  />
                ) : (
                  <div className="h-48 w-48 flex items-center justify-center bg-gray-100 dark:bg-gray-600 rounded-md">
                    <p className="text-gray-500 dark:text-gray-400 text-center">Error loading QR code</p>
                  </div>
                )}
              </div>

              <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 rounded-md text-sm">
                <p className="font-medium">Having trouble with the QR code?</p>
                <p>You can manually set up your authenticator app using the secret key below.</p>
                <ol className="list-decimal list-inside mt-2 space-y-1">
                  <li>Open your authenticator app (Google Authenticator, Authy, etc.)</li>
                  <li>Select "Enter setup key" or "Enter manually"</li>
                  <li>Enter your email address as the account name</li>
                  <li>Enter the secret key: <span className="font-mono bg-white dark:bg-gray-800 px-1 py-0.5 rounded">{setupData.secret}</span></li>
                  <li>Make sure Time-based (TOTP) is selected</li>
                  <li>Click Add</li>
                </ol>

                <div className="mt-3">
                  <p className="font-medium">On mobile? Try these direct links:</p>
                  <div className="flex flex-wrap gap-2 mt-1">
                    <a
                      href={`otpauth://totp/FixMyCal:User?secret=${setupData.secret}&issuer=FixMyCal`}
                      className="inline-flex items-center px-3 py-1 bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 rounded-md text-xs font-medium border border-blue-200 dark:border-blue-800"
                    >
                      Open in Authenticator App
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium">Or enter this code manually</label>
              <div className="flex items-center gap-2">
                <code className="p-2 bg-gray-100 dark:bg-gray-700 rounded-md flex-1 text-center font-mono">
                  {setupData.secret}
                </code>
                <button
                  onClick={() => copyToClipboard(setupData.secret)}
                  title="Copy to clipboard"
                  className="p-2 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  {copied ? <CheckCircle2 className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="verification-code" className="block text-sm font-medium">Enter verification code</label>
              <div className="flex gap-2">
                <input
                  id="verification-code"
                  type="text"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  placeholder="Enter 6-digit code"
                  maxLength={6}
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                <button
                  onClick={handleVerify}
                  disabled={verifying || !verificationCode}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {verifying ? <Loader2 className="h-4 w-4 animate-spin mr-2 inline" /> : null}
                  Verify
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium">Backup codes</label>
              <div className="p-4 border border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                <p className="font-medium">Save these backup codes</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  If you lose access to your authenticator app, you can use one of these backup codes to sign in. Each
                  code can only be used once.
                </p>
                <div className="mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md text-yellow-700 dark:text-yellow-400 text-sm">
                  <p className="font-medium flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" /> Important
                  </p>
                  <p className="mt-1">
                    Store these backup codes in a secure location like a password manager. You won't be able to view them again after leaving this page.
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {setupData.backup_codes.map((code, index) => (
                  <code key={index} className="p-2 bg-gray-100 dark:bg-gray-700 rounded-md text-center font-mono">
                    {code}
                  </code>
                ))}
              </div>
              <button
                className="w-full mt-2 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center justify-center"
                onClick={() => copyToClipboard(setupData.backup_codes.join('\n'))}
              >
                {copied ? <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" /> : <Copy className="h-4 w-4 mr-2" />}
                Copy all backup codes
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <div className="font-medium">Enable two-factor authentication</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Require a verification code when signing in to your account
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={isEnabled}
                  onChange={() => handleSetup()}
                  disabled={isSettingUp}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        )}
      </div>

      <div className="border-t border-gray-200 dark:border-gray-700 p-4 flex justify-between">
        {setupData && (
          <button
            onClick={() => setSetupData(null)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
        )}
      </div>
    </div>
  );
}
