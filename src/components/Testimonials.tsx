import { Star } from 'lucide-react';

export function Testimonials() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "CEO, TechStart Inc.",
      image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&w=200&h=200&q=80",
      content: "FixMyCal has transformed how we manage our team's schedules. The AI suggestions are incredibly accurate, and we've reduced scheduling conflicts by 90%."
    },
    {
      name: "<PERSON>",
      role: "Freelance Consultant",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=200&h=200&q=80",
      content: "As a consultant juggling multiple clients, <PERSON>xMyCal has been a game-changer. The automated scheduling and reminders have practically eliminated no-shows."
    },
    {
      name: "<PERSON>",
      role: "Head of Sales, Global Corp",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=200&h=200&q=80",
      content: "The time zone management feature alone has saved our international team countless hours. It's like having a personal assistant who never makes mistakes."
    }
  ];

  return (
    <section id="testimonials" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Loved by Professionals Worldwide
          </h2>
          <p className="text-xl text-gray-600">
            Join thousands of satisfied users who have transformed their scheduling experience
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-white p-8 rounded-2xl shadow-lg">
              <div className="flex items-center gap-1 text-yellow-400 mb-6">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 mb-6">{testimonial.content}</p>
              <div className="flex items-center gap-4">
                <img
                  src={testimonial.image}
                  alt={testimonial.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div>
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-sm text-gray-500">{testimonial.role}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <div className="inline-flex items-center gap-2 text-gray-600">
            <Star className="h-5 w-5 text-yellow-400 fill-current" />
            <span className="font-semibold">4.9/5 average rating</span>
            <span className="text-gray-400">|</span>
            <span>1000+ reviews</span>
          </div>
        </div>
      </div>
    </section>
  );
}
