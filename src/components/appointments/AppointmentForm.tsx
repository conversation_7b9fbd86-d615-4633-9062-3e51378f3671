import { useState, useEffect } from 'react';
import { RotateCcw, AlertCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { AppointmentFormData } from '../../services/appointmentService';
import { Button } from '../ui';
import { clientService } from '../../services/clientService';
import { serviceService, Service } from '../../services/serviceService';
import { Client } from '../../types/api';
import { appointmentService } from '../../services/appointmentService';

interface AppointmentFormProps {
  initialData?: Partial<AppointmentFormData>;
  onSubmit: (data: AppointmentFormData) => Promise<void>;
  onCancel: () => void;
  title: string;
  submitLabel: string;
  isSubmitting?: boolean;
}

export function AppointmentForm({
  initialData,
  onSubmit,
  onCancel,
  title,
  submitLabel,
  isSubmitting = false
}: AppointmentFormProps) {
  // Get current time rounded to the nearest 15 minutes
  const getCurrentTimeRounded = () => {
    const now = new Date();
    const minutes = now.getMinutes();
    const roundedMinutes = Math.ceil(minutes / 15) * 15;
    now.setMinutes(roundedMinutes);
    now.setSeconds(0);
    now.setMilliseconds(0);
    return now;
  };

  const currentTime = getCurrentTimeRounded();
  const thirtyMinutesLater = new Date(currentTime.getTime() + 30 * 60 * 1000);

  const [formData, setFormData] = useState<AppointmentFormData>({
    client_id: initialData?.client_id || '',
    service_id: initialData?.service_id || '',
    start_time: initialData?.start_time || currentTime.toISOString().slice(0, 16),
    end_time: initialData?.end_time || thirtyMinutesLater.toISOString().slice(0, 16),
    notes: initialData?.notes || '',
    status: initialData?.status || 'CONFIRMED',

    // Recurrence fields
    is_recurring: initialData?.is_recurring || false,
    recurrence_frequency: initialData?.recurrence_frequency || 'WEEKLY',
    recurrence_interval: initialData?.recurrence_interval || 1,
    recurrence_end_date: initialData?.recurrence_end_date || '',
    recurrence_count: initialData?.recurrence_count || 4,
    recurrence_days: initialData?.recurrence_days || []
  });

  const [showRecurrenceOptions, setShowRecurrenceOptions] = useState(initialData?.is_recurring || false);
  const [hasConflicts, setHasConflicts] = useState(false);
  const [conflicts, setConflicts] = useState<{id: string; title: string; start: string; end: string}[]>([]);

  const [errors, setErrors] = useState<Record<string, string>>({});
  // Keeping this state for future form submission handling
  const setIsSubmittingForm = useState(false)[1];
  const [clients, setClients] = useState<Client[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [isLoadingClients, setIsLoadingClients] = useState(false);
  const [isLoadingServices, setIsLoadingServices] = useState(false);

  // Reset form when initialData changes
  useEffect(() => {
    if (initialData) {
      // Create a new form data object with all the required fields
      const newFormData = {
        // Start with the current form data as a base
        ...formData,
        // Override with initialData values
        client_id: initialData.client_id || formData.client_id,
        service_id: initialData.service_id || formData.service_id,
        start_time: initialData.start_time || formData.start_time,
        end_time: initialData.end_time || formData.end_time,
        notes: initialData.notes || formData.notes,
        status: initialData.status || formData.status,
        // Ensure recurrence fields are preserved
        is_recurring: initialData.is_recurring !== undefined ? initialData.is_recurring : formData.is_recurring,
        recurrence_frequency: initialData.recurrence_frequency || formData.recurrence_frequency,
        recurrence_interval: initialData.recurrence_interval || formData.recurrence_interval,
        recurrence_end_date: initialData.recurrence_end_date || formData.recurrence_end_date,
        recurrence_count: initialData.recurrence_count || formData.recurrence_count,
        recurrence_days: initialData.recurrence_days || formData.recurrence_days
      };

      setFormData(newFormData);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialData]);

  // Update end time when service or start time changes
  useEffect(() => {
    // Force a manual calculation of the end time
    const calculateEndTime = () => {
      // Only proceed if we have both a service ID and a start time
      if (!formData.service_id || !formData.start_time) {
        // Missing service_id or start_time, cannot calculate end time
        return;
      }

      // Find the selected service
      const selectedService = services.find(service => service.id === formData.service_id);
      // Process selected service

      if (!selectedService) {
        console.error(`Service with ID ${formData.service_id} not found in services list`);
        return;
      }

      // Check if the service has a valid duration
      if (!selectedService.duration_minutes || isNaN(selectedService.duration_minutes)) {
        console.error('Service has invalid duration:', selectedService.duration_minutes);
        return;
      }

      // Parse the duration as a number to be safe
      const duration = parseInt(String(selectedService.duration_minutes), 10);
      // Use parsed duration in minutes

      // Calculate the end time based on service duration
      try {
        // Parse the start time string directly to preserve the local timezone
        const startTimeStr = formData.start_time;
        // Extract date and time parts
        const [datePart, timePart] = startTimeStr.split('T');
        const [year, month, day] = datePart.split('-').map(Number);
        const [hours, minutes] = timePart.split(':').map(Number);

        // Create a date object in local time
        const startTime = new Date(year, month - 1, day, hours, minutes);
        // Add the duration
        const durationMs = duration * 60 * 1000;
        const endTime = new Date(startTime.getTime() + durationMs);
        // Format the end time for the input field, preserving local time
        const endYear = endTime.getFullYear();
        const endMonth = String(endTime.getMonth() + 1).padStart(2, '0');
        const endDay = String(endTime.getDate()).padStart(2, '0');
        const endHours = String(endTime.getHours()).padStart(2, '0');
        const endMinutes = String(endTime.getMinutes()).padStart(2, '0');

        const newEndTime = `${endYear}-${endMonth}-${endDay}T${endHours}:${endMinutes}`;
        // Update the form data with the new end time
        setFormData(prev => {
          if (prev.end_time !== newEndTime) {
            // Updating end time
            return {
              ...prev,
              end_time: newEndTime
            };
          }
          return prev;
        });
      } catch (error) {
        console.error('Error calculating end time:', error);
      }
    };

    // Call the calculation function
    calculateEndTime();

  }, [formData.service_id, formData.start_time, services]);

  // Load clients and services
  useEffect(() => {
    const loadClients = async () => {
      setIsLoadingClients(true);
      try {
        // Fetch all clients with the maximum allowed page size
        const response = await clientService.getClients({ page_size: 100 });
        setClients(response.items);
      } catch (error) {
        console.error('Error loading clients:', error);
      } finally {
        setIsLoadingClients(false);
      }
    };

    const loadServices = async () => {
      // Loading services
      setIsLoadingServices(true);
      try {
        // Fetch all services with the maximum allowed page size
        const response = await serviceService.getServices({ limit: 100 });
        // Verify that services have duration_minutes property
        if (response.items && response.items.length > 0) {
          // Validate service duration_minutes

          // Check if any services have missing or invalid duration_minutes
          const invalidServices = response.items.filter(s => !s.duration_minutes || isNaN(s.duration_minutes));
          if (invalidServices.length > 0) {
            console.error('Found services with invalid duration_minutes:', invalidServices);
          }
        } else {
          console.warn('No services returned from API');
        }

        setServices(response.items);
      } catch (error) {
        console.error('Error loading services:', error);
        // Falling back to mock services
        // Fallback to mock services if API fails
        setServices([
          {
            id: '1',
            user_id: 'mock',
            name: 'Haircut',
            description: 'Basic haircut service',
            duration_minutes: 30,
            price: 25,
            type_id: 'mock',
            active: true
          },
          {
            id: '2',
            user_id: 'mock',
            name: 'Massage',
            description: 'Relaxing massage',
            duration_minutes: 60,
            price: 50,
            type_id: 'mock',
            active: true
          },
          {
            id: '3',
            user_id: 'mock',
            name: 'Consultation',
            description: 'Initial consultation',
            duration_minutes: 45,
            price: 40,
            type_id: 'mock',
            active: true
          },
          {
            id: '4',
            user_id: 'mock',
            name: 'Physiotherapy',
            description: 'Physiotherapy session',
            duration_minutes: 60,
            price: 60,
            type_id: 'mock',
            active: true
          }
        ]);
      } finally {
        setIsLoadingServices(false);
      }
    };

    loadClients();
    loadServices();
  }, []);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type, checked } = e.target;

    // Allow end_time to be manually edited
    // The auto-calculation will only happen when service or start time changes

    // Handle checkbox inputs
    if (type === 'checkbox') {
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));

      // Toggle recurrence options visibility
      if (name === 'is_recurring') {
        setShowRecurrenceOptions(checked);
      }
    } else {
      // Handle all other inputs
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }

    // Note: We don't need to calculate end time here anymore
    // The useEffect hook will handle that when service_id or start_time changes
    // Field value changed

    // Check for conflicts when changing dates
    if ((name === 'start_time' || name === 'service_id') && formData.start_time && formData.service_id) {
      setTimeout(() => checkForConflicts(), 500); // Delay to allow form state to update
    }
  };

  // Check for appointment conflicts
  const checkForConflicts = async () => {
    if (!formData.start_time || !formData.end_time) return;

    try {
      const result = await appointmentService.checkConflicts(
        formData.start_time,
        formData.end_time,
        initialData?.id
      );

      setHasConflicts(result.has_conflicts);
      setConflicts(result.conflicts || []);
    } catch (error) {
      console.error('Error checking for conflicts:', error);
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    const now = new Date();

    if (!formData.client_id) {
      newErrors.client_id = 'Client is required';
    }

    if (!formData.service_id) {
      newErrors.service_id = 'Service is required';
    }

    if (!formData.start_time) {
      newErrors.start_time = 'Start time is required';
    } else {
      // Check if start time is in the past
      const startTime = new Date(formData.start_time);
      if (startTime < now) {
        newErrors.start_time = 'Start time cannot be in the past';
      }
    }

    if (!formData.end_time) {
      newErrors.end_time = 'End time is required';
    } else if (new Date(formData.end_time) <= new Date(formData.start_time)) {
      newErrors.end_time = 'End time must be after start time';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmittingForm(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting appointment:', error);
      setErrors(prev => ({
        ...prev,
        form: 'Failed to save appointment. Please try again.'
      }));
    } finally {
      setIsSubmittingForm(false);
    }
  };

  // Animation variants
  const formVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        damping: 25,
        stiffness: 500,
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };

  const inputVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        type: "spring",
        damping: 25,
        stiffness: 500
      }
    }
  };

  return (
    <motion.div
      className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden"
      initial="hidden"
      animate="visible"
      variants={formVariants}
    >
      <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b dark:border-gray-600">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">{title}</h3>
      </div>

      <motion.form onSubmit={handleSubmit} className="p-6" variants={formVariants}>
        <AnimatePresence>
          {errors.form && (
            <motion.div
              className="mb-4 p-3 bg-red-50 text-red-700 rounded-md"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
            >
              {errors.form}
            </motion.div>
          )}
        </AnimatePresence>

        <div className="space-y-4">
          {/* Client Field */}
          <motion.div variants={inputVariants}>
            <label htmlFor="client_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Client <span className="text-red-500">*</span>
            </label>
            <select
              id="client_id"
              name="client_id"
              value={formData.client_id}
              onChange={handleChange}
              className={`mt-1 block w-full rounded-md border ${
                errors.client_id ? 'border-red-300 dark:border-red-700' : 'border-gray-300 dark:border-gray-600'
              } shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500
              bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              disabled={isLoadingClients}
            >
              <option value="">Select a client</option>
              {clients.map(client => (
                <option key={client.id} value={client.id}>
                  {client.name}
                </option>
              ))}
            </select>
            <AnimatePresence>
              {errors.client_id && (
                <motion.p
                  className="mt-1 text-sm text-red-600"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                >
                  {errors.client_id}
                </motion.p>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Service Field */}
          <motion.div variants={inputVariants}>
            <label htmlFor="service_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Service <span className="text-red-500">*</span>
            </label>
            <select
              id="service_id"
              name="service_id"
              value={formData.service_id}
              onChange={handleChange}
              className={`mt-1 block w-full rounded-md border ${
                errors.service_id ? 'border-red-300 dark:border-red-700' : 'border-gray-300 dark:border-gray-600'
              } shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500
              bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              disabled={isLoadingServices}
            >
              <option value="">Select a service</option>
              {services.map(service => (
                <option key={service.id} value={service.id}>
                  {service.name} ({service.duration_minutes} min - ${service.price})
                </option>
              ))}
            </select>
            <AnimatePresence>
              {errors.service_id && (
                <motion.p
                  className="mt-1 text-sm text-red-600"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                >
                  {errors.service_id}
                </motion.p>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Start Time Field */}
          <motion.div variants={inputVariants}>
            <label htmlFor="start_time" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Start Time <span className="text-red-500">*</span>
            </label>
            <input
              type="datetime-local"
              id="start_time"
              name="start_time"
              value={formData.start_time}
              min={new Date().toISOString().slice(0, 16)}
              onChange={handleChange}
              className={`mt-1 block w-full rounded-md border ${
                errors.start_time ? 'border-red-300 dark:border-red-700' : 'border-gray-300 dark:border-gray-600'
              } shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500
              bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            />
            <AnimatePresence>
              {errors.start_time && (
                <motion.p
                  className="mt-1 text-sm text-red-600"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                >
                  {errors.start_time}
                </motion.p>
              )}
            </AnimatePresence>
          </motion.div>

          {/* End Time Field */}
          <motion.div variants={inputVariants}>
            <div className="flex justify-between items-center">
              <label htmlFor="end_time" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                End Time <span className="text-red-500">*</span>
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">(Auto-calculated but can be modified)</span>
              </label>
              {/* No recalculate button needed as end time is auto-calculated */}
            </div>
            <input
              type="datetime-local"
              id="end_time"
              name="end_time"
              value={formData.end_time}
              onChange={handleChange}
              className={`mt-1 block w-full rounded-md border ${
                errors.end_time ? 'border-red-300 dark:border-red-700' : 'border-gray-300 dark:border-gray-600'
              } shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white`}
            />
            <AnimatePresence>
              {errors.end_time && (
                <motion.p
                  className="mt-1 text-sm text-red-600"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                >
                  {errors.end_time}
                </motion.p>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Status Field */}
          <motion.div variants={inputVariants}>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700">
              Status
            </label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="CONFIRMED">Confirmed</option>
              <option value="PENDING">Pending</option>
              <option value="CANCELLED">Cancelled</option>
              <option value="RESCHEDULED">Rescheduled</option>
            </select>
          </motion.div>

          {/* Recurring Appointment Toggle */}
          <motion.div variants={inputVariants} className="mt-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_recurring"
                name="is_recurring"
                checked={formData.is_recurring}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="is_recurring" className="ml-2 block text-sm font-medium text-gray-700">
                Recurring Appointment
              </label>
              <span className="ml-2 text-gray-500">
                <RotateCcw size={16} />
              </span>
            </div>
          </motion.div>

          {/* Recurrence Options */}
          <AnimatePresence>
            {showRecurrenceOptions && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-4 p-4 border border-gray-200 rounded-md bg-gray-50"
              >
                <h4 className="text-sm font-medium text-gray-700 mb-3">Recurrence Options</h4>

                {/* Frequency */}
                <div className="mb-3">
                  <label htmlFor="recurrence_frequency" className="block text-sm font-medium text-gray-700">
                    Frequency
                  </label>
                  <select
                    id="recurrence_frequency"
                    name="recurrence_frequency"
                    value={formData.recurrence_frequency}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                  >
                    <option value="DAILY">Daily</option>
                    <option value="WEEKLY">Weekly</option>
                    <option value="BIWEEKLY">Biweekly</option>
                    <option value="MONTHLY">Monthly</option>
                    <option value="YEARLY">Yearly</option>
                  </select>
                </div>

                {/* Interval */}
                <div className="mb-3">
                  <label htmlFor="recurrence_interval" className="block text-sm font-medium text-gray-700">
                    Repeat every
                  </label>
                  <div className="flex items-center mt-1">
                    <input
                      type="number"
                      id="recurrence_interval"
                      name="recurrence_interval"
                      value={formData.recurrence_interval}
                      onChange={handleChange}
                      min="1"
                      max="99"
                      className="block w-20 rounded-md border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                    />
                    <span className="ml-2 text-sm text-gray-500">
                      {formData.recurrence_frequency === 'DAILY' && 'day(s)'}
                      {formData.recurrence_frequency === 'WEEKLY' && 'week(s)'}
                      {formData.recurrence_frequency === 'BIWEEKLY' && 'biweek(s)'}
                      {formData.recurrence_frequency === 'MONTHLY' && 'month(s)'}
                      {formData.recurrence_frequency === 'YEARLY' && 'year(s)'}
                    </span>
                  </div>
                </div>

                {/* End recurrence */}
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    End recurrence
                  </label>
                  <div className="flex items-center mb-2">
                    <input
                      type="radio"
                      id="end_after"
                      name="end_type"
                      checked={!formData.recurrence_end_date}
                      onChange={() => {
                        setFormData(prev => ({
                          ...prev,
                          recurrence_end_date: '',
                          recurrence_count: 4
                        }));
                      }}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <label htmlFor="end_after" className="ml-2 block text-sm text-gray-700">
                      After
                    </label>
                    <input
                      type="number"
                      id="recurrence_count"
                      name="recurrence_count"
                      value={formData.recurrence_count}
                      onChange={handleChange}
                      min="1"
                      max="99"
                      disabled={!!formData.recurrence_end_date}
                      className="ml-2 block w-16 rounded-md border border-gray-300 shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                    />
                    <span className="ml-2 text-sm text-gray-500">occurrence(s)</span>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="end_on_date"
                      name="end_type"
                      checked={!!formData.recurrence_end_date}
                      onChange={() => {
                        // Set default end date to 3 months from now
                        const threeMonthsLater = new Date();
                        threeMonthsLater.setMonth(threeMonthsLater.getMonth() + 3);

                        setFormData(prev => ({
                          ...prev,
                          recurrence_end_date: threeMonthsLater.toISOString().slice(0, 10),
                          recurrence_count: 0
                        }));
                      }}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <label htmlFor="end_on_date" className="ml-2 block text-sm text-gray-700">
                      On date
                    </label>
                    <input
                      type="date"
                      id="recurrence_end_date"
                      name="recurrence_end_date"
                      value={formData.recurrence_end_date ? formData.recurrence_end_date.slice(0, 10) : ''}
                      onChange={handleChange}
                      min={new Date().toISOString().slice(0, 10)}
                      disabled={!formData.recurrence_end_date}
                      className="ml-2 block rounded-md border border-gray-300 shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                    />
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Notes Field */}
          <motion.div variants={inputVariants}>
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              rows={3}
              className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-600 shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </motion.div>

          {/* Conflict Warning */}
          <AnimatePresence>
            {hasConflicts && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md flex items-start"
              >
                <AlertCircle className="text-yellow-500 mr-2 flex-shrink-0 mt-0.5" size={16} />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Scheduling Conflict Detected</p>
                  <p className="text-sm text-yellow-700 mt-1">
                    This appointment conflicts with {conflicts.length} existing appointment(s).
                    You can still save, but be aware of the overlap.
                  </p>
                  {conflicts.length > 0 && (
                    <ul className="mt-2 text-xs text-yellow-700 list-disc list-inside">
                      {conflicts.slice(0, 3).map((conflict, index) => (
                        <li key={index}>
                          {new Date(conflict.start_time).toLocaleString()} - {new Date(conflict.end_time).toLocaleTimeString()}
                        </li>
                      ))}
                      {conflicts.length > 3 && <li>...and {conflicts.length - 3} more</li>}
                    </ul>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <motion.div
          className="mt-6 flex justify-end gap-3"
          variants={inputVariants}
        >
          <Button
            type="button"
            onClick={onCancel}
            variant="outline"
            size="md"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
            variant="primary"
            size="md"
          >
            {isSubmitting ? 'Saving...' : submitLabel}
          </Button>
        </motion.div>
      </motion.form>
    </motion.div>
  );
}
