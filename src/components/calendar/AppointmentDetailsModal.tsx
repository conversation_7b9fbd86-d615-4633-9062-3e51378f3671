import { Clock, User, Tag, CalendarIcon } from 'lucide-react';
import { Appointment, formatDate, formatTime } from './CalendarTypes';

interface AppointmentDetailsModalProps {
  appointment: Appointment;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
}

export const AppointmentDetailsModal: React.FC<AppointmentDetailsModalProps> = ({
  appointment,
  onClose,
  onEdit,
  onDelete
}) => {
  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-md w-full overflow-hidden">
        <div className="p-6 max-h-[90vh] overflow-y-auto">
          <h3 className="text-xl font-bold text-gray-900 mb-4 break-words">{appointment.title}</h3>

          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <Clock className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
              <div className="overflow-hidden">
                <div className="font-medium text-gray-900">{formatDate(appointment.start)}</div>
                <div className="text-gray-600">
                  {formatTime(appointment.start)} - {formatTime(appointment.end)}
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <User className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
              <div className="overflow-hidden">
                <div className="font-medium text-gray-900">Client</div>
                <div className="text-gray-600 break-words">
                  {typeof appointment.client === 'string' && appointment.client.includes('-')
                    ? 'Loading client...'
                    : appointment.client || 'No client'}
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Tag className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
              <div className="overflow-hidden">
                <div className="font-medium text-gray-900">Service</div>
                <div className="text-gray-600 break-words whitespace-normal">{appointment.service}</div>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className={`h-5 w-5 rounded-full mt-0.5 flex-shrink-0 ${
                (appointment.status || '').toLowerCase() === 'confirmed' ? 'bg-green-500' : 'bg-yellow-500'
              }`}></div>
              <div className="overflow-hidden">
                <div className="font-medium text-gray-900">Status</div>
                <div className="text-gray-600 capitalize">{appointment.status ? appointment.status.toLowerCase() : 'pending'}</div>
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-3 mt-6">
            {/* Primary actions */}
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={onClose}
                className="py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 truncate"
              >
                Close
              </button>

              {/* Always show Edit button for app-created appointments */}
              {appointment.source === 'google' ? (
                <a
                  href={appointment.googleLink || (appointment.googleEventId ?
                    `https://calendar.google.com/calendar/event?eid=${btoa(appointment.googleEventId.replace('@google.com', ''))}` :
                    'https://calendar.google.com')}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-center truncate"
                >
                  View in Google Calendar
                </a>
              ) : (
                <button
                  onClick={onEdit}
                  className="py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 truncate"
                >
                  Edit
                </button>
              )}
            </div>

            {/* Secondary actions - always show Delete for app-created appointments */}
            {appointment.source !== 'google' && (
              <button
                onClick={onDelete}
                className="py-2 px-4 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 truncate"
              >
                Delete Appointment
              </button>
            )}

            {/* Google Calendar link if synced - always show for app-created appointments with googleEventId */}
            {appointment.googleEventId && appointment.source !== 'google' && (
              <a
                href={appointment.googleLink || (appointment.googleEventId ?
                  `https://calendar.google.com/calendar/event?eid=${btoa(appointment.googleEventId.replace('@google.com', ''))}` :
                  'https://calendar.google.com')}
                target="_blank"
                rel="noopener noreferrer"
                className="py-2 px-4 border border-blue-300 text-blue-600 rounded-lg hover:bg-blue-50 text-center truncate"
              >
                View in Google Calendar
              </a>
            )}
          </div>

          {(appointment.source === 'google' || appointment.googleEventId) && (
            <div className="mt-4 pt-4 border-t text-sm text-gray-500 flex items-center overflow-hidden">
              <CalendarIcon className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="truncate">
                {appointment.source === 'google'
                  ? 'Synced from Google Calendar'
                  : 'Synced to Google Calendar'
                }
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
