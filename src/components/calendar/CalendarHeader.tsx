import { ChevronLeft, ChevronRight, CalendarIcon, RefreshCw, Plus } from 'lucide-react';
import { MONTHS } from './CalendarTypes';

interface CalendarHeaderProps {
  currentDate: Date;
  selectedDate: Date;
  viewMode: 'day' | 'week' | 'month';
  googleCalendarConnected: boolean;
  isLoadingGoogleEvents: boolean;
  getWeekDays: () => Date[];
  goToPreviousDate: () => void;
  goToNextDate: () => void;
  goToToday: () => void;
  setViewMode: (mode: 'day' | 'week' | 'month') => void;
  loadGoogleCalendarEvents: () => Promise<void>;
  handleAddAppointment: () => void;
}

export const CalendarHeader: React.FC<CalendarHeaderProps> = ({
  currentDate,
  selectedDate,
  viewMode,
  googleCalendarConnected,
  isLoadingGoogleEvents,
  getWeekDays,
  goToPreviousDate,
  goToNextDate,
  goToToday,
  setViewMode,
  loadGoogleCalendarEvents,
  handleAddAppointment
}) => {
  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Calendar</h1>

        <div className="flex items-center gap-2">
          {googleCalendarConnected && (
            <button
              onClick={loadGoogleCalendarEvents}
              className="btn-secondary flex items-center gap-2 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
              disabled={isLoadingGoogleEvents}
            >
              <CalendarIcon className="h-5 w-5" />
              {isLoadingGoogleEvents ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>Syncing...</span>
                </>
              ) : (
                <span>Sync Google Calendar</span>
              )}
            </button>
          )}
          <button
            onClick={handleAddAppointment}
            className="btn-primary flex items-center gap-2 dark:bg-blue-700 dark:hover:bg-blue-600"
          >
            <Plus className="h-5 w-5" />
            <span>New Appointment</span>
          </button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center gap-3">
            <button
              onClick={goToPreviousDate}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <ChevronLeft className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </button>

            <button
              onClick={goToToday}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200 rounded-lg transition-colors text-sm font-medium"
            >
              Today
            </button>

            <button
              onClick={goToNextDate}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <ChevronRight className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </button>

            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              {viewMode === 'month'
                ? `${MONTHS[currentDate.getMonth()]} ${currentDate.getFullYear()}`
                : viewMode === 'week'
                  ? `${MONTHS[getWeekDays()[0].getMonth()]} ${getWeekDays()[0].getDate()} - ${MONTHS[getWeekDays()[6].getMonth()]} ${getWeekDays()[6].getDate()}, ${currentDate.getFullYear()}`
                  : selectedDate.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })
              }
            </h2>
          </div>

          <div className="flex items-center gap-2 bg-gray-100 dark:bg-gray-700 p-1 rounded-lg">
            <button
              onClick={() => setViewMode('day')}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                viewMode === 'day'
                  ? 'bg-white dark:bg-gray-800 shadow-sm'
                  : 'text-gray-600 dark:text-gray-300'
              }`}
            >
              Day
            </button>
            <button
              onClick={() => setViewMode('week')}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                viewMode === 'week'
                  ? 'bg-white dark:bg-gray-800 shadow-sm'
                  : 'text-gray-600 dark:text-gray-300'
              }`}
            >
              Week
            </button>
            <button
              onClick={() => setViewMode('month')}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                viewMode === 'month'
                  ? 'bg-white dark:bg-gray-800 shadow-sm'
                  : 'text-gray-600 dark:text-gray-300'
              }`}
            >
              Month
            </button>
          </div>
        </div>
      </div>
    </>
  );
};
