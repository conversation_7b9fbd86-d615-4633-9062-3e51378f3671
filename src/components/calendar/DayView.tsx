import React from 'react';
import { Appointment, formatTime } from './CalendarTypes';

interface DayViewProps {
  selectedDate: Date;
  appointments: Appointment[];
  setSelectedAppointment: (appointment: Appointment) => void;
}

export const DayView: React.FC<DayViewProps> = ({
  selectedDate,
  appointments,
  setSelectedAppointment
}) => {
  // We've removed the logging useEffect to reduce console noise

  // Create time slots for the day (e.g., 9:00 AM to 5:00 PM)
  const timeSlots = [];
  for (let hour = 9; hour <= 17; hour++) {
    const date = new Date(selectedDate);
    date.setHours(hour, 0, 0, 0);
    timeSlots.push(date);
  }

  // Filter all-day appointments
  const allDayAppointments = appointments.filter(appointment => {
    // Validate the appointment date first
    if (!(appointment.start instanceof Date) || isNaN(appointment.start.getTime())) {
      return false;
    }

    // Check if it's an all-day appointment
    return appointment.allDay === true;
  });

  return (
    <div className="mt-4">
      <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
        {selectedDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}
      </h3>

      {/* All-day events section */}
      {allDayAppointments.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden mb-4">
          <div className="p-3 bg-indigo-50 dark:bg-indigo-900/30 border-b border-indigo-100 dark:border-indigo-800">
            <h4 className="text-sm font-medium text-indigo-800 dark:text-indigo-200">All-day Events</h4>
          </div>
          <div className="p-3 dark:bg-gray-800">
            {allDayAppointments.map(appointment => (
              <div
                key={appointment.id}
                onClick={() => setSelectedAppointment({
                  ...appointment,
                  status: appointment.status === 'confirmed' ? 'confirmed' : 'pending'
                })}
                className="bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200 p-2 rounded mb-2 cursor-pointer hover:bg-indigo-200 dark:hover:bg-indigo-900/50 transition-colors"
              >
                <div className="font-medium">{appointment.title}</div>
                {appointment.source === 'google' && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Google Calendar</div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        {timeSlots.map((timeSlot, index) => {
          // Filter appointments for this specific time slot (excluding all-day events)
          const slotAppointments = appointments.filter(appointment => {
            // Validate the appointment date first
            if (!(appointment.start instanceof Date) || isNaN(appointment.start.getTime())) {
              return false;
            }

            // Skip all-day appointments
            if (appointment.allDay) {
              return false;
            }

            // ONLY check if the appointment starts at this hour
            // The parent component (CalendarPage) has already filtered by date
            return appointment.start.getHours() === timeSlot.getHours();
          });

          // We've removed time slot logging to reduce console noise

          return (
            <div
              key={index}
              className={`flex items-start p-3 border-b border-gray-200 dark:border-gray-700 ${index % 2 === 0 ? 'bg-gray-50 dark:bg-gray-700' : 'bg-white dark:bg-gray-800'}`}
            >
              <div className="w-20 text-gray-500 dark:text-gray-400 font-medium">
                {formatTime(timeSlot)}
              </div>

              <div className="flex-1">
                {slotAppointments.length > 0 ? (
                  slotAppointments.map(appointment => (
                    <div
                      key={appointment.id}
                      onClick={() => setSelectedAppointment({
                        ...appointment,
                        status: appointment.status === 'confirmed' ? 'confirmed' : 'pending'
                      })}
                      className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 p-2 rounded mb-2 cursor-pointer hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors"
                    >
                      <div className="font-medium">{appointment.title}</div>
                      <div className="text-sm">
                        {formatTime(appointment.start)} - {formatTime(appointment.end)}
                      </div>
                      {appointment.source === 'google' && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Google Calendar</div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="h-10 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 transition-colors cursor-pointer flex items-center justify-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Available</span>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
