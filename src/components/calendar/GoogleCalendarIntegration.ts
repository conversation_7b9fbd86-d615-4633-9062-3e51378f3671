import { useCallback, useEffect } from 'react';
import { googleCalendarService } from '../../services/googleCalendarService';
import { addToast } from '../../components/ui';
import { Appointment } from './CalendarTypes';

interface UseGoogleCalendarProps {
  currentDate: Date;
  viewMode: 'day' | 'week' | 'month';
  getWeekDays: () => Date[];
  setGoogleCalendarConnected: (connected: boolean) => void;
  setIsLoadingGoogleEvents: (loading: boolean) => void;
  setAppointments: React.Dispatch<React.SetStateAction<Appointment[]>>;
}

export const useGoogleCalendar = ({
  currentDate,
  viewMode,
  getWeekDays,
  setGoogleCalendarConnected,
  setIsLoadingGoogleEvents,
  setAppointments
}: UseGoogleCalendarProps) => {
  // Load Google Calendar events
  const loadGoogleCalendarEvents = useCallback(async () => {
    setIsLoadingGoogleEvents(true);

    try {
      // Calculate start and end dates based on view mode
      let startDate = new Date(currentDate);
      let endDate = new Date(currentDate);

      if (viewMode === 'month') {
        startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
      } else if (viewMode === 'week') {
        const weekDays = getWeekDays();
        startDate = weekDays[0];
        endDate = weekDays[6];
      }

      const googleEvents = await googleCalendarService.getEvents(startDate, endDate);

      // Convert Google Calendar events to appointments
      const googleAppointments = googleEvents.map(event => {
        return {
          id: `google-${event.id}`,
          title: event.summary,
          start: new Date(event.start.dateTime),
          end: new Date(event.end.dateTime),
          client: event.attendees?.length ? event.attendees[0].email : 'No attendees',
          service: event.description || 'Google Calendar Event',
          status: event.status === 'confirmed' ? 'confirmed' : 'pending',
          source: 'google' as const,
          googleEventId: event.id
        };
      });

      // Merge with existing appointments, avoiding duplicates
      setAppointments(prevAppointments => {
        const appAppointments = prevAppointments.filter(app => app.source !== 'google');
        return [...appAppointments, ...googleAppointments];
      });

      addToast('Google Calendar events loaded successfully', 'success');
    } catch (error) {
      console.error('Error loading Google Calendar events:', error);

      // Check if this is an authentication error
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('401') || errorMessage.includes('UNAUTHENTICATED') || errorMessage.includes('Invalid Credentials')) {
        addToast(
          'Your Google Calendar connection has expired. Please reconnect in Settings.',
          'error',
          10000 // Show for 10 seconds
        );

        // Set connected to false
        setGoogleCalendarConnected(false);
      }
      // Check if this is the specific API not enabled error
      else if (errorMessage.includes('Google Calendar API has not been used in project') ||
          errorMessage.includes('SERVICE_DISABLED')) {
        addToast(
          'Google Calendar API is not enabled. Please enable it in the Google Cloud Console.',
          'error',
          10000 // Show for 10 seconds
        );

        // Open the activation URL in a new tab
        const activationUrl = 'https://console.developers.google.com/apis/api/calendar-json.googleapis.com/overview?project=11367222904';
        window.open(activationUrl, '_blank');
      } else {
        addToast('Failed to load Google Calendar events', 'error');
      }
    } finally {
      setIsLoadingGoogleEvents(false);
    }
  }, [currentDate, viewMode, getWeekDays, setAppointments, setGoogleCalendarConnected, setIsLoadingGoogleEvents]);

  // Check Google Calendar connection status
  useEffect(() => {
    const checkGoogleCalendarStatus = async () => {
      try {
        const status = await googleCalendarService.getStatus();

        // Validate the token if connected
        let isTokenValid = false;
        if (status.connected) {
          isTokenValid = await googleCalendarService.validateToken();
          // If token is invalid but we're connected, try to refresh the token
          if (!isTokenValid) {
            try {
              // Attempt to refresh Google Calendar token
              const refreshResult = await googleCalendarService.refreshToken();

              // Check if the token is now valid
              isTokenValid = await googleCalendarService.validateToken();
              // If refresh was successful, show a toast
              if (isTokenValid) {
                addToast('Google Calendar connection refreshed successfully', 'success');
              }
            } catch (refreshError) {
              console.error('Error refreshing token:', refreshError);
              addToast('Failed to refresh Google Calendar connection', 'error');
            }
          }
        }

        // Only set as connected if both status is connected and token is valid
        setGoogleCalendarConnected(status.connected && isTokenValid);

        if (status.connected && isTokenValid) {
          try {
            await loadGoogleCalendarEvents();
          } catch (error) {
            console.error('Error loading Google Calendar events:', error);
            // Show a toast but don't let it break the app
            addToast('Failed to load Google Calendar events. You may need to reconnect.', 'error');
            // Set connected to false to prompt user to reconnect
            setGoogleCalendarConnected(false);
          }
        } else if (status.connected && !isTokenValid) {
          // If status is connected but token is invalid, show a message
          addToast('Your Google Calendar connection has expired. Please reconnect in Settings.', 'warning', 8000);
        }
      } catch (error) {
        console.error('Error checking Google Calendar status:', error);
      }
    };

    checkGoogleCalendarStatus();
  }, [loadGoogleCalendarEvents, setGoogleCalendarConnected]);

  return { loadGoogleCalendarEvents };
};
