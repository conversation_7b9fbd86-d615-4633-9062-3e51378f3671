import { Clock } from 'lucide-react';
import { Appointment, DAYS, formatTime } from './CalendarTypes';

interface WeekViewProps {
  weekDays: Date[];
  appointments: Appointment[];
  setSelectedAppointment: (appointment: Appointment) => void;
  setSelectedDate?: (date: Date) => void;
  setCurrentDate?: (date: Date) => void;
  setViewMode?: (mode: 'day' | 'week' | 'month') => void;
}

export const WeekView: React.FC<WeekViewProps> = ({
  weekDays,
  appointments,
  setSelectedAppointment,
  setSelectedDate,
  setCurrentDate,
  setViewMode
}) => {
  // Group appointments by day
  const appointmentsByDay: Record<string, Appointment[]> = {};
  weekDays.forEach(day => {
    const dateKey = day.toDateString();

    // Create start and end of day for comparison
    const startOfDay = new Date(day);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(day);
    endOfDay.setHours(23, 59, 59, 999);

    // Filter appointments for this day using timestamp comparison
    appointmentsByDay[dateKey] = appointments.filter(appointment => {
      const appointmentTime = appointment.start.getTime();
      return appointmentTime >= startOfDay.getTime() && appointmentTime <= endOfDay.getTime();
    }).map(appointment => ({
      ...appointment,
      status: appointment.status === 'confirmed' ? 'confirmed' : 'pending'
    }));

    // Appointments found for this day
  });

  return (
    <div className="mt-4 overflow-x-auto">
      <div className="grid grid-cols-7 gap-4 min-w-[800px]">
        {weekDays.map((day, index) => (
          <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800">
            <div
              className={`p-2 text-center border-b border-gray-200 dark:border-gray-700 ${
                day.toDateString() === new Date().toDateString()
                  ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200'
                  : 'bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
              } ${setViewMode ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600' : ''}`}
              onClick={() => {
                // If view mode functions are provided, allow clicking to switch to day view
                if (setSelectedDate && setCurrentDate && setViewMode) {
                  setSelectedDate(day);
                  setCurrentDate(day);
                  setViewMode('day');
                }
              }}
            >
              <div className="font-medium">{DAYS[day.getDay()].substring(0, 3)}</div>
              <div className="text-sm">{day.getDate()}</div>
            </div>

            <div className="p-2 h-96 overflow-y-auto">
              {appointmentsByDay[day.toDateString()]?.length > 0 ? (
                appointmentsByDay[day.toDateString()].map(appointment => (
                  <div
                    key={appointment.id}
                    onClick={() => setSelectedAppointment({
                      ...appointment,
                      status: appointment.status === 'confirmed' ? 'confirmed' : 'pending'
                    })}
                    className={`p-2 mb-2 rounded cursor-pointer text-sm ${
                      appointment.status === 'confirmed'
                        ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-900/50'
                        : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-900/50'
                    }`}
                  >
                    <div className="font-medium truncate">{appointment.title}</div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatTime(appointment.start)}
                    </div>
                  </div>
                ))
              ) : (
                <div className="h-full flex items-center justify-center">
                  <span className="text-sm text-gray-400 dark:text-gray-500">No appointments</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
