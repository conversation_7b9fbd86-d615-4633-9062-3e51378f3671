import { Appointment, DAYS, formatTime } from './CalendarTypes';

interface MonthViewProps {
  currentDate: Date;
  appointments: Appointment[];
  setSelectedDate: (date: Date) => void;
  setCurrentDate: (date: Date) => void;
  setViewMode: (mode: 'day' | 'week' | 'month') => void;
  onAddAppointment?: (date: Date) => void;
}

export const MonthView: React.FC<MonthViewProps> = ({
  currentDate,
  appointments,
  setSelectedDate,
  setCurrentDate,
  setViewMode,
  // onAddAppointment is not currently used but kept for future implementation
  // onAddAppointment
}) => {
  // Generate the calendar grid for the current month
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();

  // Determine the first day of the month
  const firstDayOfMonth = new Date(year, month, 1);
  const startingDayOfWeek = firstDayOfMonth.getDay(); // 0 for Sunday

  // Determine the number of days in the month
  const lastDayOfMonth = new Date(year, month + 1, 0);
  const daysInMonth = lastDayOfMonth.getDate();

  // Create calendar days array (including padding for days from prev/next months)
  const calendarDays = [];

  // Add previous month's days
  for (let i = 0; i < startingDayOfWeek; i++) {
    const day = new Date(year, month, -startingDayOfWeek + i + 1);
    calendarDays.push({ date: day, isCurrentMonth: false });
  }

  // Add current month's days
  for (let i = 1; i <= daysInMonth; i++) {
    const day = new Date(year, month, i);
    calendarDays.push({ date: day, isCurrentMonth: true });
  }

  // Add next month's days to fill the grid (so we have complete rows)
  const remainingDays = 42 - calendarDays.length; // 6 rows of 7 days = 42
  for (let i = 1; i <= remainingDays; i++) {
    const day = new Date(year, month + 1, i);
    calendarDays.push({ date: day, isCurrentMonth: false });
  }

  // Group appointments by date
  const appointmentsByDate: Record<string, Appointment[]> = {};
  appointments.forEach(appointment => {
    const dateKey = appointment.start.toDateString();
    if (!appointmentsByDate[dateKey]) {
      appointmentsByDate[dateKey] = [];
    }
    appointmentsByDate[dateKey].push({
      ...appointment,
      status: appointment.status === 'confirmed' ? 'confirmed' : 'pending'
    });
  });

  return (
    <div className="mt-4">
      <div className="grid grid-cols-7 text-center font-medium text-gray-600 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700 pb-2">
        {DAYS.map(day => (
          <div key={day}>{day.substring(0, 3)}</div>
        ))}
      </div>

      <div className="grid grid-cols-7 gap-1 mt-2">
        {calendarDays.map((day, index) => {
          const dateKey = day.date.toDateString();
          const dayAppointments = appointmentsByDate[dateKey] || [];
          const isToday = day.date.toDateString() === new Date().toDateString();

          return (
            <div
              key={index}
              onClick={() => {
                const newDate = new Date(day.date);
                const dateKey = newDate.toDateString();
                const dayAppointments = appointmentsByDate[dateKey] || [];

                // Check if there are any all-day events for this day (for future use)
                // We're not using dayAppointments directly here, but we're keeping it for future implementation
                // const hasAllDayEvent = dayAppointments.some(appointment => appointment.allDay === true);
                void dayAppointments; // Acknowledge the variable to prevent unused variable warning

                // Always allow viewing the day
                setSelectedDate(newDate);
                setCurrentDate(newDate);
                setViewMode('day');
              }}
              className={`border rounded-lg p-1 h-24 overflow-hidden cursor-pointer ${
                !day.isCurrentMonth
                  ? 'bg-gray-50 dark:bg-gray-900 text-gray-400 dark:text-gray-600 border-gray-200 dark:border-gray-700'
                  : isToday
                    ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 dark:text-white'
                    : 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 border-gray-200 dark:border-gray-700'
              }`}
            >
              <div className="text-right p-1 font-medium dark:text-gray-300">{day.date.getDate()}</div>

              <div className="space-y-1">
                {dayAppointments.slice(0, 2).map(appointment => (
                  <div
                    key={appointment.id}
                    className={`truncate text-xs px-1 py-0.5 rounded ${appointment.allDay ? 'bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200' : 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200'}`}
                  >
                    {appointment.allDay ? (
                      <span>{appointment.title}</span>
                    ) : (
                      <span>{formatTime(appointment.start)} {appointment.title}</span>
                    )}
                  </div>
                ))}

                {dayAppointments.length > 2 && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 px-1">
                    +{dayAppointments.length - 2} more
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
