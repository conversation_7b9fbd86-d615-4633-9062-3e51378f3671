/**
 * Shared types for calendar components
 */

export interface Appointment {
  id: string;
  title: string;
  start: Date;
  end: Date;
  client: string;
  clientId?: string;
  service: string;
  status: string;
  source?: 'app' | 'google';
  googleEventId?: string;
  googleLink?: string;
  allDay?: boolean;
}

// Constants
export const DAYS = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
export const MONTHS = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

// Helper functions
export const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
};

export const formatDate = (date: Date): string => {
  return date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' });
};
