import React, { useState } from 'react';
import { Loader2, Shield, Key } from 'lucide-react';
import { authService } from '../../services';

interface TwoFactorAuthFormProps {
  userId: string;
  email: string;
  password: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export default function TwoFactorAuthForm({
  // userId is not used currently but kept for future use
  // userId,
  email,
  password,
  onSuccess,
  onCancel
}: TwoFactorAuthFormProps) {
  const [token, setToken] = useState('');
  const [backupCode, setBackupCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [useBackupCode, setUseBackupCode] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      if (useBackupCode) {
        // Login with backup code
        if (!backupCode) {
          setError('Please enter a backup code');
          return;
        }
        await authService.login(email, password, undefined, backupCode);
      } else {
        // Login with token
        if (!token) {
          setError('Please enter the verification code');
          return;
        }
        await authService.login(email, password, token);
      }

      onSuccess();
    } catch (err) {
      // Handle rate limiting errors specifically
      if (err && typeof err === 'object' && 'status' in err && (err as {status: number}).status === 429) {
        setError(`Too many attempts. Please try again later. ${(err as {message?: string}).message || ''}`);
      } else {
        setError(err instanceof Error ? err.message : 'Authentication failed');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <Shield className="h-12 w-12 text-blue-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Two-Factor Authentication</h2>
        <p className="mt-2 text-gray-600 dark:text-gray-300">
          Please enter the verification code from your authenticator app
        </p>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {useBackupCode ? (
          <div>
            <label htmlFor="backupCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Backup Code
            </label>
            <input
              id="backupCode"
              name="backupCode"
              type="text"
              value={backupCode}
              onChange={(e) => setBackupCode(e.target.value)}
              placeholder="Enter backup code (e.g. XXXX-XXXX)"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              autoComplete="off"
              autoFocus
            />
          </div>
        ) : (
          <div>
            <label htmlFor="token" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Verification Code
            </label>
            <input
              id="token"
              name="token"
              type="text"
              value={token}
              onChange={(e) => setToken(e.target.value)}
              placeholder="Enter 6-digit code"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              maxLength={6}
              autoComplete="off"
              autoFocus
            />
          </div>
        )}

        <div className="flex justify-between">
          <button
            type="button"
            className="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
            onClick={() => setUseBackupCode(!useBackupCode)}
          >
            {useBackupCode ? 'Use verification code instead' : 'Use backup code instead'}
          </button>
        </div>

        <div className="flex gap-3">
          <button
            type="button"
            onClick={onCancel}
            className="flex-1 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Back
          </button>
          <button
            type="submit"
            disabled={isLoading || (!token && !backupCode)}
            className="flex-1 flex justify-center items-center gap-2 py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Verifying...
              </>
            ) : (
              <>
                <Key className="h-4 w-4" />
                Verify
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
