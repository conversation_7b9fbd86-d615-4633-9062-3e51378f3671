import React, { useMemo } from 'react';

interface PasswordStrengthMeterProps {
  password: string;
}

export default function PasswordStrengthMeter({ password }: PasswordStrengthMeterProps) {
  // Calculate password strength
  const strength = useMemo(() => {
    if (!password) return 0;

    let score = 0;

    // Length check
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;

    // Character variety checks
    if (/[A-Z]/.test(password)) score += 1; // Has uppercase
    if (/[a-z]/.test(password)) score += 1; // Has lowercase
    if (/[0-9]/.test(password)) score += 1; // Has number
    if (/[^A-Za-z0-9]/.test(password)) score += 1; // Has special character

    // Normalize to 0-4 scale
    return Math.min(4, Math.floor(score / 1.5));
  }, [password]);

  // Get strength label and color
  const { label, color } = useMemo(() => {
    switch (strength) {
      case 0:
        return { label: 'Very Weak', color: 'bg-red-500' };
      case 1:
        return { label: 'Weak', color: 'bg-orange-500' };
      case 2:
        return { label: 'Fair', color: 'bg-yellow-500' };
      case 3:
        return { label: 'Good', color: 'bg-blue-500' };
      case 4:
        return { label: 'Strong', color: 'bg-green-500' };
      default:
        return { label: '', color: 'bg-gray-200' };
    }
  }, [strength]);

  // Get suggestions for improving password strength
  const suggestions = useMemo(() => {
    if (!password) return [];

    const tips = [];

    if (password.length < 8) {
      tips.push('Use at least 8 characters');
    }

    if (!/[A-Z]/.test(password)) {
      tips.push('Add uppercase letters');
    }

    if (!/[a-z]/.test(password)) {
      tips.push('Add lowercase letters');
    }

    if (!/[0-9]/.test(password)) {
      tips.push('Add numbers');
    }

    if (!/[^A-Za-z0-9]/.test(password)) {
      tips.push('Add special characters');
    }

    return tips;
  }, [password]);

  if (!password) return null;

  return (
    <div className="mt-1 space-y-2">
      <div className="flex items-center gap-2">
        <div className="flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          <div
            className={`h-full ${color} transition-all duration-300 ease-in-out`}
            style={{ width: `${(strength / 4) * 100}%` }}
          />
        </div>
        <span className="text-xs font-medium text-gray-600 dark:text-gray-300">{label}</span>
      </div>

      {suggestions.length > 0 && (
        <ul className="text-xs text-gray-500 dark:text-gray-400 space-y-1 pl-4 list-disc">
          {suggestions.map((tip, index) => (
            <li key={index}>{tip}</li>
          ))}
        </ul>
      )}
    </div>
  );
}
