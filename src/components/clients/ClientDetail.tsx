import { Mail, Phone, CalendarClock, CalendarCheck2, Edit, Trash, MessageSquare } from 'lucide-react';
import { motion } from 'framer-motion';
import { Client } from '../../types/client';
// Button is not currently used but may be needed in the future
// import { Button } from '../ui';

interface ClientDetailProps {
  client: Client;
  onEdit: () => void;
  onDelete: () => void;
  onClose: () => void;
  onStartConversation?: () => void;
}

export function ClientDetail({ client, onEdit, onDelete, onClose, onStartConversation }: ClientDetailProps) {
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Not scheduled';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 }
    }
  };

  return (
    <motion.div
      className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600 flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Client Details</h3>
        <div className="flex gap-2">
          <motion.button
            onClick={onEdit}
            className="p-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 focus:outline-none"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Edit className="h-5 w-5" />
          </motion.button>
          <motion.button
            onClick={onDelete}
            className="p-2 text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 focus:outline-none"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Trash className="h-5 w-5" />
          </motion.button>
          <motion.button
            onClick={onClose}
            className="p-2 text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 focus:outline-none"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            &times;
          </motion.button>
        </div>
      </div>

      <motion.div className="p-6" variants={containerVariants}>
        <motion.div className="flex items-center justify-between mb-6" variants={itemVariants}>
          <div className="flex items-center">
            <motion.div
              className="h-16 w-16 rounded-full overflow-hidden bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 text-xl font-medium"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ type: "spring", stiffness: 300, damping: 10 }}
            >
              {client.profileImage ? (
                <img
                  src={client.profileImage}
                  alt={client.name}
                  className="h-full w-full object-cover"
                />
              ) : (
                client.name.charAt(0).toUpperCase()
              )}
            </motion.div>
            <div className="ml-4">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">{client.name}</h2>
            </div>
          </div>

          {onStartConversation && (
            <motion.button
              onClick={onStartConversation}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <MessageSquare className="h-5 w-5" />
              <span>Start Conversation</span>
            </motion.button>
          )}
        </motion.div>

        <motion.div className="grid grid-cols-1 md:grid-cols-2 gap-6" variants={containerVariants}>
          <motion.div variants={itemVariants}>
            <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
              Contact Information
            </h4>
            <div className="space-y-3">
              <motion.div
                className="flex items-center gap-2"
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <Mail className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                <a href={`mailto:${client.email}`} className="text-blue-600 dark:text-blue-400 hover:underline">
                  {client.email}
                </a>
              </motion.div>
              <motion.div
                className="flex items-center gap-2"
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <Phone className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                <a href={`tel:${client.phone}`} className="text-blue-600 dark:text-blue-400 hover:underline">
                  {client.phone}
                </a>
              </motion.div>
            </div>
          </motion.div>

          <motion.div variants={itemVariants}>
            <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
              Appointments
            </h4>
            <div className="space-y-3">
              <motion.div
                className="flex items-center gap-2"
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <CalendarClock className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                <span className="text-gray-700 dark:text-gray-300">Last Appointment: {formatDate(client.lastAppointment)}</span>
              </motion.div>
              <motion.div
                className="flex items-center gap-2"
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <CalendarCheck2 className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                <span className="text-gray-700 dark:text-gray-300">Next Appointment: {formatDate(client.nextAppointment)}</span>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>

        {client.tags.length > 0 && (
          <motion.div className="mt-6" variants={itemVariants}>
            <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
              Tags
            </h4>
            <div className="flex flex-wrap gap-2">
              {client.tags.map((tag, index) => (
                <motion.span
                  key={tag}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.1, backgroundColor: '#bfdbfe' }}
                >
                  {tag}
                </motion.span>
              ))}
            </div>
          </motion.div>
        )}

        {client.notes && (
          <motion.div className="mt-6" variants={itemVariants}>
            <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
              Notes
            </h4>
            <motion.div
              className="bg-gray-50 dark:bg-gray-700 p-4 rounded-md"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{client.notes}</p>
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    </motion.div>
  );
}
