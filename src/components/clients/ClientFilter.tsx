import { useState, useEffect, useRef } from 'react';
import { Search, Filter, X } from 'lucide-react';

interface ClientFilterProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filterTag: string | null;
  setFilterTag: (tag: string | null) => void;
  allTags: string[];
  sortField: string;
  setSortField: (field: string) => void;
  sortOrder: 'asc' | 'desc';
  setSortOrder: (order: 'asc' | 'desc') => void;
}

export function ClientFilter({
  searchQuery,
  setSearchQuery,
  filterTag,
  setFilterTag,
  allTags,
  sortField,
  setSortField,
  sortOrder,
  setSortOrder
}: ClientFilterProps) {
  const handleSortChange = (field: string) => {
    if (field === sortField) {
      // If clicking the same field, toggle the sort order
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // If clicking a different field, set it as the new sort field with ascending order
      setSortField(field);
      setSortOrder('asc');
    }
  };

  // Handle tag filter change
  const handleTagFilterChange = (tag: string | null) => {
    // Update tag filter
    // Reset to page 1 when changing tag filter
    setFilterTag(tag);
  };

  // Local state for search input with debouncing
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Debounce search query changes
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchQuery(localSearchQuery);
      // Keep focus on the input after search is executed
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }, 600); // 600ms delay

    return () => clearTimeout(timer);
  }, [localSearchQuery, setSearchQuery]);

  // Update local search when the prop changes (e.g., when cleared)
  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  return (
    <div className="p-4 border-b border-gray-200 dark:border-gray-700">
      <div className="flex flex-wrap items-center gap-4">
        {/* Search Input */}
        <div className="relative flex-grow max-w-md">
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search clients..."
            value={localSearchQuery}
            onChange={(e) => setLocalSearchQuery(e.target.value)}
            className="w-full px-4 py-2 pl-10 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600"
            // Keep focus when clicking the clear button
            onBlur={(e) => {
              // Prevent losing focus when clicking the clear button
              if (e.relatedTarget && e.relatedTarget.classList.contains('clear-search-btn')) {
                setTimeout(() => searchInputRef.current?.focus(), 0);
              }
            }}
          />
          {localSearchQuery && (
            <button
              onClick={() => {
                setLocalSearchQuery('');
                // Focus the input after clearing
                setTimeout(() => searchInputRef.current?.focus(), 0);
              }}
              className="absolute right-3 top-2.5 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 clear-search-btn"
            >
              <X className="h-5 w-5" />
            </button>
          )}
          <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400 dark:text-gray-500" />
          {searchQuery !== localSearchQuery && (
            <div className="absolute right-3 bottom-[-20px] text-xs text-blue-600 dark:text-blue-400">
              Searching...
            </div>
          )}
        </div>

        {/* Tag Filter */}
        <div className="relative">
          <select
            value={filterTag || ''}
            onChange={(e) => handleTagFilterChange(e.target.value || null)}
            className="appearance-none pl-10 pr-8 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600"
          >
            <option value="">All Tags</option>
            {allTags.map(tag => (
              <option key={tag} value={tag}>{tag}</option>
            ))}
          </select>
          <Filter className="absolute left-3 top-2.5 h-5 w-5 text-gray-400 dark:text-gray-500" />
        </div>

        {/* Sort Controls */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500 dark:text-gray-400">Sort by:</span>
          <select
            value={sortField}
            onChange={(e) => handleSortChange(e.target.value)}
            className="appearance-none px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600"
          >
            <option value="name">Name</option>
            <option value="email">Email</option>
            <option value="last_appointment">Last Appointment</option>
            <option value="next_appointment">Next Appointment</option>
          </select>
          <button
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            className="p-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            {sortOrder === 'asc' ? '↑' : '↓'}
          </button>
        </div>

        {/* Clear Filters Button (only show if filters are applied) */}
        {(searchQuery || filterTag) && (
          <button
            onClick={() => {
              setSearchQuery('');
              setFilterTag(null);
              // Focus the search input after clearing filters
              setTimeout(() => searchInputRef.current?.focus(), 0);
            }}
            className="flex items-center gap-1 px-3 py-2 text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
          >
            <X className="h-4 w-4" />
            Clear Filters
          </button>
        )}
      </div>
    </div>
  );
}
