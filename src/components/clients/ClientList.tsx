import { Mail, Phone, CalendarClock, CalendarCheck2, Edit, Trash, Eye, MessageSquare } from 'lucide-react';
import { motion } from 'framer-motion';
import { Client } from '../../types/client';
import { AnimatedList } from '../ui';

interface ClientListProps {
  clients: Client[];
  onSelectClient: (client: Client) => void;
  onEditClient: (client: Client) => void;
  onDeleteClient: (client: Client) => void;
  onChatWithClient?: (client: Client) => void;
  sortField: string;
  sortOrder: 'asc' | 'desc';
  handleSortChange: (field: string) => void;
}

export function ClientList({
  clients,
  onSelectClient,
  onEditClient,
  onDeleteClient,
  onChatWithClient,
  sortField,
  sortOrder,
  handleSortChange
}: ClientListProps) {
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Helper function for date cells (not used with the new grid layout)
  // Commented out as it's not currently used
  // const renderDateCell = (date: string | null | undefined) => (
  //   <div className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
  //     {formatDate(date ?? null)}
  //   </div>
  // );

  return (
    <div className="overflow-x-auto w-full">
      <div className="grid grid-cols-12 gap-0 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-700 rounded-t-lg">
        {/* Table Headers */}
        <div
          className="col-span-2 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer border-r border-gray-200 dark:border-gray-700"
          onClick={() => handleSortChange('name')}
        >
          <div className="flex items-center">
            <span>Name</span>
            {sortField === 'name' && (
              <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
            )}
          </div>
        </div>
        <div
          className="col-span-3 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer border-r border-gray-200 dark:border-gray-700"
          onClick={() => handleSortChange('email')}
        >
          <div className="flex items-center">
            <span>Contact</span>
            {sortField === 'email' && (
              <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
            )}
          </div>
        </div>
        <div
          className="col-span-2 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer border-r border-gray-200 dark:border-gray-700"
          onClick={() => handleSortChange('last_appointment')}
        >
          <div className="flex items-center">
            <span>Last Appointment</span>
            {sortField === 'last_appointment' && (
              <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
            )}
          </div>
        </div>
        <div
          className="col-span-2 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer border-r border-gray-200 dark:border-gray-700"
          onClick={() => handleSortChange('next_appointment')}
        >
          <div className="flex items-center">
            <span>Next Appointment</span>
            {sortField === 'next_appointment' && (
              <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
            )}
          </div>
        </div>
        <div className="col-span-2 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-700">
          Tags
        </div>
        <div className="col-span-1 px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
          Actions
        </div>
      </div>
      {/* Table Body */}
      <div className="bg-white dark:bg-gray-800">
        <AnimatedList
          items={clients}
          keyExtractor={(client) => client.id}
          renderItem={(client) => (
            <div className="grid grid-cols-12 gap-0 border-b border-l border-r border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
              {/* Name Column */}
              <div className="col-span-2 px-3 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700">
                <div className="flex items-center">
                  <motion.div
                    className="h-10 w-10 flex-shrink-0"
                    whileHover={{ scale: 1.1 }}
                    transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                  >
                    <div className="h-10 w-10 rounded-full overflow-hidden bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 font-medium">
                      {client.profileImage ? (
                        <img
                          src={client.profileImage}
                          alt={client.name}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        client.name.charAt(0).toUpperCase()
                      )}
                    </div>
                  </motion.div>
                  <div className="ml-4 truncate">
                    <div className="text-sm font-medium text-gray-900 dark:text-white truncate">{client.name}</div>
                  </div>
                </div>
              </div>

              {/* Contact Column */}
              <div className="col-span-3 px-3 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700">
                <div className="text-sm text-gray-900 dark:text-white flex flex-col gap-1">
                  <div className="flex items-center gap-1 truncate">
                    <Mail className="h-4 w-4 text-gray-400 dark:text-gray-500 flex-shrink-0" />
                    <a href={`mailto:${client.email}`} className="hover:text-blue-600 dark:hover:text-blue-400 truncate max-w-[150px] sm:max-w-[200px] md:max-w-full">
                      {client.email}
                    </a>
                  </div>
                  <div className="flex items-center gap-1 truncate">
                    <Phone className="h-4 w-4 text-gray-400 dark:text-gray-500 flex-shrink-0" />
                    <a href={`tel:${client.phone}`} className="hover:text-blue-600 dark:hover:text-blue-400 truncate">
                      {client.phone}
                    </a>
                  </div>
                </div>
              </div>

              {/* Last Appointment Column */}
              <div className="col-span-2 px-3 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                  <CalendarClock className="h-4 w-4 text-gray-400 dark:text-gray-500 flex-shrink-0" />
                  <span className="truncate">{formatDate(client.lastAppointment)}</span>
                </div>
              </div>

              {/* Next Appointment Column */}
              <div className="col-span-2 px-3 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                  <CalendarCheck2 className="h-4 w-4 text-gray-400 dark:text-gray-500 flex-shrink-0" />
                  <span className="truncate">{formatDate(client.nextAppointment)}</span>
                </div>
              </div>

              {/* Tags Column */}
              <div className="col-span-2 px-3 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700">
                <div className="flex flex-wrap gap-1">
                  {client.tags.map(tag => (
                    <motion.span
                      key={tag}
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300`}
                      whileHover={{ scale: 1.1, backgroundColor: '#bfdbfe' }}
                      transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                    >
                      {tag}
                    </motion.span>
                  ))}
                </div>
              </div>

              {/* Actions Column */}
              <div className="col-span-1 px-3 py-4 whitespace-nowrap text-center text-sm font-medium">
                <div className="flex justify-center gap-1">
                  <motion.button
                    onClick={() => onSelectClient(client)}
                    className="p-1 rounded-full text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/30"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    title="View"
                  >
                    <Eye className="h-4 w-4" />
                  </motion.button>
                  {onChatWithClient && (
                    <motion.button
                      onClick={() => onChatWithClient(client)}
                      className="p-1 rounded-full text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/30"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      title="Chat with AI"
                    >
                      <MessageSquare className="h-4 w-4" />
                    </motion.button>
                  )}
                  <motion.button
                    onClick={() => onEditClient(client)}
                    className="p-1 rounded-full text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/30"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    title="Edit"
                  >
                    <Edit className="h-4 w-4" />
                  </motion.button>
                  <motion.button
                    onClick={() => onDeleteClient(client)}
                    className="p-1 rounded-full text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/30"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    title="Delete"
                  >
                    <Trash className="h-4 w-4" />
                  </motion.button>
                </div>
              </div>
            </div>
          )}
        />

        {/* Empty State */}
        {clients.length === 0 && (
          <div className="border-b border-l border-r border-gray-200 dark:border-gray-700 px-3 py-8 text-center text-gray-500 dark:text-gray-400">
            <div className="flex flex-col items-center justify-center py-8">
              <svg className="w-12 h-12 text-gray-300 dark:text-gray-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
              <p className="text-lg font-medium text-gray-700 dark:text-gray-300">No clients found matching your criteria</p>
              <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">Try adjusting your search or filter parameters</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
