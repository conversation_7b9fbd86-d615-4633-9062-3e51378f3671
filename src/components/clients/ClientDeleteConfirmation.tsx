import { AlertTriangle } from 'lucide-react';
import { motion } from 'framer-motion';
import { Client } from '../../types/client';
import { Button } from '../ui';

interface ClientDeleteConfirmationProps {
  client: Client;
  onConfirm: () => Promise<void>;
  onCancel: () => void;
}

export function ClientDeleteConfirmation({
  client,
  onConfirm,
  onCancel
}: ClientDeleteConfirmationProps) {
  const handleConfirm = async () => {
    try {
      await onConfirm();
    } catch (error) {
      console.error('Error deleting client:', error);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring",
        damping: 25,
        stiffness: 500,
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        damping: 25,
        stiffness: 500
      }
    }
  };

  return (
    <motion.div
      className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <motion.div
        className="px-6 py-4 bg-red-50 dark:bg-red-900/20 border-b border-red-100 dark:border-red-900/30"
        variants={itemVariants}
      >
        <div className="flex items-center">
          <motion.div
            initial={{ rotate: -90 }}
            animate={{ rotate: 0 }}
            transition={{ type: "spring", stiffness: 500, damping: 15 }}
          >
            <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400 mr-3" />
          </motion.div>
          <h3 className="text-lg font-medium text-red-900 dark:text-red-300">Delete Client</h3>
        </div>
      </motion.div>

      <motion.div className="p-6" variants={itemVariants}>
        <motion.p
          className="text-gray-700 dark:text-gray-300 mb-4"
          variants={itemVariants}
        >
          Are you sure you want to delete <strong>{client.name}</strong>? This action cannot be undone.
        </motion.p>

        <motion.div
          className="mt-6 flex justify-end gap-3"
          variants={itemVariants}
        >
          <Button
            type="button"
            onClick={onCancel}
            variant="outline"
            size="md"
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleConfirm}
            variant="danger"
            size="md"
          >
            Delete
          </Button>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}
