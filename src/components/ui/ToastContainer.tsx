import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { AnimatePresence } from 'framer-motion';
import Toast, { ToastType } from './Toast';

export interface ToastMessage {
  id: string;
  message: string;
  type: ToastType;
  duration?: number;
}

// Create a global toast state
let toasts: ToastMessage[] = [];
let listeners: (() => void)[] = [];

// Function to add a toast
export function addToast(message: string, type: ToastType = 'info', duration: number = 5000) {
  const id = Math.random().toString(36).substring(2, 9);
  const toast = { id, message, type, duration };
  toasts = [...toasts, toast];
  notifyListeners();
  return id;
}

// Function to remove a toast
export function removeToast(id: string) {
  toasts = toasts.filter(toast => toast.id !== id);
  notifyListeners();
}

// Function to notify listeners of state changes
function notifyListeners() {
  listeners.forEach(listener => listener());
}

const ToastContainer: React.FC = () => {
  const [mounted, setMounted] = useState(false);
  const [localToasts, setLocalToasts] = useState<ToastMessage[]>([]);

  useEffect(() => {
    setMounted(true);
    
    // Subscribe to toast changes
    const updateToasts = () => {
      setLocalToasts([...toasts]);
    };
    
    listeners.push(updateToasts);
    updateToasts();
    
    return () => {
      listeners = listeners.filter(listener => listener !== updateToasts);
    };
  }, []);

  const handleClose = (id: string) => {
    removeToast(id);
  };

  if (!mounted) return null;

  // Create a portal to render toasts at the top level of the DOM
  return createPortal(
    <div className="fixed top-0 right-0 z-50 p-4 space-y-4 pointer-events-none">
      <AnimatePresence>
        {localToasts.map(toast => (
          <div key={toast.id} className="pointer-events-auto">
            <Toast
              message={toast.message}
              type={toast.type}
              duration={toast.duration}
              onClose={() => handleClose(toast.id)}
            />
          </div>
        ))}
      </AnimatePresence>
    </div>,
    document.body
  );
};

export default ToastContainer;
