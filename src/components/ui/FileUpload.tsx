import React, { useState, useRef } from 'react';
import { Upload, X } from 'lucide-react';

interface FileUploadProps {
  onChange: (file: File | null) => void;
  value: File | string | null;
  accept?: string;
  maxSizeMB?: number;
  className?: string;
  previewClassName?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onChange,
  value,
  accept = 'image/*',
  maxSizeMB = 5,
  className = '',
  previewClassName = ''
}) => {
  const [error, setError] = useState<string | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Convert MB to bytes
  const maxSizeBytes = maxSizeMB * 1024 * 1024;

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setError(null);

    if (!file) {
      onChange(null);
      setPreview(null);
      setSelectedFile(null);
      return;
    }

    // Validate file size
    if (file.size > maxSizeBytes) {
      setError(`File size exceeds ${maxSizeMB}MB limit`);
      onChange(null);

      return;
    }

    // Create preview URL
    const previewUrl = URL.createObjectURL(file);
    setPreview(previewUrl);
    onChange(file);
  };

  // Handle removing the file
  const handleRemove = () => {
    onChange(null);
    setPreview(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Initialize preview if value is a string URL
  React.useEffect(() => {
    if (typeof value === 'string' && value) {
      setPreview(value);
    } else if (!value) {
      setPreview(null);
    }
  }, [value]);

  return (
    <div className={`relative ${className}`}>
      {/* File input (hidden) */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept={accept}
        className="hidden"
        id="file-upload"
      />

      {/* Preview area */}
      {preview ? (
        <div className={`relative ${previewClassName}`}>
          <img
            src={preview}
            alt="Preview"
            className="w-full h-full object-cover rounded-lg"
          />
          <button
            type="button"
            onClick={handleRemove}
            className="absolute top-1 right-1 bg-white dark:bg-gray-700 rounded-full p-1 shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
            aria-label="Remove file"
          >
            <X className="h-4 w-4 text-gray-700 dark:text-gray-300" />
          </button>
        </div>
      ) : (
        <label
          htmlFor="file-upload"
          className="w-full h-full flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <Upload className="h-8 w-8 text-gray-400 dark:text-gray-500 mb-1" />
          <span className="text-xs text-center text-gray-500 dark:text-gray-400">Upload Logo</span>
          <span className="text-xs text-center text-gray-400 dark:text-gray-500 mt-1">
            Max {maxSizeMB}MB
          </span>

        </label>
      )}

      {/* Error message */}
      {error && (
        <div className="text-red-500 dark:text-red-400 text-xs mt-1">{error}</div>
      )}


    </div>
  );
};

export default FileUpload;
