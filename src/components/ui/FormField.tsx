import React, { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle } from 'lucide-react';

interface FormFieldProps {
  id: string;
  label: string;
  type?: 'text' | 'email' | 'tel' | 'url' | 'password' | 'textarea' | 'select';
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  placeholder?: string;
  required?: boolean;
  className?: string;
  validate?: (value: string) => { isValid: boolean; message?: string };
  debounceMs?: number;
  showValidationIcon?: boolean;
  options?: Array<{ value: string; label: string }>;
}

export function FormField({
  id,
  label,
  type = 'text',
  value,
  onChange,
  onBlur,
  placeholder,
  required = false,
  className = '',
  validate,
  debounceMs = 300,
  showValidationIcon = true,
  options = []
}: FormFieldProps) {
  const [validationState, setValidationState] = useState<{ isValid: boolean; message?: string }>({ isValid: true });
  const [isDirty, setIsDirty] = useState(false);
  const [debouncedValue, setDebouncedValue] = useState(value);

  // Update debounced value after delay
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, debounceMs);

    return () => {
      clearTimeout(handler);
    };
  }, [value, debounceMs]);

  // Validate when debounced value changes
  useEffect(() => {
    if (validate && isDirty) {
      setValidationState(validate(debouncedValue));
    }
  }, [debouncedValue, validate, isDirty]);

  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setIsDirty(true);
    if (validate) {
      setValidationState(validate(value));
    }
    if (onBlur) {
      onBlur(e);
    }
  };

  const isTextarea = type === 'textarea';
  const isSelect = type === 'select';
  const showError = isDirty && !validationState.isValid;
  const showSuccess = isDirty && validationState.isValid && value !== '';

  const inputClasses = `mt-1 block w-full border ${
    showError
      ? 'border-red-300 dark:border-red-700 focus:ring-red-500 focus:border-red-500'
      : showSuccess
        ? 'border-green-300 dark:border-green-700 focus:ring-green-500 focus:border-green-500'
        : 'border-gray-300 dark:border-gray-700 focus:ring-blue-500 focus:border-blue-500'
  } rounded-md shadow-sm py-2 px-3 focus:outline-none dark:bg-gray-800 dark:text-white ${
    showValidationIcon ? 'pr-10' : ''
  }`;

  return (
    <div className={`${className}`}>
      <label htmlFor={id} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      <div className="relative">
        {isTextarea ? (
          <textarea
            id={id}
            name={id}
            value={value}
            onChange={onChange}
            onBlur={handleBlur}
            placeholder={placeholder}
            required={required}
            className={inputClasses}
            rows={4}
          />
        ) : isSelect ? (
          <select
            id={id}
            name={id}
            value={value}
            onChange={onChange}
            onBlur={handleBlur}
            required={required}
            className={inputClasses}
          >
            {options.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        ) : (
          <input
            id={id}
            name={id}
            type={type}
            value={value}
            onChange={onChange}
            onBlur={handleBlur}
            placeholder={placeholder}
            required={required}
            className={inputClasses}
          />
        )}

        {showValidationIcon && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            {showError && (
              <AlertCircle className="h-5 w-5 text-red-500" aria-hidden="true" />
            )}
            {showSuccess && (
              <CheckCircle className="h-5 w-5 text-green-500" aria-hidden="true" />
            )}
          </div>
        )}
      </div>

      {showError && validationState.message && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400" id={`${id}-error`}>
          {validationState.message}
        </p>
      )}
    </div>
  );
}
