import { billingService } from '../services/billingService';

// Mock the apiClient
jest.mock('../lib/apiClient', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn()
  }
}));

// Import the mocked apiClient
import { apiClient } from '../lib/apiClient';

describe('billingService', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('downloadInvoice', () => {
    it('should handle mock invoices correctly', async () => {
      // Test with a mock invoice ID
      const mockInvoiceId = 'inv_123';
      const result = await billingService.downloadInvoice(mockInvoiceId);

      // Verify that apiClient.get was not called
      expect(apiClient.get).not.toHaveBeenCalled();

      // Verify the result
      expect(result).toEqual({
        invoice_pdf: null,
        hosted_invoice_url: null,
        message: 'This is a mock invoice and cannot be downloaded. Configure Stripe to enable real invoice downloads.'
      });
    });

    it('should call the API for real invoices', async () => {
      // Mock the API response
      const mockResponse = {
        invoice_pdf: 'https://example.com/invoice.pdf',
        hosted_invoice_url: 'https://example.com/invoice'
      };
      (apiClient.get as jest.Mock).mockResolvedValue(mockResponse);

      // Test with a real invoice ID
      const realInvoiceId = 'in_1234567890';
      const result = await billingService.downloadInvoice(realInvoiceId);

      // Verify that apiClient.get was called with the correct URL
      expect(apiClient.get).toHaveBeenCalledWith(`/dashboard/billing/invoices/${realInvoiceId}/download`);

      // Verify the result
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors correctly', async () => {
      // Mock the API error
      const mockError = {
        response: {
          status: 404,
          statusText: 'Not Found'
        }
      };
      (apiClient.get as jest.Mock).mockRejectedValue(mockError);

      // Test with a real invoice ID
      const realInvoiceId = 'in_1234567890';

      // Verify that the error is thrown
      await expect(billingService.downloadInvoice(realInvoiceId)).rejects.toThrow(
        'Failed to download invoice: 404 Not Found'
      );
    });

    it('should handle Stripe configuration errors', async () => {
      // Mock the API error for Stripe not configured
      const mockError = {
        response: {
          status: 501,
          data: {
            detail: 'Stripe is not configured'
          }
        }
      };
      (apiClient.get as jest.Mock).mockRejectedValue(mockError);

      // Test with a real invoice ID
      const realInvoiceId = 'in_1234567890';

      // Verify that the error is thrown with the correct message
      await expect(billingService.downloadInvoice(realInvoiceId)).rejects.toThrow(
        'Stripe is not configured. Please add your Stripe API keys to the .env file.'
      );
    });
  });
});
