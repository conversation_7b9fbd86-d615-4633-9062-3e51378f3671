import { createBrowserRouter, RouterProvider, Route, createRoutesFromElements } from 'react-router-dom';
import NotFoundPage from './pages/NotFoundPage';
import LandingPage from './pages/LandingPage';
import AuthPage from './pages/AuthPage';
import DashboardPage from './pages/dashboard/DashboardPage';
import CalendarPage from './pages/dashboard/CalendarPage';
import MessagesPage from './pages/dashboard/MessagesPage';
import ClientsPage from './pages/dashboard/ClientsPage';
import ClientChatPage from './pages/dashboard/ClientChatPage';
import AIConfigPage from './pages/dashboard/AIConfigPage';
import SettingsPage from './pages/dashboard/SettingsPage';
import GoogleCallbackPage from './pages/dashboard/GoogleCallbackPage';


import { ProtectedRoute } from './components/ProtectedRoute';
import { AuthProvider } from './components/AuthProvider';
import { ToastContainer, ToastProvider } from './components/ui';
import { ThemeProvider } from './contexts/ThemeContext';

// Create router with React Router v7
const router = createBrowserRouter(
  createRoutesFromElements(
    <Route element={<AuthProvider />}>
      <Route path="/" element={<LandingPage />} />
      <Route path="/login" element={<AuthPage />} />
      <Route path="/auth" element={<AuthPage />} />

      <Route path="/dashboard" element={<ProtectedRoute />}>
        <Route index element={<DashboardPage />} />
        <Route path="calendar" element={<CalendarPage />} />
        <Route path="messages" element={<MessagesPage />} />
        <Route path="clients" element={<ClientsPage />} />
        <Route path="clients/:clientId/chat" element={<ClientChatPage />} />
        <Route path="ai-config" element={<AIConfigPage />} />

        <Route path="settings" element={<SettingsPage />} />
        <Route path="settings/google-callback" element={<GoogleCallbackPage />} />
      </Route>
      {/* Catch-all route for 404 errors */}
      <Route path="*" element={<NotFoundPage />} />
    </Route>
  )
);

function App() {
  return (
    <ThemeProvider>
      <ToastProvider>
        <ToastContainer />
        <RouterProvider router={router} />
      </ToastProvider>
    </ThemeProvider>
  );
}

export default App;
