/**
 * Simple in-memory rate limiter to prevent abuse
 * 
 * This implementation uses a sliding window approach to track
 * the number of operations within a specified time window.
 */

interface RateLimiterOptions {
  maxOperations: number;  // Maximum number of operations allowed
  windowMs: number;       // Time window in milliseconds
}

interface RateLimiterState {
  operations: number[];   // Timestamps of operations
  blocked: boolean;       // Whether the user is currently blocked
  blockedUntil: number;   // Timestamp when the block expires
}

class RateLimiter {
  private options: RateLimiterOptions;
  private state: Map<string, RateLimiterState>;
  
  constructor(options: RateLimiterOptions) {
    this.options = {
      maxOperations: options.maxOperations || 5,
      windowMs: options.windowMs || 60000, // Default: 1 minute
    };
    this.state = new Map();
  }
  
  /**
   * Check if an operation is allowed for a specific key
   * @param key Unique identifier (e.g., user ID)
   * @returns Object with allowed status and optional error message
   */
  checkOperation(key: string): { allowed: boolean; message?: string; remainingTime?: number } {
    const now = Date.now();
    
    // Initialize state for this key if it doesn't exist
    if (!this.state.has(key)) {
      this.state.set(key, {
        operations: [],
        blocked: false,
        blockedUntil: 0
      });
    }
    
    const keyState = this.state.get(key)!;
    
    // Check if currently blocked
    if (keyState.blocked) {
      if (now >= keyState.blockedUntil) {
        // Block has expired, reset state
        keyState.blocked = false;
        keyState.operations = [];
      } else {
        // Still blocked
        const remainingTime = Math.ceil((keyState.blockedUntil - now) / 1000);
        return {
          allowed: false,
          message: `Too many operations. Please try again in ${remainingTime} seconds.`,
          remainingTime
        };
      }
    }
    
    // Remove operations outside the time window
    keyState.operations = keyState.operations.filter(
      timestamp => now - timestamp < this.options.windowMs
    );
    
    // Check if limit is reached
    if (keyState.operations.length >= this.options.maxOperations) {
      // Block for twice the window time
      keyState.blocked = true;
      keyState.blockedUntil = now + (this.options.windowMs * 2);
      
      const remainingTime = Math.ceil((keyState.blockedUntil - now) / 1000);
      return {
        allowed: false,
        message: `Rate limit exceeded. Please try again in ${remainingTime} seconds.`,
        remainingTime
      };
    }
    
    // Add current operation
    keyState.operations.push(now);
    
    // Calculate remaining operations
    const remainingOperations = this.options.maxOperations - keyState.operations.length;
    
    return {
      allowed: true,
      message: remainingOperations <= 2 ? 
        `Warning: You have ${remainingOperations} operations remaining in this time window.` : 
        undefined
    };
  }
  
  /**
   * Reset the rate limiter state for a specific key
   * @param key Unique identifier to reset
   */
  reset(key: string): void {
    this.state.delete(key);
  }
  
  /**
   * Get the current state for a specific key
   * @param key Unique identifier
   */
  getState(key: string): RateLimiterState | undefined {
    return this.state.get(key);
  }
}

// Create a singleton instance for client creation
export const clientCreationLimiter = new RateLimiter({
  maxOperations: 5,  // Maximum 5 client creations
  windowMs: 60000    // Within a 1-minute window
});

export default RateLimiter;
