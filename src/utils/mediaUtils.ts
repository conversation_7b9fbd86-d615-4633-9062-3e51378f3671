/**
 * Media Utilities
 * This file contains utility functions for handling media files.
 */
import { apiClient } from '../lib/apiClient';

import { getAuthenticatedMediaUrl } from '../services/whatsappService';

/**
 * Format a media URL with authentication if needed
 * @param url The media URL to format
 * @returns A properly formatted URL for display that uses our backend proxy
 */
export function formatMediaUrl(url: string | undefined): string {
  if (!url) return '';

  // Check if this is a local URL
  if (url.startsWith('blob:') || url.startsWith('data:')) {
    return url; // Return as is for local URLs
  }

  // Check if this is already a proxied URL
  if (url.includes('/media/whatsapp-proxy')) {
    // If it's already proxied, check if it has a cache-busting parameter
    if (!url.includes('_cb=')) {
      // Add cache-busting parameter
      const separator = url.includes('?') ? '&' : '?';
      return `${url}${separator}_cb=${Date.now()}`;
    }
    return url; // Already proxied with cache-busting
  }

  // Use a hardcoded URL for the backend proxy
  // This avoids issues with apiClient.defaults.baseURL being undefined
  const backendUrl = window.location.protocol + '//' + window.location.hostname;
  const port = '8000'; // Backend port

  // Construct the full URL to the proxy endpoint with cache-busting
  return `${backendUrl}:${port}/media/whatsapp-proxy?url=${encodeURIComponent(url)}&_cb=${Date.now()}`;
}

/**
 * Get the appropriate media type from a MIME type
 * @param mimeType The MIME type string
 * @returns The general media type (image, video, audio, document)
 */
export function getMediaTypeFromMime(mimeType: string | undefined): 'image' | 'video' | 'audio' | 'document' | 'text' {
  if (!mimeType) return 'document';

  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  if (mimeType.startsWith('audio/')) return 'audio';

  return 'document';
}

/**
 * Check if a media URL is valid by making a GET request
 * @param url The media URL to check
 * @returns A promise that resolves to true if the URL is valid, false otherwise
 */
export async function isMediaUrlValid(url: string | undefined): Promise<boolean> {
  if (!url) return false;

  // Local URLs are always considered valid
  if (url.startsWith('blob:') || url.startsWith('data:')) {
    return true;
  }

  try {
    // Format the URL to use our proxy
    const formattedUrl = formatMediaUrl(url);

    // Make a HEAD request first to check if the URL is valid without downloading content
    try {
      const headResponse = await fetch(formattedUrl, {
        method: 'HEAD',
        cache: 'no-store' // Prevent caching
      });

      if (headResponse.ok) {
        // Media URL validation successful (HEAD)
        return true;
      }
    } catch (headError) {
      console.warn('HEAD request failed, falling back to GET:', headError);
    }

    // If HEAD fails, fall back to a GET request with a small range
    const response = await fetch(formattedUrl, {
      method: 'GET',
      headers: {
        'Range': 'bytes=0-1024' // Only request the first 1KB
      },
      cache: 'no-store' // Prevent caching
    });

    // Media URL validation response (GET)

    // Accept 206 Partial Content as valid too (response to Range request)
    return response.ok || response.status === 206;
  } catch (error) {
    console.error('Error checking media URL:', error);
    return false;
  }
}
