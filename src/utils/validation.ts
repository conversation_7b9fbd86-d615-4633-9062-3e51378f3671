/**
 * Email validation
 */
export function validateEmail(email: string) {
  if (!email) {
    return { isValid: false, message: 'Email is required' };
  }

  // Simple email regex to avoid escape character issues
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!emailRegex.test(email)) {
    return { isValid: false, message: 'Please enter a valid email address' };
  }

  return { isValid: true };
}

/**
 * Password validation
 */
export function validatePassword(password: string) {
  if (!password) {
    return { isValid: false, message: 'Password is required' };
  }

  if (password.length < 8) {
    return { isValid: false, message: 'Password must be at least 8 characters long' };
  }

  // Check for at least one uppercase letter
  if (!/[A-Z]/.test(password)) {
    return { isValid: false, message: 'Password must contain at least one uppercase letter' };
  }

  // Check for at least one lowercase letter
  if (!/[a-z]/.test(password)) {
    return { isValid: false, message: 'Password must contain at least one lowercase letter' };
  }

  // Check for at least one number
  if (!/[0-9]/.test(password)) {
    return { isValid: false, message: 'Password must contain at least one number' };
  }

  return { isValid: true };
}

/**
 * URL validation
 */
export function validateUrl(url: string) {
  if (!url) {
    return { isValid: false, message: 'URL is required' };
  }

  // Add protocol if missing
  let urlWithProtocol = url;
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    urlWithProtocol = 'https://' + url;
  }

  try {
    // Use URL constructor for validation
    new URL(urlWithProtocol);
    return { isValid: true };
  } catch {
    return {
      isValid: false,
      message: 'Please enter a valid website URL (e.g., example.com or https://example.com)'
    };
  }
}

/**
 * Phone number validation
 */
export function validatePhone(phone: string) {
  if (!phone) {
    return { isValid: false, message: 'Phone number is required' };
  }

  // Remove all non-numeric characters except +
  const cleanedPhone = phone.replace(/[^\d+]/g, '');

  // Basic phone validation - must be at least 10 digits
  // and can optionally start with + followed by country code
  const phoneRegex = /^\+?[0-9]{10,15}$/;

  if (!phoneRegex.test(cleanedPhone)) {
    return {
      isValid: false,
      message: 'Please enter a valid phone number (e.g., +1234567890 or 1234567890)'
    };
  }

  return { isValid: true };
}

/**
 * Name validation
 */
export function validateName(name: string) {
  if (!name) {
    return { isValid: false, message: 'Name is required' };
  }

  if (name.trim().length < 2) {
    return { isValid: false, message: 'Name must be at least 2 characters long' };
  }

  return { isValid: true };
}

/**
 * Required field validation
 */
export function validateRequired(value: string, fieldName: string) {
  if (!value || value.trim() === '') {
    return {
      isValid: false,
      message: `${fieldName} is required`
    };
  }

  return { isValid: true };
}

/**
 * Date validation
 */
export function validateDate(date: string) {
  if (!date) {
    return { isValid: false, message: 'Date is required' };
  }

  const dateObj = new Date(date);

  if (isNaN(dateObj.getTime())) {
    return { isValid: false, message: 'Please enter a valid date' };
  }

  return { isValid: true };
}

/**
 * Business name validation
 */
export function validateBusinessName(name: string) {
  if (!name) {
    return { isValid: false, message: 'Business name is required' };
  }

  if (name.trim().length < 2) {
    return { isValid: false, message: 'Business name must be at least 2 characters long' };
  }

  return { isValid: true };
}

/**
 * Address validation
 */
export function validateAddress(address: string) {
  if (!address) {
    return { isValid: false, message: 'Address is required' };
  }

  if (address.trim().length < 5) {
    return { isValid: false, message: 'Please enter a valid address' };
  }

  return { isValid: true };
}

/**
 * Currency validation
 */
export function validateCurrency(currency: string) {
  if (!currency) {
    return { isValid: false, message: 'Currency is required' };
  }

  // Basic validation for currency code (3 letters)
  if (!/^[A-Z]{3}$/.test(currency)) {
    return { isValid: false, message: 'Please enter a valid currency code (e.g., USD, EUR)' };
  }

  return { isValid: true };
}
