import { UserProfile } from '../services/userService';

// Define weights for different profile fields
const FIELD_WEIGHTS = {
  businessName: 25, // Essential
  email: 25,        // Essential
  phone: 15,        // Important
  address: 10,      // Useful
  website: 10,      // Useful
  currency: 5,      // Basic
  logo: 10          // Visual identity
};

// Total possible score
const TOTAL_SCORE = Object.values(FIELD_WEIGHTS).reduce((sum, weight) => sum + weight, 0);

/**
 * Calculate profile completeness percentage
 * @param profile User profile data
 * @returns Object with percentage and suggestions
 */
export function calculateProfileCompleteness(profile: Partial<UserProfile>): {
  percentage: number;
  score: number;
  maxScore: number;
  missingFields: string[];
  nextSuggestion: string;
} {
  let score = 0;
  const missingFields: string[] = [];

  // Check each field and add its weight if it has a value
  if (profile.businessName) score += FIELD_WEIGHTS.businessName;
  else missingFields.push('Business Name');

  if (profile.email) score += FIELD_WEIGHTS.email;
  else missingFields.push('Email');

  if (profile.phone) score += FIELD_WEIGHTS.phone;
  else missingFields.push('Phone Number');

  if (profile.address) score += FIELD_WEIGHTS.address;
  else missingFields.push('Address');

  if (profile.website) score += FIELD_WEIGHTS.website;
  else missingFields.push('Website');

  // Currency is always selected in the form, so we'll check if it's a non-empty string
  if (profile.currency && profile.currency.trim() !== '') score += FIELD_WEIGHTS.currency;
  else missingFields.push('Currency');

  if (profile.logo) score += FIELD_WEIGHTS.logo;
  else missingFields.push('Business Logo');

  // Calculate percentage
  const percentage = Math.round((score / TOTAL_SCORE) * 100);

  // Determine the next suggestion based on missing fields
  // Prioritize essential fields first
  let nextSuggestion = '';
  if (missingFields.length > 0) {
    if (missingFields.includes('Business Name')) {
      nextSuggestion = 'Add your business name';
    } else if (missingFields.includes('Email')) {
      nextSuggestion = 'Add your email address';
    } else if (missingFields.includes('Phone Number')) {
      nextSuggestion = 'Add your phone number';
    } else if (missingFields.includes('Business Logo')) {
      nextSuggestion = 'Upload your business logo';
    } else if (missingFields.includes('Address')) {
      nextSuggestion = 'Add your business address';
    } else if (missingFields.includes('Website')) {
      nextSuggestion = 'Add your website URL';
    } else if (missingFields.includes('Currency')) {
      nextSuggestion = 'Select your preferred currency';
    }
  }

  return {
    percentage,
    score,
    maxScore: TOTAL_SCORE,
    missingFields,
    nextSuggestion
  };
}

/**
 * Get color based on completeness percentage
 */
export function getCompletenessColor(percentage: number): string {
  if (percentage < 40) return 'text-red-500';
  if (percentage < 70) return 'text-yellow-500';
  if (percentage < 100) return 'text-blue-500';
  return 'text-green-500';
}

/**
 * Get status text based on completeness percentage
 */
export function getCompletenessStatus(percentage: number): string {
  if (percentage < 40) return 'Incomplete';
  if (percentage < 70) return 'Getting there';
  if (percentage < 100) return 'Almost complete';
  return 'Complete';
}
