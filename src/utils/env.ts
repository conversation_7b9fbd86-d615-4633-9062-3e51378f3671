/**
 * Utility functions for working with environment variables
 */

/**
 * Get an environment variable with a fallback value
 * @param key The environment variable key
 * @param fallback The fallback value if the environment variable is not set
 * @returns The environment variable value or the fallback
 */
export const getEnv = (key: string, fallback: string = ''): string => {
  // For Vite, environment variables are exposed on import.meta.env
  if (import.meta.env && typeof import.meta.env[key] === 'string') {
    return import.meta.env[key];
  }
  
  // For Node.js
  if (typeof process !== 'undefined' && process.env && typeof process.env[key] === 'string') {
    return process.env[key];
  }
  
  return fallback;
};

/**
 * Get the Stripe publishable key
 * @returns The Stripe publishable key
 */
export const getStripePublishableKey = (): string => {
  return getEnv('VITE_STRIPE_PUBLISHABLE_KEY');
};

/**
 * Check if Stripe is configured
 * @returns True if Stripe is configured
 */
export const isStripeConfigured = (): boolean => {
  const key = getStripePublishableKey();
  return key !== '' && key !== undefined;
};
