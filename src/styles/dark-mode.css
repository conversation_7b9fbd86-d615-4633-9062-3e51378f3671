/* Dark Mode Utility Classes */

/* Common background colors */
.dark-bg-primary {
  @apply bg-gray-900 dark:bg-gray-900;
}

.dark-bg-secondary {
  @apply bg-white dark:bg-gray-800;
}

.dark-bg-tertiary {
  @apply bg-gray-50 dark:bg-gray-700;
}

.dark-bg-hover {
  @apply hover:bg-gray-100 dark:hover:bg-gray-700;
}

/* Common text colors */
.dark-text-primary {
  @apply text-gray-900 dark:text-white;
}

.dark-text-secondary {
  @apply text-gray-700 dark:text-gray-300;
}

.dark-text-muted {
  @apply text-gray-500 dark:text-gray-400;
}

/* Common border colors */
.dark-border {
  @apply border-gray-200 dark:border-gray-700;
}

.dark-border-light {
  @apply border-gray-100 dark:border-gray-800;
}

/* Form elements */
.dark-input {
  @apply bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600;
}

.dark-select {
  @apply bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-blue-500 dark:focus:border-blue-600;
}

/* Button styles */
.dark-btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white;
}

.dark-btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200;
}

.dark-btn-danger {
  @apply bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 text-white;
}

/* Card styles */
.dark-card {
  @apply bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg;
}

/* Modal styles */
.dark-modal {
  @apply bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700;
}

.dark-modal-header {
  @apply border-b border-gray-200 dark:border-gray-700;
}

.dark-modal-footer {
  @apply border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900;
}

/* Table styles */
.dark-table-header {
  @apply bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
}

.dark-table-row {
  @apply border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700;
}

.dark-table-cell {
  @apply text-gray-900 dark:text-gray-200;
}

/* Chat specific styles */
.dark-message-outgoing {
  @apply bg-blue-500 dark:bg-blue-600 text-white;
}

.dark-message-incoming {
  @apply bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200;
}

.dark-message-timestamp {
  @apply text-gray-500 dark:text-gray-400;
}

/* Calendar specific styles */
.dark-calendar-day {
  @apply bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700;
}

.dark-calendar-day-selected {
  @apply bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-300;
}

.dark-calendar-day-today {
  @apply bg-yellow-50 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-300;
}

.dark-calendar-event {
  @apply bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200;
}
