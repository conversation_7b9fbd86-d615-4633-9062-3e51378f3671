.react-grid-layout {
  position: relative;
  transition: height 200ms ease;
}

.react-grid-item {
  transition: all 200ms ease;
  transition-property: left, top;
}

.react-grid-item.cssTransforms {
  transition-property: transform;
}

.react-grid-item.resizing {
  z-index: 1;
  will-change: width, height;
}

.react-grid-item.react-draggable-dragging {
  transition: none;
  z-index: 3;
  will-change: transform;
}

.react-grid-item.dropping {
  visibility: hidden;
}

.react-grid-item.react-grid-placeholder {
  background: #3b82f6;
  opacity: 0.2;
  transition-duration: 100ms;
  z-index: 2;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.react-grid-item > .react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  cursor: se-resize;
}

.react-grid-item > .react-resizable-handle::after {
  content: "";
  position: absolute;
  right: 3px;
  bottom: 3px;
  width: 5px;
  height: 5px;
  border-right: 2px solid rgba(0, 0, 0, 0.4);
  border-bottom: 2px solid rgba(0, 0, 0, 0.4);
}

.react-resizable-hide > .react-resizable-handle {
  display: none;
}

/* Dashboard widget styles */
.dashboard-widget {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Dark mode support */
.dark .dashboard-widget {
  background-color: #1f2937;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

.dashboard-widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.dark .dashboard-widget-header {
  border-bottom: 1px solid #374151;
}

.dashboard-widget-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.dark .dashboard-widget-title {
  color: #f3f4f6;
}

.dashboard-widget-content {
  padding: 1.5rem;
  flex: 1;
  overflow: auto;
}

/* Edit mode styles */
.edit-mode .dashboard-widget {
  border: 2px dashed #93c5fd;
}

.dark .edit-mode .dashboard-widget {
  border: 2px dashed #3b82f6;
}

.edit-mode .react-grid-item:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark .edit-mode .react-grid-item:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .react-grid-item {
    width: 100% !important;
    position: static !important;
    transform: none !important;
    margin-bottom: 1rem;
  }

  .react-grid-layout {
    height: auto !important;
  }
}

/* Prevent horizontal scrolling */
.dashboard-grid {
  max-width: 100%;
  margin: 0 auto;
  overflow-x: hidden !important;
}

.react-grid-layout {
  width: 100% !important;
}

/* Simple transitions */
.react-grid-item {
  transition: transform 0.15s ease !important;
}

/* Edit mode effect */
.edit-mode .react-grid-item {
  cursor: move;
}

/* Dragging effect */
.react-grid-item.react-grid-placeholder {
  background: rgba(59, 130, 246, 0.1) !important;
  border: 2px dashed #3b82f6 !important;
  border-radius: 12px !important;
  opacity: 0.7 !important;
}

.react-grid-item.react-draggable-dragging {
  z-index: 100 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
  opacity: 0.95 !important;
}

/* Edit mode cursor */
.edit-mode .react-grid-item {
  cursor: move;
}

/* Override cursor for widget remove button */
.edit-mode .react-grid-item .widget-remove-button,
.edit-mode .react-grid-item .widget-remove-button button {
  cursor: pointer !important;
}

/* Fix for stats cards */
.dashboard-widget .stats-card {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 16px 16px 16px 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Ensure content is visible */
.dashboard-widget-content {
  min-height: 100px;
}

/* Fix for chart containers */
.recharts-responsive-container {
  min-height: 200px;
}

/* Widget remove button styles */
.widget-remove-button {
  position: relative;
  z-index: 100;
  cursor: pointer !important;
  pointer-events: auto !important;
}

.widget-remove-button button {
  cursor: pointer !important;
  pointer-events: auto !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Dark mode styles for widgets */
.dark .react-grid-item > div {
  background-color: #1f2937;
  border-color: #374151;
}

.dark .react-grid-item p {
  color: #e5e7eb;
}

.dark .react-grid-item h2,
.dark .react-grid-item h3 {
  color: #f3f4f6;
}

.dark .react-grid-placeholder {
  background: rgba(59, 130, 246, 0.2) !important;
  border: 2px dashed #3b82f6 !important;
}

/* Confirmation dialog dark mode */
.dark .confirmation-dialog {
  background-color: #1f2937;
  color: #f3f4f6;
  border-color: #374151;
}

.dark .confirmation-dialog-header {
  border-color: #374151;
}

.dark .confirmation-dialog-footer {
  background-color: #111827;
  border-color: #374151;
}
