/**
 * Application Configuration
 * This file contains global configuration settings for the application.
 */

// API URL from environment variables or default to localhost
export const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

// WhatsApp instance name - imported from whatsapp/config.ts
// This is kept for backward compatibility
import { getInstanceName } from './services/whatsapp/config';
export const getWhatsAppInstance = getInstanceName;

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 20;
export const DEFAULT_PAGE = 1;

// Debounce delay for search inputs (in milliseconds)
export const SEARCH_DEBOUNCE_DELAY = 500;

// Maximum file upload size (in bytes)
export const MAX_UPLOAD_SIZE = 5 * 1024 * 1024; // 5MB
