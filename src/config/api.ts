/**
 * API Configuration
 * This file contains configuration settings for API endpoints.
 */

// API URL from environment variables or default to localhost
export const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

// Evolution API URL from environment variables or default to localhost
export const EVOLUTION_API_URL = import.meta.env.VITE_EVOLUTION_API_URL || 'http://localhost:8080';

// API timeout in milliseconds
export const API_TIMEOUT = 30000; // 30 seconds

// Maximum number of retries for API calls
export const MAX_RETRIES = 3;

// Delay between retries (in milliseconds)
export const RETRY_DELAY = 2000;
