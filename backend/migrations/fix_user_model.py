#!/usr/bin/env python3
import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Fix the User model by adding the missing JSON import"""
    try:
        # Path to the user model file
        user_model_path = '/app/app/models/user.py'
        
        # Read the current content
        with open(user_model_path, 'r') as f:
            content = f.read()
        
        # Check if the import is already there
        if 'from sqlalchemy.types import JSON' in content:
            logger.info("JSON import already exists in user.py")
            return 0
        
        # Add the import
        import_line = 'from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, UUID\n'
        new_import_line = 'from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, UUID\nfrom sqlalchemy.types import JSON\n'
        
        # Replace the import line
        new_content = content.replace(import_line, new_import_line)
        
        # Write the updated content
        with open(user_model_path, 'w') as f:
            f.write(new_content)
        
        logger.info("Successfully added JSON import to user.py")
        return 0
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
