"""add settings to notification preferences

Revision ID: add_settings_to_notification_preferences
Revises:
Create Date: 2025-04-13 18:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '38d603ece296'
down_revision = 'c0f652bb8884'
branch_labels = None
depends_on = None


def upgrade():
    # Add settings column to notification_preferences table
    op.add_column('notification_preferences', sa.Column('settings', postgresql.JSONB(astext_type=sa.Text()), nullable=True))


def downgrade():
    # Remove settings column from notification_preferences table
    op.drop_column('notification_preferences', 'settings')
