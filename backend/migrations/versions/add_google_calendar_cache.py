"""add_google_calendar_cache_table

Revision ID: add_google_calendar_cache
Revises: manual_ai_config_tables
Create Date: 2023-05-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_google_calendar_cache'
down_revision = 'manual_ai_config_tables'
branch_labels = None
depends_on = None


def upgrade():
    # Create google_calendar_cache table
    op.create_table('google_calendar_cache',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('event_id', sa.String(), nullable=False),
        sa.Column('event_data', sa.JSON(), nullable=False),
        sa.Column('start_time', sa.DateTime(), nullable=False),
        sa.Column('end_time', sa.DateTime(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_google_calendar_cache_event_id'), 'google_calendar_cache', ['event_id'], unique=False)
    op.create_index(op.f('ix_google_calendar_cache_start_time'), 'google_calendar_cache', ['start_time'], unique=False)
    op.create_index(op.f('ix_google_calendar_cache_end_time'), 'google_calendar_cache', ['end_time'], unique=False)


def downgrade():
    # Drop google_calendar_cache table
    op.drop_index(op.f('ix_google_calendar_cache_end_time'), table_name='google_calendar_cache')
    op.drop_index(op.f('ix_google_calendar_cache_start_time'), table_name='google_calendar_cache')
    op.drop_index(op.f('ix_google_calendar_cache_event_id'), table_name='google_calendar_cache')
    op.drop_table('google_calendar_cache')
