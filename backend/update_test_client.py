"""
Update test client name
"""

import sys
import os
from uuid import UUID

# Add the parent directory to the path so we can import the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models.client import Client

def update_test_client():
    """Update test client name"""
    # Create a database session
    db = SessionLocal()
    
    try:
        # User ID for the test user
        user_id = UUID("b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7")  # <EMAIL>
        
        # Find the test client
        client = db.query(Client).filter(
            Client.user_id == user_id,
            Client.phone.like("%1234567890%")
        ).first()
        
        if not client:
            print(f"No test client found for user {user_id}")
            return
        
        # Update the client name
        client.name = "<PERSON>"
        db.commit()
        
        print(f"Updated test client name to '<PERSON>' for user {user_id}")
    
    except Exception as e:
        db.rollback()
        print(f"Error updating test client: {str(e)}")
    
    finally:
        db.close()

if __name__ == "__main__":
    update_test_client()
