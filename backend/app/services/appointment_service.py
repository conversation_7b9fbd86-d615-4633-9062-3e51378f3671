"""
Service for managing appointments, including recurring appointments and conflict detection
"""

from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
from uuid import UUID
import json
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.models.appointment import Appointment, AppointmentStatus, RecurrenceFrequency
from app.models.client import Client
from app.models.service import Service

class AppointmentService:
    """Service for managing appointments"""

    def __init__(self, db: Session):
        """Initialize the appointment service"""
        self.db = db

    def create_appointment(self, appointment_data: Dict) -> Appointment:
        """
        Create a new appointment

        Args:
            appointment_data: Appointment data

        Returns:
            Created appointment
        """
        # Check for conflicts
        conflicts = self.check_for_conflicts(
            user_id=appointment_data["user_id"],
            start_time=appointment_data["start_time"],
            end_time=appointment_data["end_time"],
            appointment_id=None  # No appointment ID for new appointments
        )

        # Create appointment
        appointment = Appointment(**appointment_data)

        # Mark as having conflicts if any were found
        if conflicts:
            appointment.has_conflict = True
            appointment.conflict_notes = f"Conflicts with {len(conflicts)} other appointment(s)"

        self.db.add(appointment)
        self.db.commit()
        self.db.refresh(appointment)

        # Create recurring appointments if needed
        if appointment.is_recurring and appointment.recurrence_frequency:
            self._create_recurring_instances(appointment)

        return appointment

    def update_appointment(self, appointment_id: UUID, appointment_data: Dict) -> Appointment:
        """
        Update an appointment

        Args:
            appointment_id: Appointment ID
            appointment_data: Updated appointment data

        Returns:
            Updated appointment
        """
        # Get the appointment
        appointment = self.db.query(Appointment).filter(Appointment.id == appointment_id).first()
        if not appointment:
            raise ValueError(f"Appointment not found: {appointment_id}")

        # Check if this is a recurring appointment
        is_recurring_update = appointment.is_recurring or appointment_data.get("is_recurring", False)

        # Check for conflicts if time is changing
        if "start_time" in appointment_data or "end_time" in appointment_data:
            start_time = appointment_data.get("start_time", appointment.start_time)
            end_time = appointment_data.get("end_time", appointment.end_time)

            conflicts = self.check_for_conflicts(
                user_id=appointment.user_id,
                start_time=start_time,
                end_time=end_time,
                appointment_id=appointment_id  # Exclude this appointment from conflict check
            )

            # Update conflict status
            appointment.has_conflict = bool(conflicts)
            if conflicts:
                appointment.conflict_notes = f"Conflicts with {len(conflicts)} other appointment(s)"
            else:
                appointment.conflict_notes = None

        # Handle recurring appointment updates
        if is_recurring_update and "recurrence_frequency" in appointment_data:
            # If this is a parent appointment, update all child appointments
            if not appointment.recurrence_parent_id:
                # Delete existing child appointments
                self.db.query(Appointment).filter(
                    Appointment.recurrence_parent_id == appointment_id
                ).delete()

                # Update the parent appointment
                for key, value in appointment_data.items():
                    setattr(appointment, key, value)

                self.db.commit()
                self.db.refresh(appointment)

                # Create new child appointments
                self._create_recurring_instances(appointment)
            else:
                # This is a child appointment, update the parent and regenerate all children
                parent_id = appointment.recurrence_parent_id
                parent = self.db.query(Appointment).filter(Appointment.id == parent_id).first()

                if parent:
                    # Update the parent appointment
                    for key, value in appointment_data.items():
                        if key not in ["recurrence_parent_id"]:  # Don't change the parent relationship
                            setattr(parent, key, value)

                    # Delete all child appointments
                    self.db.query(Appointment).filter(
                        Appointment.recurrence_parent_id == parent_id
                    ).delete()

                    self.db.commit()
                    self.db.refresh(parent)

                    # Create new child appointments
                    self._create_recurring_instances(parent)

                    # Return the updated parent
                    return parent
        else:
            # Regular appointment update
            for key, value in appointment_data.items():
                setattr(appointment, key, value)

            self.db.commit()
            self.db.refresh(appointment)

        return appointment

    def delete_appointment(self, appointment_id: UUID, delete_series: bool = False) -> bool:
        """
        Delete an appointment

        Args:
            appointment_id: Appointment ID
            delete_series: Whether to delete the entire series for recurring appointments

        Returns:
            True if successful
        """
        # Get the appointment
        appointment = self.db.query(Appointment).filter(Appointment.id == appointment_id).first()
        if not appointment:
            raise ValueError(f"Appointment not found: {appointment_id}")

        # Handle recurring appointments
        if appointment.is_recurring or appointment.recurrence_parent_id:
            if delete_series:
                # Delete the entire series
                parent_id = appointment.id if not appointment.recurrence_parent_id else appointment.recurrence_parent_id

                # Delete all child appointments
                self.db.query(Appointment).filter(
                    Appointment.recurrence_parent_id == parent_id
                ).delete()

                # Delete the parent appointment
                self.db.query(Appointment).filter(Appointment.id == parent_id).delete()
            else:
                # Delete just this appointment
                appointment.status = AppointmentStatus.CANCELLED

                # If this is a parent, mark it as cancelled but keep the children
                if not appointment.recurrence_parent_id:
                    # Update the first child to be the new parent
                    first_child = self.db.query(Appointment).filter(
                        Appointment.recurrence_parent_id == appointment_id,
                        Appointment.start_time > datetime.now()
                    ).order_by(Appointment.start_time).first()

                    if first_child:
                        # Make this the new parent
                        first_child.recurrence_parent_id = None
                        first_child.is_recurring = True
                        first_child.recurrence_frequency = appointment.recurrence_frequency
                        first_child.recurrence_interval = appointment.recurrence_interval
                        first_child.recurrence_end_date = appointment.recurrence_end_date
                        first_child.recurrence_count = appointment.recurrence_count
                        first_child.recurrence_days = appointment.recurrence_days

                        # Update all other children to point to the new parent
                        self.db.query(Appointment).filter(
                            Appointment.recurrence_parent_id == appointment_id,
                            Appointment.id != first_child.id
                        ).update({"recurrence_parent_id": first_child.id})
                else:
                    # This is a child appointment, just mark it as cancelled
                    appointment.status = AppointmentStatus.CANCELLED
        else:
            # Regular appointment deletion
            self.db.delete(appointment)

        self.db.commit()
        return True

    def get_appointment(self, appointment_id: UUID) -> Optional[Appointment]:
        """
        Get an appointment by ID

        Args:
            appointment_id: Appointment ID

        Returns:
            Appointment or None if not found
        """
        return self.db.query(Appointment).filter(Appointment.id == appointment_id).first()

    def get_appointments(
        self,
        user_id: UUID,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        client_id: Optional[UUID] = None,
        status: Optional[AppointmentStatus] = None,
        include_cancelled: bool = False
    ) -> List[Appointment]:
        """
        Get appointments for a user

        Args:
            user_id: User ID
            start_date: Start date for filtering
            end_date: End date for filtering
            client_id: Client ID for filtering
            status: Status for filtering
            include_cancelled: Whether to include cancelled appointments

        Returns:
            List of appointments
        """
        # Base query
        query = self.db.query(Appointment).filter(Appointment.user_id == user_id)

        # Apply filters
        if start_date:
            query = query.filter(Appointment.end_time >= start_date)

        if end_date:
            query = query.filter(Appointment.start_time <= end_date)

        if client_id:
            query = query.filter(Appointment.client_id == client_id)

        if status:
            query = query.filter(Appointment.status == status)
        elif not include_cancelled:
            query = query.filter(Appointment.status != AppointmentStatus.CANCELLED)

        # Order by start time
        query = query.order_by(Appointment.start_time)

        return query.all()

    def check_for_conflicts(
        self,
        user_id: UUID,
        start_time: datetime,
        end_time: datetime,
        appointment_id: Optional[UUID] = None
    ) -> List[Appointment]:
        """
        Check for appointment conflicts

        Args:
            user_id: User ID
            start_time: Start time
            end_time: End time
            appointment_id: Appointment ID to exclude from the check

        Returns:
            List of conflicting appointments
        """
        # Base query for active appointments
        query = self.db.query(Appointment).filter(
            Appointment.user_id == user_id,
            Appointment.status != AppointmentStatus.CANCELLED,
            # Overlapping time ranges
            and_(
                Appointment.start_time < end_time,
                Appointment.end_time > start_time
            )
        )

        # Exclude the current appointment if provided
        if appointment_id:
            query = query.filter(Appointment.id != appointment_id)

        return query.all()

    async def sync_with_google_calendar(self, user_id: UUID) -> Dict:
        """
        Sync appointments with Google Calendar

        Args:
            user_id: User ID

        Returns:
            Sync results
        """
        # Import here to avoid circular imports
        from app.services.google_calendar_service import get_google_calendar_service

        # Get the Google Calendar service
        google_calendar_service = get_google_calendar_service(self.db)

        # Sync appointments with Google Calendar
        return await google_calendar_service.sync_appointments_with_google(user_id)

    def _create_recurring_instances(self, parent_appointment: Appointment) -> List[Appointment]:
        """
        Create instances of a recurring appointment

        Args:
            parent_appointment: Parent appointment

        Returns:
            List of created appointments
        """
        if not parent_appointment.is_recurring or not parent_appointment.recurrence_frequency:
            return []

        created_appointments = []
        start_date = parent_appointment.start_time

        # Determine the end date for recurrence
        end_date = None
        if parent_appointment.recurrence_end_date:
            end_date = parent_appointment.recurrence_end_date
        elif parent_appointment.recurrence_count:
            # Calculate end date based on count and frequency
            if parent_appointment.recurrence_frequency == RecurrenceFrequency.DAILY:
                end_date = start_date + timedelta(days=parent_appointment.recurrence_count * parent_appointment.recurrence_interval)
            elif parent_appointment.recurrence_frequency == RecurrenceFrequency.WEEKLY:
                end_date = start_date + timedelta(weeks=parent_appointment.recurrence_count * parent_appointment.recurrence_interval)
            elif parent_appointment.recurrence_frequency == RecurrenceFrequency.BIWEEKLY:
                end_date = start_date + timedelta(weeks=2 * parent_appointment.recurrence_count * parent_appointment.recurrence_interval)
            elif parent_appointment.recurrence_frequency == RecurrenceFrequency.MONTHLY:
                # Approximate months as 30 days
                end_date = start_date + timedelta(days=30 * parent_appointment.recurrence_count * parent_appointment.recurrence_interval)
            elif parent_appointment.recurrence_frequency == RecurrenceFrequency.YEARLY:
                # Approximate years as 365 days
                end_date = start_date + timedelta(days=365 * parent_appointment.recurrence_count * parent_appointment.recurrence_interval)
        else:
            # Default to 1 year of recurrence
            end_date = start_date + timedelta(days=365)

        # Calculate the duration of the appointment
        duration = parent_appointment.end_time - parent_appointment.start_time

        # Generate recurring instances
        current_date = start_date
        count = 0
        max_instances = 100  # Safety limit

        while current_date < end_date and count < max_instances:
            # Skip the first instance (it's the parent)
            if count > 0:
                # Calculate the next occurrence
                if parent_appointment.recurrence_frequency == RecurrenceFrequency.DAILY:
                    current_date = start_date + timedelta(days=count * parent_appointment.recurrence_interval)
                elif parent_appointment.recurrence_frequency == RecurrenceFrequency.WEEKLY:
                    current_date = start_date + timedelta(weeks=count * parent_appointment.recurrence_interval)
                elif parent_appointment.recurrence_frequency == RecurrenceFrequency.BIWEEKLY:
                    current_date = start_date + timedelta(weeks=2 * count * parent_appointment.recurrence_interval)
                elif parent_appointment.recurrence_frequency == RecurrenceFrequency.MONTHLY:
                    # Handle month calculation (this is simplified)
                    new_month = start_date.month + (count * parent_appointment.recurrence_interval)
                    new_year = start_date.year + (new_month - 1) // 12
                    new_month = ((new_month - 1) % 12) + 1

                    # Try to maintain the same day, but handle month length differences
                    try:
                        current_date = start_date.replace(year=new_year, month=new_month)
                    except ValueError:
                        # Handle case where the day doesn't exist in the target month
                        if start_date.day > 28:
                            # Use the last day of the month
                            if new_month == 2:
                                # February
                                if (new_year % 4 == 0 and new_year % 100 != 0) or new_year % 400 == 0:
                                    # Leap year
                                    current_date = start_date.replace(year=new_year, month=new_month, day=29)
                                else:
                                    current_date = start_date.replace(year=new_year, month=new_month, day=28)
                            elif new_month in [4, 6, 9, 11]:
                                # 30-day months
                                current_date = start_date.replace(year=new_year, month=new_month, day=30)
                            else:
                                # 31-day months
                                current_date = start_date.replace(year=new_year, month=new_month, day=31)
                elif parent_appointment.recurrence_frequency == RecurrenceFrequency.YEARLY:
                    current_date = start_date.replace(year=start_date.year + count * parent_appointment.recurrence_interval)

                # Calculate the end time based on the duration
                current_end_date = current_date + duration

                # Create the recurring instance
                instance = Appointment(
                    user_id=parent_appointment.user_id,
                    client_id=parent_appointment.client_id,
                    service_id=parent_appointment.service_id,
                    start_time=current_date,
                    end_time=current_end_date,
                    status=parent_appointment.status,
                    notes=parent_appointment.notes,
                    is_recurring=False,  # Child instances are not themselves recurring
                    recurrence_parent_id=parent_appointment.id,
                    synced_with_google=False
                )

                # Check for conflicts
                conflicts = self.check_for_conflicts(
                    user_id=parent_appointment.user_id,
                    start_time=current_date,
                    end_time=current_end_date,
                    appointment_id=None
                )

                if conflicts:
                    instance.has_conflict = True
                    instance.conflict_notes = f"Conflicts with {len(conflicts)} other appointment(s)"

                self.db.add(instance)
                created_appointments.append(instance)

            count += 1

        self.db.commit()

        # Refresh all created appointments
        for appointment in created_appointments:
            self.db.refresh(appointment)

        return created_appointments
