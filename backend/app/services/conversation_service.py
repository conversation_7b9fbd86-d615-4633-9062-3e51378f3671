"""
Service for managing conversation contexts and messages
"""

import logging
import json
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import desc

from app.models.ai_context import AIConversationContext, ConversationMessage
from app.models.client import Client
from app.models.user import User
from app.services.language_detection import detect_language
from app.services.intent_detection import detect_intent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConversationService:
    """Service for managing conversation contexts and messages"""
    
    def __init__(self, db: Session):
        """Initialize the conversation service"""
        self.db = db
    
    def get_or_create_context(self, user_id: UUID, client_id: UUID) -> AIConversationContext:
        """
        Get or create a conversation context for a user and client
        
        Args:
            user_id: User ID
            client_id: Client ID
            
        Returns:
            Conversation context
        """
        # Check if context exists
        context = self.db.query(AIConversationContext).filter(
            AIConversationContext.user_id == user_id,
            AIConversationContext.client_id == client_id
        ).first()
        
        if not context:
            # Create new context
            context = AIConversationContext(
                user_id=user_id,
                client_id=client_id,
                context_data={},
                conversation_language="en",
                message_count=0
            )
            self.db.add(context)
            self.db.commit()
            self.db.refresh(context)
        
        return context
    
    def add_message(
        self, 
        conversation_id: UUID, 
        content: str, 
        role: str, 
        user_id: Optional[UUID] = None, 
        client_id: Optional[UUID] = None,
        metadata: Optional[Dict] = None
    ) -> ConversationMessage:
        """
        Add a message to a conversation
        
        Args:
            conversation_id: Conversation ID
            content: Message content
            role: Message role (user, assistant, system)
            user_id: User ID (optional)
            client_id: Client ID (optional)
            metadata: Additional metadata (optional)
            
        Returns:
            Created message
        """
        # Get conversation context
        context = self.db.query(AIConversationContext).filter(
            AIConversationContext.id == conversation_id
        ).first()
        
        if not context:
            raise ValueError(f"Conversation context not found: {conversation_id}")
        
        # Detect language if it's a user message
        detected_language = None
        detected_intent = None
        
        if role == "user":
            # Detect language
            try:
                detected_language = detect_language(content)
            except Exception as e:
                logger.error(f"Error detecting language: {str(e)}")
            
            # Detect intent
            try:
                detected_intent = detect_intent(content)
            except Exception as e:
                logger.error(f"Error detecting intent: {str(e)}")
            
            # Update conversation context
            context.message_count += 1
            context.last_message_at = datetime.now()
            
            if detected_language:
                context.conversation_language = detected_language
            
            if detected_intent:
                context.last_intent = detected_intent
                
                # Check for booking intent
                if "booking" in detected_intent or "appointment" in detected_intent:
                    context.has_booking_intent = True
                    context.booking_stage = "initial"
        
        # Create message
        message = ConversationMessage(
            conversation_id=conversation_id,
            user_id=user_id,
            client_id=client_id,
            content=content,
            role=role,
            detected_language=detected_language,
            detected_intent=detected_intent,
            message_metadata=metadata or {}
        )
        
        self.db.add(message)
        self.db.commit()
        self.db.refresh(message)
        
        return message
    
    def get_conversation_messages(
        self, 
        conversation_id: UUID, 
        limit: int = 50, 
        offset: int = 0
    ) -> List[ConversationMessage]:
        """
        Get messages for a conversation
        
        Args:
            conversation_id: Conversation ID
            limit: Maximum number of messages to return
            offset: Offset for pagination
            
        Returns:
            List of messages
        """
        return self.db.query(ConversationMessage).filter(
            ConversationMessage.conversation_id == conversation_id
        ).order_by(
            desc(ConversationMessage.created_at)
        ).offset(offset).limit(limit).all()
    
    def get_conversation_history(
        self, 
        conversation_id: UUID, 
        limit: int = 10
    ) -> List[Dict]:
        """
        Get conversation history in a format suitable for AI models
        
        Args:
            conversation_id: Conversation ID
            limit: Maximum number of messages to return
            
        Returns:
            List of message dictionaries with role and content
        """
        messages = self.db.query(ConversationMessage).filter(
            ConversationMessage.conversation_id == conversation_id,
            ConversationMessage.role.in_(["user", "assistant"])  # Exclude system messages
        ).order_by(
            ConversationMessage.created_at
        ).limit(limit).all()
        
        return [
            {"role": msg.role, "content": msg.content}
            for msg in messages
        ]
    
    def update_context_data(self, conversation_id: UUID, data: Dict) -> AIConversationContext:
        """
        Update the context data for a conversation
        
        Args:
            conversation_id: Conversation ID
            data: Data to update
            
        Returns:
            Updated conversation context
        """
        context = self.db.query(AIConversationContext).filter(
            AIConversationContext.id == conversation_id
        ).first()
        
        if not context:
            raise ValueError(f"Conversation context not found: {conversation_id}")
        
        # Update context data
        context_data = context.context_data or {}
        context_data.update(data)
        context.context_data = context_data
        
        self.db.commit()
        self.db.refresh(context)
        
        return context
    
    def update_booking_data(self, conversation_id: UUID, booking_data: Dict, stage: str) -> AIConversationContext:
        """
        Update booking data for a conversation
        
        Args:
            conversation_id: Conversation ID
            booking_data: Booking data to update
            stage: Current booking stage
            
        Returns:
            Updated conversation context
        """
        context = self.db.query(AIConversationContext).filter(
            AIConversationContext.id == conversation_id
        ).first()
        
        if not context:
            raise ValueError(f"Conversation context not found: {conversation_id}")
        
        # Update booking data
        current_data = context.booking_data or {}
        current_data.update(booking_data)
        
        context.booking_data = current_data
        context.booking_stage = stage
        
        self.db.commit()
        self.db.refresh(context)
        
        return context

# Create placeholder functions for language and intent detection
# These will be replaced with actual implementations later
def detect_language(text: str) -> str:
    """
    Detect the language of a text
    
    Args:
        text: Text to detect language for
        
    Returns:
        Language code (e.g., 'en', 'es')
    """
    # Simple placeholder implementation
    # In a real implementation, this would use a language detection library
    lower_text = text.lower()
    
    # Check for Spanish keywords
    spanish_keywords = ["hola", "gracias", "buenos días", "buenas tardes", "cómo estás"]
    if any(keyword in lower_text for keyword in spanish_keywords):
        return "es"
    
    # Check for Catalan keywords
    catalan_keywords = ["bon dia", "bona tarda", "adéu", "gràcies", "com estàs"]
    if any(keyword in lower_text for keyword in catalan_keywords):
        return "ca"
    
    # Default to English
    return "en"

def detect_intent(text: str) -> str:
    """
    Detect the intent of a message
    
    Args:
        text: Text to detect intent for
        
    Returns:
        Intent name
    """
    # Simple placeholder implementation
    # In a real implementation, this would use an NLU model
    lower_text = text.lower()
    
    # Check for booking intent
    booking_keywords = ["book", "appointment", "schedule", "reserve", "slot", "time", "available"]
    if any(keyword in lower_text for keyword in booking_keywords):
        return "booking"
    
    # Check for information intent
    info_keywords = ["info", "information", "tell me about", "what is", "how does", "explain"]
    if any(keyword in lower_text for keyword in info_keywords):
        return "information"
    
    # Check for greeting intent
    greeting_keywords = ["hello", "hi", "hey", "good morning", "good afternoon", "greetings"]
    if any(keyword in lower_text for keyword in greeting_keywords):
        return "greeting"
    
    # Default to general
    return "general"
