"""
WhatsApp service for interacting with Evolution API
"""

import requests
import time
import logging
import json
import asyncio
import io
import qrcode
import base64
from typing import Dict, List, Optional, Any, Tuple, Union
from uuid import UUID

from fastapi import HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.config import settings
from app.models.client import Client
from app.services.ai_assistant import AIAssistant
from app.services.conversation_service import ConversationService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Evolution API headers
EVOLUTION_HEADERS = {
    "apikey": settings.EVOLUTION_API_KEY,
    "Content-Type": "application/json"
}

class WhatsAppService:
    """Service for interacting with WhatsApp via Evolution API"""

    def __init__(self, db: Session):
        """Initialize the WhatsApp service"""
        self.db = db
        self.base_url = settings.EVOLUTION_API_URL
        self.headers = EVOLUTION_HEADERS
        self.max_retries = 3
        self.retry_delay = 2  # seconds

    async def get_connection_status(self, user_id: str) -> Dict:
        """
        Get the connection status of a WhatsApp instance

        Args:
            user_id: User ID (used as instance name)

        Returns:
            Connection status information
        """
        try:
            # Check if the instance exists
            instances = await self._fetch_instances()
            instance_exists = any(
                isinstance(i, dict) and
                'instance' in i and
                i['instance'].get('instanceName') == user_id
                for i in instances
            )

            if not instance_exists:
                return {
                    "connected": False,
                    "message": "WhatsApp instance not found"
                }

            # Check connection state
            state_response = await self._make_request(
                "GET",
                f"/instance/connectionState/{user_id}"
            )

            if not state_response:
                return {
                    "connected": False,
                    "message": "Failed to get connection state"
                }

            state = state_response.get('instance', {}).get('state')

            if state != 'open':
                return {
                    "connected": False,
                    "status": state,
                    "message": f"WhatsApp is not connected (status: {state})"
                }

            # Get instance information
            instances_response = await self._make_request(
                "GET",
                "/instance/fetchInstances"
            )

            if not instances_response:
                return {
                    "connected": True,
                    "phone": "Unknown",
                    "name": "Unknown",
                    "status": state
                }

            user_instance = next((i for i in instances_response if i.get("name") == user_id), None)

            if not user_instance:
                return {
                    "connected": True,
                    "phone": "Unknown",
                    "name": "Unknown",
                    "status": state
                }

            # Extract phone number from ownerJid (format: "<EMAIL>")
            owner_jid = user_instance.get("ownerJid", "")
            phone_number = "Unknown"
            if owner_jid and "@" in owner_jid:
                phone_number = "+" + owner_jid.split("@")[0]

            return {
                "connected": True,
                "phone": phone_number,
                "name": user_instance.get("profileName", "Unknown"),
                "status": state
            }

        except Exception as e:
            logger.error(f"Error checking WhatsApp status: {str(e)}")
            return {"connected": False, "message": f"Error: {str(e)}"}

    async def connect_whatsapp(self, user_id: str) -> Dict:
        """
        Connect to WhatsApp by creating an instance and generating a QR code

        Args:
            user_id: User ID (used as instance name)

        Returns:
            QR code information or error message
        """
        try:
            logger.info(f"=== Starting WhatsApp connection process for user {user_id} ===")

            # Step 1: Check if instance exists
            logger.info("Step 1: Checking if instance already exists...")
            instances = await self._fetch_instances()

            instance_exists = any(
                isinstance(i, dict) and
                'instance' in i and
                i['instance'].get('instanceName') == user_id
                for i in instances
            )

            # Step 2: If instance exists, try to connect to it
            if instance_exists:
                logger.info("Step 2: Instance exists, trying to connect to it...")

                # Check connection state
                state_response = await self._make_request(
                    "GET",
                    f"/instance/connectionState/{user_id}"
                )

                connection_state = None
                if state_response:
                    connection_state = state_response.get('instance', {}).get('state')
                    logger.info(f"Connection state: {connection_state}")

                    # If already connected, return success
                    if connection_state == 'open':
                        logger.info("Instance is already connected!")
                        return {"success": True, "message": "WhatsApp is already connected"}

                # Try to get QR code for existing instance
                logger.info("Trying to get QR code for existing instance...")
                qr_response = await self._make_request(
                    "GET",
                    f"/instance/connect/{user_id}"
                )

                qr_code_str = None
                if qr_response:
                    # Get the QR code string
                    qr_code_str = qr_response.get("code")

                    # Check if we have a pairing code instead
                    if not qr_code_str:
                        pairing_code = qr_response.get("pairingCode")
                        if pairing_code:
                            logger.info(f"Got pairing code: {pairing_code}")
                            qr_code_str = f"https://wa.me/qr/{pairing_code}"

                # If we got a QR code, return it
                if qr_code_str:
                    logger.info("Successfully got QR code for existing instance")
                    return self._generate_qr_response(qr_code_str)

                # If we couldn't get a QR code, delete the instance and create a new one
                logger.info("Failed to get QR code for existing instance, deleting it...")

                # Step 3: Delete the existing instance
                await self._delete_instance(user_id)

            # Step 4: Create a new instance with QR code
            logger.info("Step 4: Creating new instance with QR code...")
            create_response = await self._make_request(
                "POST",
                "/instance/create",
                json={
                    "instanceName": user_id,
                    "token": user_id,
                    "qrcode": True,
                    "integration": "WHATSAPP-BAILEYS"
                }
            )

            if not create_response:
                return {"success": False, "message": "Failed to create WhatsApp instance"}

            # Step 5: Get the QR code for the new instance
            logger.info("Step 5: Getting QR code for new instance...")

            # Wait for the instance to initialize
            await asyncio.sleep(3)

            # Connect to the instance to get QR code
            connect_response = await self._make_request(
                "GET",
                f"/instance/connect/{user_id}"
            )

            if not connect_response:
                return {"success": False, "message": "Failed to connect to WhatsApp instance"}

            # Get the QR code string
            qr_code_str = connect_response.get("code")

            # Check if we have a pairing code instead
            if not qr_code_str:
                pairing_code = connect_response.get("pairingCode")
                if pairing_code:
                    logger.info(f"Got pairing code: {pairing_code}")
                    qr_code_str = f"https://wa.me/qr/{pairing_code}"

            if not qr_code_str:
                return {"success": False, "message": "Failed to get QR code"}

            # Generate QR code image and return it
            return self._generate_qr_response(qr_code_str)

        except Exception as e:
            logger.error(f"Error connecting to WhatsApp: {str(e)}")
            return {"success": False, "message": f"Error: {str(e)}"}

    async def send_message(self, user_id: str, phone: str, message: str, media_url: Optional[str] = None) -> Dict:
        """
        Send a WhatsApp message

        Args:
            user_id: User ID (used as instance name)
            phone: Phone number to send message to
            message: Message text
            media_url: Optional URL to media to send

        Returns:
            Response from WhatsApp API
        """
        try:
            # Ensure phone number is in the correct format
            if phone.startswith("+"):
                phone = phone[1:]

            # Check if we need to send media
            if media_url:
                # Send media message
                response = await self._make_request(
                    "POST",
                    f"/message/sendMedia/{user_id}",
                    json={
                        "number": phone,
                        "mediaUrl": media_url,
                        "caption": message
                    }
                )
            else:
                # Send text message
                response = await self._make_request(
                    "POST",
                    f"/message/sendText/{user_id}",
                    json={
                        "number": phone,
                        "options": {
                            "delay": 1200,
                            "presence": "composing"
                        },
                        "textMessage": {
                            "text": message
                        }
                    }
                )

            if not response:
                return {"success": False, "message": "Failed to send message"}

            return {"success": True, "message": "Message sent successfully", "data": response}

        except Exception as e:
            logger.error(f"Error sending WhatsApp message: {str(e)}")
            return {"success": False, "message": f"Error: {str(e)}"}

    async def process_incoming_message(self, data: Dict) -> Dict:
        """
        Process an incoming WhatsApp message

        Args:
            data: Webhook data from Evolution API

        Returns:
            Processing result
        """
        try:
            # Extract message data
            if not data or "data" not in data:
                return {"success": False, "message": "Invalid webhook data"}

            message_data = data["data"]

            # Check if this is a message
            if message_data.get("msgType") != "conversation":
                return {"success": False, "message": "Not a conversation message"}

            # Extract message details
            instance_name = data.get("instance", {}).get("instanceName")
            if not instance_name:
                return {"success": False, "message": "Missing instance name"}

            # Get user ID from instance name
            user_id = instance_name

            # Get sender information
            sender = message_data.get("key", {}).get("remoteJid", "")
            if not sender or "@" not in sender:
                return {"success": False, "message": "Invalid sender information"}

            # Extract phone number from sender (format: "<EMAIL>")
            phone_number = sender.split("@")[0]
            if not phone_number:
                return {"success": False, "message": "Invalid phone number"}

            # Format phone number with + prefix
            formatted_phone = "+" + phone_number

            # Get message content
            message_content = message_data.get("message", {}).get("conversation", "")
            if not message_content:
                return {"success": False, "message": "Empty message content"}

            # Find or create client based on phone number
            client = self._find_or_create_client(user_id, formatted_phone)
            if not client:
                return {"success": False, "message": "Failed to find or create client"}

            # Process message with AI assistant
            ai_response = await self._process_with_ai(user_id, client.id, message_content)

            # Send response back to WhatsApp
            if ai_response:
                await self.send_message(user_id, formatted_phone, ai_response)

            return {
                "success": True,
                "message": "Message processed successfully",
                "client_id": str(client.id),
                "ai_response": ai_response
            }

        except Exception as e:
            logger.error(f"Error processing incoming WhatsApp message: {str(e)}")
            return {"success": False, "message": f"Error: {str(e)}"}

    async def check_whatsapp_contacts(self, user_id: str, phone_numbers: List[str]) -> Dict:
        """
        Check which phone numbers are registered on WhatsApp

        Args:
            user_id: User ID (used as instance name)
            phone_numbers: List of phone numbers to check

        Returns:
            List of WhatsApp contacts
        """
        try:
            # Ensure phone numbers are in the correct format
            formatted_numbers = []
            for phone in phone_numbers:
                if phone.startswith("+"):
                    phone = phone[1:]
                formatted_numbers.append(phone)

            # Check contacts
            response = await self._make_request(
                "POST",
                f"/chat/whatsappNumbers/{user_id}",
                json={"numbers": formatted_numbers}
            )

            if not response:
                return {"success": False, "message": "Failed to check WhatsApp contacts"}

            return {"success": True, "contacts": response}

        except Exception as e:
            logger.error(f"Error checking WhatsApp contacts: {str(e)}")
            return {"success": False, "message": f"Error: {str(e)}"}

    async def get_whatsapp_contacts(self, user_id: str, limit: int = 20, page: int = 1) -> Dict:
        """
        Get WhatsApp contacts for a user

        Args:
            user_id: User ID (used as instance name)
            limit: Maximum number of contacts to return
            page: Page number for pagination

        Returns:
            List of WhatsApp contacts
        """
        try:
            # Get contacts
            response = await self._make_request(
                "GET",
                f"/chat/findContacts/{user_id}"
            )

            if not response:
                return {"success": False, "message": "Failed to get WhatsApp contacts"}

            # Filter and format contacts
            contacts = []
            for contact in response:
                # Skip non-user contacts
                if not contact.get("id") or not contact.get("name"):
                    continue

                # Format contact data
                contacts.append({
                    "id": contact.get("id"),
                    "name": contact.get("name"),
                    "phone": contact.get("id").split("@")[0] if "@" in contact.get("id", "") else "",
                    "profile_picture": contact.get("profilePicture", "")
                })

            # Sort contacts by name
            contacts.sort(key=lambda x: x["name"])

            # Paginate results
            start_idx = (page - 1) * limit
            end_idx = start_idx + limit
            paginated_contacts = contacts[start_idx:end_idx]

            return {
                "success": True,
                "contacts": paginated_contacts,
                "total": len(contacts),
                "page": page,
                "limit": limit,
                "pages": (len(contacts) + limit - 1) // limit
            }

        except Exception as e:
            logger.error(f"Error getting WhatsApp contacts: {str(e)}")
            return {"success": False, "message": f"Error: {str(e)}"}

    async def get_whatsapp_messages(self, user_id: str, contact_id: str, limit: int = 50) -> Dict:
        """
        Get WhatsApp messages for a specific contact

        Args:
            user_id: User ID (used as instance name)
            contact_id: Contact ID (JID)
            limit: Maximum number of messages to return

        Returns:
            List of WhatsApp messages
        """
        try:
            # Get messages
            response = await self._make_request(
                "POST",
                f"/chat/findMessages/{user_id}",
                json={"number": contact_id, "count": limit}
            )

            if not response:
                return {"success": False, "message": "Failed to get WhatsApp messages"}

            # Format messages
            messages = []
            for msg in response:
                # Skip non-conversation messages
                if msg.get("type") != "conversation" and msg.get("type") != "extendedTextMessage":
                    continue

                # Determine message direction
                is_from_me = msg.get("fromMe", False)

                # Get message content
                content = msg.get("content", "")

                # Format timestamp
                timestamp = msg.get("timestamp", 0)

                # Add message to list
                messages.append({
                    "id": msg.get("id", ""),
                    "content": content,
                    "timestamp": timestamp,
                    "from_me": is_from_me,
                    "sender": "me" if is_from_me else "them",
                    "status": msg.get("status", "sent")
                })

            # Sort messages by timestamp (newest first)
            messages.sort(key=lambda x: x["timestamp"], reverse=True)

            return {
                "success": True,
                "messages": messages,
                "contact_id": contact_id
            }

        except Exception as e:
            logger.error(f"Error getting WhatsApp messages: {str(e)}")
            return {"success": False, "message": f"Error: {str(e)}"}

    # Private methods

    async def _fetch_instances(self) -> List[Dict]:
        """Fetch all WhatsApp instances"""
        response = await self._make_request("GET", "/instance/fetchInstances")
        return response or []

    async def _delete_instance(self, instance_name: str) -> bool:
        """Delete a WhatsApp instance"""
        logger.info(f"Deleting instance {instance_name}...")

        # Try normal delete first
        delete_response = await self._make_request(
            "DELETE",
            f"/instance/delete/{instance_name}"
        )

        if delete_response:
            logger.info(f"Successfully deleted instance {instance_name}")
            return True

        # If normal delete fails, try force delete
        logger.info(f"Failed to delete instance {instance_name}, trying force delete...")
        force_delete_response = await self._make_request(
            "DELETE",
            f"/instance/delete/{instance_name}?force=true"
        )

        if force_delete_response:
            logger.info(f"Successfully force deleted instance {instance_name}")
            return True

        logger.error(f"Failed to delete instance {instance_name} even with force option")
        return False

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        json: Optional[Dict] = None,
        retry_count: int = 0
    ) -> Optional[Any]:
        """Make a request to the Evolution API with retry logic"""
        url = f"{self.base_url}{endpoint}"

        try:
            if method == "GET":
                response = requests.get(url, headers=self.headers)
            elif method == "POST":
                response = requests.post(url, headers=self.headers, json=json)
            elif method == "PUT":
                response = requests.put(url, headers=self.headers, json=json)
            elif method == "DELETE":
                response = requests.delete(url, headers=self.headers)
            else:
                logger.error(f"Unsupported HTTP method: {method}")
                return None

            # Check if request was successful
            if response.status_code in [200, 201, 202]:
                try:
                    return response.json()
                except:
                    return {"status": "success"}

            # Handle rate limiting
            if response.status_code == 429 and retry_count < self.max_retries:
                retry_count += 1
                wait_time = self.retry_delay * retry_count
                logger.warning(f"Rate limited, retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
                return await self._make_request(method, endpoint, json, retry_count)

            # Handle other errors
            logger.error(f"API request failed: {response.status_code} - {response.text}")

            # Retry on server errors
            if response.status_code >= 500 and retry_count < self.max_retries:
                retry_count += 1
                wait_time = self.retry_delay * retry_count
                logger.warning(f"Server error, retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
                return await self._make_request(method, endpoint, json, retry_count)

            return None

        except Exception as e:
            logger.error(f"Request error: {str(e)}")

            # Retry on connection errors
            if retry_count < self.max_retries:
                retry_count += 1
                wait_time = self.retry_delay * retry_count
                logger.warning(f"Connection error, retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
                return await self._make_request(method, endpoint, json, retry_count)

            return None

    def _generate_qr_response(self, qr_code_str: str) -> Dict:
        """Generate QR code image from string"""
        try:
            # Generate QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(qr_code_str)
            qr.make(fit=True)

            # Create image
            img = qr.make_image(fill_color="black", back_color="white")

            # Convert to base64
            buffered = io.BytesIO()
            img.save(buffered, format="PNG")
            img_str = base64.b64encode(buffered.getvalue()).decode()

            return {
                "success": True,
                "qrcode": f"data:image/png;base64,{img_str}",
                "code": qr_code_str
            }

        except Exception as e:
            logger.error(f"Error generating QR code: {str(e)}")
            return {"success": False, "message": f"Error generating QR code: {str(e)}"}

    def _find_or_create_client(self, user_id: str, phone: str) -> Optional[Client]:
        """Find or create a client based on phone number"""
        try:
            # Find client by phone number
            client = self.db.query(Client).filter(
                Client.user_id == user_id,
                Client.phone == phone
            ).first()

            if client:
                return client

            # Create new client
            new_client = Client(
                user_id=user_id,
                name=f"WhatsApp Contact ({phone})",
                phone=phone,
                source="whatsapp"
            )

            self.db.add(new_client)
            self.db.commit()
            self.db.refresh(new_client)

            return new_client

        except Exception as e:
            logger.error(f"Error finding or creating client: {str(e)}")
            return None

    async def _process_with_ai(self, user_id: str, client_id: UUID, message: str) -> Optional[str]:
        """Process a message with the AI assistant"""
        try:
            # Create AI assistant
            ai_assistant = AIAssistant(self.db, user_id)

            # Process message
            response, action = await ai_assistant.process_message(str(client_id), message)

            # Handle actions (e.g., booking appointments)
            if action and action.get("action") == "book_appointment":
                logger.info(f"Booking appointment action detected: {json.dumps(action)}")

                # Attempt to book the appointment
                success, booking_message = await ai_assistant.book_appointment(action)

                if success:
                    logger.info(f"Appointment booked successfully: {booking_message}")
                    # Append the booking confirmation to the response
                    response += f"\n\n{booking_message}"
                else:
                    logger.error(f"Failed to book appointment: {booking_message}")
                    # Append the failure message to the response
                    response += f"\n\nI'm sorry, but there was an issue booking your appointment: {booking_message}"

            return response

        except Exception as e:
            logger.error(f"Error processing message with AI: {str(e)}")
            return "I'm sorry, I'm having trouble processing your message right now. Please try again later."

# Helper function to generate QR code image
def generate_qr_image(qr_code_str: str) -> Dict:
    """Generate QR code image from string"""
    try:
        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(qr_code_str)
        qr.make(fit=True)

        # Create image
        img = qr.make_image(fill_color="black", back_color="white")

        # Convert to base64
        buffered = io.BytesIO()
        img.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode()

        return {
            "success": True,
            "qrcode": f"data:image/png;base64,{img_str}",
            "code": qr_code_str
        }

    except Exception as e:
        logger.error(f"Error generating QR code: {str(e)}")
        return {"success": False, "message": f"Error generating QR code: {str(e)}"}
