import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from uuid import UUID
from sqlalchemy.orm import Session

# Import both AI services
from openai import OpenAI
from app.services.gemini_service import gemini_service

from app.models.appointment import Appointment, AppointmentStatus
from app.models.client import Client
from app.models.service import Service
from app.models.user import User
from app.models.ai_configuration import AIConfiguration, KnowledgeDocument
from app.models.ai_context import AIConversationContext, ConversationMessage
from app.services.conversation_service import ConversationService
from app.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configure OpenAI client (as fallback)
client = OpenAI(api_key=settings.OPENAI_API_KEY)

# Use Gemini as the primary AI service
use_gemini = True

class AIAssistant:
    """AI Assistant for handling client messages and booking appointments"""

    def __init__(self, db: Session, user_id: str):
        """Initialize the AI Assistant

        Args:
            db: Database session
            user_id: ID of the business user
        """
        self.db = db
        self.user_id = user_id
        self.model = "gpt-4o"  # Using GPT-4o for best performance
        self.max_tokens = 500
        self.temperature = 0.7

        # Load business information
        self.business_info = self._load_business_info()

        # Load available services
        self.services = self._load_services()

        # Load AI configuration
        self.ai_config = self._load_ai_configuration()

        # Load knowledge documents
        self.knowledge_documents = self._load_knowledge_documents()

        # System prompt template
        self.system_prompt = self._create_system_prompt()

    def _load_business_info(self) -> Dict:
        """Load business information from the database"""
        user = self.db.query(User).filter(User.id == self.user_id).first()
        if not user:
            return {
                "name": "Our Business",
                "address": "Our Location",
                "hours": "9 AM - 5 PM, Monday to Friday",
                "phone": "",
                "email": "",
                "website": "",
                "description": "A business that provides various services."
            }

        # Get business settings from user.settings if available
        try:
            settings = user.settings or {}
        except AttributeError:
            # If user doesn't have a settings attribute, use empty dict
            logger.warning(f"User {self.user_id} doesn't have a settings attribute")
            settings = {}

        # Get business name
        try:
            business_name = user.business_name or "Our Business"
        except AttributeError:
            business_name = "Our Business"
            logger.warning(f"User {self.user_id} doesn't have a business_name attribute")

        return {
            "name": business_name,
            "address": settings.get('address', "Our Location"),
            "hours": settings.get('workingHours', {}).get('formatted', "9 AM - 5 PM, Monday to Friday"),
            "phone": settings.get('phone', ""),
            "email": getattr(user, 'email', ""),
            "website": settings.get('website', ""),
            "description": settings.get('description', "A business that provides various services.")
        }

    def _load_services(self) -> List[Dict]:
        """Load available services from the database"""
        services = self.db.query(Service).filter(Service.user_id == self.user_id).all()
        return [
            {
                "id": str(service.id),
                "name": service.name,
                "description": service.description or "",
                "duration_minutes": service.duration_minutes,
                "price": service.price
            }
            for service in services
        ]

    def _load_ai_configuration(self) -> Dict:
        """Load AI configuration from the database"""
        config = self.db.query(AIConfiguration).filter(AIConfiguration.user_id == self.user_id).first()

        if not config:
            # Return default configuration
            return {
                "system_prompt": None,  # Will use the default prompt
                "tone": "professional",
                "primary_language": "en",
                "supported_languages": ["en"],
                "business_rules": {},
                "response_templates": {}
            }

        return {
            "system_prompt": config.system_prompt,
            "tone": config.tone,
            "primary_language": config.primary_language,
            "supported_languages": config.supported_languages,
            "business_rules": config.business_rules or {},
            "response_templates": config.response_templates or {}
        }

    def _load_knowledge_documents(self) -> List[Dict]:
        """Load knowledge documents from the database"""
        documents = self.db.query(KnowledgeDocument).filter(KnowledgeDocument.user_id == self.user_id).all()

        return [
            {
                "id": str(doc.id),
                "title": doc.title,
                "content": doc.content,
                "metadata": doc.document_metadata or {}
            }
            for doc in documents
        ]

    def _create_system_prompt(self) -> str:
        """Create the system prompt for the AI"""
        services_text = "\n".join([
            f"- {s['name']}: {s['description']} ({s['duration_minutes']} minutes, ${s['price']}) [ID: {s['id']}]"
            for s in self.services
        ])

        # Get current date and time information using business timezone
        from datetime import datetime
        import pytz
        from app.services.address_service import address_service

        # Get the business timezone based on the user's address
        user = self.db.query(User).filter(User.id == self.user_id).first()
        business_timezone = address_service.get_business_timezone(user.address if user else None)

        # Use the determined timezone
        business_tz = pytz.timezone(business_timezone)
        now = datetime.now(business_tz)
        current_date = now.strftime("%A, %B %d, %Y")
        current_time = now.strftime("%I:%M %p")
        current_year = now.year

        # Use custom system prompt if available
        if self.ai_config["system_prompt"]:
            # Replace placeholders in the custom prompt
            prompt = self.ai_config["system_prompt"]
            prompt = prompt.replace("{business_name}", self.business_info['name'])
            prompt = prompt.replace("{business_address}", self.business_info['address'])
            prompt = prompt.replace("{business_hours}", self.business_info['hours'])
            prompt = prompt.replace("{business_phone}", self.business_info.get('phone', ''))
            prompt = prompt.replace("{business_email}", self.business_info.get('email', ''))
            prompt = prompt.replace("{services_list}", services_text)
            prompt = prompt.replace("{tone}", self.ai_config["tone"])

            # Add current date and time context to custom prompts
            date_context = f"\n\nCURRENT DATE AND TIME:\n- Today is: {current_date}\n- Current time: {current_time}\n- Current year: {current_year}\n- When clients mention 'today', 'tomorrow', 'next week', etc., use this date as reference"
            prompt = prompt + date_context

            return prompt

        # Default system prompt
        return f"""You are an AI assistant for {self.business_info['name']}, a business that provides various services to clients. You communicate with clients via WhatsApp and help manage their appointments.

CURRENT DATE AND TIME:
- Today is: {current_date}
- Current time: {current_time}
- Current year: {current_year}
- When clients mention "today", "tomorrow", "next week", etc., use this date as reference

BUSINESS INFORMATION:
- Name: {self.business_info['name']}
- Address: {self.business_info['address']}
- Business Hours: {self.business_info['hours']}
- Contact: {self.business_info.get('phone', '')} | {self.business_info.get('email', '')}

AVAILABLE SERVICES:
{services_text}

APPOINTMENT BOOKING INSTRUCTIONS:
When a client wants to book an appointment, follow these steps:
1. Ask for their preferred service, date, and time if not provided
2. Confirm the service exists in our list of available services
3. Check if the requested time is within business hours
4. When the client confirms they want to book (says "yes", "book it", "confirm", etc.), immediately proceed with booking
5. Use the BOOKING_CONFIRMED tag to actually book the appointment

IMPORTANT: When a client gives clear confirmation ("yes", "book it", "that works", "please book", etc.), immediately book the appointment using:
BOOKING_CONFIRMED {{"service_id": "service_uuid", "start_time": "YYYY-MM-DD HH:MM", "notes": "any additional notes"}}

Example booking confirmation:
Perfect! I've booked your Consultation for Monday, May 26th at 10:00 AM. BOOKING_CONFIRMED {{"service_id": "5b40c5c8-158e-4188-804f-8efc23c0aa13", "start_time": "2025-05-26 10:00", "notes": "Consultation appointment"}}

Make sure to use the exact service_id from the AVAILABLE SERVICES list when confirming a booking. The start_time must be in the format "YYYY-MM-DD HH:MM".

YOUR ROLE:
1. Answer questions about the business, services, pricing, and availability
2. Help clients book appointments in the calendar
3. Be friendly, professional, and concise
4. When booking appointments, collect: client name, service type, preferred date and time
5. Check if the requested time is within business hours
6. For appointment booking, you should ask for all necessary information and then confirm the details before finalizing
7. Handle conversations in multiple languages based on what the client uses
8. Respond to WhatsApp messages in a timely manner



IMPORTANT GUIDELINES:
- Don't make up information that isn't provided
- If you're unsure about something, ask for clarification
- Always be polite and helpful
- Use a friendly, conversational tone
- Keep responses concise and to the point
- Respond appropriately to the language the client is using
- For WhatsApp, keep messages shorter and more direct than you would in other channels

When discussing dates, always use the current date from your system.
"""

    async def process_message(self, client_id: str, message: str) -> Tuple[str, Optional[Dict]]:
        """Process a message from a client

        Args:
            client_id: ID of the client
            message: Message from the client

        Returns:
            Tuple containing the response message and any action to take
        """
        # Get client information
        client = self.db.query(Client).filter(Client.id == client_id).first()
        client_name = client.name if client else "Client"

        # Get or create conversation context
        conversation_service = ConversationService(self.db)
        context = conversation_service.get_or_create_context(self.user_id, client_id)

        # Get conversation history from database
        conversation_history = conversation_service.get_conversation_history(context.id)

        # Check for appointment booking intent
        booking_intent = self._detect_booking_intent(message)

        # Add available time slots if booking intent detected
        available_slots = None
        if booking_intent:
            available_slots = self._get_available_time_slots()
            if available_slots:
                slots_text = "\n".join([
                    f"- {slot['date']} at {slot['time']}"
                    for slot in available_slots[:5]  # Show only 5 slots
                ])
                message += f"\n\nAvailable time slots:\n{slots_text}"

        # Retrieve relevant knowledge documents
        relevant_knowledge = await self._retrieve_relevant_knowledge(message)

        # Get current date and time for this specific message using business timezone
        from datetime import datetime
        import pytz
        from app.services.address_service import address_service

        # Get the business timezone based on the user's address
        user = self.db.query(User).filter(User.id == self.user_id).first()
        business_timezone = address_service.get_business_timezone(user.address if user else None)

        # Use the determined timezone
        business_tz = pytz.timezone(business_timezone)
        now = datetime.now(business_tz)
        current_date = now.strftime("%A, %B %d, %Y")
        current_time = now.strftime("%I:%M %p")
        current_year = now.year

        # Add current date context to the system prompt
        system_prompt_with_date = self.system_prompt + f"\n\nIMPORTANT - CURRENT DATE AND TIME:\n- Today is: {current_date}\n- Current time: {current_time}\n- Current year: {current_year}\n- When clients mention 'today', 'tomorrow', 'next week', etc., use this date as reference\n- Always use the current year ({current_year}) when booking appointments"

        # Debug: Log the current date being sent to AI
        logger.info(f"AI System Prompt Date Context: Today is {current_date}, Current year: {current_year}")

        # Prepare messages for the API call
        messages = [
            {"role": "system", "content": system_prompt_with_date},
        ]

        # Add relevant knowledge to the system prompt if available
        if relevant_knowledge:
            knowledge_text = "\n\n".join([doc["content"] for doc in relevant_knowledge])
            messages.append({"role": "system", "content": f"\n\nRELEVANT KNOWLEDGE:\n{knowledge_text}"})

        # Add conversation history
        for msg in conversation_history:
            messages.append(msg)

        # Add the current message
        messages.append({"role": "user", "content": message})

        # Add client name to the system prompt
        client_context = f"\n\nCURRENT CLIENT: {client_name}\nPlease address {client_name} by name in your responses.\n"
        messages[0]["content"] += client_context

        # Save the user message to the conversation history
        conversation_service.add_message(
            conversation_id=context.id,
            content=message,
            role="user",
            client_id=client_id
        )

        try:
            # Use Gemini API (primary)
            if use_gemini:
                # Convert messages to a format Gemini can use
                system_prompt = None
                conversation_history = []

                # Extract system prompt and conversation history
                for msg in messages:
                    if msg["role"] == "system":
                        system_prompt = msg["content"]
                    else:
                        conversation_history.append(msg)

                # Generate response using Gemini with conversation history
                response_text = await gemini_service.generate_text_with_history(message, conversation_history, system_prompt)
            else:
                # Fallback to OpenAI
                # Check if OpenAI API key is configured
                if not settings.OPENAI_API_KEY or settings.OPENAI_API_KEY == "your-openai-api-key-here":
                    return "I'm sorry, but the AI assistant is not properly configured. Please contact the administrator to set up the OpenAI API key.", None

                try:
                    # Call the OpenAI API
                    response = client.chat.completions.create(
                        model=self.model,
                        messages=messages,
                        max_tokens=self.max_tokens,
                        temperature=self.temperature,
                        user=client_id  # For OpenAI's monitoring
                    )

                    # Extract the response text
                    response_text = response.choices[0].message.content
                except Exception as api_error:
                    # Handle API errors gracefully
                    logger.error(f"OpenAI API error: {str(api_error)}")

                    # Check for quota errors
                    if "quota" in str(api_error).lower() or "insufficient_quota" in str(api_error).lower():
                        return "I'm sorry, but the AI assistant is currently unavailable due to API quota limitations. Please try again later or contact support.", None

                    # Generic error message
                    return "I'm sorry, but I'm having trouble processing your request right now. Please try again later.", None

            # Check if we need to take any action (like booking an appointment)
            action = self._extract_action(response_text, client_id, available_slots)

            # Save the assistant response to the conversation history
            conversation_service.add_message(
                conversation_id=context.id,
                content=response_text,
                role="assistant",
                user_id=self.user_id,
                metadata={"action": action} if action else None
            )

            return response_text, action

        except Exception as e:
            logger.error(f"Error calling OpenAI API: {str(e)}")
            return "I'm sorry, I'm having trouble processing your request right now. Please try again later or contact us directly.", None

    async def _retrieve_relevant_knowledge(self, query: str, max_results: int = 3) -> List[Dict]:
        """Retrieve relevant knowledge documents based on the query

        Args:
            query: The user's query
            max_results: Maximum number of results to return

        Returns:
            List of relevant knowledge documents
        """
        if not self.knowledge_documents:
            return []

        try:
            # Use vector similarity search if we have embeddings
            from app.services.embedding_service import embedding_service
            from pgvector.sqlalchemy import Vector
            from sqlalchemy import func, cast
            from app.models.ai_configuration import KnowledgeDocument

            # Generate embedding for the query
            query_embedding = await embedding_service.get_embedding(query)

            # Convert to PostgreSQL vector format
            query_vector = cast(query_embedding, Vector(384))

            # Perform vector similarity search
            results = self.db.query(
                KnowledgeDocument,
                func.cosine_similarity(KnowledgeDocument.embedding, query_vector).label('similarity')
            ).filter(
                KnowledgeDocument.user_id == self.user_id,
                KnowledgeDocument.embedding.is_not(None)
            ).order_by(
                func.cosine_similarity(KnowledgeDocument.embedding, query_vector).desc()
            ).limit(max_results).all()

            # Convert to the expected format
            return [
                {
                    "id": str(doc.id),
                    "title": doc.title,
                    "content": doc.content,
                    "metadata": doc.document_metadata or {},
                    "similarity": similarity
                }
                for doc, similarity in results
            ]
        except Exception as e:
            # Fallback to keyword-based search if vector search fails
            logger.error(f"Vector search failed, falling back to keyword search: {str(e)}")

            # Simple keyword-based retrieval
            query_lower = query.lower()
            scored_docs = []

            for doc in self.knowledge_documents:
                # Calculate a simple relevance score based on keyword matches
                content_lower = doc["content"].lower()
                title_lower = doc["title"].lower()

                # Count occurrences of query terms in the document
                query_terms = query_lower.split()
                score = 0

                for term in query_terms:
                    if len(term) > 3:  # Ignore short words
                        title_score = title_lower.count(term) * 2  # Title matches are weighted higher
                        content_score = content_lower.count(term)
                        score += title_score + content_score

                if score > 0:
                    scored_docs.append((score, doc))

            # Sort by score (descending) and take top results
            scored_docs.sort(reverse=True, key=lambda x: x[0])
            return [doc for score, doc in scored_docs[:max_results]]

    def _detect_booking_intent(self, message: str) -> bool:
        """Detect if the message has an appointment booking intent

        Args:
            message: Message from the client

        Returns:
            True if booking intent detected, False otherwise
        """
        booking_keywords = [
            "book", "schedule", "appointment", "reserve", "slot",
            "available", "time", "when can", "opening"
        ]

        message_lower = message.lower()
        return any(keyword in message_lower for keyword in booking_keywords)

    def _get_available_time_slots(self) -> List[Dict]:
        """Get available time slots for appointments

        Returns:
            List of available time slots
        """
        # Get existing appointments for the next 7 days using business timezone
        import pytz
        from app.services.address_service import address_service

        # Get the business timezone
        user = self.db.query(User).filter(User.id == self.user_id).first()
        business_timezone = address_service.get_business_timezone(user.address if user else None)
        business_tz = pytz.timezone(business_timezone)

        now = datetime.now(business_tz)
        end_date = now + timedelta(days=7)

        existing_appointments = self.db.query(Appointment).filter(
            Appointment.user_id == self.user_id,
            Appointment.start_time >= now,
            Appointment.start_time <= end_date,
            Appointment.status != AppointmentStatus.CANCELLED
        ).all()

        # Get booked time slots
        booked_slots = set()
        for appointment in existing_appointments:
            slot_time = appointment.start_time
            while slot_time < appointment.end_time:
                booked_slots.add(slot_time.strftime("%Y-%m-%d %H:%M"))
                slot_time += timedelta(minutes=30)  # 30-minute slots

        # Generate available time slots
        available_slots = []

        # Parse business hours (simplified - assumes same hours every day)
        # Format: "9 AM - 5 PM, Monday to Friday"
        hours_text = self.business_info["hours"]
        try:
            start_hour = 9  # Default: 9 AM
            end_hour = 17   # Default: 5 PM

            if "-" in hours_text:
                hours_parts = hours_text.split("-")
                start_time = hours_parts[0].strip()

                if "am" in start_time.lower():
                    start_hour = int(start_time.lower().replace("am", "").strip())
                elif "pm" in start_time.lower():
                    start_hour = int(start_time.lower().replace("pm", "").strip()) + 12

                end_time = hours_parts[1].split(",")[0].strip()
                if "am" in end_time.lower():
                    end_hour = int(end_time.lower().replace("am", "").strip())
                elif "pm" in end_time.lower():
                    end_hour = int(end_time.lower().replace("pm", "").strip())
                    if end_hour != 12:
                        end_hour += 12
        except:
            # If parsing fails, use defaults
            pass

        # Generate slots for the next 7 days
        for day in range(7):
            slot_date = now + timedelta(days=day)

            # Skip weekends if business is Monday to Friday
            if "monday to friday" in hours_text.lower() and slot_date.weekday() >= 5:
                continue

            for hour in range(start_hour, end_hour):
                for minute in [0, 30]:  # 30-minute slots
                    slot_time = slot_date.replace(hour=hour, minute=minute, second=0, microsecond=0)

                    # Skip slots in the past
                    if slot_time <= now:
                        continue

                    # Check if slot is available
                    slot_str = slot_time.strftime("%Y-%m-%d %H:%M")
                    if slot_str not in booked_slots:
                        available_slots.append({
                            "date": slot_time.strftime("%A, %B %d"),
                            "time": slot_time.strftime("%I:%M %p"),
                            "datetime": slot_str
                        })

        return available_slots

    def _extract_action(self, response_text: str, client_id: str, available_slots: Optional[List[Dict]]) -> Optional[Dict]:
        """Extract any action to take from the response

        Args:
            response_text: Response from the AI
            client_id: ID of the client
            available_slots: Available time slots

        Returns:
            Action to take, if any
        """
        # Check if the response contains a booking confirmation
        if "BOOKING_CONFIRMED" in response_text:
            # Extract booking details
            try:
                # Find the JSON part
                start_idx = response_text.find("{")
                end_idx = response_text.rfind("}") + 1

                if start_idx >= 0 and end_idx > start_idx:
                    booking_json = response_text[start_idx:end_idx]
                    booking_data = json.loads(booking_json)

                    # Validate required fields
                    if not booking_data.get("service_id"):
                        logger.error("Missing service_id in booking data")
                        return None

                    if not booking_data.get("start_time"):
                        logger.error("Missing start_time in booking data")
                        return None

                    # Remove the JSON from the response
                    clean_response = response_text.replace(booking_json, "")
                    clean_response = clean_response.replace("BOOKING_CONFIRMED", "")

                    # Create the action data
                    action_data = {
                        "action": "book_appointment",
                        "client_id": client_id,
                        "service_id": booking_data.get("service_id"),
                        "start_time": booking_data.get("start_time"),
                        "notes": booking_data.get("notes", "")
                    }

                    # Add any additional fields that might be useful
                    if booking_data.get("service_name"):
                        action_data["service_name"] = booking_data.get("service_name")

                    if booking_data.get("client_name"):
                        action_data["client_name"] = booking_data.get("client_name")

                    return action_data
            except Exception as e:
                logger.error(f"Error extracting booking details: {str(e)}")
                # Log the problematic response text for debugging
                logger.error(f"Problematic response text: {response_text}")

        # Check for booking intent without confirmation
        # This helps identify when the AI is discussing booking but hasn't confirmed yet
        booking_keywords = ["appointment", "schedule", "booking", "reserve"]
        if any(keyword in response_text.lower() for keyword in booking_keywords):
            # Log that we detected booking intent but no confirmation
            logger.info("Booking intent detected but no confirmation found")
            # We don't take action yet, just log it

        return None

    async def book_appointment(self, action_data: Dict) -> Tuple[bool, str]:
        """Book an appointment based on the extracted action

        Args:
            action_data: Action data extracted from the response

        Returns:
            Tuple containing success status and message
        """
        try:
            client_id = action_data.get("client_id")
            service_id_or_name = action_data.get("service_id")
            start_time_str = action_data.get("start_time")
            notes = action_data.get("notes", "")

            # Log the booking attempt
            logger.info(f"Attempting to book appointment: {json.dumps(action_data)}")

            # Validate inputs
            if not client_id or not service_id_or_name or not start_time_str:
                logger.error(f"Missing required booking information: client_id={client_id}, service_id={service_id_or_name}, start_time={start_time_str}")
                return False, "Missing required booking information"

            # Get client information
            try:
                client = self.db.query(Client).filter(Client.id == client_id).first()
                if not client:
                    logger.error(f"Client not found: {client_id}")
                    return False, f"Client not found: {client_id}"

                logger.info(f"Booking for client: {client.name} (ID: {client.id})")
            except Exception as e:
                logger.error(f"Error retrieving client: {str(e)}")
                return False, f"Error retrieving client: {str(e)}"

            # Parse start time and validate using business timezone
            try:
                import pytz
                from app.services.address_service import address_service

                # Get the business timezone
                user = self.db.query(User).filter(User.id == self.user_id).first()
                business_timezone = address_service.get_business_timezone(user.address if user else None)
                business_tz = pytz.timezone(business_timezone)

                # Parse the start time as naive datetime first
                start_time_naive = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M")

                # Localize it to the business timezone
                start_time = business_tz.localize(start_time_naive)

                # Get current time in business timezone for comparison
                current_time = datetime.now(business_tz)

                # Check if the appointment is in the past
                if start_time < current_time:
                    logger.error(f"Cannot book appointment in the past: {start_time_str} (current time: {current_time})")
                    current_date_str = current_time.strftime("%A, %B %d, %Y")
                    return False, f"Cannot book appointment in the past. Today is {current_date_str}. Please choose a future date."

                logger.info(f"Appointment start time: {start_time} ({business_timezone})")

                # Convert to UTC for database storage
                start_time = start_time.astimezone(pytz.UTC).replace(tzinfo=None)
            except ValueError as e:
                logger.error(f"Invalid date format: {start_time_str} - {str(e)}")
                return False, f"Invalid date format: {start_time_str}"

            # Try to get service by ID first
            service = None
            try:
                # Try to parse as UUID
                service_uuid = UUID(service_id_or_name)
                service = self.db.query(Service).filter(Service.id == service_uuid).first()
            except (ValueError, TypeError):
                # Not a valid UUID, try to find by name
                service = self.db.query(Service).filter(
                    Service.user_id == self.user_id,
                    Service.name.ilike(f"%{service_id_or_name}%")
                ).first()

            if not service:
                logger.error(f"Service not found: {service_id_or_name}")

                # Get available services for better error message
                available_services = self.db.query(Service).filter(Service.user_id == self.user_id).all()
                service_names = [s.name for s in available_services]

                if service_names:
                    return False, f"Service '{service_id_or_name}' not found. Available services: {', '.join(service_names)}"
                else:
                    return False, f"Service not found: {service_id_or_name}"

            # Calculate end time
            end_time = start_time + timedelta(minutes=service.duration_minutes)
            logger.info(f"Appointment end time: {end_time} (duration: {service.duration_minutes} minutes)")

            # Check for conflicts
            conflicts = self.db.query(Appointment).filter(
                Appointment.user_id == self.user_id,
                Appointment.status != AppointmentStatus.CANCELLED,
                Appointment.start_time < end_time,
                Appointment.end_time > start_time
            ).all()

            if conflicts:
                logger.error(f"Time slot conflict detected: {len(conflicts)} existing appointments")

                # Get alternative time slots
                available_slots = self._get_available_time_slots()
                alternative_slots = []

                # Find the next 3 available slots
                for slot in available_slots[:5]:
                    alternative_slots.append(f"{slot['date']} at {slot['time']}")

                if alternative_slots:
                    return False, f"The requested time slot is no longer available. Alternative times: {', '.join(alternative_slots)}"
                else:
                    return False, "The requested time slot is no longer available"

            # Create the appointment
            try:
                new_appointment = Appointment(
                    user_id=self.user_id,
                    client_id=client_id,
                    service_id=service.id,  # Use the service.id from the service we found
                    start_time=start_time,
                    end_time=end_time,
                    status=AppointmentStatus.CONFIRMED,
                    notes=notes
                )

                self.db.add(new_appointment)
                self.db.commit()
                self.db.refresh(new_appointment)

                logger.info(f"Appointment created successfully: ID={new_appointment.id}")
            except Exception as db_error:
                logger.error(f"Database error creating appointment: {str(db_error)}")
                return False, f"Error creating appointment: {str(db_error)}"

            # Sync with Google Calendar if available
            google_calendar_synced = False
            try:
                # Import here to avoid circular imports
                from app.services.google_calendar_service import get_google_calendar_service

                # Get the Google Calendar service
                google_calendar_service = get_google_calendar_service(self.db)

                # Sync the appointment with Google Calendar
                sync_result = await google_calendar_service.sync_appointment_to_google(new_appointment)

                if sync_result["success"]:
                    logger.info(f"Appointment synced to Google Calendar: {sync_result.get('event_id')}")
                    google_calendar_synced = True
                else:
                    logger.warning(f"Failed to sync with Google Calendar: {sync_result.get('message')}")
            except Exception as e:
                logger.error(f"Error syncing appointment to Google Calendar: {str(e)}")
                # Don't fail the booking if Google Calendar sync fails

            # Format a nice confirmation message
            confirmation_message = f"Appointment booked successfully for {client.name}:\n"
            confirmation_message += f"Service: {service.name}\n"
            confirmation_message += f"Date: {start_time.strftime('%A, %B %d, %Y')}\n"
            confirmation_message += f"Time: {start_time.strftime('%I:%M %p')} - {end_time.strftime('%I:%M %p')}\n"

            if notes:
                confirmation_message += f"Notes: {notes}\n"

            if google_calendar_synced:
                confirmation_message += "\nThis appointment has been added to your Google Calendar."

            return True, confirmation_message

        except Exception as e:
            logger.error(f"Error booking appointment: {str(e)}")
            return False, f"Error booking appointment: {str(e)}"
