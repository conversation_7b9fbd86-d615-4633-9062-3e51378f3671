"""
Address verification and timezone detection service
"""

import logging
import requests
from typing import Op<PERSON>, Dict, Tuple
from datetime import datetime
import pytz
from app.config import settings

logger = logging.getLogger(__name__)

class AddressService:
    """Service for address verification and timezone detection using Google APIs"""

    def __init__(self):
        # Google APIs
        self.geocoding_base_url = "https://maps.googleapis.com/maps/api/geocode/json"
        self.timezone_base_url = "https://maps.googleapis.com/maps/api/timezone/json"
        self.google_api_key = settings.GOOGLE_MAPS_API_KEY if hasattr(settings, 'GOOGLE_MAPS_API_KEY') else None

    async def verify_and_get_timezone(self, address: str) -> Dict:
        """
        Verify an address and get its timezone information

        Args:
            address: The business address to verify

        Returns:
            Dict containing verification results and timezone info
        """
        try:
            # Step 1: Geocode the address
            geocode_result = await self._geocode_address(address)
            if not geocode_result:
                return {
                    "success": False,
                    "error": "Could not geocode the provided address",
                    "address": address
                }

            lat, lon, formatted_address = geocode_result
            logger.info(f"Geocoded address '{address}' to coordinates: {lat}, {lon}")
            logger.info(f"Formatted address: {formatted_address}")

            # Step 2: Get timezone from coordinates
            timezone_info = await self._get_timezone_from_coordinates(lat, lon)
            if not timezone_info:
                return {
                    "success": False,
                    "error": "Could not determine timezone for the location",
                    "address": address,
                    "coordinates": {"latitude": lat, "longitude": lon}
                }

            # Step 3: Validate the timezone
            try:
                tz = pytz.timezone(timezone_info["timezone"])
                current_time = datetime.now(tz)

                return {
                    "success": True,
                    "address": address,
                    "coordinates": {
                        "latitude": lat,
                        "longitude": lon
                    },
                    "timezone": timezone_info["timezone"],
                    "current_time": current_time.isoformat(),
                    "utc_offset": timezone_info.get("utc_offset"),
                    "formatted_address": formatted_address,
                    "timezone_name": timezone_info.get("timezone_name")
                }
            except Exception as e:
                logger.error(f"Invalid timezone '{timezone_info['timezone']}': {str(e)}")
                return {
                    "success": False,
                    "error": f"Invalid timezone: {timezone_info['timezone']}",
                    "address": address
                }

        except Exception as e:
            logger.error(f"Error verifying address '{address}': {str(e)}")
            return {
                "success": False,
                "error": f"Address verification failed: {str(e)}",
                "address": address
            }

    async def _geocode_address(self, address: str) -> Optional[Tuple[float, float, str]]:
        """
        Geocode an address using Google Geocoding API

        Args:
            address: The address to geocode

        Returns:
            Tuple of (latitude, longitude, formatted_address) or None if failed
        """
        if not self.google_api_key:
            logger.warning("Google Maps API key not configured, falling back to simple timezone detection")
            return None

        try:
            params = {
                "address": address,
                "key": self.google_api_key
            }

            response = requests.get(
                self.geocoding_base_url,
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "OK" and data.get("results"):
                    result = data["results"][0]
                    location = result["geometry"]["location"]
                    lat = float(location["lat"])
                    lng = float(location["lng"])
                    formatted_address = result["formatted_address"]

                    logger.info(f"Successfully geocoded '{address}' to {lat}, {lng}")
                    return (lat, lng, formatted_address)
                else:
                    logger.warning(f"Geocoding failed for address '{address}': {data.get('status', 'Unknown error')}")
                    return None
            else:
                logger.warning(f"Geocoding API request failed: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"Error geocoding address '{address}': {str(e)}")
            return None

    async def _get_timezone_from_coordinates(self, lat: float, lon: float) -> Optional[Dict]:
        """
        Get timezone information from coordinates using Google Timezone API

        Args:
            lat: Latitude
            lon: Longitude

        Returns:
            Dict with timezone information or None if failed
        """
        if not self.google_api_key:
            logger.warning("Google Maps API key not configured, using simple timezone detection")
            # Fall back to simple timezone detection
            timezone_name = self._get_timezone_from_coordinates_simple(lat, lon)
            if timezone_name:
                try:
                    tz = pytz.timezone(timezone_name)
                    now = datetime.now(tz)
                    utc_offset = now.strftime('%z')

                    return {
                        "timezone": timezone_name,
                        "utc_offset": utc_offset,
                        "coordinates": {"latitude": lat, "longitude": lon}
                    }
                except Exception as e:
                    logger.error(f"Error with timezone '{timezone_name}': {str(e)}")
            return None

        try:
            # Use Google Timezone API
            import time
            timestamp = int(time.time())  # Current timestamp

            params = {
                "location": f"{lat},{lon}",
                "timestamp": timestamp,
                "key": self.google_api_key
            }

            response = requests.get(
                self.timezone_base_url,
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "OK":
                    timezone_id = data.get("timeZoneId")
                    timezone_name = data.get("timeZoneName")

                    if timezone_id:
                        try:
                            tz = pytz.timezone(timezone_id)
                            now = datetime.now(tz)
                            utc_offset = now.strftime('%z')

                            logger.info(f"Successfully got timezone '{timezone_id}' for coordinates {lat}, {lon}")
                            return {
                                "timezone": timezone_id,
                                "timezone_name": timezone_name,
                                "utc_offset": utc_offset,
                                "coordinates": {"latitude": lat, "longitude": lon}
                            }
                        except Exception as e:
                            logger.error(f"Error with timezone '{timezone_id}': {str(e)}")
                else:
                    logger.warning(f"Timezone API failed: {data.get('status', 'Unknown error')}")
            else:
                logger.warning(f"Timezone API request failed: {response.status_code}")

            return None

        except Exception as e:
            logger.error(f"Error getting timezone for coordinates {lat}, {lon}: {str(e)}")
            return None

    def _get_timezone_from_coordinates_simple(self, lat: float, lon: float) -> Optional[str]:
        """
        Simple timezone detection based on coordinates
        This is a basic implementation - in production you'd use a proper timezone API
        """
        # Basic timezone mapping for common regions
        # This is simplified and should be replaced with a proper timezone API

        # Europe
        if 35 <= lat <= 70 and -10 <= lon <= 40:
            # Spain, France, Germany, etc.
            if 36 <= lat <= 44 and -9 <= lon <= 3:  # Spain/Portugal
                return "Europe/Madrid"
            elif 41 <= lat <= 51 and -5 <= lon <= 9:  # France/Belgium/Netherlands
                return "Europe/Paris"
            elif 47 <= lat <= 55 and 5 <= lon <= 15:  # Germany/Austria/Switzerland
                return "Europe/Berlin"
            elif 45 <= lat <= 70 and 10 <= lon <= 30:  # Eastern Europe
                return "Europe/Warsaw"
            else:
                return "Europe/London"  # Default for Europe

        # North America
        elif 25 <= lat <= 70 and -170 <= lon <= -50:
            if -125 <= lon <= -114:  # Pacific Time
                return "America/Los_Angeles"
            elif -114 <= lon <= -104:  # Mountain Time
                return "America/Denver"
            elif -104 <= lon <= -87:  # Central Time
                return "America/Chicago"
            elif -87 <= lon <= -67:  # Eastern Time
                return "America/New_York"
            else:
                return "America/New_York"  # Default for North America

        # Add more regions as needed
        else:
            # Default to UTC for unknown regions
            return "UTC"

    def get_business_timezone(self, user_address: Optional[str]) -> str:
        """
        Get the business timezone, with fallback to default

        Args:
            user_address: The user's business address

        Returns:
            Timezone string (defaults to Europe/Madrid for Spain)
        """
        if not user_address:
            # Default to Spain timezone since the current business is in Girona
            return "Europe/Madrid"

        # For now, we'll do a simple check for known locations
        address_lower = user_address.lower()

        if any(country in address_lower for country in ["spain", "españa", "girona", "barcelona", "madrid"]):
            return "Europe/Madrid"
        elif any(country in address_lower for country in ["france", "paris"]):
            return "Europe/Paris"
        elif any(country in address_lower for country in ["germany", "berlin"]):
            return "Europe/Berlin"
        elif any(country in address_lower for country in ["uk", "london", "england", "britain"]):
            return "Europe/London"
        elif any(country in address_lower for country in ["usa", "united states", "america"]):
            # Default to Eastern Time for US
            return "America/New_York"
        else:
            # Default to Spain timezone
            return "Europe/Madrid"

# Create a singleton instance
address_service = AddressService()
