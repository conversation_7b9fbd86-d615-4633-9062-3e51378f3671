"""
Embedding service for generating vector embeddings from text
"""

import os
import numpy as np
import logging
from typing import List, Dict, Any, Optional
from openai import OpenAI
from app.config import settings
from app.services.gemini_service import gemini_service

logger = logging.getLogger(__name__)

# Configure OpenAI client (as fallback)
client = OpenAI(api_key=settings.OPENAI_API_KEY)

# Use Gemini as the primary embedding service
use_gemini = True

class EmbeddingService:
    """Service for generating and managing text embeddings"""

    def __init__(self):
        """Initialize the embedding service"""
        self.model = "text-embedding-3-small"  # OpenAI's embedding model
        self.openai_embedding_dim = 384  # Dimension of OpenAI embeddings
        self.gemini_embedding_dim = 768  # Dimension of Gemini embeddings
        self.embedding_dim = self.openai_embedding_dim  # Default dimension for fallback

    async def get_embedding(self, text: str) -> List[float]:
        """
        Generate an embedding for a single text

        Args:
            text: The text to embed

        Returns:
            A list of floats representing the embedding vector
        """
        try:
            if use_gemini:
                # Use Gemini for embeddings
                embedding = await gemini_service.get_embedding(text)
                if embedding:
                    # Resize Gemini embeddings to match OpenAI dimensions
                    # This is a simple approach - we take every other value to reduce from 768 to 384
                    if len(embedding) == self.gemini_embedding_dim:
                        resized_embedding = embedding[::2]  # Take every other value
                        return resized_embedding
                    return embedding
                # Fall back to OpenAI if Gemini fails

            # Use OpenAI for embeddings
            if not settings.OPENAI_API_KEY or settings.OPENAI_API_KEY == "your-openai-api-key-here":
                logger.warning("OpenAI API key not configured, using fallback embedding")
                return [0.0] * self.embedding_dim

            response = client.embeddings.create(
                model=self.model,
                input=text
            )

            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            # Return a fallback embedding
            return [0.0] * self.embedding_dim

    async def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts

        Args:
            texts: List of texts to embed

        Returns:
            List of embedding vectors
        """
        # Just return zero vectors for now to avoid API issues
        # This is a temporary solution until we fix the embedding service
        logger.info(f"Using fallback embeddings for {len(texts)} texts")
        return [[0.0] * self.embedding_dim for _ in range(len(texts))]

    def cosine_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calculate cosine similarity between two embeddings

        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector

        Returns:
            Cosine similarity score (between -1 and 1)
        """
        # Convert to numpy arrays
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)

        # Calculate cosine similarity
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)

    def search_similar(self, query_embedding: List[float], embeddings: List[List[float]], top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Search for similar embeddings

        Args:
            query_embedding: The query embedding vector
            embeddings: List of embedding vectors to search through
            top_k: Number of results to return

        Returns:
            List of dictionaries with index and similarity score
        """
        results = []

        for i, embedding in enumerate(embeddings):
            similarity = self.cosine_similarity(query_embedding, embedding)
            results.append({
                "index": i,
                "similarity": similarity
            })

        # Sort by similarity (descending)
        results.sort(key=lambda x: x["similarity"], reverse=True)

        # Return top k results
        return results[:top_k]

# Create a singleton instance
embedding_service = EmbeddingService()
