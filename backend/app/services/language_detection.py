"""
Language detection service for identifying the language of text messages
"""
from typing import Optional
import re

# Common language patterns and keywords
LANGUAGE_PATTERNS = {
    'en': [
        r'\b(hello|hi|hey|good morning|good afternoon|good evening|thanks|thank you|please|sorry|yes|no)\b',
        r'\b(what|when|where|who|why|how|is|are|was|were|will|would|could|should|can|may|might)\b',
        r'\b(appointment|schedule|book|cancel|reschedule|confirm|time|date|day|week|month|year)\b'
    ],
    'es': [
        r'\b(hola|buenos días|buenas tardes|buenas noches|gracias|por favor|perdón|sí|no)\b',
        r'\b(qué|cuándo|dónde|quién|por qué|cómo|es|son|era|eran|será|sería|podría|debería|puede|podría)\b',
        r'\b(cita|programar|reservar|cancelar|reprogramar|confirmar|hora|fecha|día|semana|mes|año)\b'
    ],
    'ca': [
        r'\b(hola|bon dia|bona tarda|bona nit|gràcies|si us plau|perdó|sí|no)\b',
        r'\b(què|quan|on|qui|per què|com|és|són|era|eren|serà|seria|podria|hauria|pot|podria)\b',
        r'\b(cita|programar|reservar|cancel·lar|reprogramar|confirmar|hora|data|dia|setmana|mes|any)\b'
    ],
    'fr': [
        r'\b(bonjour|salut|bonsoir|merci|s\'il vous plaît|pardon|oui|non)\b',
        r'\b(quoi|quand|où|qui|pourquoi|comment|est|sont|était|étaient|sera|serait|pourrait|devrait|peut|pourrait)\b',
        r'\b(rendez-vous|programmer|réserver|annuler|reprogrammer|confirmer|heure|date|jour|semaine|mois|année)\b'
    ],
    'de': [
        r'\b(hallo|guten morgen|guten tag|guten abend|danke|bitte|entschuldigung|ja|nein)\b',
        r'\b(was|wann|wo|wer|warum|wie|ist|sind|war|waren|wird|würde|könnte|sollte|kann|könnte)\b',
        r'\b(termin|planen|buchen|stornieren|umplanen|bestätigen|zeit|datum|tag|woche|monat|jahr)\b'
    ],
    'it': [
        r'\b(ciao|buongiorno|buonasera|buonanotte|grazie|per favore|scusa|sì|no)\b',
        r'\b(cosa|quando|dove|chi|perché|come|è|sono|era|erano|sarà|sarebbe|potrebbe|dovrebbe|può|potrebbe)\b',
        r'\b(appuntamento|programmare|prenotare|cancellare|riprogrammare|confermare|ora|data|giorno|settimana|mese|anno)\b'
    ],
    'pt': [
        r'\b(olá|bom dia|boa tarde|boa noite|obrigado|obrigada|por favor|desculpe|sim|não)\b',
        r'\b(o que|quando|onde|quem|por que|como|é|são|era|eram|será|seria|poderia|deveria|pode|poderia)\b',
        r'\b(consulta|agendar|reservar|cancelar|reagendar|confirmar|hora|data|dia|semana|mês|ano)\b'
    ]
}

def detect_language(text: str) -> Optional[str]:
    """
    Detect the language of a text message
    
    Args:
        text: The text to analyze
        
    Returns:
        The detected language code (en, es, ca, fr, de, it, pt) or None if unable to detect
    """
    if not text or not isinstance(text, str):
        return None
    
    # Normalize text: lowercase and strip punctuation
    text = text.lower()
    
    # Count matches for each language
    scores = {}
    for lang, patterns in LANGUAGE_PATTERNS.items():
        scores[lang] = 0
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            scores[lang] += len(matches)
    
    # Find the language with the highest score
    max_score = 0
    detected_lang = None
    
    for lang, score in scores.items():
        if score > max_score:
            max_score = score
            detected_lang = lang
    
    # If no clear winner or very low score, return None
    if max_score < 1:
        return None
    
    return detected_lang
