"""
Gemini AI service for generating text responses and embeddings
"""

import os
import json
import logging
import asyncio
import httpx
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from app.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GeminiService:
    """Service for interacting with Google's Gemini API"""

    def __init__(self):
        """Initialize the Gemini service"""
        self.api_key = settings.GEMINI_API_KEY
        self.base_url = "https://generativelanguage.googleapis.com/v1"
        self.model = "gemini-1.5-pro"  # Using the latest model
        self.embedding_model = "embedding-001"  # Gemini's embedding model
        self.embedding_dim = 768  # Dimension of Gemini embeddings

    async def generate_text(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """
        Generate text using Gemini API

        Args:
            prompt: The user prompt
            system_prompt: Optional system prompt to guide the model

        Returns:
            Generated text response
        """
        try:
            # Check if API key is configured
            if not self.api_key:
                logger.warning("Gemini API key not configured")
                return "I'm sorry, but the AI assistant is not properly configured. Please contact the administrator to set up the Gemini API key."

            # Prepare the request
            url = f"{self.base_url}/models/{self.model}:generateContent?key={self.api_key}"

            # Build the content parts
            # Gemini doesn't support system prompts directly, so we'll prepend it to the user prompt
            if system_prompt:
                full_prompt = f"[System: {system_prompt}]\n\n{prompt}"
            else:
                full_prompt = prompt

            # Add user prompt
            content_parts = [{
                "role": "user",
                "parts": [{"text": full_prompt}]
            }]

            # Prepare the request payload
            payload = {
                "contents": content_parts,
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 800,
                }
            }

            # Make the API request
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=payload, timeout=30.0)

                # Check for errors
                if response.status_code != 200:
                    logger.error(f"Gemini API error: {response.status_code} - {response.text}")
                    return f"I'm sorry, but I'm having trouble processing your request right now. Error: {response.status_code}"

                # Parse the response
                result = response.json()

                # Extract the generated text
                if "candidates" in result and len(result["candidates"]) > 0:
                    candidate = result["candidates"][0]
                    if "content" in candidate and "parts" in candidate["content"]:
                        parts = candidate["content"]["parts"]
                        if len(parts) > 0 and "text" in parts[0]:
                            return parts[0]["text"]

                # If we couldn't extract the text, return an error message
                logger.error(f"Failed to extract text from Gemini response: {result}")
                return "I'm sorry, but I couldn't generate a response. Please try again later."

        except Exception as e:
            logger.error(f"Error generating text with Gemini: {str(e)}")
            return f"I'm sorry, but I encountered an error: {str(e)}"

    async def generate_text_with_history(self, prompt: str, conversation_history: List[Dict], system_prompt: Optional[str] = None) -> str:
        """
        Generate text using Gemini API with conversation history

        Args:
            prompt: The current user prompt
            conversation_history: List of previous messages in the conversation
            system_prompt: Optional system prompt to guide the model

        Returns:
            Generated text response
        """
        try:
            # Check if API key is configured
            if not self.api_key:
                logger.warning("Gemini API key not configured")
                return "I'm sorry, but the AI assistant is not properly configured. Please contact the administrator to set up the Gemini API key."

            # Prepare the request
            url = f"{self.base_url}/models/{self.model}:generateContent?key={self.api_key}"

            # Build the content parts
            content_parts = []

            # Add system prompt as a user message if provided
            if system_prompt:
                content_parts.append({
                    "role": "user",
                    "parts": [{"text": f"[System: {system_prompt}]"}]
                })
                # Add a model response to separate system from conversation
                content_parts.append({
                    "role": "model",
                    "parts": [{"text": "I understand my role and will assist accordingly."}]
                })

            # Add conversation history
            for msg in conversation_history:
                role = "user" if msg["role"] == "user" else "model"
                content_parts.append({
                    "role": role,
                    "parts": [{"text": msg["content"]}]
                })

            # Add current user prompt
            content_parts.append({
                "role": "user",
                "parts": [{"text": prompt}]
            })

            # Prepare the request payload
            payload = {
                "contents": content_parts,
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 800,
                }
            }

            # Make the API request
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=payload, timeout=30.0)

                # Check for errors
                if response.status_code != 200:
                    logger.error(f"Gemini API error: {response.status_code} - {response.text}")
                    return f"I'm sorry, but I'm having trouble processing your request right now. Error: {response.status_code}"

                # Parse the response
                result = response.json()

                # Extract the generated text
                if "candidates" in result and len(result["candidates"]) > 0:
                    candidate = result["candidates"][0]
                    if "content" in candidate and "parts" in candidate["content"]:
                        parts = candidate["content"]["parts"]
                        if len(parts) > 0 and "text" in parts[0]:
                            return parts[0]["text"]

                # If we couldn't extract the text, return an error message
                logger.error(f"Failed to extract text from Gemini response: {result}")
                return "I'm sorry, but I couldn't generate a response. Please try again later."

        except Exception as e:
            logger.error(f"Error generating text with Gemini: {str(e)}")
            return f"I'm sorry, but I encountered an error: {str(e)}"

    async def get_embedding(self, text: str) -> List[float]:
        """
        Generate an embedding for a single text using Gemini API

        Args:
            text: The text to embed

        Returns:
            A list of floats representing the embedding vector
        """
        try:
            # Check if API key is configured
            if not self.api_key:
                logger.warning("Gemini API key not configured. Using fallback embedding.")
                return [0.0] * self.embedding_dim

            # Truncate text if it's too long
            truncated_text = text[:2048]  # Gemini has a smaller token limit than OpenAI

            # Prepare the request
            url = f"{self.base_url}/models/{self.embedding_model}:embedContent?key={self.api_key}"

            # Prepare the request payload
            payload = {
                "content": {
                    "parts": [
                        {"text": truncated_text}
                    ]
                },
                "taskType": "RETRIEVAL_DOCUMENT"  # For document embeddings
            }

            # Make the API request
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=payload, timeout=30.0)

                # Check for errors
                if response.status_code != 200:
                    logger.error(f"Gemini API error: {response.status_code} - {response.text}")
                    return [0.0] * self.embedding_dim

                # Parse the response
                result = response.json()

                # Extract the embedding
                if "embedding" in result and "values" in result["embedding"]:
                    return result["embedding"]["values"]

                # If we couldn't extract the embedding, return a zero vector
                logger.error(f"Failed to extract embedding from Gemini response: {result}")
                return [0.0] * self.embedding_dim

        except Exception as e:
            logger.error(f"Error generating embedding with Gemini: {str(e)}")
            return [0.0] * self.embedding_dim

    async def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts

        Args:
            texts: List of texts to embed

        Returns:
            List of embedding vectors
        """
        # Gemini doesn't support batch embedding, so we need to make multiple requests
        embeddings = []
        for text in texts:
            embedding = await self.get_embedding(text)
            embeddings.append(embedding)

        return embeddings

# Create a singleton instance
gemini_service = GeminiService()
