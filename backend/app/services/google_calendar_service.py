"""
Google Calendar Service
Handles syncing appointments with Google Calendar
"""

import logging
import requests
from typing import Dict, List, Optional, Any
from datetime import datetime
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy.sql import func

from app.models.appointment import Appointment, AppointmentStatus
from app.models.client import Client
from app.models.service import Service
from app.models.integration import ExternalIntegration
from app.models.user import User
from app.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Google Calendar API URL
GOOGLE_CALENDAR_API = "https://www.googleapis.com/calendar/v3"
GOOGLE_TOKEN_URL = "https://oauth2.googleapis.com/token"

class GoogleCalendarService:
    """Service for interacting with Google Calendar"""
    
    def __init__(self, db: Session):
        """Initialize the service with a database session"""
        self.db = db
    
    async def ensure_valid_token(self, integration: ExternalIntegration) -> str:
        """Ensure the access token is valid, refreshing if necessary"""
        credentials = integration.credentials
        logger.info(f"Ensuring valid token for integration")
        
        # Check if we have a refresh token
        if "refresh_token" not in credentials:
            logger.warning("No refresh token found, can't refresh")
            return credentials["access_token"]
        
        # Check if token is expired
        token_expired = True
        if "expiration_timestamp" in credentials:
            expiration_timestamp = credentials["expiration_timestamp"]
            current_timestamp = datetime.now().timestamp()
            
            # Add a buffer of 5 minutes to ensure we refresh before expiration
            buffer_seconds = 300
            token_expired = current_timestamp + buffer_seconds >= expiration_timestamp
        
        # Refresh token if expired
        if token_expired:
            try:
                logger.info("Refreshing token...")
                # Refresh the token
                response = requests.post(
                    GOOGLE_TOKEN_URL,
                    data={
                        "client_id": settings.GOOGLE_CLIENT_ID,
                        "client_secret": settings.GOOGLE_CLIENT_SECRET,
                        "refresh_token": credentials["refresh_token"],
                        "grant_type": "refresh_token"
                    }
                )
                
                logger.info(f"Refresh response status: {response.status_code}")
                
                new_tokens = response.json()
                
                if "error" in new_tokens:
                    logger.error(f"Error in refresh response: {new_tokens['error']}")
                    raise Exception(f"Error refreshing token: {new_tokens['error']}")
                
                # Update credentials with new tokens
                credentials["access_token"] = new_tokens["access_token"]
                credentials["token_type"] = new_tokens["token_type"]
                credentials["expires_in"] = new_tokens["expires_in"]
                
                # Calculate expiration timestamp
                expiration_timestamp = datetime.now().timestamp() + new_tokens["expires_in"]
                credentials["expiration_timestamp"] = expiration_timestamp
                
                # Save updated credentials
                integration.credentials = credentials
                integration.active = True
                self.db.commit()
                logger.info("Token refreshed and saved successfully")
            except Exception as e:
                logger.error(f"Error refreshing token: {str(e)}")
                raise Exception(f"Failed to refresh Google token: {str(e)}")
        else:
            logger.info("Token is still valid, no refresh needed")
        
        return credentials["access_token"]
    
    async def sync_appointment_to_google(self, appointment: Appointment) -> Dict:
        """Sync a single appointment to Google Calendar"""
        logger.info(f"Syncing appointment {appointment.id} to Google Calendar")
        
        # Get the user's Google Calendar integration
        integration = self.db.query(ExternalIntegration).filter(
            ExternalIntegration.user_id == appointment.user_id,
            ExternalIntegration.integration_type == "google_calendar"
        ).first()
        
        if not integration or not integration.active:
            logger.warning(f"No active Google Calendar integration for user {appointment.user_id}")
            return {"success": False, "message": "No active Google Calendar integration"}
        
        # Ensure we have a valid token
        try:
            access_token = await self.ensure_valid_token(integration)
        except Exception as e:
            logger.error(f"Error ensuring valid token: {str(e)}")
            return {"success": False, "message": f"Error with Google Calendar token: {str(e)}"}
        
        # Get client and service details
        client = self.db.query(Client).filter(Client.id == appointment.client_id).first()
        service = self.db.query(Service).filter(Service.id == appointment.service_id).first()
        user = self.db.query(User).filter(User.id == appointment.user_id).first()
        
        if not client or not service:
            logger.warning(f"Client or service not found for appointment {appointment.id}")
            return {"success": False, "message": "Client or service not found"}
        
        # Create event data
        event_data = {
            "summary": f"{service.name} - {client.name}",
            "description": f"Appointment with {client.name}\n\nService: {service.name}\n\nNotes: {appointment.notes or 'No notes'}\n\nManaged by FixMyCal - Appointment ID: {appointment.id}",
            "start": {
                "dateTime": appointment.start_time.isoformat(),
                "timeZone": "UTC"
            },
            "end": {
                "dateTime": appointment.end_time.isoformat(),
                "timeZone": "UTC"
            },
            "status": "confirmed" if appointment.status == AppointmentStatus.CONFIRMED else "tentative",
        }
        
        # Add client as attendee if email is available
        if client.email:
            event_data["attendees"] = [
                {
                    "email": client.email,
                    "displayName": client.name,
                    "responseStatus": "needsAction"
                }
            ]
        
        # Add location if business has an address
        if user and hasattr(user, 'settings') and user.settings and 'business' in user.settings and 'address' in user.settings['business']:
            event_data["location"] = user.settings['business']['address']
        
        try:
            # Check if the appointment already has a Google event ID
            if appointment.google_event_id:
                # Update existing event
                logger.info(f"Updating existing Google Calendar event {appointment.google_event_id}")
                response = requests.put(
                    f"{GOOGLE_CALENDAR_API}/calendars/primary/events/{appointment.google_event_id}",
                    json=event_data,
                    headers={
                        "Authorization": f"Bearer {access_token}",
                        "Content-Type": "application/json"
                    }
                )
                
                if response.status_code != 200:
                    logger.error(f"Error updating Google Calendar event: {response.status_code} - {response.text}")
                    return {"success": False, "message": f"Error updating Google Calendar event: {response.text}"}
                
                event = response.json()
            else:
                # Create new event
                logger.info("Creating new Google Calendar event")
                response = requests.post(
                    f"{GOOGLE_CALENDAR_API}/calendars/primary/events",
                    json=event_data,
                    headers={
                        "Authorization": f"Bearer {access_token}",
                        "Content-Type": "application/json"
                    }
                )
                
                if response.status_code not in (200, 201):
                    logger.error(f"Error creating Google Calendar event: {response.status_code} - {response.text}")
                    return {"success": False, "message": f"Error creating Google Calendar event: {response.text}"}
                
                event = response.json()
            
            # Update appointment with Google event ID
            appointment.google_event_id = event["id"]
            appointment.synced_with_google = True
            appointment.last_synced = datetime.now()
            self.db.commit()
            
            logger.info(f"Successfully synced appointment {appointment.id} to Google Calendar")
            return {"success": True, "event_id": event["id"], "html_link": event.get("htmlLink")}
        
        except Exception as e:
            logger.error(f"Error syncing appointment to Google Calendar: {str(e)}")
            return {"success": False, "message": f"Error syncing to Google Calendar: {str(e)}"}
    
    async def delete_google_calendar_event(self, appointment: Appointment) -> Dict:
        """Delete an event from Google Calendar"""
        logger.info(f"Deleting Google Calendar event for appointment {appointment.id}")
        
        # Check if the appointment has a Google event ID
        if not appointment.google_event_id:
            logger.warning(f"Appointment {appointment.id} has no Google event ID")
            return {"success": True, "message": "No Google event ID to delete"}
        
        # Get the user's Google Calendar integration
        integration = self.db.query(ExternalIntegration).filter(
            ExternalIntegration.user_id == appointment.user_id,
            ExternalIntegration.integration_type == "google_calendar"
        ).first()
        
        if not integration or not integration.active:
            logger.warning(f"No active Google Calendar integration for user {appointment.user_id}")
            return {"success": False, "message": "No active Google Calendar integration"}
        
        # Ensure we have a valid token
        try:
            access_token = await self.ensure_valid_token(integration)
        except Exception as e:
            logger.error(f"Error ensuring valid token: {str(e)}")
            return {"success": False, "message": f"Error with Google Calendar token: {str(e)}"}
        
        try:
            # Delete the event
            response = requests.delete(
                f"{GOOGLE_CALENDAR_API}/calendars/primary/events/{appointment.google_event_id}",
                headers={"Authorization": f"Bearer {access_token}"}
            )
            
            if response.status_code not in (200, 204):
                logger.error(f"Error deleting Google Calendar event: {response.status_code} - {response.text}")
                return {"success": False, "message": f"Error deleting Google Calendar event: {response.text}"}
            
            # Update appointment
            appointment.google_event_id = None
            appointment.synced_with_google = False
            appointment.last_synced = datetime.now()
            self.db.commit()
            
            logger.info(f"Successfully deleted Google Calendar event for appointment {appointment.id}")
            return {"success": True, "message": "Event deleted successfully"}
        
        except Exception as e:
            logger.error(f"Error deleting Google Calendar event: {str(e)}")
            return {"success": False, "message": f"Error deleting from Google Calendar: {str(e)}"}
    
    async def sync_appointments_with_google(self, user_id: UUID) -> Dict:
        """Sync all appointments for a user with Google Calendar"""
        logger.info(f"Syncing all appointments for user {user_id} with Google Calendar")
        
        # Get all active appointments that need syncing
        appointments = self.db.query(Appointment).filter(
            Appointment.user_id == user_id,
            Appointment.status != AppointmentStatus.CANCELLED,
            (Appointment.synced_with_google == False) | 
            (Appointment.last_synced < Appointment.updated_at)
        ).all()
        
        logger.info(f"Found {len(appointments)} appointments to sync")
        
        # Get cancelled appointments that need to be deleted from Google Calendar
        cancelled_appointments = self.db.query(Appointment).filter(
            Appointment.user_id == user_id,
            Appointment.status == AppointmentStatus.CANCELLED,
            Appointment.google_event_id != None,
            Appointment.synced_with_google == True
        ).all()
        
        logger.info(f"Found {len(cancelled_appointments)} cancelled appointments to delete from Google Calendar")
        
        # Sync results
        results = {
            "total": len(appointments) + len(cancelled_appointments),
            "synced": 0,
            "deleted": 0,
            "failed": 0,
            "details": []
        }
        
        # Sync active appointments
        for appointment in appointments:
            result = await self.sync_appointment_to_google(appointment)
            
            if result["success"]:
                results["synced"] += 1
            else:
                results["failed"] += 1
            
            results["details"].append({
                "appointment_id": str(appointment.id),
                "success": result["success"],
                "message": result.get("message", ""),
                "event_id": result.get("event_id", ""),
                "html_link": result.get("html_link", "")
            })
        
        # Delete cancelled appointments
        for appointment in cancelled_appointments:
            result = await self.delete_google_calendar_event(appointment)
            
            if result["success"]:
                results["deleted"] += 1
            else:
                results["failed"] += 1
            
            results["details"].append({
                "appointment_id": str(appointment.id),
                "success": result["success"],
                "message": result.get("message", ""),
                "action": "delete"
            })
        
        # Update the last synced timestamp for the integration
        integration = self.db.query(ExternalIntegration).filter(
            ExternalIntegration.user_id == user_id,
            ExternalIntegration.integration_type == "google_calendar"
        ).first()
        
        if integration:
            integration.last_synced = func.now()
            self.db.commit()
        
        logger.info(f"Sync completed: {results['synced']} synced, {results['deleted']} deleted, {results['failed']} failed")
        
        return {
            "success": True,
            "synced": results["synced"],
            "deleted": results["deleted"],
            "failed": results["failed"],
            "total": results["total"]
        }

# Create a singleton instance
google_calendar_service = GoogleCalendarService(None)

def get_google_calendar_service(db: Session) -> GoogleCalendarService:
    """Get the Google Calendar service with the current database session"""
    google_calendar_service.db = db
    return google_calendar_service
