"""
Intent detection service for identifying user intents in messages
"""
from typing import Dict, List, Optional, Tuple
import re

# Define common intents and their patterns
INTENT_PATTERNS = {
    'book_appointment': [
        r'\b(book|schedule|reserve|make|set up|arrange)\b.{0,30}\b(appointment|meeting|session|consultation|visit)\b',
        r'\b(need|want|looking for|interested in)\b.{0,30}\b(appointment|slot|time|booking)\b',
        r'\b(available|free|open)\b.{0,30}\b(slot|time|appointment|schedule)\b',
        r'\b(when|what time).{0,30}(available|free|open|book|schedule)\b'
    ],
    'cancel_appointment': [
        r'\b(cancel|reschedule|postpone|change|delete)\b.{0,30}\b(appointment|meeting|session|booking|reservation)\b',
        r'\b(can\'t make|unable to attend|won\'t be able)\b.{0,30}\b(appointment|meeting|session)\b',
        r'\b(remove|take off).{0,30}(calendar|schedule)\b'
    ],
    'check_appointment': [
        r'\b(check|confirm|verify|what is|when is|what time is|remind me of)\b.{0,30}\b(appointment|meeting|session|booking|schedule)\b',
        r'\b(do I have|is there|have I got)\b.{0,30}\b(appointment|meeting|booking|scheduled)\b',
        r'\b(what|which|when).{0,30}(next appointment|upcoming appointment)\b'
    ],
    'greeting': [
        r'^(hi|hello|hey|good morning|good afternoon|good evening|greetings|howdy)[\s\.,!]*$',
        r'^(hi|hello|hey|good morning|good afternoon|good evening|greetings|howdy)[\s\.,!]+(there|everyone|all|folks)'
    ],
    'goodbye': [
        r'^(bye|goodbye|see you|talk to you later|until next time|have a good day|have a nice day|take care)[\s\.,!]*$',
        r'^(thanks|thank you).{0,20}(bye|goodbye|see you)'
    ],
    'thanks': [
        r'^(thanks|thank you|appreciate it|grateful|much appreciated)[\s\.,!]*$',
        r'^(thanks|thank you|appreciate it|grateful|much appreciated).{0,20}(for|so much)'
    ],
    'help': [
        r'\b(help|assist|support|guide|how do I|how can I|how to)\b',
        r'\b(confused|lost|don\'t understand|not sure|uncertain)\b',
        r'\b(what can you do|how does this work|what are you capable of)\b'
    ],
    'hours': [
        r'\b(hours|time|when).{0,20}(open|close|operation|business|working)\b',
        r'\b(what|which).{0,20}(days|hours).{0,20}(open|available|working)\b'
    ],
    'services': [
        r'\b(services|treatments|options|offerings|provide|offer).{0,30}(available|provide|have|offer)\b',
        r'\b(what|which).{0,20}(services|treatments).{0,20}(available|provide|have|offer)\b',
        r'\b(do you|can you|available).{0,20}(services|treatments|options)\b'
    ],
    'prices': [
        r'\b(price|cost|fee|charge|how much|rate).{0,30}(service|appointment|session|treatment|visit)\b',
        r'\b(what|how much).{0,20}(cost|price|fee|charge|rate)\b'
    ],
    'location': [
        r'\b(where|location|address|place|directions|how to get).{0,30}(located|found|situated|office|clinic|practice)\b',
        r'\b(what|which).{0,20}(address|location)\b'
    ]
}

def detect_intent(text: str) -> Tuple[Optional[str], float]:
    """
    Detect the primary intent of a message
    
    Args:
        text: The text to analyze
        
    Returns:
        A tuple containing the detected intent and confidence score (0.0-1.0),
        or (None, 0.0) if no intent is detected
    """
    if not text or not isinstance(text, str):
        return None, 0.0
    
    # Normalize text: lowercase
    text = text.lower()
    
    # Check each intent pattern
    matches: Dict[str, int] = {}
    for intent, patterns in INTENT_PATTERNS.items():
        matches[intent] = 0
        for pattern in patterns:
            if re.search(pattern, text, re.IGNORECASE):
                matches[intent] += 1
    
    # Find the intent with the most matches
    max_matches = 0
    detected_intent = None
    
    for intent, count in matches.items():
        if count > max_matches:
            max_matches = count
            detected_intent = intent
    
    # Calculate confidence score (simple version)
    confidence = 0.0
    if max_matches > 0:
        # Base confidence on number of matches and text length
        # Shorter texts with matches should have higher confidence
        text_length_factor = min(1.0, 100 / max(len(text), 10))  # Normalize by text length
        confidence = min(0.95, 0.5 + (0.1 * max_matches) + (0.2 * text_length_factor))
    
    return detected_intent, confidence

def get_intent_response(intent: str, language: str = 'en') -> str:
    """
    Get a generic response for a detected intent
    
    Args:
        intent: The detected intent
        language: The language code (default: 'en')
        
    Returns:
        A generic response for the intent
    """
    responses = {
        'en': {
            'book_appointment': "I'd be happy to help you book an appointment. Could you please tell me what day and time would work best for you?",
            'cancel_appointment': "I can help you cancel or reschedule your appointment. Could you confirm which appointment you'd like to change?",
            'check_appointment': "I'll check your upcoming appointments for you. One moment please.",
            'greeting': "Hello! How can I assist you today?",
            'goodbye': "Thank you for contacting us. Have a great day!",
            'thanks': "You're welcome! Is there anything else I can help you with?",
            'help': "I'm here to help! I can assist with appointments, answer questions about our services, hours, or location. What would you like to know?",
            'hours': "Our business hours are Monday to Friday from 9 AM to 6 PM, and Saturday from 10 AM to 4 PM. We're closed on Sundays.",
            'services': "We offer a variety of services including consultations, treatments, and follow-up appointments. Would you like more specific information about any particular service?",
            'prices': "Our prices vary depending on the specific service. I'd be happy to provide you with detailed pricing information. Which service are you interested in?",
            'location': "We're located at 123 Main Street, Suite 456, in downtown. Would you like me to send you directions?"
        },
        'es': {
            'book_appointment': "Me encantaría ayudarte a reservar una cita. ¿Podrías decirme qué día y hora te vendría mejor?",
            'cancel_appointment': "Puedo ayudarte a cancelar o reprogramar tu cita. ¿Podrías confirmar qué cita te gustaría cambiar?",
            'check_appointment': "Verificaré tus próximas citas. Un momento por favor.",
            'greeting': "¡Hola! ¿Cómo puedo ayudarte hoy?",
            'goodbye': "Gracias por contactarnos. ¡Que tengas un buen día!",
            'thanks': "¡De nada! ¿Hay algo más en lo que pueda ayudarte?",
            'help': "¡Estoy aquí para ayudar! Puedo asistirte con citas, responder preguntas sobre nuestros servicios, horarios o ubicación. ¿Qué te gustaría saber?",
            'hours': "Nuestro horario de atención es de lunes a viernes de 9 AM a 6 PM, y sábados de 10 AM a 4 PM. Cerramos los domingos.",
            'services': "Ofrecemos una variedad de servicios que incluyen consultas, tratamientos y citas de seguimiento. ¿Te gustaría información más específica sobre algún servicio en particular?",
            'prices': "Nuestros precios varían según el servicio específico. Me encantaría proporcionarte información detallada sobre precios. ¿En qué servicio estás interesado?",
            'location': "Estamos ubicados en Calle Principal 123, Suite 456, en el centro. ¿Te gustaría que te enviara indicaciones?"
        }
    }
    
    # Default to English if the requested language is not available
    if language not in responses:
        language = 'en'
    
    # Return the response for the intent, or a default message if the intent is not recognized
    return responses[language].get(
        intent, 
        "I'm not sure I understand. Could you please rephrase your question?" if language == 'en' else 
        "No estoy seguro de entender. ¿Podrías reformular tu pregunta?"
    )
