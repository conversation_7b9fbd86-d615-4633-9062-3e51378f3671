"""
CORS Configuration Fix

This module provides a function to configure CORS for the FastAPI application.
It allows all origins during development to fix CORS issues.
"""

from fastapi.middleware.cors import CORSMiddleware
from fastapi import FastAPI
import logging

logger = logging.getLogger(__name__)

def configure_cors(app: FastAPI, is_development: bool = True):
    """
    Configure CORS for the FastAPI application.

    Args:
        app: The FastAPI application
        is_development: Whether the application is running in development mode
    """
    if is_development:
        # In development mode, allow all origins without credentials
        logger.info("Configuring CORS for development mode (allowing all origins)")
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # Allow all origins
            allow_credentials=False,  # Must be False when using wildcard origins
            allow_methods=["*"],  # Allow all methods
            allow_headers=["*"],  # Allow all headers
            expose_headers=["*"]  # Expose all headers
        )
    else:
        # In production mode, only allow specific origins
        logger.info("Configuring CORS for production mode (allowing specific origins)")
        app.add_middleware(
            CORSMiddleware,
            allow_origins=[
                "http://localhost:5173",
                "http://localhost:5174",
                "http://localhost:5175",
                "http://localhost:3000",
                # Add production domains here
            ],
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
            allow_headers=["*"],
            expose_headers=["*"]
        )
