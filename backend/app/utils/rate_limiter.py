"""
Rate limiting utility for API endpoints
"""
import time
from typing import <PERSON><PERSON>, <PERSON><PERSON>, Optional
from fastapi import HTTPEx<PERSON>, status

class RateLimiter:
    """
    Simple in-memory rate limiter
    """
    def __init__(self, max_attempts: int = 5, window_seconds: int = 300):
        """
        Initialize the rate limiter
        
        Args:
            max_attempts: Maximum number of attempts allowed in the time window
            window_seconds: Time window in seconds
        """
        self.max_attempts = max_attempts
        self.window_seconds = window_seconds
        self.attempts: Dict[str, Dict[str, Tuple[int, float]]] = {}  # {key: {action: (attempts, first_attempt_time)}}
    
    def check_rate_limit(self, key: str, action: str) -> None:
        """
        Check if the rate limit has been exceeded for the given key and action
        
        Args:
            key: Identifier for the rate limit (e.g., user ID, IP address)
            action: The action being rate limited (e.g., 'login', '2fa')
            
        Raises:
            HTTPException: If the rate limit has been exceeded
        """
        current_time = time.time()
        
        # Initialize if this is the first attempt for this key
        if key not in self.attempts:
            self.attempts[key] = {}
        
        # Initialize if this is the first attempt for this action
        if action not in self.attempts[key]:
            self.attempts[key][action] = (1, current_time)
            return
        
        attempts, first_attempt_time = self.attempts[key][action]
        
        # Check if the time window has expired
        if current_time - first_attempt_time > self.window_seconds:
            # Reset the counter
            self.attempts[key][action] = (1, current_time)
            return
        
        # Check if the maximum attempts have been exceeded
        if attempts >= self.max_attempts:
            # Calculate time remaining in the window
            time_remaining = int(self.window_seconds - (current_time - first_attempt_time))
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Too many attempts. Please try again in {time_remaining} seconds."
            )
        
        # Increment the counter
        self.attempts[key][action] = (attempts + 1, first_attempt_time)
    
    def reset(self, key: str, action: Optional[str] = None) -> None:
        """
        Reset the rate limit counter for the given key and action
        
        Args:
            key: Identifier for the rate limit
            action: The action to reset. If None, reset all actions for this key.
        """
        if key not in self.attempts:
            return
        
        if action is None:
            # Reset all actions for this key
            self.attempts.pop(key)
        elif action in self.attempts[key]:
            # Reset just this action
            self.attempts[key].pop(action)

# Create a global instance for 2FA rate limiting
# More restrictive limits for security-sensitive operations
twofa_rate_limiter = RateLimiter(max_attempts=5, window_seconds=300)  # 5 attempts per 5 minutes

# Create a global instance for login rate limiting
login_rate_limiter = RateLimiter(max_attempts=10, window_seconds=300)  # 10 attempts per 5 minutes
