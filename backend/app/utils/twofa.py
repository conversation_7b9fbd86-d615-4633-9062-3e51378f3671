"""
Two-Factor Authentication utilities
"""
import pyotp
import secrets
import string
from typing import List, Dict, Any, Tu<PERSON>
from sqlalchemy.orm import Session
from ..models.user import User

def generate_totp_secret() -> str:
    """Generate a new TOTP secret key"""
    return pyotp.random_base32()

def generate_totp_uri(secret: str, email: str, issuer: str = "FixMyCal") -> str:
    """Generate a TOTP URI for QR code generation"""
    totp = pyotp.TOTP(secret)
    return totp.provisioning_uri(name=email, issuer_name=issuer)

def verify_totp(secret: str, token: str) -> bool:
    """Verify a TOTP token against a secret"""
    totp = pyotp.TOTP(secret)
    return totp.verify(token)

def generate_backup_codes(count: int = 10) -> List[str]:
    """Generate backup codes for account recovery"""
    codes = []
    for _ in range(count):
        # Generate a random 8-character code
        code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
        # Format as XXXX-XXXX for readability
        formatted_code = f"{code[:4]}-{code[4:]}"
        codes.append(formatted_code)
    return codes

def verify_backup_code(user: User, code: str) -> bool:
    """Verify a backup code and remove it if valid"""
    # Normalize the code by removing any hyphens
    normalized_code = code.replace("-", "")
    
    # Check each backup code
    for i, backup_code in enumerate(user.backup_codes):
        # Normalize the stored code as well
        stored_code = backup_code.replace("-", "")
        if secrets.compare_digest(normalized_code, stored_code):
            # Remove the used backup code
            user.backup_codes.pop(i)
            return True
    
    return False

def setup_2fa_for_user(db: Session, user: User) -> Dict[str, Any]:
    """Set up 2FA for a user and return the secret and backup codes"""
    # Generate a new TOTP secret
    secret = generate_totp_secret()
    
    # Generate backup codes
    backup_codes = generate_backup_codes()
    
    # Update the user record
    user.twofa_secret = secret
    user.backup_codes = backup_codes
    # Don't enable 2FA yet - it will be enabled after verification
    user.twofa_enabled = False
    
    # Commit the changes
    db.commit()
    
    # Generate the TOTP URI for QR code
    totp_uri = generate_totp_uri(secret, user.email)
    
    return {
        "secret": secret,
        "totp_uri": totp_uri,
        "backup_codes": backup_codes
    }

def enable_2fa_for_user(db: Session, user: User, token: str) -> bool:
    """Enable 2FA for a user after verifying the token"""
    # Verify the token
    if not verify_totp(user.twofa_secret, token):
        return False
    
    # Enable 2FA
    user.twofa_enabled = True
    db.commit()
    
    return True

def disable_2fa_for_user(db: Session, user: User) -> bool:
    """Disable 2FA for a user"""
    user.twofa_enabled = False
    user.twofa_secret = None
    user.backup_codes = []
    db.commit()
    
    return True
