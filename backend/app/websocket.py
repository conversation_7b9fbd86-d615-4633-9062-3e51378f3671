"""
WebSocket server for real-time communication
"""
import json
import logging
from typing import Dict, List, Any, Optional
from fastapi import WebSocket, WebSocketDisconnect, Depends, Query
from jose import JWTError, jwt
from app.config import settings
from app.auth import SECRET_KEY, ALGORITHM

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        # Map of user_id to list of WebSocket connections
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        """Connect a WebSocket for a user"""
        await websocket.accept()

        if user_id not in self.active_connections:
            self.active_connections[user_id] = []

        self.active_connections[user_id].append(websocket)
        logger.info(f"WebSocket connected for user {user_id}. Total connections: {len(self.active_connections[user_id])}")

    def disconnect(self, websocket: WebSocket, user_id: str):
        """Disconnect a WebSocket for a user"""
        if user_id in self.active_connections:
            if websocket in self.active_connections[user_id]:
                self.active_connections[user_id].remove(websocket)

            # Remove the user entry if no connections remain
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]

        logger.info(f"WebSocket disconnected for user {user_id}")

    async def send_personal_message(self, message: Dict[str, Any], user_id: str):
        """Send a message to a specific user"""
        if user_id in self.active_connections:
            disconnected = []

            for connection in self.active_connections[user_id]:
                try:
                    await connection.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error sending message to user {user_id}: {str(e)}")
                    disconnected.append(connection)

            # Clean up any disconnected connections
            for connection in disconnected:
                self.disconnect(connection, user_id)

    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast a message to all connected users"""
        disconnected_users = []

        for user_id, connections in self.active_connections.items():
            disconnected = []

            for connection in connections:
                try:
                    await connection.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error broadcasting to user {user_id}: {str(e)}")
                    disconnected.append(connection)

            # Clean up any disconnected connections
            for connection in disconnected:
                self.disconnect(connection, user_id)

            # Mark user for removal if no connections remain
            if user_id in self.active_connections and not self.active_connections[user_id]:
                disconnected_users.append(user_id)

        # Remove any disconnected users
        for user_id in disconnected_users:
            if user_id in self.active_connections:
                del self.active_connections[user_id]

# Create a global connection manager
manager = ConnectionManager()

# Event types
class WebSocketEventType:
    WHATSAPP_MESSAGE = "whatsapp_message"
    WHATSAPP_STATUS = "whatsapp_status"
    APPOINTMENT_UPDATE = "appointment_update"
    NOTIFICATION = "notification"

async def get_user_from_token(token: Optional[str] = Query(None)) -> Optional[str]:
    """Get user ID from JWT token"""
    if not token:
        return None
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("id")

        if user_id is None:
            # Try to get user_id from 'sub' claim as fallback
            email = payload.get("sub")
            if email:
                # In a real implementation, you would look up the user_id from the email
                # For now, we'll just return the email as the user_id
                return email
            return None

        return user_id
    except JWTError as e:
        logger.error(f"JWT Error: {str(e)}")
        return None

async def websocket_endpoint(websocket: WebSocket, user_id: Optional[str] = Depends(get_user_from_token)):
    """WebSocket endpoint for real-time communication"""
    if not user_id:
        try:
            await websocket.accept()
            await websocket.send_text(json.dumps({
                "event": "error",
                "data": {
                    "message": "Invalid or missing authentication token"
                }
            }))
            await websocket.close(code=1008, reason="Invalid authentication token")
        except Exception as e:
            logger.error(f"Error handling unauthenticated WebSocket connection: {str(e)}")
        return

    try:
        await manager.connect(websocket, user_id)

        # Send a welcome message
        await websocket.send_text(json.dumps({
            "event": "connection_established",
            "data": {
                "message": "Connected to WebSocket server"
            }
        }))

        # Listen for messages from the client
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)

                # Process the message based on its event type
                event_type = message.get("event")
                event_data = message.get("data", {})

                logger.info(f"Received WebSocket message from user {user_id}: {event_type}")

                # Handle different event types
                if event_type == "ping":
                    await websocket.send_text(json.dumps({
                        "event": "pong",
                        "data": {
                            "timestamp": event_data.get("timestamp")
                        }
                    }))

                # Add more event handlers as needed

            except json.JSONDecodeError:
                logger.error(f"Invalid JSON received from user {user_id}")

    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {str(e)}")
        manager.disconnect(websocket, user_id)

# Function to send a message to a specific user
async def send_message_to_user(user_id: str, event_type: str, data: Dict[str, Any]):
    """Send a message to a specific user"""
    await manager.send_personal_message({
        "event": event_type,
        "data": data
    }, user_id)

# Function to broadcast a message to all users
async def broadcast_message(event_type: str, data: Dict[str, Any]):
    """Broadcast a message to all users"""
    await manager.broadcast({
        "event": event_type,
        "data": data
    })
