"""
Vector database setup for PostgreSQL
This module handles the setup of pgvector extension and vector operations
"""

from sqlalchemy import text
from sqlalchemy.orm import Session
import numpy as np
import logging

logger = logging.getLogger(__name__)

def setup_vector_extensions(db: Session):
    """
    Set up the pgvector extension in PostgreSQL if it's not already installed
    """
    try:
        # Check if pgvector extension is already installed
        result = db.execute(text("SELECT * FROM pg_extension WHERE extname = 'vector'")).fetchone()
        
        if not result:
            # Create the extension
            db.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
            db.commit()
            logger.info("Successfully installed pgvector extension")
        else:
            logger.info("pgvector extension is already installed")
            
    except Exception as e:
        logger.error(f"Error setting up vector extensions: {str(e)}")
        db.rollback()
        raise

def create_vector_index(db: Session, table_name: str, column_name: str, index_type: str = 'ivfflat'):
    """
    Create a vector index on a specific column
    
    Args:
        db: Database session
        table_name: Name of the table
        column_name: Name of the vector column
        index_type: Type of index (ivfflat, hnsw)
    """
    try:
        # Create index based on type
        if index_type == 'ivfflat':
            db.execute(text(f"CREATE INDEX IF NOT EXISTS idx_{table_name}_{column_name}_ivfflat ON {table_name} USING ivfflat ({column_name}) WITH (lists = 100)"))
        elif index_type == 'hnsw':
            db.execute(text(f"CREATE INDEX IF NOT EXISTS idx_{table_name}_{column_name}_hnsw ON {table_name} USING hnsw ({column_name})"))
        else:
            # Default to standard index
            db.execute(text(f"CREATE INDEX IF NOT EXISTS idx_{table_name}_{column_name} ON {table_name} USING ivfflat ({column_name})"))
        
        db.commit()
        logger.info(f"Successfully created {index_type} index on {table_name}.{column_name}")
    except Exception as e:
        logger.error(f"Error creating vector index: {str(e)}")
        db.rollback()
        raise

def vector_similarity_search(db: Session, table_name: str, column_name: str, query_vector, limit: int = 10, filter_clause: str = ""):
    """
    Perform a vector similarity search
    
    Args:
        db: Database session
        table_name: Name of the table
        column_name: Name of the vector column
        query_vector: The query vector (numpy array)
        limit: Maximum number of results to return
        filter_clause: Additional SQL WHERE clause for filtering
    
    Returns:
        List of results ordered by similarity
    """
    try:
        # Convert numpy array to string representation
        if isinstance(query_vector, np.ndarray):
            vector_str = ','.join(map(str, query_vector))
            vector_sql = f"'{vector_str}'"
        else:
            vector_sql = str(query_vector)
        
        # Build the query
        query = f"""
        SELECT *, {column_name} <-> {vector_sql}::vector AS distance
        FROM {table_name}
        {filter_clause}
        ORDER BY distance
        LIMIT {limit}
        """
        
        # Execute the query
        results = db.execute(text(query)).fetchall()
        return results
    except Exception as e:
        logger.error(f"Error performing vector similarity search: {str(e)}")
        raise
