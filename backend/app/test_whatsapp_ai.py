"""
Test script for WhatsApp AI integration
This script simulates a WhatsApp message and tests the AI response
"""

import os
import json
import logging
import asyncio
from datetime import datetime, timedelta
from uuid import UUID, uuid4
from sqlalchemy.orm import Session

from app.database import get_db, SessionLocal
from app.services.ai_assistant import AIAssistant
from app.services.embedding_service import embedding_service
from app.models.message import Message
from app.models.client import Client
from app.models.user import User
from app.models.service import Service
from app.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_whatsapp_ai_response(user_id: str, client_phone: str, message_text: str):
    """Test the AI response to a simulated WhatsApp message"""
    logger.info(f"Testing WhatsApp AI response for user {user_id}")

    # Create a database session
    db = SessionLocal()

    try:
        # Find the user
        try:
            user_uuid = UUID(user_id)
            user = db.query(User).filter(User.id == user_uuid).first()
            if not user:
                logger.error(f"User not found: {user_id}")
                return
        except ValueError:
            logger.error(f"Invalid user ID: {user_id}")
            return

        # Find the client by phone number
        client = db.query(Client).filter(
            Client.user_id == user_uuid,
            Client.phone.like(f"%{client_phone}%")
        ).first()

        # If client doesn't exist, create a new one
        if not client:
            logger.info(f"Client not found for phone {client_phone}, creating new client")
            client = Client(
                id=uuid4(),
                user_id=user_uuid,
                name=f"John Smith",  # Use a realistic name as if from WhatsApp contact
                phone=client_phone,
                email="",
                tags=["test"],
                notes="Automatically created for WhatsApp AI testing",
                created_at=datetime.now()
            )
            db.add(client)
            db.commit()
            db.refresh(client)

        # Generate embedding for the message
        message_embedding = await embedding_service.get_embedding(message_text)

        # Save the incoming message
        incoming_message = Message(
            user_id=user_uuid,
            client_id=client.id,
            content=message_text,
            is_from_business=False,  # From client
            sent_at=datetime.now(),
            embedding=message_embedding,
            source="whatsapp",  # This is a simulated WhatsApp message
            intent_data={
                "whatsapp_message_id": f"test-{uuid4()}",
                "whatsapp_chat_id": f"{client_phone}@s.whatsapp.net",
                "whatsapp_message_type": "conversation",
                "whatsapp_timestamp": int(datetime.now().timestamp()),
                "test": True
            }
        )
        db.add(incoming_message)
        db.commit()
        db.refresh(incoming_message)

        logger.info(f"Created test message: {message_text}")

        # Process the message with AI
        ai_assistant = AIAssistant(db, user_id)

        # Print the system prompt for debugging
        logger.info(f"System prompt:\n{ai_assistant.system_prompt}")

        response_text, action = await ai_assistant.process_message(str(client.id), message_text)

        # Generate embedding for the AI response
        response_embedding = await embedding_service.get_embedding(response_text)

        # Save the AI response
        outgoing_message = Message(
            user_id=user_uuid,
            client_id=client.id,
            content=response_text,
            is_from_business=True,  # From business
            sent_at=datetime.now(),
            embedding=response_embedding,
            parent_id=incoming_message.id,  # Link to the incoming message
            source="whatsapp",  # This is a simulated WhatsApp message
            intent_data={
                "whatsapp_parent_message_id": incoming_message.intent_data["whatsapp_message_id"],
                "whatsapp_chat_id": incoming_message.intent_data["whatsapp_chat_id"],
                "ai_generated": True,
                "response_timestamp": int(datetime.now().timestamp()),
                "test": True
            }
        )
        db.add(outgoing_message)
        db.commit()

        logger.info(f"AI Response: {response_text}")

        # Handle any actions (like booking appointments)
        if action and action.get("action") == "book_appointment":
            success, booking_message = await ai_assistant.book_appointment(action)

            if success:
                logger.info(f"Appointment booked successfully: {booking_message}")
                logger.info(f"Appointment details: {json.dumps(action, indent=2)}")
            else:
                logger.error(f"Failed to book appointment: {booking_message}")

        return {
            "success": True,
            "client_id": str(client.id),
            "message_id": str(incoming_message.id),
            "response_id": str(outgoing_message.id),
            "response_text": response_text,
            "action": action
        }

    except Exception as e:
        logger.error(f"Error testing WhatsApp AI response: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }
    finally:
        db.close()

async def main():
    """Run the test"""
    # Use a valid user ID from the database
    user_id = "b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7"  # <EMAIL>

    # Use a test phone number
    client_phone = "+1234567890"

    # Get available services for this user
    db = SessionLocal()
    try:
        services = db.query(Service).filter(Service.user_id == UUID(user_id)).all()
        if services:
            service = services[0]  # Use the first service
            service_id = str(service.id)
            service_name = service.name
            logger.info(f"Using service: {service_name} (ID: {service_id})")
        else:
            logger.warning("No services found for this user. Test may fail.")
            service_name = "Haircut"  # Fallback service name
    except Exception as e:
        logger.error(f"Error getting services: {str(e)}")
        service_name = "Haircut"  # Fallback service name
    finally:
        db.close()

    # Get tomorrow's date
    tomorrow = datetime.now() + timedelta(days=1)
    tomorrow_str = tomorrow.strftime("%A")

    # First message - booking request
    first_message = f"I'd like to book a {service_name} for {tomorrow_str} at 10am"
    logger.info(f"Sending first message: {first_message}")
    first_result = await test_whatsapp_ai_response(user_id, client_phone, first_message)

    if first_result and first_result.get("success"):
        logger.info(f"First response: {first_result['response_text']}")

        # Wait a moment before sending the second message
        await asyncio.sleep(2)

        # Second message - confirmation
        second_message = "Yes, that's correct. Please book it for me."
        logger.info(f"Sending second message: {second_message}")
        result = await test_whatsapp_ai_response(user_id, client_phone, second_message)

        # If no action was detected in the second message, try a more explicit confirmation
        if result and result.get("success") and not result.get("action"):
            logger.info("No action detected in second message, trying a more explicit confirmation")
            await asyncio.sleep(2)

            # Third message - explicit confirmation
            third_message = f"Yes, please book the {service_name} for tomorrow at 10am."
            logger.info(f"Sending third message: {third_message}")
            result = await test_whatsapp_ai_response(user_id, client_phone, third_message)
    else:
        logger.error("First message failed, skipping second message")
        result = first_result

    if result and result.get("success"):
        logger.info("\nTest completed successfully!")
        logger.info(f"Response: {result['response_text']}")

        if result.get("action"):
            logger.info(f"Action detected: {json.dumps(result['action'], indent=2)}")

            # If an action was detected, try to book the appointment
            if result["action"].get("action") == "book_appointment":
                logger.info("Attempting to book the appointment...")

                # Create a database session
                db = SessionLocal()
                try:
                    # Create AI assistant
                    ai_assistant = AIAssistant(db, user_id)

                    # Book the appointment
                    success, booking_message = await ai_assistant.book_appointment(result["action"])

                    if success:
                        logger.info(f"Appointment booked successfully: {booking_message}")
                    else:
                        logger.error(f"Failed to book appointment: {booking_message}")
                except Exception as e:
                    logger.error(f"Error booking appointment: {str(e)}")
                finally:
                    db.close()
        else:
            logger.warning("No action detected in the final response")
    else:
        logger.error("\nTest failed!")
        if result:
            logger.error(f"Error: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    asyncio.run(main())
