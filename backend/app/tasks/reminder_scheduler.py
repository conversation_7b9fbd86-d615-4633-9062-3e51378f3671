import asyncio
import logging
from datetime import datetime, timedelta
from sqlalchemy import and_, func
from sqlalchemy.orm import Session
from uuid import UUID

from app.database import get_db, SessionLocal
from app.models.appointment import Appointment, AppointmentStatus
from app.models.client import Client
from app.models.service import Service
from app.models.notification import NotificationPreference
from app.models.integration import ExternalIntegration
from app.models.user import User
from app.config import settings

import requests
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Evolution API headers
EVOLUTION_HEADERS = {
    "Content-Type": "application/json",
    "apikey": settings.EVOLUTION_API_KEY
}

async def send_reminder(
    user_id: UUID,
    appointment_id: UUID,
    client_phone: str,
    message: str
) -> bool:
    """Send a WhatsApp reminder for an appointment"""
    try:
        # Format phone number (remove any non-numeric characters)
        phone = ''.join(filter(str.isdigit, client_phone))
        
        # Add country code if not present
        if not phone.startswith('34') and not phone.startswith('1'):
            phone = '34' + phone  # Default to Spain country code
        
        # Check if the WhatsApp instance is connected
        status_response = requests.get(
            f"{settings.EVOLUTION_API_URL}/instance/connectionState/{user_id}",
            headers=EVOLUTION_HEADERS
        )
        
        if status_response.status_code != 200:
            logger.error(f"WhatsApp instance not found for user {user_id}")
            return False
        
        status_data = status_response.json()
        state = status_data.get('instance', {}).get('state')
        
        if state != 'open':
            logger.error(f"WhatsApp is not connected for user {user_id}. Current state: {state}")
            return False
        
        # Send the message
        send_response = requests.post(
            f"{settings.EVOLUTION_API_URL}/message/sendText/{user_id}",
            headers=EVOLUTION_HEADERS,
            json={
                "number": phone,
                "text": message,
                "delay": 1200,
                "linkPreview": False
            }
        )
        
        if send_response.status_code != 201:
            error_detail = send_response.text
            try:
                error_json = send_response.json()
                error_detail = json.dumps(error_json)
            except:
                pass
            
            logger.error(f"Failed to send WhatsApp message: {error_detail}")
            return False
        
        logger.info(f"Successfully sent reminder for appointment {appointment_id} to {phone}")
        return True
    except Exception as e:
        logger.error(f"Error sending WhatsApp reminder: {str(e)}")
        return False

async def check_and_send_reminders():
    """Check for upcoming appointments and send reminders"""
    logger.info("Starting reminder check...")
    
    db = SessionLocal()
    try:
        # Get all users with active WhatsApp integration
        users_with_whatsapp = db.query(User.id).join(
            ExternalIntegration,
            and_(
                ExternalIntegration.user_id == User.id,
                ExternalIntegration.integration_type == "whatsapp",
                ExternalIntegration.active == True
            )
        ).all()
        
        if not users_with_whatsapp:
            logger.info("No users with active WhatsApp integration found")
            return
        
        logger.info(f"Found {len(users_with_whatsapp)} users with active WhatsApp")
        
        # Current time
        now = datetime.now()
        
        # Process each user
        for user_row in users_with_whatsapp:
            user_id = user_row[0]
            
            # Get reminder settings for this user
            notification_prefs = db.query(NotificationPreference).filter(
                NotificationPreference.user_id == user_id,
                NotificationPreference.event_type == "appointment_reminder"
            ).first()
            
            if not notification_prefs or not notification_prefs.settings or not notification_prefs.settings.get("enabled", True):
                logger.info(f"Reminders not enabled for user {user_id}")
                continue
            
            # Get reminder times from settings
            reminder_times = notification_prefs.settings.get("reminder_times", [24, 1])
            
            # Get message template
            message_template = notification_prefs.settings.get(
                "message_template", 
                "Hi {{clientName}}, this is a reminder for your appointment on {{date}} at {{time}}. Please let us know if you need to reschedule."
            )
            
            # Process each reminder time
            for hours_before in reminder_times:
                # Calculate the time window for this reminder
                reminder_time = now + timedelta(hours=hours_before)
                window_start = reminder_time - timedelta(minutes=5)  # 5-minute window
                window_end = reminder_time + timedelta(minutes=5)
                
                # Find appointments in this time window
                appointments = db.query(Appointment).filter(
                    Appointment.user_id == user_id,
                    Appointment.status == AppointmentStatus.CONFIRMED,
                    Appointment.start_time > now,  # Only future appointments
                    Appointment.start_time <= now + timedelta(hours=hours_before + 1),  # Within the next (hours_before + 1) hours
                    # Check if the appointment is within the reminder window
                    func.date_part('hour', Appointment.start_time - now) >= hours_before - 0.1,
                    func.date_part('hour', Appointment.start_time - now) <= hours_before + 0.1
                ).all()
                
                logger.info(f"Found {len(appointments)} appointments for user {user_id} at {hours_before} hours before")
                
                # Process each appointment
                for appointment in appointments:
                    # Get client details
                    client = db.query(Client).filter(Client.id == appointment.client_id).first()
                    
                    if not client or not client.phone:
                        logger.warning(f"Client not found or doesn't have a phone number for appointment {appointment.id}")
                        continue
                    
                    # Get service details
                    service = db.query(Service).filter(Service.id == appointment.service_id).first()
                    
                    # Format date and time
                    appointment_date = appointment.start_time.strftime("%A, %B %d, %Y")
                    appointment_time = appointment.start_time.strftime("%I:%M %p")
                    
                    # Replace placeholders in the template
                    message = message_template.replace("{{clientName}}", client.name)
                    message = message.replace("{{date}}", appointment_date)
                    message = message.replace("{{time}}", appointment_time)
                    
                    # Add service details if enabled
                    if notification_prefs.settings.get("include_service_details", True) and service:
                        message = message.replace("{{service}}", service.name)
                    
                    # Add location if enabled
                    if notification_prefs.settings.get("include_location", True):
                        # Get user's business address
                        user_query = db.execute(f"SELECT address FROM users WHERE id = '{user_id}'")
                        user_data = user_query.fetchone()
                        location = user_data[0] if user_data and user_data[0] else "our location"
                        message = message.replace("{{location}}", location)
                    
                    # Send the reminder
                    success = await send_reminder(user_id, appointment.id, client.phone, message)
                    
                    if success:
                        # TODO: Log the reminder in a reminders table
                        logger.info(f"Sent reminder for appointment {appointment.id}")
                    else:
                        logger.error(f"Failed to send reminder for appointment {appointment.id}")
    
    except Exception as e:
        logger.error(f"Error in reminder scheduler: {str(e)}")
    finally:
        db.close()

async def run_scheduler():
    """Run the reminder scheduler periodically"""
    while True:
        await check_and_send_reminders()
        # Wait for 5 minutes before checking again
        await asyncio.sleep(300)

def start_scheduler():
    """Start the reminder scheduler in a background task"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.create_task(run_scheduler())
    loop.run_forever()
