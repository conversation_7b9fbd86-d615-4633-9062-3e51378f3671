"""
Task for periodically syncing WhatsApp messages
"""

import asyncio
import logging
import requests
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
from uuid import UUID

from sqlalchemy.orm import Session
from app.database import get_db
from app.models.user import User
from app.config import settings
from app.routes.whatsapp import process_new_messages

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Headers for Evolution API requests
EVOLUTION_HEADERS = {
    "Content-Type": "application/json",
    "apikey": settings.EVOLUTION_API_KEY
}

async def sync_whatsapp_messages():
    """
    Periodically check for and process any missed WhatsApp messages.

    This task runs in the background continuously, even when users are not logged in,
    ensuring that WhatsApp AI auto-responses work 24/7 without requiring user interaction.
    """
    logger.info("Starting WhatsApp message sync task")

    # Get database session
    db = next(get_db())

    try:
        # Get all users
        users = db.query(User).all()

        for user in users:
            user_id = str(user.id)

            # Check if the user has a WhatsApp instance
            try:
                state_response = requests.get(
                    f"{settings.EVOLUTION_API_URL}/instance/connectionState/{user_id}",
                    headers=EVOLUTION_HEADERS,
                    timeout=5
                )

                # Skip users without a connected WhatsApp instance
                if state_response.status_code != 200:
                    logger.info(f"User {user_id} does not have an active WhatsApp connection (status: {state_response.status_code}), skipping")
                    continue

                # Check the connection state in the correct nested structure
                state_data = state_response.json()
                connection_state = state_data.get('instance', {}).get('state')

                if connection_state != 'open':
                    logger.info(f"User {user_id} does not have an active WhatsApp connection (state: {connection_state}), skipping")
                    continue

                logger.info(f"Syncing WhatsApp messages for user {user_id}")

                # Get recent messages (last 24 hours)
                yesterday = int((datetime.now() - timedelta(days=1)).timestamp())

                # Fetch recent chats
                chats_response = requests.post(
                    f"{settings.EVOLUTION_API_URL}/chat/findChats/{user_id}",
                    headers=EVOLUTION_HEADERS,
                    json={
                        "where": {
                            "updatedAt": {"$gt": yesterday}
                        },
                        "limit": 20,
                        "sort": {"updatedAt": -1}
                    },
                    timeout=10
                )

                if chats_response.status_code != 200:
                    logger.error(f"Failed to fetch recent chats for user {user_id}: {chats_response.status_code} - {chats_response.text}")
                    continue

                chats_data = chats_response.json()

                # Process each chat
                for chat in chats_data:
                    chat_id = chat.get('id')
                    if not chat_id:
                        continue

                    # Fetch messages for this chat
                    messages_response = requests.post(
                        f"{settings.EVOLUTION_API_URL}/chat/findMessages/{user_id}",
                        headers=EVOLUTION_HEADERS,
                        json={
                            "where": {
                                "remoteJid": chat_id,
                                "fromMe": False,  # Only get messages from clients
                                "messageTimestamp": {"$gt": yesterday}
                            },
                            "limit": 50,
                            "sort": {"messageTimestamp": -1}
                        },
                        timeout=10
                    )

                    if messages_response.status_code != 200:
                        logger.error(f"Failed to fetch messages for chat {chat_id}: {messages_response.status_code} - {messages_response.text}")
                        continue

                    messages_data = messages_response.json()

                    if not messages_data:
                        continue

                    # Create webhook-like data structure
                    webhook_data = {
                        "event": "MESSAGES_UPSERT",
                        "data": {
                            "messages": messages_data
                        }
                    }

                    # Process the messages
                    await process_new_messages(user_id, webhook_data, db)

                    # Sleep briefly to avoid overwhelming the system
                    await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"Error syncing WhatsApp messages for user {user_id}: {str(e)}")
                continue

            # Sleep briefly between users
            await asyncio.sleep(2)

    except Exception as e:
        logger.error(f"Error in WhatsApp sync task: {str(e)}")
    finally:
        db.close()

    logger.info("Completed WhatsApp message sync task")

async def start_whatsapp_sync_task():
    """Start the WhatsApp sync task"""
    while True:
        try:
            await sync_whatsapp_messages()
        except Exception as e:
            logger.error(f"Error in WhatsApp sync task: {str(e)}")

        # Run every 15 minutes
        await asyncio.sleep(15 * 60)
