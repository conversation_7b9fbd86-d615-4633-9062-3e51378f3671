from fastapi import Request
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
import logging

logger = logging.getLogger(__name__)

class CORSMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Add CORS headers to all responses
        response = await call_next(request)

        # Log the request for debugging
        logger.debug(f"CORS Middleware: {request.method} {request.url.path}")

        # Add CORS headers
        origin = request.headers.get("origin", "http://localhost:5173")
        response.headers["Access-Control-Allow-Origin"] = origin
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, PATCH"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response.headers["Access-Control-Expose-Headers"] = "Content-Length, Content-Range, Content-Type"
        response.headers["Access-Control-Allow-Credentials"] = "true"

        # Handle preflight requests
        if request.method == "OPTIONS":
            return JSONResponse(
                content={"message": "OK"},
                status_code=200,
                headers=response.headers
            )

        return response
