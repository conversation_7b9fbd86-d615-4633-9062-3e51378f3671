"""
Test script for Gemini API integration
"""

import os
import json
import logging
import asyncio
from app.services.gemini_service import gemini_service
from app.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_text_generation():
    """Test text generation with Gemini API"""
    logger.info("Testing text generation with Gemini API...")
    
    try:
        # Test with a simple prompt
        prompt = "Write a haiku about AI"
        system_prompt = "You are a helpful AI assistant that writes creative poetry."
        
        response = await gemini_service.generate_text(prompt, system_prompt)
        
        logger.info("\nResponse:")
        logger.info(response)
        
        logger.info("\nText generation test: SUCCESS")
        return True
    except Exception as e:
        logger.error("\nText generation test: FAILED")
        logger.error("Error: %s", str(e))
        return False

async def test_embeddings():
    """Test embeddings with Gemini API"""
    logger.info("\nTesting embeddings with Gemini API...")
    
    try:
        # Test with a simple text
        text = "Hello, world!"
        
        embedding = await gemini_service.get_embedding(text)
        
        logger.info("\nEmbedding dimension: %s", len(embedding))
        logger.info("First 5 values: %s", embedding[:5])
        
        logger.info("\nEmbeddings test: SUCCESS")
        return True
    except Exception as e:
        logger.error("\nEmbeddings test: FAILED")
        logger.error("Error: %s", str(e))
        return False

async def main():
    """Run all tests"""
    logger.info("Using Gemini API key: %s...%s", settings.GEMINI_API_KEY[:5], settings.GEMINI_API_KEY[-4:])
    
    text_success = await test_text_generation()
    embeddings_success = await test_embeddings()
    
    if text_success and embeddings_success:
        logger.info("\nAll tests passed successfully!")
    else:
        logger.error("\nSome tests failed. Please check the error messages above.")

if __name__ == "__main__":
    asyncio.run(main())
