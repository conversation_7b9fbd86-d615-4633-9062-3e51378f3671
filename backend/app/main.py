from fastapi import Fast<PERSON><PERSON>, WebSocket, WebSocketDisconnect, Depends
from fastapi.middleware.cors import CORSMiddleware
from app.database import engine, get_db
from app.database.vector_setup import setup_vector_extensions, create_vector_index
from app.routes import client, auth, integrations, appointment, service, whatsapp, reminder, ai_messaging, messages, twofa, ai_configuration, conversations, ai_response, google_calendar_cache, media, client_lookup, billing, invoice, profile, address_verification, feedback
import logging
import threading
import asyncio
from app.tasks import start_scheduler
from app.tasks.whatsapp_sync import start_whatsapp_sync_task
from app.websocket import websocket_endpoint
from app.middleware.cors_middleware import CORSMiddleware as CustomCORSMiddleware

# Configure logging
logging.basicConfig(level=logging.DEBUG)

# Create the FastAPI app
app = FastAPI()

# Add our custom CORS middleware
app.add_middleware(CustomCORSMiddleware)

# Configure FastAPI's built-in CORS middleware as a fallback
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:5174", "http://localhost:3000"],  # Specific origins
    allow_credentials=True,  # Allow credentials
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],  # Specify methods explicitly
    allow_headers=["*"],  # Allow all headers
    expose_headers=["*"]  # Expose all headers
)

# Include routers with their prefixes
app.include_router(client.router, prefix="/dashboard")
app.include_router(auth.router, prefix="/auth")
app.include_router(integrations.router, prefix="/integrations")
app.include_router(appointment.router, prefix="/dashboard")
app.include_router(service.router, prefix="/dashboard")
app.include_router(whatsapp.router, prefix="/whatsapp")
app.include_router(reminder.router, prefix="/dashboard")
app.include_router(ai_messaging.router, prefix="/ai")
app.include_router(messages.router, prefix="/dashboard/messages")
app.include_router(twofa.router, prefix="/auth")
app.include_router(ai_configuration.router, prefix="/ai")
app.include_router(ai_response.router)
app.include_router(conversations.router)
app.include_router(google_calendar_cache.router)
app.include_router(media.router)
app.include_router(client_lookup.router)
app.include_router(billing.router, prefix="/dashboard")
app.include_router(invoice.router, prefix="/dashboard")
app.include_router(profile.router, prefix="/profile")
app.include_router(address_verification.router, prefix="/dashboard")
app.include_router(feedback.router, prefix="/dashboard")

# WebSocket endpoint
app.websocket("/ws")(websocket_endpoint)

# Optional: Add a root endpoint
@app.get("/")
def read_root():
    return {"message": "Welcome to the API"}

# Initialize vector database
db = next(get_db())
try:
    # Setup pgvector extension
    setup_vector_extensions(db)

    # Create vector index on messages table
    create_vector_index(db, "messages", "embedding")

    # Create vector index on knowledge_documents table
    create_vector_index(db, "knowledge_documents", "embedding")

    logging.info("Vector database setup completed successfully")
except Exception as e:
    logging.error(f"Error setting up vector database: {str(e)}")
finally:
    db.close()

# Start the reminder scheduler in a background thread
# This runs continuously even when users are not logged in
scheduler_thread = threading.Thread(target=start_scheduler, daemon=True)
scheduler_thread.start()

# Start the WhatsApp sync task in a background thread
# This ensures WhatsApp messages are processed and AI auto-responses work 24/7
# even when users are not logged into the web application
def run_whatsapp_sync():
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(start_whatsapp_sync_task())

whatsapp_sync_thread = threading.Thread(target=run_whatsapp_sync, daemon=True)
whatsapp_sync_thread.start()