"""
Two-Factor Authentication API endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from uuid import UUID

from ..database import get_db
from ..models.user import User
from ..auth import get_current_user
from ..utils.twofa import (
    setup_2fa_for_user,
    enable_2fa_for_user,
    disable_2fa_for_user,
    verify_totp,
    verify_backup_code
)
from ..utils.rate_limiter import twofa_rate_limiter

router = APIRouter(prefix="/2fa", tags=["2fa"])

# Request and response models
class TwoFASetupResponse(BaseModel):
    secret: str
    totp_uri: str
    backup_codes: List[str]

class TwoFAVerifyRequest(BaseModel):
    token: str

class TwoFAStatusResponse(BaseModel):
    enabled: bool

class TwoFABackupCodesResponse(BaseModel):
    backup_codes: List[str]

class TwoFALoginRequest(BaseModel):
    email: str
    password: str
    token: Optional[str] = None
    backup_code: Optional[str] = None

# Helper function to get the current user from the database
async def get_user_from_db(current_user: Dict = Depends(get_current_user), db: Session = Depends(get_db)) -> User:
    user_id = UUID(current_user["id"])
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@router.get("/status", response_model=TwoFAStatusResponse)
async def get_2fa_status(user: User = Depends(get_user_from_db)):
    """Get the current 2FA status for the user"""
    return {"enabled": user.twofa_enabled}

@router.post("/setup", response_model=TwoFASetupResponse)
async def setup_2fa(user: User = Depends(get_user_from_db), db: Session = Depends(get_db)):
    """Set up 2FA for the user"""
    # Check if 2FA is already enabled
    if user.twofa_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Two-factor authentication is already enabled"
        )

    # Set up 2FA
    setup_data = setup_2fa_for_user(db, user)

    return setup_data

@router.post("/verify", response_model=TwoFAStatusResponse)
async def verify_2fa(
    request: TwoFAVerifyRequest,
    user: User = Depends(get_user_from_db),
    db: Session = Depends(get_db),
    req: Request = None
):
    """Verify and enable 2FA for the user"""
    # Apply rate limiting
    twofa_rate_limiter.check_rate_limit(str(user.id), "verify")

    # Check if 2FA is already enabled
    if user.twofa_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Two-factor authentication is already enabled"
        )

    # Check if 2FA has been set up
    if not user.twofa_secret:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Two-factor authentication has not been set up"
        )

    # Verify the token and enable 2FA
    if not enable_2fa_for_user(db, user, request.token):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid verification code"
        )

    # Reset rate limit counter on success
    twofa_rate_limiter.reset(str(user.id), "verify")

    return {"enabled": True}

@router.post("/disable", response_model=TwoFAStatusResponse)
async def disable_2fa(
    request: TwoFAVerifyRequest,
    user: User = Depends(get_user_from_db),
    db: Session = Depends(get_db),
    req: Request = None
):
    """Disable 2FA for the user"""
    # Apply rate limiting
    twofa_rate_limiter.check_rate_limit(str(user.id), "disable")

    # Check if 2FA is enabled
    if not user.twofa_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Two-factor authentication is not enabled"
        )

    # Verify the token before disabling 2FA
    if not verify_totp(user.twofa_secret, request.token):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid verification code"
        )

    # Disable 2FA
    disable_2fa_for_user(db, user)

    # Reset rate limit counter on success
    twofa_rate_limiter.reset(str(user.id), "disable")

    return {"enabled": False}

@router.get("/backup-codes", response_model=TwoFABackupCodesResponse)
async def get_backup_codes(user: User = Depends(get_user_from_db)):
    """Get the backup codes for the user"""
    # Check if 2FA is enabled
    if not user.twofa_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Two-factor authentication is not enabled"
        )

    return {"backup_codes": user.backup_codes}
