from fastapi import APIRouter, Depends, HTTPException, Request, Response
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from uuid import UUID, uuid4
import json

# Try to import stripe, but don't fail if it's not available
try:
    import stripe
    STRIPE_AVAILABLE = True
except ImportError:
    STRIPE_AVAILABLE = False
    print('Warning: Stripe module not available. Billing functionality will be limited.')

from pydantic import BaseModel

from ..config import settings

# Initialize Stripe with the API key if available
if STRIPE_AVAILABLE and settings.STRIPE_SECRET_KEY:
    stripe.api_key = settings.STRIPE_SECRET_KEY

from ..database import get_db
from ..models.pricing import PricingPlan
from ..models.invoice import Invoice
from ..models.user import User
from ..models.user import User
from ..auth import get_current_user

# Create router with the correct prefix and tags
router = APIRouter(tags=["billing"])

# Pydantic models for request/response
class StripeCheckoutRequest(BaseModel):
    plan_id: str
    success_url: str
    cancel_url: str
    is_annual: bool = False

class StripePortalRequest(BaseModel):
    return_url: str

# Get all pricing plans
@router.get("/billing/plans")
async def get_pricing_plans(db: Session = Depends(get_db)):
    """Get all available pricing plans"""
    # Sort plans by monthly_fee to ensure consistent order
    plans = db.query(PricingPlan).order_by(PricingPlan.monthly_fee).all()

    # If no plans exist, create default plans
    if not plans:
        default_plans = [
            PricingPlan(
                id=uuid4(),
                name="Starter",
                monthly_fee=50,
                cost_per_message=0.01,
                cost_per_appointment=0,
                features=json.dumps([
                    "Up to 50 appointments/month",
                    "500 AI responses/month",
                    "Email support"
                ])
            ),
            PricingPlan(
                id=uuid4(),
                name="Professional",
                monthly_fee=100,
                cost_per_message=0.01,
                cost_per_appointment=0,
                features=json.dumps([
                    "Unlimited appointments",
                    "5,000 AI responses/month",
                    "Priority support"
                ])
            ),
            PricingPlan(
                id=uuid4(),
                name="Enterprise",
                monthly_fee=0,  # Custom pricing
                cost_per_message=0.01,
                cost_per_appointment=0,
                features=json.dumps([
                    "Unlimited appointments",
                    "Unlimited AI responses",
                    "24/7 support",
                    "Custom integrations"
                ])
            )
        ]

        for plan in default_plans:
            db.add(plan)

        db.commit()
        plans = default_plans

    # Format plans for response
    formatted_plans = []
    for plan in plans:
        formatted_plans.append({
            "id": str(plan.id),
            "name": plan.name,
            "monthlyFee": plan.monthly_fee,
            "features": json.loads(plan.features) if isinstance(plan.features, str) else plan.features,
            "aiResponsesLimit": 5000 if plan.name == "Professional" else (500 if plan.name == "Starter" else 999999),
            "appointmentsLimit": 50 if plan.name == "Starter" else None
        })

    return formatted_plans

# Get user's current billing info
@router.get("/billing/info")
async def get_billing_info(db: Session = Depends(get_db), current_user: Dict = Depends(get_current_user)):
    """Get the current user's billing information"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Get the user
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Get the user's current plan
    plan = db.query(PricingPlan).filter(PricingPlan.id == user.plan_id).first()
    if not plan:
        # If no plan is assigned, use a default plan
        plan = db.query(PricingPlan).first()
        if not plan:
            # Create a default plan if none exists
            plan = PricingPlan(
                id=uuid4(),
                name="Free",
                monthly_fee=0,
                cost_per_message=0.01,
                cost_per_appointment=0,
                features=json.dumps(["Limited features", "Basic support"])
            )
            db.add(plan)
            db.commit()
            db.refresh(plan)

    # Calculate AI responses used (mock data for now)
    # Use different values based on the plan
    if plan.name == "Starter":
        ai_responses_used = 345
    elif plan.name == "Professional":
        ai_responses_used = 3245
    elif plan.name == "Enterprise":
        ai_responses_used = 5000
    else:
        ai_responses_used = 100  # Default for unknown plans

    # Calculate renewal date (mock data for now)
    renewal_date = datetime.now() + timedelta(days=30)

    # Mock payment method (this would come from your payment processor)
    payment_method = {
        "type": "visa",
        "last4": "4242",
        "expiryMonth": 12,
        "expiryYear": 2025
    }

    # Return billing info
    return {
        "currentPlan": {
            "id": str(plan.id),
            "name": plan.name,
            "monthlyFee": plan.monthly_fee,
            "features": json.loads(plan.features) if isinstance(plan.features, str) else plan.features,
            "aiResponsesLimit": 5000,  # This would be defined in the plan
            "appointmentsLimit": None  # None means unlimited
        },
        "renewalDate": renewal_date.isoformat(),
        "aiResponsesUsed": ai_responses_used,
        "aiResponsesLimit": 5000,  # This would be defined in the plan
        "paymentMethod": payment_method
    }

# Get user's invoices
@router.get("/billing/invoices")
async def get_invoices(db: Session = Depends(get_db), current_user: Dict = Depends(get_current_user)):
    """Get the current user's invoice history"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Get the user's invoices
    invoices = db.query(Invoice).filter(Invoice.user_id == user_id).all()

    # Format invoices for response
    formatted_invoices = []
    for invoice in invoices:
        formatted_invoices.append({
            "id": str(invoice.id),
            "date": invoice.period_end.isoformat(),
            "amount": invoice.total,
            "status": invoice.status,
            "type": "subscription" if invoice.stripe_invoice_id else "usage",
            "invoiceUrl": f"/dashboard/billing/invoices/{invoice.id}/download",
            "stripe_invoice_id": invoice.stripe_invoice_id,
            "hosted_invoice_url": invoice.hosted_invoice_url,
            "invoice_pdf": invoice.invoice_pdf_url
        })

    # If no invoices, return mock data
    if not formatted_invoices:
        # Mock invoice data
        today = datetime.now()
        formatted_invoices = [
            {
                "id": "inv_1",
                "date": (today - timedelta(days=15)).isoformat(),
                "amount": 50,
                "status": "paid",
                "type": "subscription",
                "invoiceUrl": "#",
                "stripe_invoice_id": None,
                "hosted_invoice_url": None,
                "invoice_pdf": None
            },
            {
                "id": "inv_2",
                "date": (today - timedelta(days=15)).isoformat(),
                "amount": 4.25,
                "status": "paid",
                "type": "usage",
                "invoiceUrl": "#",
                "stripe_invoice_id": None,
                "hosted_invoice_url": None,
                "invoice_pdf": None
            },
            {
                "id": "inv_3",
                "date": (today - timedelta(days=45)).isoformat(),
                "amount": 50,
                "status": "paid",
                "type": "subscription",
                "invoiceUrl": "#",
                "stripe_invoice_id": None,
                "hosted_invoice_url": None,
                "invoice_pdf": None
            }
        ]

    return formatted_invoices

# Create Stripe checkout session
@router.post("/billing/checkout")
async def create_checkout_session(
    request: StripeCheckoutRequest,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Create a Stripe checkout session for subscription"""
    # Set the Stripe API key
    stripe.api_key = settings.STRIPE_SECRET_KEY

    if not stripe.api_key:
        raise HTTPException(status_code=501, detail="Stripe integration not configured")

    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Get the user
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Get the plan
    plan = db.query(PricingPlan).filter(PricingPlan.id == UUID(request.plan_id)).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")

    # Ensure the user has a Stripe customer ID
    customer_id = await ensure_stripe_customer(user, db)

    try:
        # Create a Stripe checkout session
        # Choose the appropriate price ID based on billing interval
        price_id = None
        if request.is_annual and plan.stripe_annual_price_id:
            price_id = plan.stripe_annual_price_id
        elif not request.is_annual and plan.stripe_price_id:
            price_id = plan.stripe_price_id

        if price_id:
            # If the plan has a Stripe price ID, use it
            checkout_session = stripe.checkout.Session.create(
                customer=customer_id,  # Use the customer ID instead of email
                payment_method_types=["card"],
                line_items=[{
                    "price": price_id,
                    "quantity": 1
                }],
                mode="subscription",
                success_url=request.success_url,
                cancel_url=request.cancel_url,
                metadata={
                    "user_id": str(user_id),
                    "plan_id": request.plan_id,
                    "is_annual": str(request.is_annual).lower()
                }
            )
        else:
            # Fallback to creating a price on the fly
            # Calculate the price based on billing interval
            unit_amount = 0
            interval = "month"

            if request.is_annual:
                # Apply 20% discount for annual billing
                unit_amount = int(plan.monthly_fee * 12 * 0.8 * 100)  # Convert to cents
                interval = "year"
            else:
                unit_amount = int(plan.monthly_fee * 100)  # Convert to cents
                interval = "month"

            checkout_session = stripe.checkout.Session.create(
                customer=customer_id,  # Use the customer ID instead of email
                payment_method_types=["card"],
                line_items=[{
                    "price_data": {
                        "currency": "eur",
                        "product_data": {
                            "name": f"{plan.name} Plan ({interval}ly)",
                            "description": f"{plan.name} subscription plan ({interval}ly billing)"
                        },
                        "unit_amount": unit_amount,
                        "recurring": {
                            "interval": interval
                        }
                    },
                    "quantity": 1
                }],
                mode="subscription",
                success_url=request.success_url,
                cancel_url=request.cancel_url,
                metadata={
                    "user_id": str(user_id),
                    "plan_id": request.plan_id,
                    "is_annual": str(request.is_annual).lower()
                }
            )

        return {"url": checkout_session.url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Helper function to ensure a user has a Stripe customer ID
async def ensure_stripe_customer(user: User, db: Session):
    """Ensure the user has a Stripe customer ID, creating one if needed"""
    # For testing purposes, always return a mock customer ID
    if not user.stripe_customer_id:
        mock_customer_id = f"mock_cus_{str(user.id).replace('-', '')[:16]}"
        user.stripe_customer_id = mock_customer_id
        db.commit()
    return user.stripe_customer_id

# Create Stripe customer portal session
@router.post("/billing/portal")
async def create_portal_session(
    request: StripePortalRequest,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Create a Stripe customer portal session"""
    # Set the Stripe API key
    stripe.api_key = settings.STRIPE_SECRET_KEY

    if not stripe.api_key:
        raise HTTPException(status_code=501, detail="Stripe integration not configured")

    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Get the user
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Ensure the user has a Stripe customer ID
    customer_id = await ensure_stripe_customer(user, db)

    # For testing purposes, use the pre-created customer portal link
    return {"url": "https://billing.stripe.com/p/login/test_00w7sKdqV5ph0rC9x32oE00"}

# Stripe webhook handler
@router.post("/billing/webhook")
async def stripe_webhook(request: Request, db: Session = Depends(get_db)):
    """Handle Stripe webhook events"""
    if not settings.STRIPE_SECRET_KEY:
        raise HTTPException(status_code=501, detail="Stripe integration not configured")

    # Get the request body
    payload = await request.body()

    try:
        # If webhook secret is configured, verify the signature
        if settings.STRIPE_WEBHOOK_SECRET:
            # Get the webhook signature
            signature = request.headers.get("stripe-signature")
            if not signature:
                raise HTTPException(status_code=400, detail="Missing Stripe signature")

            # Verify the webhook signature
            event = stripe.Webhook.construct_event(
                payload, signature, settings.STRIPE_WEBHOOK_SECRET
            )
        else:
            # For testing without a webhook secret, parse the payload directly
            payload_json = json.loads(payload)
            event = payload_json
    except Exception as e:
        return JSONResponse(status_code=400, content={"detail": f"Error verifying webhook: {str(e)}"})

    try:
        # Handle the event
        if event["type"] == "checkout.session.completed":
            # Get the checkout session
            session = event["data"]["object"]

            # Get the user and plan from the metadata
            user_id = session["metadata"]["user_id"]
            plan_id = session["metadata"]["plan_id"]

            # Get the user
            user = db.query(User).filter(User.id == UUID(user_id)).first()
            if not user:
                return JSONResponse(status_code=404, content={"detail": "User not found"})

            # Get the plan
            plan = db.query(PricingPlan).filter(PricingPlan.id == UUID(plan_id)).first()
            if not plan:
                return JSONResponse(status_code=404, content={"detail": "Plan not found"})

            # Update the user's plan and Stripe customer ID
            user.plan_id = plan.id
            user.stripe_customer_id = session["customer"]
            db.commit()

            # Create an invoice
            try:
                # Get the Stripe invoice details
                stripe_invoice = stripe.Invoice.retrieve(session["invoice"])

                invoice = Invoice(
                    id=uuid4(),
                    user_id=user.id,
                    plan_id=plan.id,
                    period_start=datetime.now().date(),
                    period_end=(datetime.now() + timedelta(days=30)).date(),
                    total=plan.monthly_fee,
                    stripe_invoice_id=session["invoice"],
                    status="paid",
                    invoice_pdf_url=stripe_invoice.invoice_pdf if hasattr(stripe_invoice, 'invoice_pdf') else None,
                    hosted_invoice_url=stripe_invoice.hosted_invoice_url if hasattr(stripe_invoice, 'hosted_invoice_url') else None
                )
                db.add(invoice)
                db.commit()
            except Exception as e:
                print(f"Error creating invoice: {str(e)}")
                # Create a basic invoice without Stripe URLs if there's an error
                invoice = Invoice(
                    id=uuid4(),
                    user_id=user.id,
                    plan_id=plan.id,
                    period_start=datetime.now().date(),
                    period_end=(datetime.now() + timedelta(days=30)).date(),
                    total=plan.monthly_fee,
                    stripe_invoice_id=session["invoice"],
                    status="paid"
                )
                db.add(invoice)
                db.commit()

        return Response(status_code=200)
    except Exception as e:
        print(f"Error processing webhook: {str(e)}")
        return JSONResponse(status_code=400, content={"detail": f"Error processing webhook: {str(e)}"})

# Change plan (manual, without Stripe)
@router.post("/billing/change-plan")
async def change_plan(
    plan_id: str,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Change the user's subscription plan (manual, without Stripe)"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Get the user
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Get the plan
    plan = db.query(PricingPlan).filter(PricingPlan.id == UUID(plan_id)).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")

    # Update the user's plan
    user.plan_id = plan.id
    db.commit()

    # Return updated billing info
    return await get_billing_info(db, current_user)

# Update payment method
@router.post("/billing/update-payment")
async def update_payment_method(
    payment_method_id: str,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Update the user's payment method"""
    # This would integrate with your payment processor
    # For now, just return the current billing info
    return await get_billing_info(db, current_user)

# Download invoice
@router.get("/billing/invoices/{invoice_id}/download")
async def download_invoice(
    invoice_id: str,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Download a specific invoice"""
    # For testing purposes, we'll allow mock invoices even if Stripe is not configured
    stripe_configured = settings.STRIPE_SECRET_KEY is not None

    try:
        # Check if this is a mock invoice ID (starts with 'inv_')
        if invoice_id.startswith('inv_') and len(invoice_id) < 10:
            # For mock invoices, return a mock PDF URL
            return {"invoice_pdf": None, "hosted_invoice_url": None, "message": "This is a mock invoice and cannot be downloaded"}

        # Check if this is a UUID (our internal invoice ID)
        try:
            invoice_uuid = UUID(invoice_id)
            # Get the invoice from the database
            invoice = db.query(Invoice).filter(Invoice.id == invoice_uuid).first()
            if not invoice:
                raise HTTPException(status_code=404, detail="Invoice not found")

            # Check if the invoice belongs to the current user
            if str(invoice.user_id) != current_user["id"]:
                raise HTTPException(status_code=403, detail="Not authorized to access this invoice")

            # If the invoice has a Stripe invoice ID, get the URLs from Stripe
            if invoice.stripe_invoice_id:
                # If we already have the URLs stored in the database, use those
                if invoice.invoice_pdf_url or invoice.hosted_invoice_url:
                    return {
                        "invoice_pdf": invoice.invoice_pdf_url,
                        "hosted_invoice_url": invoice.hosted_invoice_url
                    }

                # If Stripe is configured, try to get the URLs from Stripe
                if stripe_configured:
                    try:
                        stripe_invoice = stripe.Invoice.retrieve(invoice.stripe_invoice_id)

                        # Store the URLs in the database for future use
                        invoice.invoice_pdf_url = stripe_invoice.invoice_pdf
                        invoice.hosted_invoice_url = stripe_invoice.hosted_invoice_url
                        db.commit()

                        return {
                            "invoice_pdf": stripe_invoice.invoice_pdf,
                            "hosted_invoice_url": stripe_invoice.hosted_invoice_url
                        }
                    except stripe.error.StripeError as e:
                        # If there's an error with Stripe, return a message
                        return {"invoice_pdf": None, "hosted_invoice_url": None, "message": f"Stripe error: {str(e)}"}
                else:
                    # If Stripe is not configured, return a message
                    return {"invoice_pdf": None, "hosted_invoice_url": None, "message": "Stripe is not configured. This is a mock invoice for testing purposes."}
            else:
                # If the invoice doesn't have a Stripe invoice ID, it's a mock invoice
                return {"invoice_pdf": None, "hosted_invoice_url": None, "message": "This invoice doesn't have a Stripe invoice ID"}
        except ValueError:
            # Not a UUID, assume it's a Stripe invoice ID
            if stripe_configured:
                stripe_invoice = stripe.Invoice.retrieve(invoice_id)

                # Return the PDF URL and hosted invoice URL
                return {
                    "invoice_pdf": stripe_invoice.invoice_pdf,
                    "hosted_invoice_url": stripe_invoice.hosted_invoice_url
                }
            else:
                # If Stripe is not configured, return a message
                return {"invoice_pdf": None, "hosted_invoice_url": None, "message": "Stripe is not configured. Cannot retrieve invoice directly from Stripe."}
    except stripe.error.StripeError as e:
        raise HTTPException(status_code=400, detail=f"Stripe error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving invoice: {str(e)}")
