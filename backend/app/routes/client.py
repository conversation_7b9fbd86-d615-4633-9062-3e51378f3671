from fastapi import APIRouter, Depends, HTTPException, Query, Response
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
from datetime import datetime
import csv
import io
from ..database import get_db
from ..models.client import Client, ClientSchema, PaginatedClientResponse
from ..auth import get_current_user
from typing import List, Dict, Optional
from math import ceil

# Create router with the correct prefix and tags
router = APIRouter(tags=["clients"])

@router.get("/clients/tags/{client_id}", summary="View Client Tags")
async def debug_client_tags(
    client_id: str,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """View and inspect tags for a specific client"""
    try:
        # Convert the client ID to UUID
        from uuid import UUID
        client_uuid = UUID(client_id)

        # Get the client
        client = db.query(Client).filter(Client.id == client_uuid).first()

        if not client:
            return {"error": "Client not found"}

        # Return client info and tags
        return {
            "client_id": str(client.id),
            "name": client.name,
            "tags": client.tags,
            "tags_type": str(type(client.tags)),
            "tags_repr": repr(client.tags)
        }
    except Exception as e:
        return {"error": str(e)}

@router.get("/clients/all-tags", summary="View All Client Tags")
async def debug_all_client_tags(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """View and inspect tags for all clients"""
    try:
        # Convert user_id from string to UUID
        from uuid import UUID
        user_id = UUID(current_user["id"])

        # Get all clients for this user
        clients = db.query(Client).filter(Client.user_id == user_id).all()

        # Return client info and tags
        result = []
        for client in clients:
            result.append({
                "client_id": str(client.id),
                "name": client.name,
                "tags": client.tags,
                "tags_type": str(type(client.tags)),
                "tags_repr": repr(client.tags)
            })

        return result
    except Exception as e:
        return {"error": str(e)}

@router.get("/clients/filter-tag/{tag}", summary="Filter Clients by Tag")
async def debug_filter_tag(
    tag: str,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Filter and view clients that have a specific tag"""
    try:
        # Convert user_id from string to UUID
        from uuid import UUID
        user_id = UUID(current_user["id"])

        # Get all clients for this user
        all_clients = db.query(Client).filter(Client.user_id == user_id).all()

        # Filter clients that have the tag
        filtered_clients = []
        for client in all_clients:
            # Handle case where tags might be None
            client_tags = client.tags or []

            # Try different approaches to find the tag
            found = False

            # Approach 1: Direct comparison
            if tag in client_tags:
                found = True
                reason = "Direct comparison"

            # Approach 2: Case-insensitive comparison
            if not found and any(t.lower() == tag.lower() for t in client_tags if isinstance(t, str)):
                found = True
                reason = "Case-insensitive comparison"

            # Approach 3: Check if it's a string that contains the tag
            if not found and isinstance(client_tags, str) and tag.lower() in client_tags.lower():
                found = True
                reason = "Substring in string"

            if found:
                filtered_clients.append({
                    "client_id": str(client.id),
                    "name": client.name,
                    "tags": client_tags,
                    "found_by": reason
                })

        return {
            "tag": tag,
            "total_clients": len(all_clients),
            "filtered_clients": len(filtered_clients),
            "clients": filtered_clients
        }
    except Exception as e:
        return {"error": str(e)}

@router.get("/clients", response_model=PaginatedClientResponse)
async def get_clients(
    sort_by: str = "name",
    order: str = "asc",
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Items per page"),
    search: Optional[str] = None,
    tag_filter: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Fetch all clients from the database with sorting"""
    # Validate sort_by field
    valid_sort_fields = ["name", "email", "last_appointment", "next_appointment"]
    if sort_by not in valid_sort_fields:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid sort field. Must be one of: {', '.join(valid_sort_fields)}"
        )

    # Validate order
    if order not in ["asc", "desc"]:
        raise HTTPException(
            status_code=400,
            detail="Invalid order. Must be 'asc' or 'desc'"
        )

    # Handle null values for appointment fields
    if sort_by in ["last_appointment", "next_appointment"]:
        sort_column = func.coalesce(getattr(Client, sort_by), datetime.min)
    else:
        sort_column = getattr(Client, sort_by)

    # Apply ordering
    if order == "desc":
        sort_column = sort_column.desc()
    else:
        sort_column = sort_column.asc()

    # Convert the user ID from the token to UUID for the database query
    from uuid import UUID
    user_id = UUID(current_user["id"])

    # Start building the query
    query = db.query(Client).filter(Client.user_id == user_id)

    # Apply search filter if provided
    if search:
        search_term = f"%{search.lower()}%"
        query = query.filter(
            func.lower(Client.name).like(search_term) |
            func.lower(Client.email).like(search_term) |
            Client.phone.like(search_term)
        )

    # Apply tag filter if provided
    if tag_filter:
        # Get all clients for this user
        all_clients = db.query(Client).filter(Client.user_id == user_id).all()

        # Filter clients that have the tag
        filtered_client_ids = []
        for client in all_clients:
            # Handle case where tags might be None
            client_tags = client.tags or []

            # Try different approaches to find the tag
            found = False

            # Approach 1: Direct comparison (exact match in array)
            if isinstance(client_tags, list) and tag_filter in client_tags:
                found = True

            # Approach 2: Case-insensitive comparison
            if not found and isinstance(client_tags, list) and any(tag.lower() == tag_filter.lower() for tag in client_tags if isinstance(tag, str)):
                found = True

            # Approach 3: Check if it's a string that contains the tag
            if not found and isinstance(client_tags, str) and tag_filter.lower() in client_tags.lower():
                found = True

            if found:
                filtered_client_ids.append(client.id)

        # If we found any clients with this tag, filter the query
        if filtered_client_ids:
            query = db.query(Client).filter(Client.id.in_(filtered_client_ids))
        else:
            # If no clients have this tag, return an empty result
            query = db.query(Client).filter(Client.id == None)  # This will return no results

    # Count total matching records for pagination info
    total_count = query.count()
    total_pages = ceil(total_count / page_size)

    # Apply pagination
    query = query.order_by(sort_column)
    query = query.offset((page - 1) * page_size).limit(page_size)

    # Execute the query
    clients = query.all()

    # Ensure notes are not None
    for client in clients:
        if client.notes is None:
            client.notes = ""

    # Return paginated response
    return {
        "items": clients,
        "total": total_count,
        "page": page,
        "page_size": page_size,
        "total_pages": total_pages
    }

@router.post("/clients")
async def add_client(client_data: dict, db: Session = Depends(get_db), current_user: Dict = Depends(get_current_user)):
    try:
        # Log the received data for debugging
        print(f"Received client data: {client_data}")

        # Ensure the client is associated with the current user
        from uuid import UUID

        # Set the user_id to the current user's ID from the token
        user_id = UUID(current_user["id"])
        client_data["user_id"] = str(user_id)

        # Log for debugging
        print(f"Setting client user_id to: {user_id}")

        try:
            # Validate the data using the Pydantic model
            client = ClientSchema(**client_data)
            print(f"Validated client data: {client.dict()}")
        except Exception as e:
            print(f"Validation error: {str(e)}")
            raise HTTPException(status_code=422, detail=str(e))

        # Create the DB model
        db_client = Client(**client.dict())

        # Add to DB and commit
        db.add(db_client)
        db.commit()
        db.refresh(db_client)
        return db_client
    except Exception as e:
        db.rollback()
        # Log the error for debugging
        import traceback
        print(f"Error adding client: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/clients/{client_id}")
async def update_client(client_id: str, client_data: dict, db: Session = Depends(get_db), current_user: Dict = Depends(get_current_user)):
    # Convert the user ID from the token to UUID for the database query
    from uuid import UUID
    user_id = UUID(current_user["id"])

    # Find the client and ensure it belongs to the current user
    db_client = db.query(Client).filter(Client.id == client_id, Client.user_id == user_id).first()
    if not db_client:
        raise HTTPException(status_code=404, detail="Client not found or you don't have permission to modify it")

    try:
        # Log the received data for debugging
        print(f"Received client update data: {client_data}")

        # Ensure the user_id is not changed
        client_data["user_id"] = str(user_id)  # Ensure the user_id stays the same
        client_data["id"] = client_id  # Ensure the ID is set correctly

        # Validate the data using the Pydantic model
        try:
            # Validate the data using the Pydantic model
            validated_client = ClientSchema(**client_data)
            print(f"Validated client data: {validated_client.dict()}")
            client_data = validated_client.dict()
        except Exception as e:
            print(f"Validation error: {str(e)}")
            raise HTTPException(status_code=422, detail=str(e))

        # Update the client
        for key, value in client_data.items():
            setattr(db_client, key, value)

        db.commit()
        db.refresh(db_client)
        return db_client
    except Exception as e:
        db.rollback()
        # Log the error for debugging
        import traceback
        print(f"Error updating client: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/clients/{client_id}", response_model=dict)
async def delete_client(client_id: str, db: Session = Depends(get_db), current_user: Dict = Depends(get_current_user)):
    # Convert the user ID from the token to UUID for the database query
    from uuid import UUID
    from sqlalchemy.exc import IntegrityError
    from ..models.message import Message
    from ..models.appointment import Appointment
    from ..models.ai_context import ConversationMessage

    user_id = UUID(current_user["id"])
    client_uuid = UUID(client_id)

    # Find the client and ensure it belongs to the current user
    db_client = db.query(Client).filter(Client.id == client_uuid, Client.user_id == user_id).first()
    if not db_client:
        raise HTTPException(status_code=404, detail="Client not found or you don't have permission to delete it")

    try:
        # Delete related data first to avoid foreign key constraint violations
        # Order matters due to foreign key dependencies

        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Starting deletion of client {client_id}")

        # 1. First get all AI conversation contexts for this client
        from ..models.ai_context import AIConversationContext
        ai_contexts = db.query(AIConversationContext).filter(AIConversationContext.client_id == client_uuid).all()
        logger.info(f"Found {len(ai_contexts)} AI conversation contexts")

        # 2. Delete conversation messages for each context
        total_conv_messages = 0
        for context in ai_contexts:
            conv_msg_count = db.query(ConversationMessage).filter(ConversationMessage.conversation_id == context.id).count()
            total_conv_messages += conv_msg_count
            db.query(ConversationMessage).filter(ConversationMessage.conversation_id == context.id).delete()
        logger.info(f"Deleted {total_conv_messages} conversation messages")

        # 3. Now delete AI conversation contexts
        ai_ctx_count = len(ai_contexts)
        db.query(AIConversationContext).filter(AIConversationContext.client_id == client_uuid).delete()
        logger.info(f"Deleted {ai_ctx_count} AI conversation contexts")

        # 4. Delete messages
        msg_count = db.query(Message).filter(Message.client_id == client_uuid).count()
        logger.info(f"Deleting {msg_count} messages")
        db.query(Message).filter(Message.client_id == client_uuid).delete()

        # 5. Delete appointments
        appt_count = db.query(Appointment).filter(Appointment.client_id == client_uuid).count()
        logger.info(f"Deleting {appt_count} appointments")
        db.query(Appointment).filter(Appointment.client_id == client_uuid).delete()

        # 6. Finally delete the client
        logger.info(f"Deleting client {client_id}")
        db.delete(db_client)

        # Commit all changes
        db.commit()
        logger.info(f"Successfully deleted client {client_id} and all related data")

        return {"message": "Client and all related data deleted successfully"}

    except IntegrityError as e:
        db.rollback()
        # Log the specific error for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Foreign key constraint error when deleting client {client_id}: {str(e)}")

        raise HTTPException(
            status_code=409,
            detail="Cannot delete client because it has related data. Please contact support if this persists."
        )
    except Exception as e:
        db.rollback()
        # Log the error for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error deleting client {client_id}: {str(e)}")

        raise HTTPException(status_code=500, detail=f"Error deleting client: {str(e)}")

@router.get("/clients/export/csv")
async def export_clients_csv(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Export all clients as a CSV file"""
    # Convert the user ID from the token to UUID for the database query
    from uuid import UUID
    user_id = UUID(current_user["id"])

    # Query all clients for the current user
    clients = db.query(Client).filter(Client.user_id == user_id).all()

    # Create a StringIO object to write CSV data
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header row
    writer.writerow(["ID", "Name", "Email", "Phone", "Created At", "Last Appointment", "Next Appointment", "Tags", "Notes", "Profile Image"])

    # Write data rows
    for client in clients:
        # Format dates properly
        created_at = client.created_at.strftime("%Y-%m-%d %H:%M:%S") if client.created_at else ""
        last_appointment = client.last_appointment.strftime("%Y-%m-%d %H:%M:%S") if client.last_appointment else ""
        next_appointment = client.next_appointment.strftime("%Y-%m-%d %H:%M:%S") if client.next_appointment else ""

        # Format tags as comma-separated string
        tags = ", ".join(client.tags) if client.tags else ""

        writer.writerow([
            str(client.id),
            client.name,
            client.email,
            client.phone,
            created_at,
            last_appointment,
            next_appointment,
            tags,
            client.notes or "",
            "Yes" if client.profile_image else "No"
        ])

    # Prepare the response with the CSV data
    output.seek(0)
    response = Response(content=output.getvalue())
    response.headers["Content-Disposition"] = f"attachment; filename=clients_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    response.headers["Content-Type"] = "text/csv"

    return response