"""
AI Image Analysis Routes
This module provides routes for analyzing images using AI.
"""

import logging
from typing import Dict, List, Optional, Union
from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from pydantic import BaseModel
import requests
import base64
import io
from PIL import Image

from app.database import get_db
from app.auth import get_current_user
from app.config import settings
from app.services.ai_service import generate_ai_response

# Set up logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/ai",
    tags=["AI Image Analysis"],
    responses={404: {"description": "Not found"}},
)

# Request models
class ImageAnalysisRequest(BaseModel):
    """Request model for image analysis"""
    imageUrl: str

class ImageResponseRequest(BaseModel):
    """Request model for generating a response to an image"""
    imageUrl: str
    analysis: Optional[Dict] = None
    prompt: Optional[str] = None

# Response models
class ImageAnalysisResponse(BaseModel):
    """Response model for image analysis"""
    description: str
    tags: List[str]
    objects: List[str]
    text: Optional[str] = None
    error: Optional[str] = None

class ImageResponseResponse(BaseModel):
    """Response model for image response generation"""
    response: str

@router.post("/analyze-image", response_model=ImageAnalysisResponse)
async def analyze_image(
    request: ImageAnalysisRequest = Body(...),
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Analyze an image using AI"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    try:
        # Download the image
        image_url = request.imageUrl
        
        # If the URL is from our Evolution API, we need to add authentication
        if settings.EVOLUTION_API_URL in image_url:
            headers = {
                "apikey": settings.EVOLUTION_API_KEY
            }
        else:
            headers = {}
        
        image_response = requests.get(image_url, headers=headers, stream=True)
        
        if image_response.status_code != 200:
            raise HTTPException(status_code=400, detail=f"Failed to download image: {image_response.status_code}")
        
        # Use Gemini API to analyze the image
        # For now, we'll return a placeholder response
        # In a real implementation, you would call the Gemini API here
        
        return {
            "description": "This is a placeholder image analysis",
            "tags": ["placeholder", "image", "analysis"],
            "objects": ["placeholder"],
            "text": None
        }
    
    except Exception as e:
        logger.error(f"Error analyzing image: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error analyzing image: {str(e)}")

@router.post("/generate-image-response", response_model=ImageResponseResponse)
async def generate_image_response(
    request: ImageResponseRequest = Body(...),
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Generate a response to an image"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    try:
        # If analysis is provided, use it; otherwise, analyze the image
        analysis = request.analysis
        if not analysis:
            # Call the analyze_image endpoint
            analysis_request = ImageAnalysisRequest(imageUrl=request.imageUrl)
            analysis = await analyze_image(analysis_request, db, current_user)
        
        # Generate a prompt for the AI based on the analysis
        prompt = request.prompt or "Please describe this image and provide any relevant information."
        
        # Create a context for the AI
        context = f"""
        Image Analysis:
        Description: {analysis.get('description', 'No description available')}
        Tags: {', '.join(analysis.get('tags', []))}
        Objects: {', '.join(analysis.get('objects', []))}
        Text: {analysis.get('text', 'No text detected')}
        
        User prompt: {prompt}
        """
        
        # Generate a response using the AI service
        response_text = generate_ai_response(
            user_id=current_user["id"],
            message=context,
            system_prompt="You are an assistant that helps analyze images. Provide helpful, accurate information about the image based on the analysis provided."
        )
        
        return {"response": response_text}
    
    except Exception as e:
        logger.error(f"Error generating image response: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating image response: {str(e)}")
