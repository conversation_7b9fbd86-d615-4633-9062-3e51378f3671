from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
from datetime import datetime
from uuid import UUID
from typing import List, Dict, Optional
from pydantic import BaseModel, Field, ConfigDict, field_validator
import logging

from ..database import get_db
from ..models.service import Service, ServiceType
from ..auth import get_current_user

# Create router with the correct prefix and tags
router = APIRouter(tags=["services"])

# Pydantic models for request/response
class ServiceTypeResponse(BaseModel):
    id: str
    name: str

    model_config = ConfigDict(from_attributes=True)

    @field_validator("id", mode="before")
    def convert_uuid_to_str(cls, v):
        if isinstance(v, UUID):
            return str(v)
        return v

class ServiceBase(BaseModel):
    name: str
    description: Optional[str] = None
    price: float
    duration_minutes: int
    type_id: str
    active: bool = True

class ServiceCreate(ServiceBase):
    pass

class ServiceUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    duration_minutes: Optional[int] = None
    type_id: Optional[str] = None
    active: Optional[bool] = None

class ServiceResponse(ServiceBase):
    id: str
    user_id: str

    model_config = ConfigDict(from_attributes=True)

    @field_validator("id", "user_id", "type_id", mode="before")
    def convert_uuid_to_str(cls, v):
        if isinstance(v, UUID):
            return str(v)
        return v

class PaginatedServiceResponse(BaseModel):
    items: List[ServiceResponse]
    total: int
    page: int
    limit: int

# Service Type CRUD operations

class ServiceTypeCreate(BaseModel):
    name: str

class ServiceTypeUpdate(BaseModel):
    name: str

@router.get("/service-types", response_model=List[ServiceTypeResponse])
async def get_service_types(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Get all service types"""
    try:
        # Log that we're fetching service types
        logging.info(f"Fetching service types for user {current_user['email']}")

        # Get all service types
        service_types = db.query(ServiceType).all()
        logging.info(f"Found {len(service_types)} service types in database")

        # Log all service types for debugging
        for st in service_types:
            logging.info(f"Service type in DB: ID={st.id}, Name={st.name}")

        # If no service types exist, create some default ones
        if not service_types:
            logging.info("No service types found, creating defaults")
            default_types = [
                ServiceType(name="Haircut"),
                ServiceType(name="Massage"),
                ServiceType(name="Consultation"),
                ServiceType(name="Physiotherapy"),
                ServiceType(name="Other")
            ]
            db.add_all(default_types)
            db.commit()

            # Refresh to get the IDs
            for type_obj in default_types:
                db.refresh(type_obj)

            service_types = default_types
            logging.info(f"Created {len(default_types)} default service types")

        # Convert UUID objects to strings for the response
        result = []
        for type_obj in service_types:
            result.append({
                "id": str(type_obj.id),
                "name": type_obj.name
            })

        logging.info(f"Returning {len(result)} service types")
        return result
    except Exception as e:
        # Log the error
        logging.error(f"Error getting service types: {str(e)}")

        # Return a default list of service types as fallback
        return [
            {"id": "haircut", "name": "Haircut"},
            {"id": "massage", "name": "Massage"},
            {"id": "consultation", "name": "Consultation"},
            {"id": "physiotherapy", "name": "Physiotherapy"},
            {"id": "other", "name": "Other"}
        ]

@router.post("/service-types", response_model=ServiceTypeResponse)
async def create_service_type(
    service_type: ServiceTypeCreate,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Create a new service type"""
    try:
        logging.info(f"Creating new service type with name: {service_type.name}")

        # Check if service type with this name already exists
        existing_type = db.query(ServiceType).filter(ServiceType.name == service_type.name).first()
        if existing_type:
            logging.warning(f"Service type with name '{service_type.name}' already exists")
            raise HTTPException(status_code=400, detail="Service type with this name already exists")

        # Create new service type
        new_type = ServiceType(name=service_type.name)
        db.add(new_type)
        db.commit()
        db.refresh(new_type)

        # Convert UUID to string for response
        result = {
            "id": str(new_type.id),
            "name": new_type.name
        }

        logging.info(f"Successfully created service type: {service_type.name} with ID: {result['id']}")
        return result
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log any other errors
        logging.error(f"Error creating service type: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating service type: {str(e)}")

@router.put("/service-types/{type_id}", response_model=ServiceTypeResponse)
async def update_service_type(
    type_id: str,
    service_type: ServiceTypeUpdate,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Update an existing service type"""
    try:
        logging.info(f"Updating service type {type_id} with name {service_type.name}")

        # Find service type
        try:
            type_uuid = UUID(type_id)
        except ValueError:
            logging.error(f"Invalid UUID format for type_id: {type_id}")
            raise HTTPException(status_code=400, detail="Invalid service type ID format")

        db_type = db.query(ServiceType).filter(ServiceType.id == type_uuid).first()
        if not db_type:
            logging.error(f"Service type with ID {type_id} not found")
            raise HTTPException(status_code=404, detail="Service type not found")

        # Check if another service type with this name already exists
        existing_type = db.query(ServiceType).filter(
            ServiceType.name == service_type.name,
            ServiceType.id != type_uuid
        ).first()
        if existing_type:
            logging.error(f"Another service type with name {service_type.name} already exists")
            raise HTTPException(status_code=400, detail="Another service type with this name already exists")

        # Update service type
        db_type.name = service_type.name
        db.commit()
        db.refresh(db_type)

        # Convert UUID to string for response
        result = {
            "id": str(db_type.id),
            "name": db_type.name
        }

        logging.info(f"Successfully updated service type {type_id} to {service_type.name}")
        return result
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log any other errors
        logging.error(f"Error updating service type {type_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating service type: {str(e)}")

@router.delete("/service-types/{type_id}")
async def delete_service_type(
    type_id: str,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Delete a service type"""
    try:
        logging.info(f"Deleting service type {type_id}")

        # Find service type
        try:
            type_uuid = UUID(type_id)
        except ValueError:
            logging.error(f"Invalid UUID format for type_id: {type_id}")
            raise HTTPException(status_code=400, detail="Invalid service type ID format")

        db_type = db.query(ServiceType).filter(ServiceType.id == type_uuid).first()
        if not db_type:
            logging.error(f"Service type with ID {type_id} not found")
            raise HTTPException(status_code=404, detail="Service type not found")

        # Check if service type is used in services
        service_count = db.query(Service).filter(Service.type_id == type_uuid).count()

        if service_count > 0:
            # If service type is used in services, return a 400 error
            logging.warning(f"Cannot delete service type {type_id} because it is used in {service_count} services")
            return {
                "success": False,
                "message": f"Cannot delete service type because it is used in {service_count} services. Please update or delete those services first."
            }

        # If service type is not used in services, delete it
        db.delete(db_type)
        db.commit()

        logging.info(f"Successfully deleted service type {type_id}")
        return {"success": True, "message": "Service type deleted successfully"}
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        db.rollback()
        # Log the error and return a more user-friendly error message
        logging.error(f"Error deleting service type {type_id}: {str(e)}")
        return {
            "success": False,
            "message": f"Could not delete service type: {str(e)}"
        }

@router.get("/services", response_model=PaginatedServiceResponse)
async def get_services(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Get all services for the current user"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Base query - filter by user_id
    query = db.query(Service).filter(Service.user_id == user_id)

    # Count total before pagination
    total = query.count()

    # Apply pagination
    query = query.order_by(Service.name)
    query = query.offset((page - 1) * limit).limit(limit)

    # Execute query
    services = query.all()

    return {
        "items": services,
        "total": total,
        "page": page,
        "limit": limit
    }

@router.post("/services", response_model=ServiceResponse)
async def create_service(
    service: ServiceCreate,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Create a new service"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Verify service type exists
    service_type = db.query(ServiceType).filter(ServiceType.id == UUID(service.type_id)).first()
    if not service_type:
        raise HTTPException(status_code=404, detail="Service type not found")

    # Create service
    new_service = Service(
        user_id=user_id,
        name=service.name,
        description=service.description,
        price=service.price,
        duration_minutes=service.duration_minutes,
        type_id=UUID(service.type_id),
        active=service.active
    )

    db.add(new_service)
    db.commit()
    db.refresh(new_service)

    return new_service

@router.get("/services/{service_id}", response_model=ServiceResponse)
async def get_service(
    service_id: str,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Get a specific service by ID"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Find service and ensure it belongs to the current user
    service = db.query(Service).filter(
        Service.id == UUID(service_id),
        Service.user_id == user_id
    ).first()

    if not service:
        raise HTTPException(status_code=404, detail="Service not found or doesn't belong to you")

    return service

@router.put("/services/{service_id}", response_model=ServiceResponse)
async def update_service(
    service_id: str,
    service_update: ServiceUpdate,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Update an existing service"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Find service and ensure it belongs to the current user
    db_service = db.query(Service).filter(
        Service.id == UUID(service_id),
        Service.user_id == user_id
    ).first()

    if not db_service:
        raise HTTPException(status_code=404, detail="Service not found or doesn't belong to you")

    # Update fields if provided
    update_data = service_update.dict(exclude_unset=True)

    if "type_id" in update_data:
        # Verify service type exists
        service_type = db.query(ServiceType).filter(ServiceType.id == UUID(update_data["type_id"])).first()
        if not service_type:
            raise HTTPException(status_code=404, detail="Service type not found")

        db_service.type_id = UUID(update_data["type_id"])

    if "name" in update_data:
        db_service.name = update_data["name"]

    if "description" in update_data:
        db_service.description = update_data["description"]

    if "price" in update_data:
        db_service.price = update_data["price"]

    if "duration_minutes" in update_data:
        db_service.duration_minutes = update_data["duration_minutes"]

    if "active" in update_data:
        db_service.active = update_data["active"]

    db.commit()
    db.refresh(db_service)

    return db_service

@router.delete("/services/{service_id}")
async def delete_service(
    service_id: str,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Delete a service"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Find service and ensure it belongs to the current user
    service = db.query(Service).filter(
        Service.id == UUID(service_id),
        Service.user_id == user_id
    ).first()

    if not service:
        raise HTTPException(status_code=404, detail="Service not found or doesn't belong to you")

    try:
        # Check if service is used in appointments
        from app.models.appointment import Appointment
        appointment_count = db.query(Appointment).filter(Appointment.service_id == UUID(service_id)).count()

        # Delete the service even if it's used in appointments
        db.delete(service)
        db.commit()

        if appointment_count > 0:
            # If service was used in appointments, return a success message with a warning
            return {
                "success": True,
                "message": f"Service deleted successfully. Note: This service was referenced by {appointment_count} appointments."
            }
        else:
            # If service was not used in appointments, return a simple success message
            return {"success": True, "message": "Service deleted successfully"}
    except Exception as e:
        db.rollback()
        # Return a more user-friendly error message
        return {
            "success": False,
            "message": f"Could not delete service: {str(e)}"
        }
