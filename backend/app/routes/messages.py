from fastapi import APIRouter, Depends, HTTPException, Body, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, desc, text
from datetime import datetime, timedelta
from uuid import UUID
from typing import Dict, List, Optional, Union
from pydantic import BaseModel, Field

from ..database import get_db
from ..models.client import Client
from ..models.message import Message
from ..services.embedding_service import embedding_service
from ..auth import get_current_user
from ..services.ai_assistant import AIAssistant

router = APIRouter(tags=["messages"])

class ContactResponse(BaseModel):
    id: str
    name: str
    avatar: str
    lastMessage: str
    timestamp: str
    unread: int
    source: str
    phone: Optional[str] = None
    email: Optional[str] = None

class MessageResponse(BaseModel):
    id: str
    text: str
    timestamp: str
    sender: str
    status: Optional[str] = None
    source: str
    similarity: Optional[float] = None

class SendMessageRequest(BaseModel):
    text: str
    source: str = "app"

class SearchMessagesRequest(BaseModel):
    query: str
    limit: int = 10

@router.get("/contacts", response_model=Dict[str, List[ContactResponse]])
async def get_contacts(
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Get all contacts with their latest messages"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Get all clients
    clients = db.query(Client).filter(Client.user_id == user_id).all()

    # Prepare response
    contacts = []

    for client in clients:
        # Get the latest message for this client
        latest_message = db.query(Message).filter(
            Message.user_id == user_id,
            Message.client_id == client.id
        ).order_by(desc(Message.sent_at)).first()

        # Get unread count
        unread_count = db.query(func.count(Message.id)).filter(
            Message.user_id == user_id,
            Message.client_id == client.id,
            Message.is_from_business == False,
            # No read field in the current model
        ).scalar()

        # Determine the source (WhatsApp, AI, or app)
        source = "app"
        if latest_message and latest_message.source:
            source = latest_message.source

        # Create contact object
        contact = {
            "id": str(client.id),
            "name": client.name,
            "avatar": "",  # Will be generated on the frontend
            "lastMessage": latest_message.content if latest_message else "",
            "timestamp": latest_message.sent_at.isoformat() if latest_message else datetime.now().isoformat(),
            "unread": unread_count,
            "source": source,
            "phone": client.phone,
            "email": client.email
        }

        contacts.append(contact)

    # Sort contacts by timestamp (most recent first)
    contacts.sort(key=lambda x: x["timestamp"], reverse=True)

    return {"contacts": contacts}

@router.get("/{client_id}", response_model=Dict[str, List[MessageResponse]])
async def get_messages(
    client_id: str,
    source: str = Query("app", description="Message source: app, whatsapp, or ai"),
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Get messages for a specific client"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    try:
        client_uuid = UUID(client_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid client ID")

    # Check if client belongs to user
    client = db.query(Client).filter(
        Client.id == client_uuid,
        Client.user_id == user_id
    ).first()

    if not client:
        raise HTTPException(status_code=404, detail="Client not found")

    # Get messages filtered by source if the column exists
    try:
        messages = db.query(Message).filter(
            Message.user_id == user_id,
            Message.client_id == client_uuid,
            Message.source == source
        ).order_by(Message.sent_at.asc()).all()
    except Exception as e:
        # If the source column doesn't exist yet, ignore the filter
        print(f"Error filtering by source: {str(e)}")
        messages = db.query(Message).filter(
            Message.user_id == user_id,
            Message.client_id == client_uuid
        ).order_by(Message.sent_at.asc()).all()

    # Format messages
    formatted_messages = []
    for msg in messages:
        formatted_messages.append({
            "id": str(msg.id),
            "text": msg.content,
            "timestamp": msg.sent_at.isoformat(),
            "sender": "me" if msg.is_from_business else "them",
            "status": "read",  # Simplified for now
            "source": getattr(msg, 'source', 'app')
        })

    return {"messages": formatted_messages}

@router.post("/{client_id}/send", response_model=Dict[str, MessageResponse])
async def send_message(
    client_id: str,
    request: SendMessageRequest,
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Send a message to a client"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    try:
        client_uuid = UUID(client_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid client ID")

    # Check if client belongs to user
    client = db.query(Client).filter(
        Client.id == client_uuid,
        Client.user_id == user_id
    ).first()

    if not client:
        raise HTTPException(status_code=404, detail="Client not found")

    try:
        # Generate embedding for the message
        message_embedding = await embedding_service.get_embedding(request.text)
        print(f"Generated embedding with length: {len(message_embedding)}")

        # Create message
        try:
            new_message = Message(
                user_id=user_id,
                client_id=client_uuid,
                content=request.text,
                is_from_business=True,
                sent_at=datetime.now(),
                embedding=message_embedding,
                source=request.source
            )
        except Exception as e:
            # If the source column doesn't exist yet, create without it
            print(f"Error setting source: {str(e)}")
            new_message = Message(
                user_id=user_id,
                client_id=client_uuid,
                content=request.text,
                is_from_business=True,
                sent_at=datetime.now(),
                embedding=message_embedding
            )
    except Exception as e:
        print(f"Error in send_message: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing message: {str(e)}")

    db.add(new_message)
    db.commit()
    db.refresh(new_message)

    # If this is WhatsApp, send the message via WhatsApp
    if request.source == "whatsapp":
        from app.config import settings
        import requests

        # Get the client's phone number
        client = db.query(Client).filter(Client.id == client_uuid).first()
        if not client or not client.phone:
            raise HTTPException(status_code=400, detail="Client has no phone number")

        # Format phone number (remove any non-numeric characters)
        phone = ''.join(filter(str.isdigit, client.phone))

        # Add country code if not present
        if not phone.startswith('34') and not phone.startswith('1'):
            phone = '34' + phone  # Default to Spain country code

        # Send the message using Evolution API
        EVOLUTION_HEADERS = {
            "Content-Type": "application/json",
            "apikey": settings.EVOLUTION_API_KEY
        }

        send_response = requests.post(
            f"{settings.EVOLUTION_API_URL}/message/sendText/{current_user['id']}",
            headers=EVOLUTION_HEADERS,
            json={
                "number": phone,
                "text": request.text,
                "delay": 1200,
                "linkPreview": False
            }
        )

        if send_response.status_code != 201:
            print(f"WhatsApp send error: {send_response.status_code} - {send_response.text}")
            # We still save the message, but log the error

    # Return the created message
    return {
        "message": {
            "id": str(new_message.id),
            "text": new_message.content,
            "timestamp": new_message.sent_at.isoformat(),
            "sender": "me",
            "status": "sent",
            "source": request.source
        }
    }

@router.post("/{client_id}/read")
async def mark_as_read(
    client_id: str,
    request: Dict = Body(...),
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Mark messages as read"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    try:
        client_uuid = UUID(client_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid client ID")

    # Check if client belongs to user
    client = db.query(Client).filter(
        Client.id == client_uuid,
        Client.user_id == user_id
    ).first()

    if not client:
        raise HTTPException(status_code=404, detail="Client not found")

    # No read field in the current model
    # Just return success
    return {"success": True}

@router.post("/search", response_model=Dict[str, List[MessageResponse]])
async def search_messages(
    request: SearchMessagesRequest,
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Search messages using vector similarity"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Generate embedding for the search query
    query_embedding = await embedding_service.get_embedding(request.query)

    # Perform vector similarity search
    try:
        # Use raw SQL for vector search
        query = """
        SELECT m.*, m.embedding <=> :query_embedding AS distance
        FROM messages m
        WHERE m.user_id = :user_id
        ORDER BY distance
        LIMIT :limit
        """

        results = db.execute(
            text(query),
            {
                "query_embedding": query_embedding,
                "user_id": user_id,
                "limit": request.limit
            }
        ).fetchall()

        # Format results
        messages = []
        for row in results:
            messages.append({
                "id": str(row.id),
                "text": row.content,
                "timestamp": row.sent_at.isoformat(),
                "sender": "me" if row.is_from_business else "them",
                "status": "read",
                "source": getattr(row, 'source', 'app'),
                "similarity": 1.0 - float(row.distance)  # Convert distance to similarity score
            })

        return {"messages": messages}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching messages: {str(e)}")
