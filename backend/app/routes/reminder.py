from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from datetime import datetime, timedelta
from uuid import UUID
from typing import Dict, List, Optional, Union
from pydantic import BaseModel, Field

from ..database import get_db
from ..models.appointment import Appointment, AppointmentStatus
from ..models.client import Client
from ..models.service import Service
from ..models.notification import NotificationPreference
from ..models.integration import ExternalIntegration
from ..auth import get_current_user
from ..config import settings

import requests
import json

router = APIRouter(tags=["reminders"])

# Pydantic models
class ReminderSettings(BaseModel):
    enabled: bool = True
    reminder_times: List[int] = [24, 1]  # Hours before appointment
    message_template: str = "Hi {{clientName}}, this is a reminder for your appointment on {{date}} at {{time}}. Please let us know if you need to reschedule."
    include_location: bool = True
    include_service_details: bool = True

class ReminderResponse(BaseModel):
    success: bool
    message: str
    details: Optional[Dict] = None

class TestReminderRequest(BaseModel):
    phone: str

# Evolution API headers
EVOLUTION_HEADERS = {
    "Content-Type": "application/json",
    "apikey": settings.EVOLUTION_API_KEY
}

@router.get("/reminder-settings", response_model=ReminderSettings)
async def get_reminder_settings(
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Get reminder settings for the current user"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Get notification preferences from the database
    notification_prefs = db.query(NotificationPreference).filter(
        NotificationPreference.user_id == user_id,
        NotificationPreference.event_type == "appointment_reminder"
    ).first()

    if not notification_prefs:
        # Create default preferences if none exist
        notification_prefs = NotificationPreference(
            user_id=user_id,
            event_type="appointment_reminder",
            email_enabled=True,
            sms_enabled=False,
            push_enabled=False,
            reminder_minutes=1440,  # 24 hours in minutes
            settings={
                "enabled": True,
                "reminder_times": [24, 1],
                "message_template": "Hi {{clientName}}, this is a reminder for your appointment on {{date}} at {{time}}. Please let us know if you need to reschedule.",
                "include_location": True,
                "include_service_details": True
            }
        )
        db.add(notification_prefs)
        db.commit()
        db.refresh(notification_prefs)

    # Get settings from notification preferences
    settings_data = notification_prefs.settings if hasattr(notification_prefs, 'settings') and notification_prefs.settings else {}

    return ReminderSettings(
        enabled=settings_data.get("enabled", True),
        reminder_times=settings_data.get("reminder_times", [24, 1]),
        message_template=settings_data.get("message_template", "Hi {{clientName}}, this is a reminder for your appointment on {{date}} at {{time}}. Please let us know if you need to reschedule."),
        include_location=settings_data.get("include_location", True),
        include_service_details=settings_data.get("include_service_details", True)
    )

@router.put("/reminder-settings", response_model=ReminderSettings)
async def update_reminder_settings(
    settings_data: ReminderSettings,
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Update reminder settings for the current user"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Get notification preferences from the database
    notification_prefs = db.query(NotificationPreference).filter(
        NotificationPreference.user_id == user_id,
        NotificationPreference.event_type == "appointment_reminder"
    ).first()

    if not notification_prefs:
        # Create new preferences if none exist
        notification_prefs = NotificationPreference(
            user_id=user_id,
            event_type="appointment_reminder",
            email_enabled=True,
            sms_enabled=False,
            push_enabled=False,
            reminder_minutes=1440  # 24 hours in minutes
        )
        db.add(notification_prefs)

    # Update settings
    notification_prefs.settings = {
        "enabled": settings_data.enabled,
        "reminder_times": settings_data.reminder_times,
        "message_template": settings_data.message_template,
        "include_location": settings_data.include_location,
        "include_service_details": settings_data.include_service_details
    }

    # Update reminder_minutes based on the first reminder time
    if settings_data.reminder_times and len(settings_data.reminder_times) > 0:
        notification_prefs.reminder_minutes = settings_data.reminder_times[0] * 60

    db.commit()
    db.refresh(notification_prefs)

    return settings_data

@router.get("/appointments/upcoming-reminders", response_model=Dict)
async def get_upcoming_reminders(
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Get upcoming appointments that need reminders"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Get reminder settings
    notification_prefs = db.query(NotificationPreference).filter(
        NotificationPreference.user_id == user_id,
        NotificationPreference.event_type == "appointment_reminder"
    ).first()

    if not notification_prefs or not notification_prefs.settings or not notification_prefs.settings.get("enabled", True):
        return {"appointments": []}

    # Get reminder times from settings
    reminder_times = notification_prefs.settings.get("reminder_times", [24, 1])

    # Current time
    now = datetime.now()

    # Find appointments that need reminders
    appointments_needing_reminders = []

    for hours_before in reminder_times:
        # Calculate the time window for this reminder
        reminder_time = now + timedelta(hours=hours_before)
        window_start = reminder_time - timedelta(minutes=5)  # 5-minute window
        window_end = reminder_time + timedelta(minutes=5)

        # Find appointments in this time window
        appointments = db.query(Appointment).filter(
            Appointment.user_id == user_id,
            Appointment.status == AppointmentStatus.CONFIRMED,
            Appointment.start_time > now,  # Only future appointments
            Appointment.start_time <= now + timedelta(hours=hours_before + 1),  # Within the next (hours_before + 1) hours
            # Check if the appointment is within the reminder window
            func.date_part('hour', Appointment.start_time - now) >= hours_before - 0.1,
            func.date_part('hour', Appointment.start_time - now) <= hours_before + 0.1
        ).all()

        appointments_needing_reminders.extend(appointments)

    # Convert to response format
    result = []
    for appointment in appointments_needing_reminders:
        # Get client details
        client = db.query(Client).filter(Client.id == appointment.client_id).first()

        # Get service details
        service = db.query(Service).filter(Service.id == appointment.service_id).first()

        if client:
            result.append({
                "id": str(appointment.id),
                "user_id": str(appointment.user_id),
                "client_id": str(appointment.client_id),
                "title": f"Appointment with {client.name}" if client.name else "Appointment",
                "description": service.name if service else "",
                "start_time": appointment.start_time.isoformat(),
                "end_time": appointment.end_time.isoformat(),
                "status": appointment.status.value,
                "created_at": appointment.created_at.isoformat(),
                "client": {
                    "name": client.name,
                    "phone": client.phone,
                    "email": client.email
                },
                "service": {
                    "name": service.name if service else "",
                    "duration_minutes": service.duration_minutes if service else 0
                }
            })

    return {"appointments": result}

@router.post("/appointments/{appointment_id}/send-reminder", response_model=ReminderResponse)
async def send_appointment_reminder(
    appointment_id: str,
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Send a reminder for a specific appointment"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Get the appointment
    appointment = db.query(Appointment).filter(
        Appointment.id == UUID(appointment_id),
        Appointment.user_id == user_id
    ).first()

    if not appointment:
        raise HTTPException(status_code=404, detail="Appointment not found or doesn't belong to you")

    # Get the client
    client = db.query(Client).filter(Client.id == appointment.client_id).first()
    if not client or not client.phone:
        return {
            "success": False,
            "message": "Client not found or doesn't have a phone number"
        }

    # Get the service
    service = db.query(Service).filter(Service.id == appointment.service_id).first()

    # Get reminder settings
    notification_prefs = db.query(NotificationPreference).filter(
        NotificationPreference.user_id == user_id,
        NotificationPreference.event_type == "appointment_reminder"
    ).first()

    if not notification_prefs or not notification_prefs.settings:
        return {
            "success": False,
            "message": "Reminder settings not found"
        }

    # Get message template from settings
    message_template = notification_prefs.settings.get(
        "message_template",
        "Hi {{clientName}}, this is a reminder for your appointment on {{date}} at {{time}}. Please let us know if you need to reschedule."
    )

    # Format date and time
    appointment_date = appointment.start_time.strftime("%A, %B %d, %Y")
    appointment_time = appointment.start_time.strftime("%I:%M %p")

    # Replace placeholders in the template
    message = message_template.replace("{{clientName}}", client.name)
    message = message.replace("{{date}}", appointment_date)
    message = message.replace("{{time}}", appointment_time)

    # Add service details if enabled
    if notification_prefs.settings.get("include_service_details", True) and service:
        message = message.replace("{{service}}", service.name)

    # Add location if enabled
    if notification_prefs.settings.get("include_location", True):
        # Get user's business address
        user_query = db.execute(f"SELECT address FROM users WHERE id = '{user_id}'")
        user_data = user_query.fetchone()
        location = user_data[0] if user_data and user_data[0] else "our location"
        message = message.replace("{{location}}", location)

    # Check if WhatsApp is connected
    whatsapp_integration = db.query(ExternalIntegration).filter(
        ExternalIntegration.user_id == user_id,
        ExternalIntegration.integration_type == "whatsapp",
        ExternalIntegration.active == True
    ).first()

    if not whatsapp_integration:
        return {
            "success": False,
            "message": "WhatsApp is not connected"
        }

    # Format phone number (remove any non-numeric characters)
    phone = ''.join(filter(str.isdigit, client.phone))

    # Add country code if not present
    if not phone.startswith('34') and not phone.startswith('1'):
        phone = '34' + phone  # Default to Spain country code

    # Send the WhatsApp message
    try:
        # Check if the WhatsApp instance is connected
        status_response = requests.get(
            f"{settings.EVOLUTION_API_URL}/instance/connectionState/{user_id}",
            headers=EVOLUTION_HEADERS
        )

        if status_response.status_code != 200:
            return {
                "success": False,
                "message": "WhatsApp instance not found or not accessible"
            }

        status_data = status_response.json()
        state = status_data.get('instance', {}).get('state')

        if state != 'open':
            return {
                "success": False,
                "message": f"WhatsApp is not connected. Current state: {state}"
            }

        # Send the message
        send_response = requests.post(
            f"{settings.EVOLUTION_API_URL}/message/sendText/{user_id}",
            headers=EVOLUTION_HEADERS,
            json={
                "number": phone,
                "text": message,
                "delay": 1200,
                "linkPreview": False
            }
        )

        if send_response.status_code != 201:
            error_detail = send_response.text
            try:
                error_json = send_response.json()
                error_detail = error_json
            except:
                pass

            return {
                "success": False,
                "message": f"Failed to send message: {error_detail}"
            }

        response_data = send_response.json()

        # Log the reminder in the database
        # TODO: Create a reminders table to track sent reminders

        return {
            "success": True,
            "message": "Reminder sent successfully",
            "details": response_data
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Error sending reminder: {str(e)}"
        }

@router.post("/send-test-reminder", response_model=ReminderResponse)
async def send_test_reminder(
    request_data: TestReminderRequest,
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Send a test reminder message"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Get reminder settings
    notification_prefs = db.query(NotificationPreference).filter(
        NotificationPreference.user_id == user_id,
        NotificationPreference.event_type == "appointment_reminder"
    ).first()

    if not notification_prefs or not notification_prefs.settings:
        return {
            "success": False,
            "message": "Reminder settings not found"
        }

    # Get message template from settings
    message_template = notification_prefs.settings.get(
        "message_template",
        "Hi {{clientName}}, this is a reminder for your appointment on {{date}} at {{time}}. Please let us know if you need to reschedule."
    )

    # Format a test message
    message = message_template.replace("{{clientName}}", "Test Client")
    message = message.replace("{{date}}", "Monday, January 1, 2024")
    message = message.replace("{{time}}", "10:00 AM")
    message = message.replace("{{service}}", "Test Service")
    message = message.replace("{{location}}", "Test Location")

    # Add a prefix to indicate it's a test
    message = "TEST MESSAGE: " + message

    # Format phone number (remove any non-numeric characters)
    phone = ''.join(filter(str.isdigit, request_data.phone))

    # Add country code if not present
    if not phone.startswith('34') and not phone.startswith('1'):
        phone = '34' + phone  # Default to Spain country code

    # Send the WhatsApp message
    try:
        # Check if the WhatsApp instance is connected
        status_response = requests.get(
            f"{settings.EVOLUTION_API_URL}/instance/connectionState/{user_id}",
            headers=EVOLUTION_HEADERS
        )

        if status_response.status_code != 200:
            return {
                "success": False,
                "message": "WhatsApp instance not found or not accessible"
            }

        status_data = status_response.json()
        state = status_data.get('instance', {}).get('state')

        if state != 'open':
            return {
                "success": False,
                "message": f"WhatsApp is not connected. Current state: {state}"
            }

        # Send the message
        send_response = requests.post(
            f"{settings.EVOLUTION_API_URL}/message/sendText/{user_id}",
            headers=EVOLUTION_HEADERS,
            json={
                "number": phone,
                "text": message,
                "delay": 1200,
                "linkPreview": False
            }
        )

        if send_response.status_code != 201:
            error_detail = send_response.text
            try:
                error_json = send_response.json()
                error_detail = error_json
            except:
                pass

            return {
                "success": False,
                "message": f"Failed to send test message: {error_detail}"
            }

        response_data = send_response.json()

        return {
            "success": True,
            "message": "Test reminder sent successfully",
            "details": response_data
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Error sending test reminder: {str(e)}"
        }

@router.post("/check-and-send-reminders")
async def check_and_send_reminders(
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Check for upcoming appointments and send reminders if needed"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Get upcoming appointments that need reminders
    upcoming_response = await get_upcoming_reminders(db=db, current_user=current_user)
    appointments = upcoming_response.get("appointments", [])

    if not appointments:
        return {
            "success": True,
            "message": "No appointments need reminders at this time",
            "reminders_sent": 0
        }

    # Send reminders for each appointment
    reminders_sent = 0
    for appointment in appointments:
        reminder_response = await send_appointment_reminder(
            appointment_id=appointment["id"],
            db=db,
            current_user=current_user
        )

        if reminder_response["success"]:
            reminders_sent += 1

    return {
        "success": True,
        "message": f"Sent {reminders_sent} reminders out of {len(appointments)} appointments",
        "reminders_sent": reminders_sent,
        "total_appointments": len(appointments)
    }
