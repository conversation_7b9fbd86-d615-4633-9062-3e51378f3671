from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from typing import Dict, List, Optional, Union
from uuid import UUID
from pydantic import BaseModel, Field
from datetime import datetime

from ..database import get_db
from ..models.client import Client
from ..models.message import Message, MessageDirection
from ..auth import get_current_user
from ..services.ai_assistant import AIAssistant
from ..services.embedding_service import embedding_service
from ..models.message import Message as MessageModel
from pgvector.sqlalchemy import Vector

router = APIRouter(tags=["ai-messaging"])

class MessageRequest(BaseModel):
    client_id: str
    message: str

class MessageResponse(BaseModel):
    success: bool
    message: Dict
    aiResponse: Dict
    appointment_booked: Optional[bool] = None
    appointment_details: Optional[Dict] = None

@router.post("/send-message", response_model=MessageResponse)
async def process_client_message(
    request: MessageRequest,
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Process a message from a client and get AI response"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Validate client
    try:
        client_id = UUID(request.client_id)
        client = db.query(Client).filter(
            Client.id == client_id,
            Client.user_id == user_id
        ).first()

        if not client:
            raise HTTPException(status_code=404, detail="Client not found")
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid client ID")

    # Generate embedding for the incoming message
    message_embedding = await embedding_service.get_embedding(request.message)

    # Save the incoming message with embedding
    incoming_message = MessageModel(
        user_id=user_id,
        client_id=client_id,
        content=request.message,
        is_from_business=False,
        sent_at=datetime.now(),
        embedding=message_embedding,
        source="ai"
    )
    db.add(incoming_message)
    db.commit()

    # Process the message with AI
    ai_assistant = AIAssistant(db, str(user_id))
    response_text, action = await ai_assistant.process_message(str(client_id), request.message)

    # Generate embedding for the AI response
    response_embedding = await embedding_service.get_embedding(response_text)

    # Save the AI response with embedding
    outgoing_message = MessageModel(
        user_id=user_id,
        client_id=client_id,
        content=response_text,
        is_from_business=True,
        sent_at=datetime.now(),
        embedding=response_embedding,
        parent_id=incoming_message.id,  # Link to the incoming message
        source="ai"
    )
    db.add(outgoing_message)
    db.commit()

    # Handle any actions (like booking appointments)
    appointment_booked = False
    appointment_details = None

    if action and action.get("action") == "book_appointment":
        success, booking_message = await ai_assistant.book_appointment(action)
        appointment_booked = success

        if success:
            appointment_details = {
                "client_name": client.name,
                "service_id": action.get("service_id"),
                "start_time": action.get("start_time")
            }

    return {
        "success": True,
        "message": {
            "id": str(incoming_message.id),
            "text": incoming_message.content,
            "timestamp": incoming_message.sent_at.isoformat(),
            "sender": "me",
            "status": "sent",
            "source": "ai"
        },
        "aiResponse": {
            "id": str(outgoing_message.id),
            "text": outgoing_message.content,
            "timestamp": outgoing_message.sent_at.isoformat(),
            "sender": "them",
            "source": "ai"
        },
        "appointment_booked": appointment_booked,
        "appointment_details": appointment_details
    }

@router.get("/messages/{client_id}", response_model=List[Dict])
async def get_client_messages(
    client_id: str,
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Get message history for a client"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Validate client
    try:
        client_uuid = UUID(client_id)
        client = db.query(Client).filter(
            Client.id == client_uuid,
            Client.user_id == user_id
        ).first()

        if not client:
            raise HTTPException(status_code=404, detail="Client not found")
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid client ID")

    # Get messages
    messages = db.query(Message).filter(
        Message.user_id == user_id,
        Message.client_id == client_uuid
    ).order_by(Message.sent_at.asc()).all()

    return [
        {
            "id": str(msg.id),
            "content": msg.content,
            "direction": "incoming" if not msg.is_from_business else "outgoing",
            "timestamp": msg.sent_at.isoformat()
        }
        for msg in messages
    ]
