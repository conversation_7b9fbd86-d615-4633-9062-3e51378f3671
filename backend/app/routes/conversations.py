from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Dict, List, Optional
from uuid import UUID
from pydantic import BaseModel
from datetime import datetime

from ..database import get_db
from ..models.ai_context import AIConversationContext, ConversationMessage
from ..auth import get_current_user
from ..services.conversation_service import ConversationService

router = APIRouter(tags=["conversations"])

# Pydantic models for request/response
class MessageBase(BaseModel):
    content: str
    role: str

class MessageCreate(MessageBase):
    metadata: Optional[Dict] = None

class MessageResponse(MessageBase):
    id: str
    conversation_id: str
    created_at: datetime
    detected_language: Optional[str] = None
    detected_intent: Optional[str] = None
    message_metadata: Optional[Dict] = None

    class Config:
        orm_mode = True

class ConversationResponse(BaseModel):
    id: str
    client_id: str
    message_count: int
    conversation_language: str
    last_message_at: Optional[datetime] = None
    has_booking_intent: bool
    booking_stage: Optional[str] = None

    class Config:
        orm_mode = True

# Endpoints
@router.get("/conversations", response_model=List[ConversationResponse])
async def get_conversations(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user),
    limit: int = Query(20, ge=1, le=100),
    offset: int = Query(0, ge=0)
):
    """Get all conversations for the current user"""
    user_id = current_user["id"]
    
    conversations = db.query(AIConversationContext).filter(
        AIConversationContext.user_id == user_id
    ).order_by(
        AIConversationContext.last_message_at.desc()
    ).offset(offset).limit(limit).all()
    
    return conversations

@router.get("/conversations/{conversation_id}", response_model=ConversationResponse)
async def get_conversation(
    conversation_id: UUID,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Get a specific conversation"""
    user_id = current_user["id"]
    
    conversation = db.query(AIConversationContext).filter(
        AIConversationContext.id == conversation_id,
        AIConversationContext.user_id == user_id
    ).first()
    
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    return conversation

@router.get("/conversations/{conversation_id}/messages", response_model=List[MessageResponse])
async def get_conversation_messages(
    conversation_id: UUID,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user),
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0)
):
    """Get messages for a specific conversation"""
    user_id = current_user["id"]
    
    # Check if conversation exists and belongs to the user
    conversation = db.query(AIConversationContext).filter(
        AIConversationContext.id == conversation_id,
        AIConversationContext.user_id == user_id
    ).first()
    
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    # Get conversation service
    conversation_service = ConversationService(db)
    
    # Get messages
    messages = conversation_service.get_conversation_messages(
        conversation_id=conversation_id,
        limit=limit,
        offset=offset
    )
    
    return messages

@router.post("/conversations/{conversation_id}/messages", response_model=MessageResponse)
async def add_conversation_message(
    conversation_id: UUID,
    message: MessageCreate,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Add a message to a conversation"""
    user_id = current_user["id"]
    
    # Check if conversation exists and belongs to the user
    conversation = db.query(AIConversationContext).filter(
        AIConversationContext.id == conversation_id,
        AIConversationContext.user_id == user_id
    ).first()
    
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    # Get conversation service
    conversation_service = ConversationService(db)
    
    # Add message
    new_message = conversation_service.add_message(
        conversation_id=conversation_id,
        content=message.content,
        role=message.role,
        user_id=user_id,
        metadata=message.metadata
    )
    
    return new_message
