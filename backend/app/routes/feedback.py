from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, desc, func
from datetime import datetime, timedelta
from uuid import UUID
from typing import Dict, List, Optional, Union
from pydantic import BaseModel, Field, ConfigDict

from ..database import get_db
from ..models.feedback import Feedback, FeedbackSettings, FeedbackRating, FeedbackStatus
from ..models.appointment import Appointment, AppointmentStatus
from ..models.client import Client
from ..models.service import Service
from ..auth import get_current_user

router = APIRouter(tags=["feedback"])

# Pydantic models for request/response
class FeedbackSettingsRequest(BaseModel):
    enabled: bool = True
    auto_request_after_appointment: bool = True
    request_delay_hours: int = 2
    allow_anonymous_feedback: bool = True
    require_rating: bool = True
    require_comment: bool = False
    show_public_reviews: bool = True
    minimum_rating_for_public: int = 3
    request_template: str = "Hi {{clientName}}! We hope you enjoyed your {{serviceName}} appointment. We'd love to hear your feedback! Please rate your experience: {{feedbackLink}}"

class FeedbackSettingsResponse(FeedbackSettingsRequest):
    id: str
    user_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

class FeedbackCreateRequest(BaseModel):
    appointment_id: str
    rating: int = Field(..., ge=1, le=5)
    comment: Optional[str] = None
    service_rating: Optional[int] = Field(None, ge=1, le=5)
    is_anonymous: bool = False

class FeedbackResponse(BaseModel):
    id: str
    user_id: str
    client_id: str
    appointment_id: str
    rating: int
    comment: Optional[str] = None
    service_rating: Optional[int] = None
    status: str
    is_anonymous: bool
    is_public: bool
    business_response: Optional[str] = None
    responded_at: Optional[datetime] = None
    request_sent_at: Optional[datetime] = None
    submitted_at: datetime
    created_at: datetime
    updated_at: Optional[datetime] = None

    # Related data
    client_name: Optional[str] = None
    service_name: Optional[str] = None
    appointment_date: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

class BusinessResponseRequest(BaseModel):
    business_response: str

class FeedbackAnalytics(BaseModel):
    total_feedback: int
    average_rating: float
    rating_distribution: Dict[int, int]
    recent_feedback_count: int
    response_rate: float

class PaginatedFeedbackResponse(BaseModel):
    items: List[FeedbackResponse]
    total: int
    page: int
    limit: int

# Settings endpoints
@router.get("/feedback-settings", response_model=FeedbackSettingsResponse)
async def get_feedback_settings(
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Get feedback settings for the current user"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Get feedback settings from the database
    settings = db.query(FeedbackSettings).filter(
        FeedbackSettings.user_id == user_id
    ).first()

    if not settings:
        # Create default settings if none exist
        settings = FeedbackSettings(
            user_id=user_id,
            enabled=True,
            auto_request_after_appointment=True,
            request_delay_hours=2,
            allow_anonymous_feedback=True,
            require_rating=True,
            require_comment=False,
            show_public_reviews=True,
            minimum_rating_for_public=3,
            request_template="Hi {{clientName}}! We hope you enjoyed your {{serviceName}} appointment. We'd love to hear your feedback! Please rate your experience: {{feedbackLink}}"
        )
        db.add(settings)
        db.commit()
        db.refresh(settings)

    # Convert to response format
    return FeedbackSettingsResponse(
        id=str(settings.id),
        user_id=str(settings.user_id),
        enabled=settings.enabled,
        auto_request_after_appointment=settings.auto_request_after_appointment,
        request_delay_hours=settings.request_delay_hours,
        allow_anonymous_feedback=settings.allow_anonymous_feedback,
        require_rating=settings.require_rating,
        require_comment=settings.require_comment,
        show_public_reviews=settings.show_public_reviews,
        minimum_rating_for_public=settings.minimum_rating_for_public,
        request_template=settings.request_template,
        created_at=settings.created_at,
        updated_at=settings.updated_at
    )

@router.put("/feedback-settings", response_model=FeedbackSettingsResponse)
async def update_feedback_settings(
    settings_data: FeedbackSettingsRequest,
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Update feedback settings for the current user"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Get existing settings or create new ones
    settings = db.query(FeedbackSettings).filter(
        FeedbackSettings.user_id == user_id
    ).first()

    if not settings:
        settings = FeedbackSettings(user_id=user_id)
        db.add(settings)

    # Update settings
    settings.enabled = settings_data.enabled
    settings.auto_request_after_appointment = settings_data.auto_request_after_appointment
    settings.request_delay_hours = settings_data.request_delay_hours
    settings.allow_anonymous_feedback = settings_data.allow_anonymous_feedback
    settings.require_rating = settings_data.require_rating
    settings.require_comment = settings_data.require_comment
    settings.show_public_reviews = settings_data.show_public_reviews
    settings.minimum_rating_for_public = settings_data.minimum_rating_for_public
    settings.request_template = settings_data.request_template

    db.commit()
    db.refresh(settings)

    return FeedbackSettingsResponse(
        id=str(settings.id),
        user_id=str(settings.user_id),
        enabled=settings.enabled,
        auto_request_after_appointment=settings.auto_request_after_appointment,
        request_delay_hours=settings.request_delay_hours,
        allow_anonymous_feedback=settings.allow_anonymous_feedback,
        require_rating=settings.require_rating,
        require_comment=settings.require_comment,
        show_public_reviews=settings.show_public_reviews,
        minimum_rating_for_public=settings.minimum_rating_for_public,
        request_template=settings.request_template,
        created_at=settings.created_at,
        updated_at=settings.updated_at
    )

# Feedback management endpoints
@router.get("/feedback", response_model=PaginatedFeedbackResponse)
async def get_feedback(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    rating_filter: Optional[int] = Query(None, ge=1, le=5),
    status_filter: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Get feedback for the current user with pagination and filters"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Build query
    query = db.query(Feedback).filter(Feedback.user_id == user_id)

    # Apply filters
    if rating_filter:
        query = query.filter(Feedback.rating == FeedbackRating(rating_filter))

    if status_filter:
        query = query.filter(Feedback.status == FeedbackStatus(status_filter))

    # Get total count
    total = query.count()

    # Apply pagination and ordering
    feedback_items = query.order_by(desc(Feedback.submitted_at)).offset((page - 1) * limit).limit(limit).all()

    # Convert to response format
    items = []
    for feedback in feedback_items:
        # Get related data
        client = db.query(Client).filter(Client.id == feedback.client_id).first()
        appointment = db.query(Appointment).filter(Appointment.id == feedback.appointment_id).first()
        service = db.query(Service).filter(Service.id == appointment.service_id).first() if appointment else None

        items.append(FeedbackResponse(
            id=str(feedback.id),
            user_id=str(feedback.user_id),
            client_id=str(feedback.client_id),
            appointment_id=str(feedback.appointment_id),
            rating=feedback.rating.value,
            comment=feedback.comment,
            service_rating=feedback.service_rating.value if feedback.service_rating else None,
            status=feedback.status.value,
            is_anonymous=feedback.is_anonymous,
            is_public=feedback.is_public,
            business_response=feedback.business_response,
            responded_at=feedback.responded_at,
            request_sent_at=feedback.request_sent_at,
            submitted_at=feedback.submitted_at,
            created_at=feedback.created_at,
            updated_at=feedback.updated_at,
            client_name=client.name if client and not feedback.is_anonymous else "Anonymous",
            service_name=service.name if service else None,
            appointment_date=appointment.start_time if appointment else None
        ))

    return PaginatedFeedbackResponse(
        items=items,
        total=total,
        page=page,
        limit=limit
    )

@router.post("/feedback/{feedback_id}/respond")
async def respond_to_feedback(
    feedback_id: str,
    response_data: BusinessResponseRequest,
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Respond to client feedback"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Get feedback
    feedback = db.query(Feedback).filter(
        Feedback.id == UUID(feedback_id),
        Feedback.user_id == user_id
    ).first()

    if not feedback:
        raise HTTPException(status_code=404, detail="Feedback not found")

    # Update feedback with business response
    feedback.business_response = response_data.business_response
    feedback.responded_at = datetime.now()
    feedback.status = FeedbackStatus.RESPONDED

    db.commit()

    return {"success": True, "message": "Response added successfully"}

@router.get("/feedback/analytics", response_model=FeedbackAnalytics)
async def get_feedback_analytics(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: Union[Dict, None] = Depends(get_current_user)
):
    """Get feedback analytics for the current user"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = UUID(current_user["id"])

    # Date range for recent feedback
    since_date = datetime.now() - timedelta(days=days)

    # Total feedback count
    total_feedback = db.query(Feedback).filter(Feedback.user_id == user_id).count()

    # Average rating
    avg_rating_result = db.query(func.avg(Feedback.rating)).filter(Feedback.user_id == user_id).scalar()
    average_rating = float(avg_rating_result) if avg_rating_result else 0.0

    # Rating distribution
    rating_distribution = {}
    for rating in range(1, 6):
        count = db.query(Feedback).filter(
            Feedback.user_id == user_id,
            Feedback.rating == FeedbackRating(rating)
        ).count()
        rating_distribution[rating] = count

    # Recent feedback count
    recent_feedback_count = db.query(Feedback).filter(
        Feedback.user_id == user_id,
        Feedback.submitted_at >= since_date
    ).count()

    # Response rate (feedback with business responses)
    total_with_responses = db.query(Feedback).filter(
        Feedback.user_id == user_id,
        Feedback.business_response.isnot(None)
    ).count()

    response_rate = (total_with_responses / total_feedback * 100) if total_feedback > 0 else 0.0

    return FeedbackAnalytics(
        total_feedback=total_feedback,
        average_rating=round(average_rating, 2),
        rating_distribution=rating_distribution,
        recent_feedback_count=recent_feedback_count,
        response_rate=round(response_rate, 2)
    )

# Public feedback endpoints (for client submission)
@router.post("/feedback/submit")
async def submit_feedback(
    feedback_data: FeedbackCreateRequest,
    db: Session = Depends(get_db)
):
    """Submit feedback for an appointment (public endpoint for clients)"""
    try:
        appointment_id = UUID(feedback_data.appointment_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid appointment ID")

    # Get appointment and verify it exists and is completed
    appointment = db.query(Appointment).filter(Appointment.id == appointment_id).first()

    if not appointment:
        raise HTTPException(status_code=404, detail="Appointment not found")

    if appointment.status != AppointmentStatus.CONFIRMED:
        raise HTTPException(status_code=400, detail="Can only provide feedback for completed appointments")

    # Check if feedback already exists for this appointment
    existing_feedback = db.query(Feedback).filter(Feedback.appointment_id == appointment_id).first()
    if existing_feedback:
        raise HTTPException(status_code=400, detail="Feedback already submitted for this appointment")

    # Create feedback
    feedback = Feedback(
        user_id=appointment.user_id,
        client_id=appointment.client_id,
        appointment_id=appointment_id,
        rating=FeedbackRating(feedback_data.rating),
        comment=feedback_data.comment,
        service_rating=FeedbackRating(feedback_data.service_rating) if feedback_data.service_rating else None,
        is_anonymous=feedback_data.is_anonymous,
        status=FeedbackStatus.SUBMITTED
    )

    db.add(feedback)
    db.commit()

    return {"success": True, "message": "Feedback submitted successfully"}

@router.get("/feedback/public/{user_id}")
async def get_public_feedback(
    user_id: str,
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db)
):
    """Get public feedback for a business (for public reviews display)"""
    try:
        user_uuid = UUID(user_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid user ID")

    # Get feedback settings to check if public reviews are enabled
    settings = db.query(FeedbackSettings).filter(FeedbackSettings.user_id == user_uuid).first()

    if not settings or not settings.show_public_reviews:
        return {"items": [], "message": "Public reviews are not enabled for this business"}

    # Get public feedback above minimum rating
    feedback_items = db.query(Feedback).filter(
        Feedback.user_id == user_uuid,
        Feedback.is_public == True,
        Feedback.rating >= FeedbackRating(settings.minimum_rating_for_public)
    ).order_by(desc(Feedback.submitted_at)).limit(limit).all()

    # Convert to public response format (anonymized)
    items = []
    for feedback in feedback_items:
        appointment = db.query(Appointment).filter(Appointment.id == feedback.appointment_id).first()
        service = db.query(Service).filter(Service.id == appointment.service_id).first() if appointment else None
        client = db.query(Client).filter(Client.id == feedback.client_id).first()

        items.append({
            "rating": feedback.rating.value,
            "comment": feedback.comment,
            "service_rating": feedback.service_rating.value if feedback.service_rating else None,
            "service_name": service.name if service else None,
            "submitted_at": feedback.submitted_at,
            "client_name": "Anonymous" if feedback.is_anonymous else (client.name[:1] + "***" if client else "Anonymous"),
            "business_response": feedback.business_response
        })

    return {"items": items}
