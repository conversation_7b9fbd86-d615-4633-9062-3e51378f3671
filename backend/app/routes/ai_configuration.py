from fastapi import APIRouter, Depends, HTTPException, Body, UploadFile, File
from sqlalchemy.orm import Session
from typing import Dict, List, Optional, Union
from uuid import UUID
from pydantic import BaseModel, Field
from datetime import datetime
import asyncio
import io
import PyPDF2
from docx import Document
import os
import logging

from ..database import get_db
from ..models.ai_configuration import AIConfiguration, KnowledgeDocument
from ..models.user import User
from ..models.service import Service, ServiceType
from ..auth import get_current_user
from ..services.embedding_service import embedding_service

router = APIRouter(tags=["ai-configuration"])

# Pydantic models for request/response
class WhatsAppSettings(BaseModel):
    enableAutoResponses: bool = True
    responseDelay: int = 2000
    maxResponseLength: int = 500
    enableDetailedLogging: bool = True

class AIConfigurationBase(BaseModel):
    system_prompt: str
    tone: Optional[str] = "professional"
    primary_language: Optional[str] = "en"
    supported_languages: Optional[List[str]] = ["en"]
    business_rules: Optional[Dict] = None
    response_templates: Optional[Dict] = None
    whatsapp_settings: Optional[WhatsAppSettings] = None

class AIConfigurationCreate(AIConfigurationBase):
    pass

class AIConfigurationResponse(AIConfigurationBase):
    id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class KnowledgeDocumentBase(BaseModel):
    title: str
    content: str
    document_metadata: Optional[Dict] = None

class KnowledgeDocumentCreate(KnowledgeDocumentBase):
    pass

class KnowledgeDocumentResponse(KnowledgeDocumentBase):
    id: str
    user_id: str
    created_at: datetime

    class Config:
        orm_mode = True

# Default system prompt template
DEFAULT_SYSTEM_PROMPT = """You are an AI assistant for {business_name}, a {business_type} business.
Your role is to help clients with appointment scheduling and answering questions about our services.

BUSINESS INFORMATION:
- Name: {business_name}
- Address: {business_address}
- Business Hours: {business_hours}
- Contact: {business_phone} | {business_email}

AVAILABLE SERVICES:
{services_list}

YOUR ROLE:
1. Answer questions about the business, services, pricing, and availability
2. Help clients book appointments
3. Be friendly, professional, and concise
4. When booking appointments, collect: client name, service type, preferred date and time
5. Check if the requested time is within business hours
6. For appointment booking, you should ask for all necessary information and then confirm the details before finalizing

IMPORTANT GUIDELINES:
- Don't make up information that isn't provided
- If you're unsure about something, ask for clarification
- Always be polite and helpful
- Use a {tone} tone
- Keep responses concise and to the point

When discussing dates, always use the current date from your system.
"""

# AI Configuration endpoints
@router.get("/ai-configuration", response_model=AIConfigurationResponse)
async def get_ai_configuration(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Get the AI configuration for the current user"""
    user_id = current_user["id"]

    # Check if configuration exists
    config = db.query(AIConfiguration).filter(AIConfiguration.user_id == user_id).first()

    if not config:
        # Create default configuration
        config = AIConfiguration(
            user_id=user_id,
            system_prompt=DEFAULT_SYSTEM_PROMPT,
            tone="professional",
            primary_language="en",
            supported_languages=["en"],
            business_rules={},
            response_templates={},
            whatsapp_settings={
                "enableAutoResponses": True,
                "responseDelay": 2000,
                "maxResponseLength": 500,
                "enableDetailedLogging": True
            }
        )
        db.add(config)
        db.commit()
        db.refresh(config)

    return config

@router.post("/ai-configuration", response_model=AIConfigurationResponse)
async def create_or_update_ai_configuration(
    config_data: AIConfigurationCreate,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Create or update AI configuration for the current user"""
    user_id = current_user["id"]

    # Check if configuration exists
    config = db.query(AIConfiguration).filter(AIConfiguration.user_id == user_id).first()

    if config:
        # Update existing configuration
        for key, value in config_data.dict(exclude_unset=True).items():
            setattr(config, key, value)
    else:
        # Create new configuration
        config = AIConfiguration(user_id=user_id, **config_data.dict())
        db.add(config)

    db.commit()
    db.refresh(config)
    return config

@router.get("/generate-system-prompt")
async def generate_system_prompt(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Generate a system prompt based on user settings"""
    user_id = current_user["id"]

    # Get user information
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Get business information from dedicated columns
    business_name = user.business_name or "Our Business"
    business_address = user.address or "Our Location"
    business_phone = user.phone or ""
    business_email = user.email or ""
    business_website = user.website or ""

    # Get business hours (hardcoded for now)
    business_hours = "9 AM - 5 PM, Monday to Friday"  # Default
    # TODO: Add working hours to the database model
    # In the future, we can add working hours to the database model and format them here

    # Get services
    services = db.query(Service).filter(Service.user_id == user_id, Service.active == True).all()

    # Format services list
    services_list = ""
    if services:
        for service in services:
            # Get service type name
            service_type = db.query(ServiceType).filter(ServiceType.id == service.type_id).first()
            type_name = service_type.name if service_type else "General"

            # Format price with currency
            price_str = f"{service.price} {user.currency}" if user.currency else f"{service.price}"

            # Add service to list
            services_list += f"- {service.name} ({type_name}): {price_str}, Duration: {service.duration_minutes} minutes\n"
            if service.description:
                services_list += f"  Description: {service.description}\n"
    else:
        services_list = "No services available at this time."

    # Get AI configuration for tone
    ai_config = db.query(AIConfiguration).filter(AIConfiguration.user_id == user_id).first()
    tone = "professional"
    if ai_config:
        tone = ai_config.tone or "professional"

    # Determine business type based on services
    business_type = "service-based"
    if services:
        service_types = set()
        for service in services:
            service_type = db.query(ServiceType).filter(ServiceType.id == service.type_id).first()
            if service_type:
                service_types.add(service_type.name.lower())

        if service_types:
            if any("hair" in t or "salon" in t or "beauty" in t for t in service_types):
                business_type = "beauty salon"
            elif any("therapy" in t or "massage" in t or "wellness" in t for t in service_types):
                business_type = "wellness and therapy"
            elif any("consult" in t or "coach" in t or "advice" in t for t in service_types):
                business_type = "consulting"
            elif any("medical" in t or "health" in t or "clinic" in t for t in service_types):
                business_type = "healthcare"

    # Generate the system prompt
    system_prompt = DEFAULT_SYSTEM_PROMPT.format(
        business_name=business_name,
        business_type=business_type,
        business_address=business_address,
        business_hours=business_hours,
        business_phone=business_phone,
        business_email=business_email,
        services_list=services_list,
        tone=tone
    )

    return {"system_prompt": system_prompt}

# Knowledge Document endpoints
@router.get("/knowledge-documents", response_model=List[KnowledgeDocumentResponse])
async def get_knowledge_documents(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Get all knowledge documents for the current user"""
    user_id = current_user["id"]
    documents = db.query(KnowledgeDocument).filter(KnowledgeDocument.user_id == user_id).all()
    return documents

@router.post("/knowledge-documents", response_model=KnowledgeDocumentResponse)
async def create_knowledge_document(
    document_data: KnowledgeDocumentCreate,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Create a new knowledge document"""
    user_id = current_user["id"]

    # Create new document
    document = KnowledgeDocument(user_id=user_id, **document_data.dict())

    # Generate embedding for the document content
    try:
        embedding = await embedding_service.get_embedding(document_data.content)
        document.embedding = embedding
    except Exception as e:
        print(f"Error generating embedding: {str(e)}")
        # Continue without embedding if it fails

    db.add(document)
    db.commit()
    db.refresh(document)
    return document

@router.post("/knowledge-documents/upload", response_model=KnowledgeDocumentResponse)
async def upload_knowledge_document(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Upload a file as a knowledge document"""
    user_id = current_user["id"]

    # Read file content
    content = await file.read()
    text_content = ""

    # Extract text based on file type
    file_extension = os.path.splitext(file.filename)[1].lower()

    try:
        if file_extension == ".pdf":
            # Process PDF file
            pdf_reader = PyPDF2.PdfReader(io.BytesIO(content))
            text_content = "\n".join([page.extract_text() for page in pdf_reader.pages])

        elif file_extension in [".docx", ".doc"]:
            # Process Word document
            doc = Document(io.BytesIO(content))
            text_content = "\n".join([para.text for para in doc.paragraphs])

        elif file_extension == ".txt":
            # Process text file
            text_content = content.decode("utf-8")

        else:
            raise HTTPException(status_code=400, detail=f"Unsupported file type: {file_extension}")

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing file: {str(e)}")

    # Create document
    document = KnowledgeDocument(
        user_id=user_id,
        title=file.filename,
        content=text_content,
        document_metadata={
            "file_type": file.content_type,
            "file_size": len(content),
            "file_name": file.filename
        }
    )

    # Generate embedding
    try:
        embedding = await embedding_service.get_embedding(text_content)
        document.embedding = embedding
    except Exception as e:
        print(f"Error generating embedding: {str(e)}")
        # Continue without embedding if it fails

    db.add(document)
    db.commit()
    db.refresh(document)
    return document

@router.get("/knowledge-documents/{document_id}", response_model=KnowledgeDocumentResponse)
async def get_knowledge_document(
    document_id: UUID,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Get a specific knowledge document"""
    user_id = current_user["id"]
    document = db.query(KnowledgeDocument).filter(
        KnowledgeDocument.id == document_id,
        KnowledgeDocument.user_id == user_id
    ).first()

    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    return document

@router.delete("/knowledge-documents/{document_id}", response_model=Dict)
async def delete_knowledge_document(
    document_id: UUID,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Delete a knowledge document"""
    user_id = current_user["id"]
    document = db.query(KnowledgeDocument).filter(
        KnowledgeDocument.id == document_id,
        KnowledgeDocument.user_id == user_id
    ).first()

    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    db.delete(document)
    db.commit()

    return {"success": True, "message": "Document deleted successfully"}
