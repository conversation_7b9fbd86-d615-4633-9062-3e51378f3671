from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel

from app.database import get_db
from app.auth import get_current_user
from app.models.google_calendar_cache import GoogleCalendarCache

router = APIRouter(tags=["google-calendar"])

class GoogleCalendarEvent(BaseModel):
    id: str
    summary: str
    description: Optional[str] = None
    location: Optional[str] = None
    start: Dict[str, Any]
    end: Dict[str, Any]
    attendees: Optional[List[Dict[str, Any]]] = None
    status: Optional[str] = None
    created: Optional[str] = None
    updated: Optional[str] = None
    htmlLink: Optional[str] = None

class CacheEventsRequest(BaseModel):
    events: List[GoogleCalendarEvent]

class CacheEventsResponse(BaseModel):
    cached_count: int
    success: bool

class CachedEventsResponse(BaseModel):
    events: List[GoogleCalendarEvent]
    count: int

@router.post("/integrations/google-calendar/cache-events", response_model=CacheEventsResponse)
async def cache_events(
    request: CacheEventsRequest,
    current_user: Dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Cache Google Calendar events in the database to avoid frequent API calls
    """
    user_id = UUID(current_user["id"])
    cached_count = 0

    try:
        # Process each event
        for event in request.events:
            # Check if event already exists in cache
            existing_event = db.query(GoogleCalendarCache).filter(
                GoogleCalendarCache.user_id == user_id,
                GoogleCalendarCache.event_id == event.id
            ).first()

            # Parse start and end times
            start_time = datetime.fromisoformat(event.start["dateTime"].replace("Z", "+00:00"))
            end_time = datetime.fromisoformat(event.end["dateTime"].replace("Z", "+00:00"))

            if existing_event:
                # Update existing event
                existing_event.event_data = event.dict()
                existing_event.start_time = start_time
                existing_event.end_time = end_time
                existing_event.updated_at = datetime.utcnow()
            else:
                # Create new cached event
                new_cached_event = GoogleCalendarCache(
                    user_id=user_id,
                    event_id=event.id,
                    event_data=event.dict(),
                    start_time=start_time,
                    end_time=end_time
                )
                db.add(new_cached_event)

            cached_count += 1

        # Commit all changes
        db.commit()
        return {"cached_count": cached_count, "success": True}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to cache events: {str(e)}")

@router.get("/integrations/google-calendar/cached-events", response_model=CachedEventsResponse)
async def get_cached_events(
    start: str = Query(..., description="Start date in ISO format"),
    end: str = Query(..., description="End date in ISO format"),
    current_user: Dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get cached Google Calendar events from the database
    """
    user_id = UUID(current_user["id"])

    try:
        # Parse start and end dates
        start_date = datetime.fromisoformat(start.replace("Z", "+00:00"))
        end_date = datetime.fromisoformat(end.replace("Z", "+00:00"))

        # Query cached events within the date range
        cached_events = db.query(GoogleCalendarCache).filter(
            GoogleCalendarCache.user_id == user_id,
            GoogleCalendarCache.start_time >= start_date,
            GoogleCalendarCache.end_time <= end_date
        ).all()

        # Extract event data
        events = [event.event_data for event in cached_events]

        return {"events": events, "count": len(events)}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get cached events: {str(e)}")
