from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Optional
from uuid import UUID
import stripe

from ..config import settings
from ..database import get_db
from ..models.invoice import Invoice as DBInvoice
from ..auth import get_current_user

# Initialize Stripe with the API key
if settings.STRIPE_SECRET_KEY:
    stripe.api_key = settings.STRIPE_SECRET_KEY

# Create router with the correct prefix and tags
router = APIRouter(tags=["invoices"])

# Get a specific Stripe invoice
@router.get("/invoices/{invoice_id}/download")
async def download_invoice(
    invoice_id: str,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Get download links for a specific invoice"""
    if not settings.STRIPE_SECRET_KEY:
        raise HTTPException(status_code=501, detail="Stripe integration not configured")

    try:
        # Check if this is a mock invoice ID (starts with 'inv_')
        if invoice_id.startswith('inv_') and len(invoice_id) < 10:
            # For mock invoices, return a mock PDF URL
            return {"invoice_pdf": None, "hosted_invoice_url": None, "message": "This is a mock invoice and cannot be downloaded"}

        # For real Stripe invoices, get the invoice from Stripe
        stripe_invoice = stripe.Invoice.retrieve(invoice_id)

        # Return the PDF URL and hosted invoice URL
        return {
            "invoice_pdf": stripe_invoice.invoice_pdf,
            "hosted_invoice_url": stripe_invoice.hosted_invoice_url
        }
    except stripe.error.StripeError as e:
        raise HTTPException(status_code=400, detail=f"Stripe error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving invoice: {str(e)}")
