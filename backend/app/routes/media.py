"""
Media proxy routes for WhatsApp media
"""

import logging
import requests
from fastapi import APIRouter, HTTPException, Response, Request
from fastapi.responses import StreamingResponse
from app.config import settings

router = APIRouter(prefix="/media", tags=["media"])
logger = logging.getLogger(__name__)

@router.options("/whatsapp-proxy")
async def options_whatsapp_media():
    """Handle CORS preflight requests"""
    response = Response()
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, apikey"
    return response

@router.get("/whatsapp-proxy")
@router.head("/whatsapp-proxy")
async def proxy_whatsapp_media(url: str, request: Request):
    """
    Proxy for WhatsApp media files

    This endpoint acts as a proxy for WhatsApp media files from the Evolution API.
    It adds the necessary authentication headers and returns the media file.

    Args:
        url: The URL of the media file to proxy

    Returns:
        The media file content
    """
    try:
        # Check if the URL is valid
        if not url:
            raise HTTPException(status_code=400, detail="Missing URL parameter")

        # Check if this is a HEAD request
        is_head_request = request.method == "HEAD"
        logger.info(f"Request method: {request.method}, is_head_request: {is_head_request}")

        # Set up headers - always include the API key for WhatsApp media
        # WhatsApp media URLs come from mmg.whatsapp.net or similar domains
        # but still need the Evolution API key for authentication
        headers = {
            "apikey": settings.EVOLUTION_API_KEY
        }

        # Log the request for debugging
        logger.info(f"Fetching media from URL: {url} with headers: {headers}")

        # Make the request to fetch the media
        try:
            # Use HEAD for HEAD requests, GET for GET requests
            if is_head_request:
                response = requests.head(url, headers=headers, timeout=30)
            else:
                # Add retries for GET requests
                max_retries = 3
                retry_count = 0
                response = None
                last_error = None

                while retry_count < max_retries:
                    try:
                        logger.info(f"Fetching media (attempt {retry_count + 1}/{max_retries}): {url}")
                        response = requests.get(url, headers=headers, timeout=30, stream=True)

                        # If successful, break out of the retry loop
                        if response.status_code == 200 or response.status_code == 206:
                            break

                        # If we got a response but it's not 200/206, log it and retry
                        logger.warning(f"Got non-200/206 response: {response.status_code} - {response.text[:100]}")

                    except requests.RequestException as e:
                        last_error = e
                        logger.warning(f"Request failed (attempt {retry_count + 1}/{max_retries}): {str(e)}")

                    # Increment retry count and wait before retrying
                    retry_count += 1
                    if retry_count < max_retries:
                        import time
                        # Exponential backoff
                        wait_time = 0.5 * (2 ** retry_count)
                        logger.info(f"Waiting {wait_time} seconds before retry")
                        time.sleep(wait_time)

                # If we've exhausted all retries and still don't have a response
                if response is None:
                    error_msg = f"Failed to fetch media after {max_retries} attempts"
                    if last_error:
                        error_msg += f": {str(last_error)}"
                    logger.error(error_msg)
                    raise HTTPException(status_code=500, detail=error_msg)

        except requests.RequestException as e:
            logger.error(f"Error fetching media: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching media: {str(e)}"
            )

        # Log the response status and headers for debugging
        logger.info(f"Media fetch response status: {response.status_code}, content-type: {response.headers.get('Content-Type')}")
        logger.info(f"Response headers: {dict(response.headers)}")

        # Check if the response is successful (200 OK or 206 Partial Content)
        if response.status_code != 200 and response.status_code != 206:
            logger.error(f"Failed to fetch media: {response.status_code} - {response.text[:200]}")

            # Special handling for common error codes
            if response.status_code == 404:
                # Return a 404 with a more helpful message
                raise HTTPException(
                    status_code=404,
                    detail="Media not found. The file may have been deleted or the URL may have expired."
                )
            elif response.status_code == 401 or response.status_code == 403:
                # Authentication or authorization error
                raise HTTPException(
                    status_code=response.status_code,
                    detail="Access denied. Authentication or authorization failed."
                )
            else:
                # Generic error for other status codes
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Failed to fetch media: {response.text[:200]}"
                )

        # Log the content length for debugging
        content_length = response.headers.get('Content-Length', 'unknown')
        logger.info(f"Media content length: {content_length} bytes")

        # Get content type from response
        content_type = response.headers.get("Content-Type", "application/octet-stream")

        # For HEAD requests, return a simple response with headers
        if is_head_request:
            response_obj = Response(content="", media_type=content_type)
        else:
            # Return the media file with the correct content type and CORS headers
            # Use a streaming response to avoid loading the entire file into memory
            def iterfile():
                yield from response.iter_content(chunk_size=10*1024)

            # Create a streaming response
            response_obj = StreamingResponse(
                content=iterfile(),
                media_type=content_type
            )

        # Add CORS headers
        response_obj.headers["Access-Control-Allow-Origin"] = "*"
        response_obj.headers["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
        response_obj.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, apikey, Range"
        response_obj.headers["Access-Control-Expose-Headers"] = "Content-Length, Content-Range, Content-Type"

        # Copy any other relevant headers from the original response
        if "Content-Length" in response.headers:
            response_obj.headers["Content-Length"] = response.headers["Content-Length"]
        if "Cache-Control" in response.headers:
            response_obj.headers["Cache-Control"] = response.headers["Cache-Control"]

        return response_obj

    except Exception as e:
        logger.exception(f"Error proxying media: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error proxying media: {str(e)}")
