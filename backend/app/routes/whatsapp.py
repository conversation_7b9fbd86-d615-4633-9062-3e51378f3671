from fastapi import APIRouter, Depends, HTTPException, Body, Request, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
from datetime import datetime
import requests
import json
from uuid import UUID
from typing import Dict, List, Optional, Union
import asyncio

from ..database import get_db
from ..auth import get_current_user
from ..config import settings
from ..models.message import Message
from ..models.ai_configuration import AIConfiguration
from ..websocket import send_message_to_user, WebSocketEventType
from ..models.client import Client

router = APIRouter(tags=["whatsapp"])

# Headers for Evolution API requests
EVOLUTION_HEADERS = {
    "Content-Type": "application/json",
    "apikey": settings.EVOLUTION_API_KEY
}

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        if user_id not in self.active_connections:
            self.active_connections[user_id] = []
        self.active_connections[user_id].append(websocket)
        print(f"WebSocket connected for user {user_id}. Total connections: {len(self.active_connections[user_id])}")

    def disconnect(self, websocket: WebSocket, user_id: str):
        if user_id in self.active_connections:
            self.active_connections[user_id].remove(websocket)
            print(f"WebSocket disconnected for user {user_id}. Remaining connections: {len(self.active_connections[user_id])}")

    async def send_message(self, message: dict, user_id: str):
        if user_id in self.active_connections:
            disconnected_websockets = []
            for connection in self.active_connections[user_id]:
                try:
                    await connection.send_json(message)
                except Exception as e:
                    print(f"Error sending message to WebSocket: {str(e)}")
                    disconnected_websockets.append(connection)

            # Clean up disconnected websockets
            for ws in disconnected_websockets:
                self.active_connections[user_id].remove(ws)

manager = ConnectionManager()

@router.get("/status")
async def get_whatsapp_status(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Check if WhatsApp is connected"""
    try:
        user_id = current_user["id"]
        print(f"Checking WhatsApp status for user {user_id}...")

        # First check the connection state directly
        state_response = requests.get(
            f"{settings.EVOLUTION_API_URL}/instance/connectionState/{user_id}",
            headers=EVOLUTION_HEADERS
        )

        print(f"Connection state response: {state_response.status_code}")

        # If we can't get the connection state, the instance might not exist
        if state_response.status_code != 200:
            return {"connected": False, "message": "No active WhatsApp connection found"}

        state_data = state_response.json()
        state = state_data.get('instance', {}).get('state')
        print(f"Connection state: {state}")

        # If the state is not 'open', the connection is not active
        if state != 'open':
            return {
                "connected": False,
                "status": state,
                "message": f"WhatsApp is not connected (status: {state})"
            }

        # Get more detailed information about the instance
        response = requests.get(
            f"{settings.EVOLUTION_API_URL}/instance/fetchInstances",
            headers=EVOLUTION_HEADERS
        )

        if response.status_code != 200:
            # Even if we can't get detailed info, we know it's connected from the state check
            return {
                "connected": True,
                "phone": "Unknown",
                "name": "Unknown",
                "status": state
            }

        instances = response.json()
        user_instance = next((i for i in instances if i.get("name") == user_id), None)

        if not user_instance:
            # This shouldn't happen since we already verified the connection state
            return {
                "connected": True,
                "phone": "Unknown",
                "name": "Unknown",
                "status": state
            }

        # Extract phone number from ownerJid (format: "<EMAIL>")
        owner_jid = user_instance.get("ownerJid", "")
        phone_number = "Unknown"
        if owner_jid and "@" in owner_jid:
            phone_number = "+" + owner_jid.split("@")[0]

        return {
            "connected": True,
            "phone": phone_number,
            "name": user_instance.get("profileName", "Unknown"),
            "status": state,
            "instanceId": user_id  # Include the instance ID in the response
        }

    except Exception as e:
        print(f"Error checking WhatsApp status: {str(e)}")
        return {"connected": False, "message": f"Error: {str(e)}"}

@router.post("/connect")
async def connect_whatsapp(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Create a WhatsApp instance and get QR code for connection"""
    try:
        import time
        user_id = current_user["id"]
        print(f"\n\n=== Starting WhatsApp connection process for user {user_id} ===")

        # Step 1: Fetch instances to check if one exists with the user's ID
        print("Step 1: Checking if instance already exists...")
        instances_response = requests.get(
            f"{settings.EVOLUTION_API_URL}/instance/fetchInstances",
            headers=EVOLUTION_HEADERS
        )

        instance_exists = False
        if instances_response.status_code == 200:
            instances = instances_response.json()
            for instance in instances:
                if isinstance(instance, dict) and 'instance' in instance:
                    instance_name = instance['instance'].get('instanceName')
                    if instance_name == user_id:
                        instance_exists = True
                        print(f"Found existing instance for user {user_id}")
                        break

        # Step 2: If an instance exists, try to connect to it and get a QR code
        if instance_exists:
            print("Step 2: Instance exists, trying to connect to it...")

            # Check the connection state
            state_response = requests.get(
                f"{settings.EVOLUTION_API_URL}/instance/connectionState/{user_id}",
                headers=EVOLUTION_HEADERS
            )

            connection_state = None
            if state_response.status_code == 200:
                state_data = state_response.json()
                connection_state = state_data.get('instance', {}).get('state')
                print(f"Connection state: {connection_state}")

                # If already connected, return success
                if connection_state == 'open':
                    print("Instance is already connected!")
                    return {"success": True, "message": "WhatsApp is already connected"}

            # Try to get QR code for existing instance
            print("Trying to get QR code for existing instance...")
            qr_response = requests.get(
                f"{settings.EVOLUTION_API_URL}/instance/connect/{user_id}",
                headers=EVOLUTION_HEADERS
            )

            print(f"QR response: {qr_response.status_code} - {qr_response.text}")

            qr_code_str = None
            if qr_response.status_code == 200:
                qr_data = qr_response.json()

                # Get the QR code string
                qr_code_str = qr_data.get("code")

                # Check if we have a pairing code instead
                if not qr_code_str:
                    pairing_code = qr_data.get("pairingCode")
                    if pairing_code:
                        print(f"Got pairing code: {pairing_code}")
                        qr_code_str = f"https://wa.me/qr/{pairing_code}"

            # If we got a QR code, return it
            if qr_code_str:
                print("Successfully got QR code for existing instance")
                # Generate QR code image and return it
                return generate_qr_image(qr_code_str)

            # If we couldn't get a QR code, delete the instance and create a new one
            print("Failed to get QR code for existing instance, deleting it...")

            # Step 3: Delete the existing instance
            print("Step 3: Deleting existing instance...")
            delete_response = requests.delete(
                f"{settings.EVOLUTION_API_URL}/instance/delete/{user_id}",
                headers=EVOLUTION_HEADERS
            )

            print(f"Delete response: {delete_response.status_code} - {delete_response.text}")

            # If delete fails, try to force delete
            if delete_response.status_code != 200:
                print(f"Failed to delete instance {user_id}, trying force delete...")
                force_delete_response = requests.delete(
                    f"{settings.EVOLUTION_API_URL}/instance/delete/{user_id}?force=true",
                    headers=EVOLUTION_HEADERS
                )

                print(f"Force delete response: {force_delete_response.status_code} - {force_delete_response.text}")

            # Wait for the deletion to take effect
            print("Waiting for deletion to take effect (5 seconds)...")
            time.sleep(5)

            # Verify the instance was deleted by checking instances again
            verify_response = requests.get(
                f"{settings.EVOLUTION_API_URL}/instance/fetchInstances",
                headers=EVOLUTION_HEADERS
            )

            if verify_response.status_code == 200:
                instances = verify_response.json()
                for instance in instances:
                    if isinstance(instance, dict) and 'instance' in instance:
                        instance_name = instance['instance'].get('instanceName')
                        if instance_name == user_id:
                            print(f"WARNING: Instance {user_id} still exists after deletion attempt")
                            # Try one more force delete
                            requests.delete(
                                f"{settings.EVOLUTION_API_URL}/instance/delete/{user_id}?force=true",
                                headers=EVOLUTION_HEADERS
                            )
                            time.sleep(3)
                            break

        # Step 4: Create a new instance with QR code
        print("Step 4: Creating new instance with QR code...")
        create_response = requests.post(
            f"{settings.EVOLUTION_API_URL}/instance/create",
            headers=EVOLUTION_HEADERS,
            json={
                "instanceName": user_id,
                "token": user_id,
                "qrcode": True,  # This is important to generate QR code
                "integration": "WHATSAPP-BAILEYS"
            }
        )

        print(f"Create response: {create_response.status_code} - {create_response.text}")

        if create_response.status_code != 201:
            error_detail = create_response.text
            try:
                error_json = create_response.json()
                error_detail = error_json
            except:
                pass

            # If we get a 403 error with "name already in use", it means the instance still exists
            # Try to delete it again and return a message to try again
            if create_response.status_code == 403 and "already in use" in str(error_detail):
                print("Instance name still in use, trying to force delete again...")
                requests.delete(
                    f"{settings.EVOLUTION_API_URL}/instance/delete/{user_id}?force=true",
                    headers=EVOLUTION_HEADERS
                )
                return {"success": False, "message": "Instance still exists. Please try again in a few moments."}

            return {"success": False, "message": f"Failed to create WhatsApp instance: {error_detail}"}

        # Step 5: Get the QR code for the new instance
        print("Step 5: Getting QR code for new instance...")

        # Wait for the instance to initialize
        print("Waiting for instance to initialize (8 seconds)...")
        time.sleep(8)

        # Try multiple times to get the QR code
        max_attempts = 12
        qr_code_str = None

        for attempt in range(max_attempts):
            wait_time = min(2 ** attempt, 15)  # Exponential backoff, capped at 15 seconds
            print(f"QR code attempt {attempt + 1}/{max_attempts}...")

            # Try to get QR code
            qr_response = requests.get(
                f"{settings.EVOLUTION_API_URL}/instance/connect/{user_id}",
                headers=EVOLUTION_HEADERS
            )

            print(f"QR response: {qr_response.status_code} - {qr_response.text}")

            if qr_response.status_code == 200:
                qr_data = qr_response.json()

                # Get the QR code string
                qr_code_str = qr_data.get("code")

                if qr_code_str:
                    print(f"Got QR code on attempt {attempt + 1}")
                    break

                # Check if we have a pairing code instead
                pairing_code = qr_data.get("pairingCode")
                if pairing_code:
                    print(f"Got pairing code: {pairing_code}")
                    qr_code_str = f"https://wa.me/qr/{pairing_code}"
                    break

            # If we still don't have a QR code, wait and try again
            if not qr_code_str:
                print(f"No QR code in response on attempt {attempt + 1}, waiting {wait_time} seconds...")
                time.sleep(wait_time)

        if not qr_code_str:
            return {"success": False, "message": "Failed to get QR code after multiple attempts. Please try again later."}

        # Step 6: Set up webhook for real-time updates
        print("Step 6: Setting up webhook for real-time updates...")
        webhook_url = f"{settings.BACKEND_URL}/whatsapp/webhook/{user_id}"

        webhook_response = requests.post(
            f"{settings.EVOLUTION_API_URL}/webhook/set/{user_id}",
            headers=EVOLUTION_HEADERS,
            json={
                "enabled": True,
                "url": webhook_url,
                "webhookByEvents": True,
                "webhookBase64": True,
                "events": [
                    "MESSAGES_UPSERT",
                    "MESSAGES_UPDATE",
                    "SEND_MESSAGE",
                    "CONNECTION_UPDATE"
                ]
            }
        )

        print(f"Webhook setup response: {webhook_response.status_code} - {webhook_response.text}")

        # Generate QR code image and return it
        qr_response = generate_qr_image(qr_code_str)

        # Add the instance ID to the response
        qr_response["instanceId"] = user_id

        return qr_response

    except Exception as e:
        print(f"Error connecting WhatsApp: {str(e)}")
        return {"success": False, "message": f"Error: {str(e)}"}


@router.post("/webhook/{user_id}")
async def whatsapp_webhook(
    user_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """Webhook for WhatsApp messages and events"""
    try:
        # Get the webhook data
        webhook_data = await request.json()
        event_type = webhook_data.get('event')

        print(f"Received webhook for user {user_id}, event: {event_type}")

        # Process based on event type
        if event_type in ["MESSAGES_UPSERT", "messages.upsert"]:
            # Handle new messages
            print(f"Calling process_new_messages for user {user_id}")
            await process_new_messages(user_id, webhook_data, db)
            print(f"Finished process_new_messages for user {user_id}")

            # Send real-time update via WebSocket
            try:
                await send_message_to_user(user_id, WebSocketEventType.WHATSAPP_MESSAGE, {
                    "type": "new_message",
                    "data": webhook_data
                })
            except Exception as e:
                print(f"Error sending WebSocket update: {str(e)}")

        elif event_type in ["MESSAGES_UPDATE", "messages.update"]:
            # Handle message updates (read status, etc.)
            print(f"Message update received: {webhook_data}")

            # Send real-time update via WebSocket
            try:
                await send_message_to_user(user_id, WebSocketEventType.WHATSAPP_MESSAGE, {
                    "type": "message_update",
                    "data": webhook_data
                })
            except Exception as e:
                print(f"Error sending WebSocket update: {str(e)}")

        elif event_type in ["SEND_MESSAGE", "send.message"]:
            # Handle sent messages confirmation
            print(f"Message sent confirmation: {webhook_data}")

            # Send real-time update via WebSocket
            try:
                await send_message_to_user(user_id, WebSocketEventType.WHATSAPP_MESSAGE, {
                    "type": "message_sent",
                    "data": webhook_data
                })
            except Exception as e:
                print(f"Error sending WebSocket update: {str(e)}")
        else:
            print(f"Unhandled event type: {event_type}")

        return {"success": True}

    except Exception as e:
        print(f"Error processing webhook: {str(e)}")
        return {"success": False, "message": f"Error: {str(e)}"}


async def process_new_messages(user_id: str, webhook_data: dict, db: Session):
    """Process new messages from WhatsApp webhook"""
    try:
        from app.models.message import Message
        from app.models.client import Client
        from app.models.ai_configuration import AIConfiguration
        from app.services.ai_assistant import AIAssistant
        from app.services.embedding_service import embedding_service
        from app.websocket import manager
        from uuid import UUID, uuid4
        from datetime import datetime
        import json
        import requests
        import asyncio
        from app.config import settings

        EVOLUTION_HEADERS = {
            "Content-Type": "application/json",
            "apikey": settings.EVOLUTION_API_KEY
        }

        # Log the webhook data for debugging
        print(f"Processing webhook data for user {user_id}: {json.dumps(webhook_data, indent=2)[:500]}...")

        # Extract the message data from the webhook payload
        # Evolution API sends the message data directly in 'data', not in 'data.messages'
        message_data = webhook_data.get('data')
        if not message_data:
            print("No message data found in webhook")
            return

        print(f"Processing single message from webhook")

        # Process the single message
        # Skip messages sent by the business
        if message_data.get('key', {}).get('fromMe', False):
            print("Skipping message sent by business")
            return

        # Extract message ID to prevent duplicates
        message_id = message_data.get('key', {}).get('id', '')
        if not message_id:
            print("No message ID found")
            return

        # Check if this message has already been processed in the database
        # We'll use the message ID as a reference in the metadata
        try:
            # Use a safer approach to check for existing messages
            # Instead of using JSONB contains which might not work in all PostgreSQL setups
            existing_message = db.query(Message).filter(
                Message.user_id == UUID(user_id),
                Message.source == "whatsapp"
            ).all()

            # Manually check if any message has the same WhatsApp message ID
            existing_message = next((msg for msg in existing_message if
                                    msg.intent_data and
                                    isinstance(msg.intent_data, dict) and
                                    msg.intent_data.get("whatsapp_message_id") == message_id), None)
        except Exception as e:
            print(f"Error checking for existing message: {str(e)}")
            existing_message = None

        if existing_message:
            print(f"Message with ID {message_id} already exists in database, skipping")
            return

        # Extract the phone number (remove the @s.whatsapp.net suffix)
        remote_jid = message_data.get('key', {}).get('remoteJid', '')
        if not remote_jid or '@' not in remote_jid:
            print(f"Invalid remote JID: {remote_jid}")
            return

        phone = remote_jid.split('@')[0]

        # Extract the message content
        message_content = ''
        message_obj = message_data.get('message', {})

        # Handle different message types
        if 'conversation' in message_obj and message_obj['conversation']:
            message_content = message_obj['conversation']
        elif 'extendedTextMessage' in message_obj and message_obj['extendedTextMessage'].get('text'):
            message_content = message_obj['extendedTextMessage'].get('text', '')
        elif 'imageMessage' in message_obj:
            caption = message_obj['imageMessage'].get('caption', '')
            message_content = f"[Image sent]{' ' + caption if caption else ''}"
        elif 'videoMessage' in message_obj:
            caption = message_obj['videoMessage'].get('caption', '')
            message_content = f"[Video sent]{' ' + caption if caption else ''}"
        elif 'audioMessage' in message_obj:
            message_content = "[Audio message sent]"
        elif 'documentMessage' in message_obj:
            filename = message_obj['documentMessage'].get('fileName', 'document')
            message_content = f"[Document sent: {filename}]"
        elif 'locationMessage' in message_obj:
            lat = message_obj['locationMessage'].get('degreesLatitude', '')
            long = message_obj['locationMessage'].get('degreesLongitude', '')
            message_content = f"[Location sent: {lat},{long}]"
        elif 'contactMessage' in message_obj:
            name = message_obj['contactMessage'].get('displayName', 'contact')
            message_content = f"[Contact shared: {name}]"
        elif 'stickerMessage' in message_obj:
            message_content = "[Sticker sent]"
        else:
            # Try to extract any text content from unknown message types
            try:
                message_content = str(message_obj)
                if len(message_content) > 100:
                    message_content = "[Unsupported message type]"
            except:
                message_content = "[Unsupported message type]"

        # Skip non-text messages for now (like senderKeyDistributionMessage)
        if not message_content or message_content.startswith("[Unsupported"):
            print(f"Skipping non-text message: {list(message_obj.keys())}")
            return

        # Skip group messages for now
        if remote_jid.endswith('@g.us'):
            print(f"Skipping group message from: {remote_jid}")
            return

        if not phone or not message_content:
            print(f"Missing phone or message content: {message_data}")
            return

        # Find the user
        try:
            user_uuid = UUID(user_id)
        except ValueError:
            print(f"Invalid user ID: {user_id}")
            return

        # Find the client by phone number
        client = db.query(Client).filter(
            Client.user_id == user_uuid,
            Client.phone.like(f"%{phone}%")
        ).first()

        # If client doesn't exist, create a new one
        if not client:
            print(f"Client not found for phone {phone}, creating new client")

            # Try to get contact info from WhatsApp
            contact_name = "Unknown"
            try:
                contacts_response = requests.post(
                    f"{settings.EVOLUTION_API_URL}/chat/findContacts/{user_id}",
                    headers=EVOLUTION_HEADERS,
                    json={
                        "where": {
                            "id": remote_jid
                        }
                    }
                )

                if contacts_response.status_code == 200:
                    contacts_data = contacts_response.json()
                    if contacts_data and len(contacts_data) > 0:
                        contact_name = contacts_data[0].get('pushName', 'Unknown')
            except Exception as e:
                print(f"Error getting contact info: {str(e)}")

            # Create a new client
            client = Client(
                id=uuid4(),  # Generate a UUID for the client
                user_id=user_uuid,
                name=contact_name if contact_name != "Unknown" else f"WhatsApp Contact ({phone})",
                phone=phone,
                email="",
                tags=["whatsapp"],  # Use lowercase for consistency
                notes="Automatically created from WhatsApp message",
                created_at=datetime.now()
            )
            db.add(client)
            db.commit()
            db.refresh(client)

        # Generate embedding for the incoming message
        message_embedding = await embedding_service.get_embedding(message_content)

        # Save the incoming message
        timestamp = datetime.fromtimestamp(message_data.get('messageTimestamp', 0))

        # Create metadata with WhatsApp-specific information
        whatsapp_metadata = {
            "whatsapp_message_id": message_id,
            "whatsapp_chat_id": remote_jid,
            "whatsapp_message_type": next(iter(message_obj.keys())) if message_obj else "unknown",
            "whatsapp_timestamp": message_data.get('messageTimestamp', 0)
        }

        incoming_message = Message(
            user_id=user_uuid,
            client_id=client.id,
            content=message_content,
            is_from_business=False,  # From client
            sent_at=timestamp,
            embedding=message_embedding,
            source="whatsapp",  # This is a WhatsApp message
            intent_data=whatsapp_metadata  # Store WhatsApp metadata
        )
        db.add(incoming_message)
        db.commit()
        db.refresh(incoming_message)

        # Check if auto-responses are enabled for this user
        ai_config = db.query(AIConfiguration).filter(AIConfiguration.user_id == user_uuid).first()
        auto_responses_enabled = True  # Default to enabled

        if ai_config and ai_config.whatsapp_settings:
            auto_responses_enabled = ai_config.whatsapp_settings.get("enableAutoResponses", True)

        if not auto_responses_enabled:
            print(f"Auto-responses are disabled for user {user_id}, skipping AI processing")
            return

        # Process the message with AI
        ai_assistant = AIAssistant(db, user_id)
        response_text, action = await ai_assistant.process_message(str(client.id), message_content)

        # Generate embedding for the AI response
        response_embedding = await embedding_service.get_embedding(response_text)

        # Save the AI response
        # Create metadata for the response
        response_metadata = {
            "whatsapp_parent_message_id": message_id,
            "whatsapp_chat_id": remote_jid,
            "ai_generated": True,
            "response_timestamp": int(datetime.now().timestamp())
        }

        outgoing_message = Message(
            user_id=user_uuid,
            client_id=client.id,
            content=response_text,
            is_from_business=True,  # From business
            sent_at=datetime.now(),
            embedding=response_embedding,
            parent_id=incoming_message.id,  # Link to the incoming message
            source="whatsapp",  # This is a WhatsApp message, even though it's AI-generated
            intent_data=response_metadata  # Store metadata
        )
        db.add(outgoing_message)
        db.commit()

        # Send the AI response back via WhatsApp with retry mechanism
        max_retries = 3
        retry_delay = 2  # seconds
        success = False

        for retry in range(max_retries):
            try:
                send_response = requests.post(
                    f"{settings.EVOLUTION_API_URL}/message/sendText/{user_id}",
                    headers=EVOLUTION_HEADERS,
                    json={
                        "number": phone,
                        "text": response_text,
                        "delay": 1200,
                        "linkPreview": False
                    },
                    timeout=10  # Add timeout to prevent hanging
                )

                if send_response.status_code == 201 or send_response.status_code == 200:
                    print(f"AI response sent to WhatsApp: {send_response.status_code} - {send_response.text}")
                    success = True

                    # Update the outgoing message with the WhatsApp message ID if available
                    try:
                        response_data = send_response.json()
                        if response_data and 'key' in response_data and 'id' in response_data['key']:
                            whatsapp_message_id = response_data['key']['id']
                            outgoing_message.intent_data['whatsapp_message_id'] = whatsapp_message_id
                            db.commit()
                    except Exception as json_error:
                        print(f"Error extracting WhatsApp message ID: {str(json_error)}")

                    break
                else:
                    print(f"Failed to send WhatsApp message (attempt {retry+1}/{max_retries}): {send_response.status_code} - {send_response.text}")

                    # Wait before retrying
                    if retry < max_retries - 1:
                        await asyncio.sleep(retry_delay * (retry + 1))  # Exponential backoff
            except Exception as send_error:
                print(f"Error sending WhatsApp message (attempt {retry+1}/{max_retries}): {str(send_error)}")

                # Wait before retrying
                if retry < max_retries - 1:
                    await asyncio.sleep(retry_delay * (retry + 1))  # Exponential backoff

        if not success:
            print("Failed to send WhatsApp message after all retries")
            # Update the message to indicate failure
            outgoing_message.intent_data['send_failed'] = True
            db.commit()

        print(f"Successfully processed WhatsApp message from {phone}: {message_content[:50]}...")

    except Exception as e:
        print(f"Error processing new messages: {str(e)}")
        import traceback
        traceback.print_exc()
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for real-time updates"""
    # Get the token from the query parameters
    token = websocket.query_params.get("token")

    # Validate the token
    if not token:
        print(f"WebSocket connection rejected: No token provided for user {user_id}")
        await websocket.close(code=1008)  # Policy violation
        return

    try:
        # Decode the token and verify it matches the user_id
        from ..auth import decode_token
        payload = decode_token(token)
        token_user_id = payload.get("sub")

        if token_user_id != user_id:
            print(f"WebSocket connection rejected: Token user ID {token_user_id} does not match {user_id}")
            await websocket.close(code=1008)  # Policy violation
            return

        # Accept the connection
        await manager.connect(websocket, user_id)

        # Send a connection confirmation message
        await websocket.send_json({
            "type": "connection_status",
            "data": {
                "status": "connected",
                "message": "WebSocket connection established"
            }
        })

        # Keep the connection alive
        while True:
            # Wait for messages from the client
            await websocket.receive_text()

    except WebSocketDisconnect:
        print(f"WebSocket disconnected for user {user_id}")
        manager.disconnect(websocket, user_id)
    except Exception as e:
        print(f"WebSocket error for user {user_id}: {str(e)}")
        try:
            manager.disconnect(websocket, user_id)
        except:
            pass
