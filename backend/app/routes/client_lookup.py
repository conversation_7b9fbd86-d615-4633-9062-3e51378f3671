from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from uuid import UUID, uuid4
from typing import Dict, Optional, List
from pydantic import BaseModel
import re

from ..database import get_db
from ..models.client import Client, ClientSchema
from ..auth import get_current_user

router = APIRouter(tags=["client-lookup"])

class PhoneLookupRequest(BaseModel):
    phone: str
    name: Optional[str] = None

class ClientResponse(BaseModel):
    id: str
    name: str
    phone: str
    email: Optional[str] = None
    is_new: bool = False

@router.post("/clients/find-by-phone", response_model=ClientResponse)
async def find_client_by_phone(
    request: PhoneLookupRequest,
    db: Session = Depends(get_db),
    current_user: Optional[Dict] = Depends(get_current_user)
):
    """Find a client by phone number or create a new one if not found"""
    # Handle both authenticated and webhook requests
    if current_user:
        user_id = UUID(current_user["id"])
    else:
        # For webhook requests, we need to extract user_id from the request path
        # This would be handled differently in a real implementation
        # For now, we'll just return an error
        raise HTTPException(status_code=401, detail="Not authenticated")

    # Format the phone number (remove any non-digit characters except +)
    formatted_phone = request.phone
    if formatted_phone.startswith("whatsapp-"):
        formatted_phone = formatted_phone.replace("whatsapp-", "")

    # Ensure the phone number starts with +
    if not formatted_phone.startswith("+") and re.match(r'^\d+$', formatted_phone):
        formatted_phone = "+" + formatted_phone

    # Try to find the client by phone number
    client = db.query(Client).filter(
        Client.user_id == user_id,
        Client.phone.like(f"%{formatted_phone}%")
    ).first()

    # If client exists, return it
    if client:
        return {
            "id": str(client.id),
            "name": client.name,
            "phone": client.phone,
            "email": client.email,
            "is_new": False
        }

    # If client doesn't exist, create a new one
    display_name = request.name if request.name else f"WhatsApp Contact ({formatted_phone})"
    profile_image = None

    # Try to get profile picture from WhatsApp if this is a WhatsApp contact
    if formatted_phone.startswith("+"):
        try:
            from ..config import settings
            import requests
            import base64

            EVOLUTION_HEADERS = {
                "Content-Type": "application/json",
                "apikey": settings.EVOLUTION_API_KEY
            }

            # First try to get contact info
            phone_without_plus = formatted_phone[1:] if formatted_phone.startswith("+") else formatted_phone
            contact_id = f"{phone_without_plus}@s.whatsapp.net"
            print(f"[client_lookup] Looking for WhatsApp contact with ID: {contact_id} for user {user_id}")

            contacts_response = requests.post(
                f"{settings.EVOLUTION_API_URL}/chat/findContacts/{str(user_id)}",
                headers=EVOLUTION_HEADERS,
                json={
                    "where": {
                        "id": contact_id
                    }
                }
            )

            print(f"[client_lookup] WhatsApp findContacts response status: {contacts_response.status_code}")

            if contacts_response.status_code == 200:
                contacts_data = contacts_response.json()
                print(f"[client_lookup] WhatsApp contacts data: {contacts_data}")

                if contacts_data and len(contacts_data) > 0:
                    # Get the contact name if available and not already provided
                    if not request.name and contacts_data[0].get('pushName'):
                        display_name = contacts_data[0].get('pushName')
                        print(f"[client_lookup] Found WhatsApp contact name: {display_name}")

                    # Try to get profile picture
                    if contacts_data[0].get('id'):
                        contact_wuid = contacts_data[0].get('id')
                        print(f"[client_lookup] Fetching profile picture for WhatsApp ID: {contact_wuid}")

                        profile_pic_response = requests.post(
                            f"{settings.EVOLUTION_API_URL}/chat/fetchProfilePictureUrl/{str(user_id)}",
                            headers=EVOLUTION_HEADERS,
                            json={
                                "wuid": contact_wuid
                            }
                        )

                        print(f"[client_lookup] Profile picture response status: {profile_pic_response.status_code}")
                        print(f"[client_lookup] Profile picture response body: {profile_pic_response.text}")

                        if profile_pic_response.status_code == 200:
                            profile_pic_data = profile_pic_response.json()
                            print(f"[client_lookup] Profile picture data: {profile_pic_data}")

                            if profile_pic_data and profile_pic_data.get('profilePictureUrl'):
                                # Download the profile picture and convert to base64
                                pic_url = profile_pic_data.get('profilePictureUrl')
                                print(f"[client_lookup] Found profile picture URL: {pic_url}")

                                pic_response = requests.get(pic_url)
                                print(f"[client_lookup] Profile picture download status: {pic_response.status_code}")

                                if pic_response.status_code == 200:
                                    import base64
                                    profile_image = f"data:image/jpeg;base64,{base64.b64encode(pic_response.content).decode('utf-8')}"
                                    print(f"[client_lookup] Successfully downloaded and encoded profile picture for {display_name} (size: {len(profile_image)} bytes)")
        except Exception as e:
            print(f"Error getting WhatsApp profile picture: {str(e)}")

    new_client = Client(
        id=uuid4(),
        user_id=user_id,
        phone=formatted_phone,
        name=display_name,
        email="",
        tags=["whatsapp"],
        notes="Automatically created from WhatsApp message",
        profile_image=profile_image
    )

    db.add(new_client)
    db.commit()
    db.refresh(new_client)

    return {
        "id": str(new_client.id),
        "name": new_client.name,
        "phone": new_client.phone,
        "email": new_client.email,
        "is_new": True
    }

@router.post("/clients/create-from-whatsapp")
async def create_client_from_whatsapp(
    data: dict = Body(...),
    db: Session = Depends(get_db)
):
    """Create a client from WhatsApp webhook data"""
    try:
        user_id = data.get("user_id")
        phone = data.get("phone")
        name = data.get("name")

        if not user_id or not phone:
            raise HTTPException(status_code=400, detail="Missing required fields")

        # Format the phone number
        if not phone.startswith("+") and re.match(r'^\d+$', phone):
            phone = "+" + phone

        # Check if client already exists
        try:
            user_uuid = UUID(user_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid user ID")

        existing_client = db.query(Client).filter(
            Client.user_id == user_uuid,
            Client.phone.like(f"%{phone}%")
        ).first()

        if existing_client:
            return {
                "success": True,
                "message": "Client already exists",
                "client_id": str(existing_client.id),
                "is_new": False
            }

        # Create new client
        display_name = name if name else f"WhatsApp Contact ({phone})"
        profile_image = None

        # Try to get profile picture from WhatsApp
        try:
            from ..config import settings
            import requests
            import base64

            EVOLUTION_HEADERS = {
                "Content-Type": "application/json",
                "apikey": settings.EVOLUTION_API_KEY
            }

            # First try to get contact info
            phone_without_plus = phone[1:] if phone.startswith("+") else phone
            contact_id = f"{phone_without_plus}@s.whatsapp.net"
            print(f"[create_from_whatsapp] Looking for WhatsApp contact with ID: {contact_id} for user {user_id}")

            contacts_response = requests.post(
                f"{settings.EVOLUTION_API_URL}/chat/findContacts/{user_id}",
                headers=EVOLUTION_HEADERS,
                json={
                    "where": {
                        "id": contact_id
                    }
                }
            )

            print(f"[create_from_whatsapp] WhatsApp findContacts response status: {contacts_response.status_code}")

            if contacts_response.status_code == 200:
                contacts_data = contacts_response.json()
                print(f"[create_from_whatsapp] WhatsApp contacts data: {contacts_data}")

                if contacts_data and len(contacts_data) > 0:
                    # Get the contact name if available and not already provided
                    if not name and contacts_data[0].get('pushName'):
                        display_name = contacts_data[0].get('pushName')
                        print(f"[create_from_whatsapp] Found WhatsApp contact name: {display_name}")

                    # Try to get profile picture
                    if contacts_data[0].get('id'):
                        contact_wuid = contacts_data[0].get('id')
                        print(f"[create_from_whatsapp] Fetching profile picture for WhatsApp ID: {contact_wuid}")

                        profile_pic_response = requests.post(
                            f"{settings.EVOLUTION_API_URL}/chat/fetchProfilePictureUrl/{user_id}",
                            headers=EVOLUTION_HEADERS,
                            json={
                                "wuid": contact_wuid
                            }
                        )

                        print(f"[create_from_whatsapp] Profile picture response status: {profile_pic_response.status_code}")
                        print(f"[create_from_whatsapp] Profile picture response body: {profile_pic_response.text}")

                        if profile_pic_response.status_code == 200:
                            profile_pic_data = profile_pic_response.json()
                            print(f"[create_from_whatsapp] Profile picture data: {profile_pic_data}")

                            if profile_pic_data and profile_pic_data.get('profilePictureUrl'):
                                # Download the profile picture and convert to base64
                                pic_url = profile_pic_data.get('profilePictureUrl')
                                print(f"[create_from_whatsapp] Found profile picture URL: {pic_url}")

                                pic_response = requests.get(pic_url)
                                print(f"[create_from_whatsapp] Profile picture download status: {pic_response.status_code}")

                                if pic_response.status_code == 200:
                                    import base64
                                    profile_image = f"data:image/jpeg;base64,{base64.b64encode(pic_response.content).decode('utf-8')}"
                                    print(f"[create_from_whatsapp] Successfully downloaded and encoded profile picture for {display_name} (size: {len(profile_image)} bytes)")
        except Exception as e:
            print(f"Error getting WhatsApp profile picture: {str(e)}")

        new_client = Client(
            id=uuid4(),
            user_id=user_uuid,
            phone=phone,
            name=display_name,
            email="",
            tags=["whatsapp"],
            notes="Automatically created from WhatsApp message",
            profile_image=profile_image
        )

        db.add(new_client)
        db.commit()
        db.refresh(new_client)

        return {
            "success": True,
            "message": "Client created successfully",
            "client_id": str(new_client.id),
            "is_new": True
        }
    except Exception as e:
        import traceback
        print(f"Error creating client from WhatsApp: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Error creating client: {str(e)}")
