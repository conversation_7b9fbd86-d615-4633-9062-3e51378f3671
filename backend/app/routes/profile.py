import logging
import hashlib
from fastapi import APIRouter, Depends, HTTPEx<PERSON>
from sqlalchemy.orm import Session
from pydantic import BaseModel
from uuid import UUID
from typing import Optional, Dict, Any
from ..database import get_db
from ..models.user import User
from ..auth import get_current_user

# Configure logger
logger = logging.getLogger(__name__)

router = APIRouter(tags=["profile"])

class UserProfileUpdate(BaseModel):
    business_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    website: Optional[str] = None
    currency: Optional[str] = None
    logo_url: Optional[str] = None

# Settings update model removed - using dedicated fields instead

@router.get("/me")
async def get_current_user_profile(db: Session = Depends(get_db), current_user: Dict = Depends(get_current_user)):
    """Get the current user's profile"""
    try:
        user_id = UUID(current_user["id"])
        db_user = db.query(User).filter(User.id == user_id).first()

        if not db_user:
            raise HTTPException(status_code=404, detail="User not found")

        # Return user profile data using dedicated columns
        return {
            "id": str(db_user.id),
            "email": db_user.email,
            "business_name": db_user.business_name,
            "currency": db_user.currency,
            "phone": db_user.phone,
            "address": db_user.address,
            "website": db_user.website,
            "logo_url": db_user.logo_url,
            "created_at": db_user.registered_at.isoformat() if db_user.registered_at else None
        }
    except Exception as e:
        logger.error(f"Error getting user profile: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get user profile: {str(e)}")

@router.put("/update")
async def update_user_profile(profile: UserProfileUpdate, db: Session = Depends(get_db), current_user: Dict = Depends(get_current_user)):
    """Update the current user's profile"""
    try:
        user_id = UUID(current_user["id"])
        db_user = db.query(User).filter(User.id == user_id).first()

        if not db_user:
            raise HTTPException(status_code=404, detail="User not found")

        # Update basic fields
        if profile.business_name is not None:
            db_user.business_name = profile.business_name

        if profile.email is not None:
            # Check if email is already taken by another user
            existing_user = db.query(User).filter(User.email == profile.email, User.id != user_id).first()
            if existing_user:
                raise HTTPException(status_code=400, detail="Email already in use")
            db_user.email = profile.email

        if profile.currency is not None:
            db_user.currency = profile.currency

        # Log the profile data received with sanitized logo_url
        profile_log = profile.dict()
        if profile_log.get('logo_url'):
            image_base64 = profile_log['logo_url']
            image_hash = hashlib.md5(image_base64.encode()).hexdigest()
            image_size = len(image_base64)
            profile_log['logo_url'] = f"<base64_image_data_omitted> (size: {image_size} bytes, md5: {image_hash})"
        logger.info(f"Received profile update: {profile_log}")

        # Update dedicated columns
        if profile.address is not None:
            db_user.address = profile.address
            logger.info(f"Updated address to: {profile.address}")

        if profile.phone is not None:
            db_user.phone = profile.phone

        if profile.website is not None:
            db_user.website = profile.website

        if profile.logo_url is not None:
            db_user.logo_url = profile.logo_url

        # Commit changes
        db.commit()
        db.refresh(db_user)

        # Return updated user profile
        return {
            "id": str(db_user.id),
            "email": db_user.email,
            "business_name": db_user.business_name,
            "currency": db_user.currency,
            "phone": db_user.phone,
            "address": db_user.address,
            "website": db_user.website,
            "logo_url": db_user.logo_url,
            "created_at": db_user.registered_at.isoformat() if db_user.registered_at else None
        }
    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating user profile: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update user profile: {str(e)}")

# Settings update endpoint removed - using update_user_profile instead
