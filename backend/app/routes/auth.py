from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from uuid import uuid4, UUID
import requests
from typing import Optional, Dict, Any
from jose import JW<PERSON>rror, jwt
from datetime import datetime
from ..database import get_db
from ..models.user import User
from ..auth import get_password_hash, verify_password, create_access_token, create_refresh_token, get_current_user
from ..utils.rate_limiter import login_rate_limiter, twofa_rate_limiter
from ..config import settings

router = APIRouter(tags=["auth"])

# Request model - only what we need from the frontend
class UserCreate(BaseModel):
    email: str
    password: str
    business_name: str

class UserLogin(BaseModel):
    email: str
    password: str
    token: Optional[str] = None
    backup_code: Optional[str] = None

# Profile-related models moved to profile.py

class TokenRefresh(BaseModel):
    refresh_token: str

@router.post("/register")
async def register(user: User<PERSON><PERSON>, db: Session = Depends(get_db)):
    try:
        # Check if user exists
        db_user = db.query(User).filter(User.email == user.email).first()
        if db_user:
            raise HTTPException(status_code=400, detail="Email already registered")

        # Create new user with defaults for other fields
        new_user = User(
            id=uuid4(),
            email=user.email,
            password_hash=get_password_hash(user.password),
            business_name=user.business_name,
        )
        db.add(new_user)
        db.commit()
        db.refresh(new_user)

        # Create tokens for the new user
        access_token = create_access_token(data={
            "sub": new_user.email,
            "id": str(new_user.id)  # Include the user ID in the token
        })
        refresh_token = create_refresh_token(data={
            "sub": new_user.email,
            "id": str(new_user.id)  # Include the user ID in the token
        })

        return {
            "message": "User created successfully",
            "user_id": str(new_user.id),
            "access_token": access_token,
            "refresh_token": refresh_token
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/login")
async def login(user: UserLogin, db: Session = Depends(get_db), request: Request = None):
    # Apply rate limiting based on email address
    login_rate_limiter.check_rate_limit(user.email, "login")

    # Find user by email
    db_user = db.query(User).filter(User.email == user.email).first()
    if not db_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    # Verify password
    if not verify_password(user.password, db_user.password_hash):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Incorrect password")

    # Check if 2FA is enabled for this user
    if db_user.twofa_enabled:
        # If 2FA is enabled, we need a token or backup code
        if not user.token and not user.backup_code:
            return {
                "message": "Two-factor authentication required",
                "requires_2fa": True,
                "user_id": str(db_user.id)
            }

        # Apply rate limiting for 2FA verification
        twofa_rate_limiter.check_rate_limit(str(db_user.id), "login")

        # Verify the token or backup code
        from ..utils.twofa import verify_totp, verify_backup_code

        is_valid = False
        if user.token:
            # Verify TOTP token
            is_valid = verify_totp(db_user.twofa_secret, user.token)
        elif user.backup_code:
            # Verify backup code
            is_valid = verify_backup_code(db_user, user.backup_code)
            if is_valid:
                # Save the updated backup codes list
                db.commit()

        if not is_valid:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid verification code")

        # Reset rate limit counter on successful 2FA verification
        twofa_rate_limiter.reset(str(db_user.id), "login")

    # After successful authentication
    # Include the user ID in the token payload
    access_token = create_access_token(data={
        "sub": db_user.email,
        "id": str(db_user.id)  # Add the user ID to the token
    })
    refresh_token = create_refresh_token(data={
        "sub": db_user.email,
        "id": str(db_user.id)  # Add the user ID to the token
    })

    # Reset rate limit counter on successful login
    login_rate_limiter.reset(user.email, "login")

    return {
        "message": "Login successful",
        "user_id": str(db_user.id),
        "access_token": access_token,
        "refresh_token": refresh_token,
        "requires_2fa": False
    }

@router.post("/google-auth")
async def google_auth(code: str):
    # Exchange code for tokens
    token_url = "https://oauth2.googleapis.com/token"
    data = {
        "code": code,
        "client_id": "11367222904-t6fop0q2gdrueu894nggise0qslcr9h9.apps.googleusercontent.com",
        "client_secret": "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",
        "redirect_uri": "http://localhost:5173/dashboard/settings/google-callback",
        "grant_type": "authorization_code"
    }

    response = requests.post(token_url, data=data)
    tokens = response.json()

    # Get user info
    user_info = requests.get(
        "https://www.googleapis.com/oauth2/v1/userinfo",
        headers={"Authorization": f"Bearer {tokens['access_token']}"}
    ).json()

    return {
        "email": user_info["email"],
        "access_token": tokens["access_token"],
        "refresh_token": tokens["refresh_token"]
    }

# Profile-related endpoints moved to profile.py

# Profile update endpoint moved to profile.py

# Settings and address update endpoints moved to profile.py

@router.post("/refresh-token")
async def refresh_token(token_data: TokenRefresh, db: Session = Depends(get_db)):
    """Refresh an access token using a refresh token"""
    try:
        # Decode the refresh token
        payload = jwt.decode(
            token_data.refresh_token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )

        # Extract user information from token
        email = payload.get("sub")
        user_id = payload.get("id")
        exp = payload.get("exp")

        # Validate token data
        if not email or not user_id or not exp:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )

        # Check if token is expired
        if datetime.utcnow().timestamp() > exp:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Refresh token has expired"
            )

        # Find the user
        db_user = db.query(User).filter(User.id == UUID(user_id)).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        # Create new tokens
        access_token = create_access_token(data={
            "sub": db_user.email,
            "id": str(db_user.id)
        })

        # Optionally create a new refresh token if the current one is close to expiry
        # For example, if it's within 7 days of expiring
        new_refresh_token = None
        days_until_expiry = (exp - datetime.utcnow().timestamp()) / (24 * 3600)

        if days_until_expiry < 7:
            new_refresh_token = create_refresh_token(data={
                "sub": db_user.email,
                "id": str(db_user.id)
            })

        return {
            "access_token": access_token,
            "refresh_token": new_refresh_token if new_refresh_token else token_data.refresh_token,
            "token_type": "bearer"
        }
    except JWTError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Could not validate refresh token: {str(e)}"
        )
    except Exception as e:
        print(f"Error refreshing token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to refresh token: {str(e)}"
        )