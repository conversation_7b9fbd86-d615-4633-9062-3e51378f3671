"""
AI Response Generation API
This module provides endpoints for generating AI responses to messages.
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from typing import Dict, List, Optional, Any
from pydantic import BaseModel
from uuid import UUID

from ..database import get_db
from ..auth import get_current_user
from ..services.ai_assistant import AIAssistant
from ..services.intent_detection import detect_intent, get_intent_response
from ..services.language_detection import detect_language

router = APIRouter(tags=["ai-response"])

# Pydantic models for request/response
class MessageItem(BaseModel):
    role: str
    content: str

class GenerateResponseRequest(BaseModel):
    message: str
    client_name: str
    intent: Optional[str] = None
    language: Optional[str] = None
    conversation_history: Optional[List[MessageItem]] = None

class GenerateResponseResponse(BaseModel):
    text: str
    intent: Optional[str] = None
    language: str
    action: Optional[Dict[str, Any]] = None

@router.post("/ai/generate-response", response_model=GenerateResponseResponse)
async def generate_response(
    request_data: GenerateResponseRequest,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Generate an AI response to a message"""
    try:
        user_id = current_user["id"]
        
        # Detect language if not provided
        language = request_data.language
        if not language:
            language = detect_language(request_data.message) or "en"
        
        # Detect intent if not provided
        intent = request_data.intent
        if not intent:
            intent_result, confidence = detect_intent(request_data.message)
            intent = intent_result if confidence > 0.5 else None
        
        # If we have a clear intent and no conversation history, use a simple rule-based response
        if intent and not request_data.conversation_history:
            response_text = get_intent_response(intent, language)
            return GenerateResponseResponse(
                text=response_text,
                intent=intent,
                language=language
            )
        
        # Otherwise, use the AI assistant for a more sophisticated response
        ai_assistant = AIAssistant(db, user_id)
        
        # Create a dummy client ID for now (in a real implementation, you'd look up the client)
        client_id = "00000000-0000-0000-0000-000000000000"
        
        # Generate the response
        response_text, action = await ai_assistant.process_message(client_id, request_data.message)
        
        return GenerateResponseResponse(
            text=response_text,
            intent=intent,
            language=language,
            action=action
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating AI response: {str(e)}")
