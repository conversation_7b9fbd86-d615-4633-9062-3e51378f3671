"""
Address verification API endpoints
"""

import logging
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Dict, Any
from ..database import get_db
from ..auth import get_current_user
from ..services.address_service import address_service

logger = logging.getLogger(__name__)

router = APIRouter(tags=["address-verification"])

class AddressVerificationRequest(BaseModel):
    address: str

class AddressVerificationResponse(BaseModel):
    success: bool
    address: str
    formatted_address: str = None
    coordinates: Dict[str, float] = None
    timezone: str = None
    timezone_name: str = None
    current_time: str = None
    utc_offset: str = None
    error: str = None

@router.post("/verify-address", response_model=AddressVerificationResponse)
async def verify_address(
    request: AddressVerificationRequest,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """
    Verify a business address and get timezone information
    """
    try:
        logger.info(f"Verifying address: {request.address}")
        
        # Verify the address and get timezone
        result = await address_service.verify_and_get_timezone(request.address)
        
        logger.info(f"Address verification result: {result}")
        
        return AddressVerificationResponse(**result)
        
    except Exception as e:
        logger.error(f"Error verifying address: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Address verification failed: {str(e)}"
        )

@router.get("/timezone-info")
async def get_timezone_info(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """
    Get timezone information for the current user's business address
    """
    try:
        from ..models.user import User
        from uuid import UUID
        
        user_id = UUID(current_user["id"])
        user = db.query(User).filter(User.id == user_id).first()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        if not user.address:
            return {
                "success": False,
                "error": "No business address configured",
                "timezone": address_service.get_business_timezone(None),  # Default timezone
                "message": "Using default timezone. Please configure your business address for accurate timezone detection."
            }
        
        # Get timezone for the user's address
        business_timezone = address_service.get_business_timezone(user.address)
        
        import pytz
        from datetime import datetime
        
        try:
            tz = pytz.timezone(business_timezone)
            current_time = datetime.now(tz)
            
            return {
                "success": True,
                "address": user.address,
                "timezone": business_timezone,
                "current_time": current_time.isoformat(),
                "utc_offset": current_time.strftime('%z'),
                "formatted_time": current_time.strftime("%A, %B %d, %Y at %I:%M %p")
            }
        except Exception as e:
            logger.error(f"Error with timezone '{business_timezone}': {str(e)}")
            return {
                "success": False,
                "error": f"Invalid timezone: {business_timezone}",
                "address": user.address
            }
        
    except Exception as e:
        logger.error(f"Error getting timezone info: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get timezone info: {str(e)}"
        )
