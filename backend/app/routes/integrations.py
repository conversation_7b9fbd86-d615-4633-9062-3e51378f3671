from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
from datetime import datetime
import requests
import json
from uuid import UUID
from typing import Dict, List, Optional

from ..database import get_db
from ..models.integration import ExternalIntegration
from ..auth import get_current_user
from ..config import settings

router = APIRouter(tags=["integrations"])

# Google Calendar API endpoints
GOOGLE_TOKEN_URL = "https://oauth2.googleapis.com/token"
GOOGLE_CALENDAR_API = "https://www.googleapis.com/calendar/v3"

@router.get("/test-google")
async def test_google():
    """Test endpoint for Google OAuth"""
    return {
        "message": "Google OAuth test endpoint is working",
        "client_id": "11367222904-t6fop0q2gdrueu894nggise0qslcr9h9.apps.googleusercontent.com",
        "redirect_uri": "http://localhost:5173/dashboard/settings/google-callback"
    }

@router.post("/google-auth")
async def google_auth(
    code: str = Body(..., embed=True),
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    return await exchange_google_code(code, db, current_user)

@router.get("/google-auth-code")
async def google_auth_code(
    code: str,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    return await exchange_google_code(code, db, current_user)

async def exchange_google_code(code: str, db: Session, current_user: Dict):
    """Exchange authorization code for tokens and store them"""
    try:
        # Check if user already has a Google Calendar integration
        user_id = UUID(current_user["id"])
        existing_integration = db.query(ExternalIntegration).filter(
            ExternalIntegration.user_id == user_id,
            ExternalIntegration.integration_type == "google_calendar"
        ).first()

        # If there's an existing integration, check if it's valid
        if existing_integration and existing_integration.credentials.get("refresh_token"):
            try:
                # Try to refresh the token
                refresh_response = requests.post(
                    GOOGLE_TOKEN_URL,
                    data={
                        "client_id": "11367222904-t6fop0q2gdrueu894nggise0qslcr9h9.apps.googleusercontent.com",
                        "client_secret": "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",
                        "refresh_token": existing_integration.credentials["refresh_token"],
                        "grant_type": "refresh_token"
                    }
                )

                if refresh_response.status_code == 200:
                    refresh_tokens = refresh_response.json()

                    # If refresh successful, update the credentials and return
                    if "access_token" in refresh_tokens:
                        # Calculate expiration timestamp
                        expires_in = refresh_tokens["expires_in"]
                        expiration_timestamp = datetime.now().timestamp() + expires_in

                        # Update credentials
                        credentials = existing_integration.credentials
                        credentials["access_token"] = refresh_tokens["access_token"]
                        credentials["token_type"] = refresh_tokens["token_type"]
                        credentials["expires_in"] = expires_in
                        credentials["expiration_timestamp"] = expiration_timestamp

                        existing_integration.credentials = credentials
                        existing_integration.last_synced = func.now()
                        db.commit()

                        return {
                            "email": credentials.get("google_email"),
                            "connected": True,
                            "last_synced": datetime.now().isoformat()
                        }
            except Exception as e:
                print(f"Error refreshing existing token: {str(e)}")
                # Continue with the new code exchange

        # Exchange code for tokens
        data = {
            "code": code,
            "client_id": "11367222904-t6fop0q2gdrueu894nggise0qslcr9h9.apps.googleusercontent.com",
            "client_secret": "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",
            "redirect_uri": "http://localhost:5173/dashboard/settings/google-callback",
            "grant_type": "authorization_code"
        }

        print(f"Exchanging code for tokens with data: {data}")
        response = requests.post(GOOGLE_TOKEN_URL, data=data)
        tokens = response.json()
        print(f"Google token response: {tokens}")

        if "error" in tokens:
            # If we get an invalid_grant error and have an existing integration,
            # return the existing integration info instead of failing
            if tokens.get("error") == "invalid_grant" and existing_integration:
                print("Got invalid_grant error but have existing integration, returning that instead")
                # Make sure the integration is marked as active
                existing_integration.active = True
                db.commit()
                return {
                    "email": existing_integration.credentials.get("google_email"),
                    "connected": True,  # Always return connected: True here
                    "last_synced": existing_integration.last_synced.isoformat() if existing_integration.last_synced else None
                }

            raise HTTPException(status_code=400, detail=f"Google OAuth error: {tokens['error']}")

        # Get user info from Google
        try:
            user_info_response = requests.get(
                "https://www.googleapis.com/oauth2/v1/userinfo",
                headers={"Authorization": f"Bearer {tokens['access_token']}"}
            )
            user_info = user_info_response.json()
            print(f"Google user info response: {user_info}")
        except Exception as e:
            print(f"Error getting Google user info: {str(e)}")
            # Try alternative endpoint
            try:
                user_info_response = requests.get(
                    "https://www.googleapis.com/oauth2/v2/userinfo",
                    headers={"Authorization": f"Bearer {tokens['access_token']}"}
                )
                user_info = user_info_response.json()
                print(f"Google user info v2 response: {user_info}")
            except Exception as e2:
                print(f"Error getting Google user info v2: {str(e2)}")
                user_info = {}

        # If we still don't have an email, try the tokeninfo endpoint
        if not user_info.get("email"):
            try:
                token_info_response = requests.get(
                    f"https://www.googleapis.com/oauth2/v1/tokeninfo?access_token={tokens['access_token']}"
                )
                token_info = token_info_response.json()
                print(f"Token info response: {token_info}")
                if 'email' in token_info:
                    user_info['email'] = token_info['email']
            except Exception as e:
                print(f"Error getting token info: {str(e)}")

        # If we still don't have an email, try the userinfo.email endpoint
        if not user_info.get("email"):
            try:
                email_info_response = requests.get(
                    "https://www.googleapis.com/oauth2/v2/userinfo",
                    headers={"Authorization": f"Bearer {tokens['access_token']}"}
                )
                email_info = email_info_response.json()
                print(f"Email info response: {email_info}")
                if 'email' in email_info:
                    user_info['email'] = email_info['email']
            except Exception as e:
                print(f"Error getting email info: {str(e)}")

        # If we still don't have an email, try one more endpoint
        if not user_info.get("email"):
            try:
                profile_info_response = requests.get(
                    "https://www.googleapis.com/plus/v1/people/me",
                    headers={"Authorization": f"Bearer {tokens['access_token']}"}
                )
                profile_info = profile_info_response.json()
                print(f"Profile info response: {profile_info}")
                if 'emails' in profile_info and len(profile_info['emails']) > 0:
                    user_info['email'] = profile_info['emails'][0]['value']
            except Exception as e:
                print(f"Error getting profile info: {str(e)}")

        # Store tokens in the database
        user_id = UUID(current_user["id"])

        # Check if integration already exists
        integration = db.query(ExternalIntegration).filter(
            ExternalIntegration.user_id == user_id,
            ExternalIntegration.integration_type == "google_calendar"
        ).first()

        google_email = user_info.get("email")
        print(f"Google email: {google_email}")

        # Calculate expiration timestamp
        expires_in = tokens["expires_in"]
        expiration_timestamp = datetime.now().timestamp() + expires_in

        credentials = {
            "access_token": tokens["access_token"],
            "refresh_token": tokens.get("refresh_token"),  # May not be present if user has already granted access
            "token_type": tokens["token_type"],
            "expires_in": expires_in,
            "expiration_timestamp": expiration_timestamp,
            "scope": tokens["scope"],
            "google_email": google_email
        }

        if integration:
            # Update existing integration
            integration.credentials = credentials
            integration.active = True  # Make sure to set active to True
            integration.last_synced = func.now()
        else:
            # Create new integration
            integration = ExternalIntegration(
                user_id=user_id,
                integration_type="google_calendar",
                credentials=credentials,
                active=True,
                last_synced=func.now()
            )
            db.add(integration)

        db.commit()

        return {
            "email": user_info.get("email"),
            "connected": True,
            "last_synced": datetime.now().isoformat()
        }
    except HTTPException as e:
        db.rollback()
        raise e
    except Exception as e:
        db.rollback()
        print(f"Exception in google_auth: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to authenticate with Google: {str(e)}")

@router.get("/google-calendar/status")
async def get_google_calendar_status(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Check if Google Calendar is connected"""
    user_id = UUID(current_user["id"])
    print(f"\n\n=== Checking Google Calendar status for user {user_id} ===")

    integration = db.query(ExternalIntegration).filter(
        ExternalIntegration.user_id == user_id,
        ExternalIntegration.integration_type == "google_calendar"
    ).first()

    if not integration:
        print("No Google Calendar integration found")
        return {"connected": False}

    google_email = integration.credentials.get("google_email")
    print(f"Retrieved Google email from database: {google_email}")
    print(f"Integration active status: {integration.active}")

    # Always try to refresh the token
    token_valid = True
    try:
        # Try to ensure we have a valid token
        await ensure_valid_token(integration, db)
        print("Token validation successful")

        # Make sure integration is marked as active
        if not integration.active:
            print("Setting integration to active")
            integration.active = True
            db.commit()
    except Exception as e:
        print(f"Error validating token: {str(e)}")
        token_valid = False

    # Always return connected: True if we have an integration
    # This is to handle the case where the token might be temporarily invalid
    # but we don't want to show the user as disconnected
    return {
        "connected": True,
        "email": google_email,
        "last_synced": integration.last_synced.isoformat() if integration.last_synced else None
    }

@router.delete("/google-calendar/disconnect")
async def disconnect_google_calendar(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Disconnect Google Calendar"""
    user_id = UUID(current_user["id"])

    integration = db.query(ExternalIntegration).filter(
        ExternalIntegration.user_id == user_id,
        ExternalIntegration.integration_type == "google_calendar"
    ).first()

    if not integration:
        raise HTTPException(status_code=404, detail="Google Calendar integration not found")

    # Revoke token if possible
    if integration.credentials.get("access_token"):
        try:
            requests.post(
                "https://oauth2.googleapis.com/revoke",
                params={"token": integration.credentials["access_token"]},
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
        except Exception as e:
            # Log error but continue
            print(f"Error revoking Google token: {str(e)}")

    # Delete the integration
    db.delete(integration)
    db.commit()

    return {"message": "Google Calendar disconnected successfully"}

@router.get("/google-calendar/events")
async def get_google_calendar_events(
    start_date: str,
    end_date: str,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Get events from Google Calendar"""
    user_id = UUID(current_user["id"])

    integration = db.query(ExternalIntegration).filter(
        ExternalIntegration.user_id == user_id,
        ExternalIntegration.integration_type == "google_calendar"
    ).first()

    if not integration or not integration.active:
        raise HTTPException(status_code=404, detail="Google Calendar integration not found or inactive")

    # Refresh token if needed
    access_token = await ensure_valid_token(integration, db)

    # Get events from Google Calendar
    try:
        response = requests.get(
            f"{GOOGLE_CALENDAR_API}/calendars/primary/events",
            params={
                "timeMin": start_date,
                "timeMax": end_date,
                "singleEvents": "true",
                "orderBy": "startTime"
            },
            headers={"Authorization": f"Bearer {access_token}"}
        )

        if response.status_code != 200:
            error_detail = response.text
            try:
                error_json = response.json()
                if "error" in error_json:
                    # Check if this is the API not enabled error
                    if "SERVICE_DISABLED" in error_json.get("error", {}).get("status", "") or \
                       "accessNotConfigured" in error_json.get("error", {}).get("errors", [{}])[0].get("reason", ""):
                        raise HTTPException(
                            status_code=403,
                            detail={
                                "message": "Google Calendar API is not enabled",
                                "activation_url": "https://console.developers.google.com/apis/api/calendar-json.googleapis.com/overview?project=11367222904",
                                "original_error": error_json
                            }
                        )
                    error_detail = error_json
            except ValueError:
                pass

            raise HTTPException(
                status_code=response.status_code,
                detail=f"Google Calendar API error: {error_detail}"
            )

        events = response.json().get("items", [])

        # Update last synced timestamp
        integration.last_synced = func.now()
        db.commit()

        return events
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch Google Calendar events: {str(e)}")

@router.post("/google-calendar/events")
async def create_google_calendar_event(
    event_data: Dict,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Create a new event in Google Calendar"""
    user_id = UUID(current_user["id"])

    integration = db.query(ExternalIntegration).filter(
        ExternalIntegration.user_id == user_id,
        ExternalIntegration.integration_type == "google_calendar"
    ).first()

    if not integration or not integration.active:
        raise HTTPException(status_code=404, detail="Google Calendar integration not found or inactive")

    # Refresh token if needed
    access_token = await ensure_valid_token(integration, db)

    # Create event in Google Calendar
    try:
        response = requests.post(
            f"{GOOGLE_CALENDAR_API}/calendars/primary/events",
            json=event_data,
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        )

        if response.status_code not in (200, 201):
            raise HTTPException(
                status_code=response.status_code,
                detail=f"Google Calendar API error: {response.text}"
            )

        created_event = response.json()

        # Update last synced timestamp
        integration.last_synced = func.now()
        db.commit()

        return created_event
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create Google Calendar event: {str(e)}")

@router.put("/google-calendar/events/{event_id}")
async def update_google_calendar_event(
    event_id: str,
    event_data: Dict,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Update an event in Google Calendar"""
    user_id = UUID(current_user["id"])

    integration = db.query(ExternalIntegration).filter(
        ExternalIntegration.user_id == user_id,
        ExternalIntegration.integration_type == "google_calendar"
    ).first()

    if not integration or not integration.active:
        raise HTTPException(status_code=404, detail="Google Calendar integration not found or inactive")

    # Refresh token if needed
    access_token = await ensure_valid_token(integration, db)

    # Update event in Google Calendar
    try:
        response = requests.put(
            f"{GOOGLE_CALENDAR_API}/calendars/primary/events/{event_id}",
            json=event_data,
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        )

        if response.status_code != 200:
            raise HTTPException(
                status_code=response.status_code,
                detail=f"Google Calendar API error: {response.text}"
            )

        updated_event = response.json()

        # Update last synced timestamp
        integration.last_synced = func.now()
        db.commit()

        return updated_event
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update Google Calendar event: {str(e)}")

@router.delete("/google-calendar/events/{event_id}")
async def delete_google_calendar_event(
    event_id: str,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Delete an event from Google Calendar"""
    user_id = UUID(current_user["id"])

    integration = db.query(ExternalIntegration).filter(
        ExternalIntegration.user_id == user_id,
        ExternalIntegration.integration_type == "google_calendar"
    ).first()

    if not integration or not integration.active:
        raise HTTPException(status_code=404, detail="Google Calendar integration not found or inactive")

    # Refresh token if needed
    access_token = await ensure_valid_token(integration, db)

    # Delete event from Google Calendar
    try:
        response = requests.delete(
            f"{GOOGLE_CALENDAR_API}/calendars/primary/events/{event_id}",
            headers={"Authorization": f"Bearer {access_token}"}
        )

        if response.status_code not in (200, 204):
            raise HTTPException(
                status_code=response.status_code,
                detail=f"Google Calendar API error: {response.text}"
            )

        # Update last synced timestamp
        integration.last_synced = func.now()
        db.commit()

        return {"message": "Event deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete Google Calendar event: {str(e)}")

@router.post("/google-calendar/sync")
async def sync_appointments_with_google_calendar(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Sync appointments between the app and Google Calendar"""
    user_id = UUID(current_user["id"])

    integration = db.query(ExternalIntegration).filter(
        ExternalIntegration.user_id == user_id,
        ExternalIntegration.integration_type == "google_calendar"
    ).first()

    if not integration or not integration.active:
        raise HTTPException(status_code=404, detail="Google Calendar integration not found or inactive")

    # Refresh token if needed
    access_token = await ensure_valid_token(integration, db)

    # Use the appointment service to sync appointments
    from app.services.appointment_service import AppointmentService
    appointment_service = AppointmentService(db)

    try:
        result = await appointment_service.sync_with_google_calendar(user_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error syncing appointments: {str(e)}")

@router.post("/google-calendar/refresh-token")
async def refresh_google_token(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Manually refresh the Google Calendar token"""
    user_id = UUID(current_user["id"])
    print(f"\n\n=== Refreshing Google token for user {user_id} ===")

    integration = db.query(ExternalIntegration).filter(
        ExternalIntegration.user_id == user_id,
        ExternalIntegration.integration_type == "google_calendar"
    ).first()

    if not integration:
        print(f"No Google Calendar integration found for user {user_id}")
        raise HTTPException(status_code=404, detail="Google Calendar integration not found")

    print(f"Found integration with active={integration.active}")
    print(f"Integration credentials: {integration.credentials}")

    # Always set active to True when refreshing
    integration.active = True
    db.commit()

    try:
        # Force token refresh
        access_token = await ensure_valid_token(integration, db)
        print(f"Token refresh successful, got access token: {access_token[:10]}...")

        return {
            "success": True,
            "message": "Token refreshed successfully",
            "email": integration.credentials.get("google_email"),
            "connected": True,
            "last_synced": datetime.now().isoformat()
        }
    except Exception as e:
        print(f"Error refreshing token: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to refresh token: {str(e)}")

async def ensure_valid_token(integration: ExternalIntegration, db: Session) -> str:
    """Ensure the access token is valid, refreshing if necessary"""
    credentials = integration.credentials
    print(f"Ensuring valid token for integration with credentials: {credentials}")

    # Check if we have a refresh token
    if "refresh_token" not in credentials:
        print("No refresh token found, can't refresh")
        # No refresh token, can't refresh
        return credentials["access_token"]

    # Check if token is expired
    token_expired = False

    # If we have an expiration timestamp, check if token is expired
    if "expiration_timestamp" in credentials:
        current_time = datetime.now().timestamp()
        if current_time >= credentials["expiration_timestamp"]:
            print(f"Token expired at {credentials['expiration_timestamp']}, current time is {current_time}")
            token_expired = True
        else:
            print(f"Token still valid, expires at {credentials['expiration_timestamp']}, current time is {current_time}")
    else:
        # If we don't have an expiration timestamp, assume token is expired
        # This will force a refresh the first time
        print("No expiration timestamp found, forcing token refresh")
        token_expired = True

    # Refresh token if expired
    if token_expired:
        try:
            print("Refreshing token...")
            # Refresh the token
            response = requests.post(
                GOOGLE_TOKEN_URL,
                data={
                    "client_id": "11367222904-t6fop0q2gdrueu894nggise0qslcr9h9.apps.googleusercontent.com",
                    "client_secret": "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",
                    "refresh_token": credentials["refresh_token"],
                    "grant_type": "refresh_token"
                }
            )

            print(f"Refresh response status: {response.status_code}")
            print(f"Refresh response text: {response.text}")

            new_tokens = response.json()

            if "error" in new_tokens:
                print(f"Error in refresh response: {new_tokens['error']}")
                raise Exception(f"Error refreshing token: {new_tokens['error']}")

            # Calculate new expiration timestamp
            expires_in = new_tokens["expires_in"]
            expiration_timestamp = datetime.now().timestamp() + expires_in

            # Update credentials
            credentials["access_token"] = new_tokens["access_token"]
            credentials["token_type"] = new_tokens["token_type"]
            credentials["expires_in"] = expires_in
            credentials["expiration_timestamp"] = expiration_timestamp

            integration.credentials = credentials
            integration.active = True  # Make sure to set active to True
            db.commit()
            print("Token refreshed and saved successfully")
        except Exception as e:
            print(f"Error refreshing token: {str(e)}")
            # Don't mark as inactive here, as it might be a temporary issue
            # integration.active = False
            # db.commit()
            raise HTTPException(status_code=401, detail=f"Failed to refresh Google token: {str(e)}")
    else:
        print("Token is still valid, no refresh needed")

    return credentials["access_token"]
