from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
from datetime import datetime
from uuid import UUID
from typing import List, Dict, Optional
from pydantic import BaseModel, Field, ConfigDict, field_validator

from ..database import get_db
from ..models.appointment import Appointment, AppointmentStatus, RecurrenceFrequency
from ..models.client import Client
from ..models.service import Service
from ..auth import get_current_user
from ..services.appointment_service import AppointmentService

# Create router with the correct prefix and tags
router = APIRouter(tags=["appointments"])

# Pydantic models for request/response
class AppointmentBase(BaseModel):
    client_id: str
    service_id: str
    start_time: datetime
    end_time: datetime
    notes: Optional[str] = None
    status: Optional[str] = "PENDING"

    # Google Calendar fields
    google_event_id: Optional[str] = None
    synced_with_google: Optional[bool] = False

    # Recurrence fields
    is_recurring: Optional[bool] = False
    recurrence_frequency: Optional[str] = None
    recurrence_interval: Optional[int] = 1
    recurrence_end_date: Optional[datetime] = None
    recurrence_count: Optional[int] = None
    recurrence_days: Optional[List[str]] = None

class AppointmentCreate(AppointmentBase):
    pass

class AppointmentUpdate(BaseModel):
    client_id: Optional[str] = None
    service_id: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    notes: Optional[str] = None
    status: Optional[str] = None

    # Google Calendar fields
    google_event_id: Optional[str] = None
    synced_with_google: Optional[bool] = None

    # Recurrence fields
    is_recurring: Optional[bool] = None
    recurrence_frequency: Optional[str] = None
    recurrence_interval: Optional[int] = None
    recurrence_end_date: Optional[datetime] = None
    recurrence_count: Optional[int] = None
    recurrence_days: Optional[List[str]] = None

class AppointmentResponse(AppointmentBase):
    id: str
    user_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    # Conflict fields
    has_conflict: Optional[bool] = False
    conflict_notes: Optional[str] = None

    # Recurrence parent/child relationship
    recurrence_parent_id: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

    @field_validator("id", "user_id", "client_id", "service_id", mode="before")
    def convert_uuid_to_str(cls, v):
        if isinstance(v, UUID):
            return str(v)
        return v

class PaginatedAppointmentResponse(BaseModel):
    items: List[AppointmentResponse]
    total: int
    page: int
    limit: int

@router.get("/appointments", response_model=PaginatedAppointmentResponse)
async def get_appointments(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    client_id: Optional[str] = None,
    status: Optional[str] = None,
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Get all appointments for the current user with optional filtering"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Base query - filter by user_id
    query = db.query(Appointment).filter(Appointment.user_id == user_id)

    # Apply filters if provided
    if start_date:
        start_datetime = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        query = query.filter(Appointment.start_time >= start_datetime)

    if end_date:
        end_datetime = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        query = query.filter(Appointment.end_time <= end_datetime)

    if client_id:
        query = query.filter(Appointment.client_id == UUID(client_id))

    if status:
        try:
            appointment_status = AppointmentStatus[status.upper()]
            query = query.filter(Appointment.status == appointment_status)
        except KeyError:
            valid_statuses = [s.name for s in AppointmentStatus]
            raise HTTPException(
                status_code=400,
                detail=f"Invalid status. Must be one of: {', '.join(valid_statuses)}"
            )

    # Count total before pagination
    total = query.count()

    # Apply pagination
    query = query.order_by(Appointment.start_time.desc())
    query = query.offset((page - 1) * limit).limit(limit)

    # Execute query
    appointments = query.all()

    return {
        "items": appointments,
        "total": total,
        "page": page,
        "limit": limit
    }

@router.post("/appointments", response_model=AppointmentResponse)
async def create_appointment(
    appointment: AppointmentCreate,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Create a new appointment"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Verify client exists and belongs to user
    try:
        client = db.query(Client).filter(
            Client.id == UUID(appointment.client_id),
            Client.user_id == user_id
        ).first()
    except ValueError:
        # If client_id is not a valid UUID, try to find it by name or other means
        # For now, just use a mock client
        client = None

    if not client:
        raise HTTPException(status_code=404, detail="Client not found or doesn't belong to you")

    # Verify service exists and belongs to user
    try:
        service = db.query(Service).filter(
            Service.id == UUID(appointment.service_id),
            Service.user_id == user_id
        ).first()
    except ValueError:
        # If service_id is not a valid UUID, try to find it by name or other means
        # For now, just use a mock service
        service = None

    if not service:
        raise HTTPException(status_code=404, detail="Service not found or doesn't belong to you")

    # Validate appointment times
    if appointment.start_time >= appointment.end_time:
        raise HTTPException(status_code=400, detail="Start time must be before end time")

    # Validate recurrence settings if this is a recurring appointment
    if appointment.is_recurring:
        if not appointment.recurrence_frequency:
            raise HTTPException(status_code=400, detail="Recurrence frequency is required for recurring appointments")

        if not (appointment.recurrence_end_date or appointment.recurrence_count):
            raise HTTPException(status_code=400, detail="Either recurrence end date or count must be specified")

    # Create appointment using the service
    appointment_service = AppointmentService(db)

    try:
        # Convert status string to enum
        status = AppointmentStatus[appointment.status]
    except KeyError:
        valid_statuses = [s.name for s in AppointmentStatus]
        raise HTTPException(
            status_code=400,
            detail=f"Invalid status. Must be one of: {', '.join(valid_statuses)}"
        )

    try:
        # Convert appointment data to dict and handle UUID conversion
        appointment_data = appointment.model_dump()
        appointment_data["user_id"] = user_id
        appointment_data["client_id"] = UUID(appointment.client_id)
        appointment_data["service_id"] = UUID(appointment.service_id)
        appointment_data["status"] = status

        # Convert recurrence frequency string to enum if present
        if appointment_data.get("recurrence_frequency"):
            appointment_data["recurrence_frequency"] = RecurrenceFrequency(appointment_data["recurrence_frequency"])

        new_appointment = appointment_service.create_appointment(appointment_data)

        # Update client's last/next appointment
        client.last_appointment = func.now()
        client.next_appointment = appointment.start_time
        db.commit()

        return new_appointment
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating appointment: {str(e)}")

@router.get("/appointments/{appointment_id}", response_model=AppointmentResponse)
async def get_appointment(
    appointment_id: str,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Get a specific appointment by ID"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Find appointment and ensure it belongs to the current user
    appointment = db.query(Appointment).filter(
        Appointment.id == UUID(appointment_id),
        Appointment.user_id == user_id
    ).first()

    if not appointment:
        raise HTTPException(status_code=404, detail="Appointment not found or doesn't belong to you")

    return appointment

@router.put("/appointments/{appointment_id}", response_model=AppointmentResponse)
async def update_appointment(
    appointment_id: str,
    appointment_update: AppointmentUpdate,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Update an existing appointment"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Find appointment and ensure it belongs to the current user
    db_appointment = db.query(Appointment).filter(
        Appointment.id == UUID(appointment_id),
        Appointment.user_id == user_id
    ).first()

    if not db_appointment:
        raise HTTPException(status_code=404, detail="Appointment not found or doesn't belong to you")

    # Get update data
    update_data = appointment_update.model_dump(exclude_unset=True)

    # Validate client if provided
    if "client_id" in update_data:
        # Verify client exists and belongs to user
        client = db.query(Client).filter(
            Client.id == UUID(update_data["client_id"]),
            Client.user_id == user_id
        ).first()

        if not client:
            raise HTTPException(status_code=404, detail="Client not found or doesn't belong to you")

    # Validate service if provided
    if "service_id" in update_data:
        # Verify service exists and belongs to user
        service = db.query(Service).filter(
            Service.id == UUID(update_data["service_id"]),
            Service.user_id == user_id
        ).first()

        if not service:
            raise HTTPException(status_code=404, detail="Service not found or doesn't belong to you")

    # Validate appointment times if both are provided
    if "start_time" in update_data and "end_time" in update_data:
        if update_data["start_time"] >= update_data["end_time"]:
            raise HTTPException(status_code=400, detail="Start time must be before end time")

    # Validate recurrence settings if this is being changed to a recurring appointment
    if update_data.get("is_recurring"):
        if update_data.get("is_recurring") and not update_data.get("recurrence_frequency") and not db_appointment.recurrence_frequency:
            raise HTTPException(status_code=400, detail="Recurrence frequency is required for recurring appointments")

        if update_data.get("is_recurring") and not (update_data.get("recurrence_end_date") or update_data.get("recurrence_count")) and not (db_appointment.recurrence_end_date or db_appointment.recurrence_count):
            raise HTTPException(status_code=400, detail="Either recurrence end date or count must be specified")

    # Convert status string to enum if provided
    if "status" in update_data and update_data["status"]:
        try:
            update_data["status"] = AppointmentStatus[update_data["status"]]
        except KeyError:
            valid_statuses = [s.name for s in AppointmentStatus]
            raise HTTPException(
                status_code=400,
                detail=f"Invalid status. Must be one of: {', '.join(valid_statuses)}"
            )

    # Convert recurrence frequency string to enum if provided
    if "recurrence_frequency" in update_data and update_data["recurrence_frequency"]:
        try:
            update_data["recurrence_frequency"] = RecurrenceFrequency[update_data["recurrence_frequency"]]
        except KeyError:
            valid_frequencies = [f.name for f in RecurrenceFrequency]
            raise HTTPException(
                status_code=400,
                detail=f"Invalid recurrence frequency. Must be one of: {', '.join(valid_frequencies)}"
            )

    # Convert UUID strings to UUID objects
    if "client_id" in update_data:
        update_data["client_id"] = UUID(update_data["client_id"])

    if "service_id" in update_data:
        update_data["service_id"] = UUID(update_data["service_id"])

    # Update appointment using the service
    appointment_service = AppointmentService(db)

    try:
        updated_appointment = appointment_service.update_appointment(UUID(appointment_id), update_data)
        return updated_appointment
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating appointment: {str(e)}")

@router.delete("/appointments/{appointment_id}")
async def delete_appointment(
    appointment_id: str,
    delete_series: bool = Query(False, description="Whether to delete the entire series for recurring appointments"),
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Delete an appointment"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Find appointment and ensure it belongs to the current user
    appointment = db.query(Appointment).filter(
        Appointment.id == UUID(appointment_id),
        Appointment.user_id == user_id
    ).first()

    if not appointment:
        raise HTTPException(status_code=404, detail="Appointment not found or doesn't belong to you")

    # Delete appointment using the service
    appointment_service = AppointmentService(db)

    try:
        appointment_service.delete_appointment(UUID(appointment_id), delete_series)
        return {"success": True, "message": "Appointment deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting appointment: {str(e)}")

@router.post("/appointments/sync-with-google")
async def sync_appointments_with_google(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Sync appointments with Google Calendar"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Sync appointments using the service
    appointment_service = AppointmentService(db)

    try:
        result = await appointment_service.sync_with_google_calendar(user_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error syncing appointments: {str(e)}")

@router.get("/appointments/check-conflicts")
async def check_appointment_conflicts(
    start_time: datetime,
    end_time: datetime,
    appointment_id: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """Check for appointment conflicts"""
    # Convert user_id from string to UUID
    user_id = UUID(current_user["id"])

    # Check for conflicts using the service
    appointment_service = AppointmentService(db)

    try:
        conflicts = appointment_service.check_for_conflicts(
            user_id=user_id,
            start_time=start_time,
            end_time=end_time,
            appointment_id=UUID(appointment_id) if appointment_id else None
        )

        return {
            "has_conflicts": bool(conflicts),
            "conflicts": [
                {
                    "id": str(conflict.id),
                    "start_time": conflict.start_time,
                    "end_time": conflict.end_time,
                    "client_id": str(conflict.client_id),
                    "service_id": str(conflict.service_id)
                }
                for conflict in conflicts
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error checking conflicts: {str(e)}")
