"""
Test script for OpenAI API integration
"""

import os
import json
import logging
from openai import OpenAI
from app.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize OpenAI client
client = OpenAI(api_key=settings.OPENAI_API_KEY)

def test_chat_completion():
    """Test chat completion API"""
    logger.info("Testing chat completion API...")
    
    try:
        completion = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "user", "content": "Write a haiku about AI"}
            ]
        )
        
        logger.info("\nResponse:")
        logger.info(completion.choices[0].message.content)
        logger.info("\nUsage:")
        logger.info("Prompt tokens: %s", completion.usage.prompt_tokens)
        logger.info("Completion tokens: %s", completion.usage.completion_tokens)
        logger.info("Total tokens: %s", completion.usage.total_tokens)
        
        logger.info("\nChat completion test: SUCCESS")
        return True
    except Exception as e:
        logger.error("\nChat completion test: FAILED")
        logger.error("Error: %s", str(e))
        return False

def test_embeddings():
    """Test embeddings API"""
    logger.info("\nTesting embeddings API...")
    
    try:
        response = client.embeddings.create(
            model="text-embedding-3-small",
            input="Hello, world!"
        )
        
        embedding = response.data[0].embedding
        
        logger.info("\nEmbedding dimension: %s", len(embedding))
        logger.info("First 5 values: %s", embedding[:5])
        
        logger.info("\nEmbeddings test: SUCCESS")
        return True
    except Exception as e:
        logger.error("\nEmbeddings test: FAILED")
        logger.error("Error: %s", str(e))
        return False

if __name__ == "__main__":
    logger.info("Using OpenAI API key: %s...%s", settings.OPENAI_API_KEY[:5], settings.OPENAI_API_KEY[-4:])
    
    chat_success = test_chat_completion()
    embeddings_success = test_embeddings()
    
    if chat_success and embeddings_success:
        logger.info("\nAll tests passed successfully!")
    else:
        logger.error("\nSome tests failed. Please check the error messages above.")
