from sqlalchemy import Column, String, Boolean, DateTime, UUID, ForeignKey, Text, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from uuid import uuid4
from app.database import Base

class User(Base):
    __tablename__ = "users"

    id = Column(UUID, primary_key=True, default=uuid4)
    email = Column(String, unique=True, index=True, nullable=False)
    password_hash = Column(String, nullable=False)
    business_name = Column(String)
    currency = Column(String, default="EUR")
    address = Column(String, nullable=True)
    phone = Column(String, nullable=True)
    website = Column(String, nullable=True)
    logo_url = Column(Text, nullable=True)
    registered_at = Column(DateTime(timezone=True), server_default=func.now())
    email_verified = Column(Boolean, default=False)
    # settings column removed - data moved to dedicated columns

    # Billing fields
    plan_id = Column(UUID, ForeignKey("pricing_plans.id"), nullable=True)
    stripe_customer_id = Column(String, nullable=True)  # Stripe customer ID

    # Two-Factor Authentication fields
    twofa_secret = Column(String, nullable=True)  # TOTP secret key
    twofa_enabled = Column(<PERSON>olean, default=False)  # Whether 2FA is enabled
    backup_codes = Column(JSON, default=list)  # Backup codes for account recovery

    # Relationships
    google_calendar_cache = relationship("GoogleCalendarCache", back_populates="user")

class UserAuth(Base):
    __tablename__ = "user_auth"

    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, nullable=False)
    refresh_token = Column(String, nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    device_info = Column(String)
    last_login = Column(DateTime(timezone=True), server_default=func.now())