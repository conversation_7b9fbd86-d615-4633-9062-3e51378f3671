from sqlalchemy import Column, String, Float, Date, ForeignKey, UUID
from uuid import uuid4
from app.database import Base

class Invoice(Base):
    __tablename__ = "invoices"

    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey("users.id"), nullable=False)
    plan_id = Column(UUID, ForeignKey("pricing_plans.id"), nullable=False)
    period_start = Column(Date, nullable=False)
    period_end = Column(Date, nullable=False)
    total = Column(Float, nullable=False)
    stripe_invoice_id = Column(String)
    status = Column(String, nullable=False)  # paid, pending, failed
    # Stripe invoice URLs
    invoice_pdf_url = Column(String, nullable=True)
    hosted_invoice_url = Column(String, nullable=True)