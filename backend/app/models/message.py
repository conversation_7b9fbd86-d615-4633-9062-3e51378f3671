from sqlalchemy import Column, Text, Boolean, JSON, DateTime, ForeignKey, UUID, Enum
from sqlalchemy.sql import func
from uuid import uuid4
from enum import Enum as PyEnum
from app.database import Base
from pgvector.sqlalchemy import Vector

class MessageDirection(PyEnum):
    INCOMING = "incoming"  # From client to business
    OUTGOING = "outgoing"  # From business to client

class Message(Base):
    __tablename__ = "messages"

    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey("users.id"), nullable=False)
    client_id = Column(UUID, ForeignKey("clients.id"), nullable=False)
    parent_id = Column(UUID, ForeignKey("messages.id"))
    content = Column(Text, nullable=False)
    is_from_business = Column(Boolean, default=True)
    sent_at = Column(DateTime(timezone=True), server_default=func.now())
    embedding = Column(Vector(384))
    intent_data = Column(JSON)
    source = Column(Text, default="app")  # Can be 'app', 'whatsapp', or 'ai'