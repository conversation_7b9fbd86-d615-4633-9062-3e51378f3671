from sqlalchemy import Column, String, DateTime, ForeignKey, UUID, Enum, Boolean, Integer, JSON
from sqlalchemy.sql import func
import enum
from uuid import uuid4
from app.database import Base

class AppointmentStatus(enum.Enum):
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    PENDING = "pending"
    RESCHEDULED = "rescheduled"

class RecurrenceFrequency(enum.Enum):
    DAILY = "daily"
    WEEKLY = "weekly"
    BIWEEKLY = "biweekly"
    MONTHLY = "monthly"
    YEARLY = "yearly"

class Appointment(Base):
    __tablename__ = "appointments"

    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey("users.id"), nullable=False)
    client_id = Column(UUID, ForeignKey("clients.id"), nullable=False)
    service_id = Column(UUID, ForeignKey("services.id"), nullable=False)
    start_time = Column(DateTime(timezone=True), nullable=False)
    end_time = Column(DateTime(timezone=True), nullable=False)
    status = Column(Enum(AppointmentStatus), default=AppointmentStatus.PENDING)
    notes = Column(String)

    # Google Calendar integration
    google_event_id = Column(String, nullable=True)
    synced_with_google = Column(Boolean, default=False)
    last_synced = Column(DateTime(timezone=True), nullable=True)

    # Recurrence fields
    is_recurring = Column(Boolean, default=False)
    recurrence_frequency = Column(Enum(RecurrenceFrequency), nullable=True)
    recurrence_interval = Column(Integer, default=1)  # e.g., every 2 weeks
    recurrence_end_date = Column(DateTime(timezone=True), nullable=True)
    recurrence_count = Column(Integer, nullable=True)  # Number of occurrences
    recurrence_days = Column(JSON, nullable=True)  # For weekly: ["MO", "WE", "FR"]
    recurrence_parent_id = Column(UUID, ForeignKey("appointments.id"), nullable=True)  # For instances of recurring appointments

    # Conflict resolution
    has_conflict = Column(Boolean, default=False)
    conflict_notes = Column(String, nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())