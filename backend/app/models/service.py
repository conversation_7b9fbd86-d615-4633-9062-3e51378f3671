from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Integer, Float, Foreign<PERSON>ey, UUID
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from uuid import uuid4
from app.database import Base

class ServiceType(Base):
    __tablename__ = "service_types"

    id = Column(UUID, primary_key=True, default=uuid4)
    name = Column(String, nullable=False)

class Service(Base):
    __tablename__ = "services"

    id = Column(UUID, primary_key=True, default=uuid4)
    type_id = Column(UUID, ForeignKey("service_types.id"))
    user_id = Column(UUID, ForeignKey("users.id"))
    name = Column(String, nullable=False)
    description = Column(String)
    price = Column(Float, nullable=False)
    duration_minutes = Column(Integer, nullable=False)
    active = Column(Boolean, default=True)
    custom_attributes = Column(JSONB, nullable=True)

    # Helper methods for custom attributes
    def set_custom_attribute(self, key, value):
        """Set a custom attribute"""
        if self.custom_attributes is None:
            self.custom_attributes = {}
        self.custom_attributes[key] = value

    def get_custom_attribute(self, key, default=None):
        """Get a custom attribute with optional default value"""
        if self.custom_attributes is None:
            return default
        return self.custom_attributes.get(key, default)