from sqlalchemy import Column, String, Float, JSON, UUID
from uuid import uuid4
from app.database import Base

class PricingPlan(Base):
    __tablename__ = "pricing_plans"

    id = Column(UUID, primary_key=True, default=uuid4)
    name = Column(String, nullable=False)
    cost_per_message = Column(Float, default=0.0)
    cost_per_appointment = Column(Float, default=0.0)
    monthly_fee = Column(Float, nullable=False)
    features = Column(JSON, default=dict)
    stripe_price_id = Column(String, nullable=True)
    stripe_product_id = Column(String, nullable=True)
    stripe_annual_price_id = Column(String, nullable=True)