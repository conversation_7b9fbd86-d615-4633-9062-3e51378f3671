from sqlalchemy import <PERSON>um<PERSON>, JSON, DateTime, Foreign<PERSON>ey, UUID, String, Integer, <PERSON>olean, Text
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import JSONB
from uuid import uuid4
from app.database import Base

class AIConversationContext(Base):
    __tablename__ = "ai_conversation_contexts"

    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey("users.id"), nullable=False)
    client_id = Column(UUID, ForeignKey("clients.id"), nullable=False)
    context_data = Column(JSONB, nullable=False, default={})

    # Conversation metadata
    conversation_language = Column(String(10), default="en")
    message_count = Column(Integer, default=0)
    last_intent = Column(String(50), nullable=True)

    # Booking context
    has_booking_intent = Column(Boolean, default=False)
    booking_stage = Column(String(50), nullable=True)
    booking_data = Column(JSONB, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_message_at = Column(DateTime(timezone=True), nullable=True)

class ConversationMessage(Base):
    __tablename__ = "conversation_messages"

    id = Column(UUID, primary_key=True, default=uuid4)
    conversation_id = Column(UUID, ForeignKey("ai_conversation_contexts.id"), nullable=False)
    user_id = Column(UUID, ForeignKey("users.id"), nullable=True)
    client_id = Column(UUID, ForeignKey("clients.id"), nullable=True)

    # Message content
    content = Column(Text, nullable=False)
    role = Column(String(20), nullable=False)  # 'user', 'assistant', 'system'

    # Message metadata
    detected_language = Column(String(10), nullable=True)
    detected_intent = Column(String(50), nullable=True)
    message_metadata = Column(JSONB, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())