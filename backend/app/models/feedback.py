from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, UUID, Boolean, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from uuid import uuid4
from app.database import Base

class FeedbackRating(enum.Enum):
    ONE_STAR = 1
    TWO_STARS = 2
    THREE_STARS = 3
    FOUR_STARS = 4
    FIVE_STARS = 5

class FeedbackStatus(enum.Enum):
    PENDING = "pending"
    SUBMITTED = "submitted"
    RESPONDED = "responded"

class Feedback(Base):
    __tablename__ = "feedback"

    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey("users.id"), nullable=False)
    client_id = Column(UUID, ForeignKey("clients.id"), nullable=False)
    appointment_id = Column(UUID, ForeignKey("appointments.id"), nullable=False)

    # Feedback content
    rating = Column(Enum(FeedbackRating), nullable=False)
    comment = Column(Text, nullable=True)
    service_rating = Column(Enum(FeedbackRating), nullable=True)  # Optional service-specific rating

    # Status and metadata
    status = Column(Enum(FeedbackStatus), default=FeedbackStatus.SUBMITTED)
    is_anonymous = Column(Boolean, default=False)
    is_public = Column(Boolean, default=True)  # Whether to show in public reviews

    # Response from business
    business_response = Column(Text, nullable=True)
    responded_at = Column(DateTime(timezone=True), nullable=True)

    # Request tracking
    request_sent_at = Column(DateTime(timezone=True), nullable=True)
    submitted_at = Column(DateTime(timezone=True), server_default=func.now())

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class FeedbackSettings(Base):
    __tablename__ = "feedback_settings"

    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey("users.id"), nullable=False, unique=True)

    # Main settings
    enabled = Column(Boolean, default=True)
    auto_request_after_appointment = Column(Boolean, default=True)
    request_delay_hours = Column(Integer, default=2)  # Hours after appointment to send request

    # Feedback requirements
    allow_anonymous_feedback = Column(Boolean, default=True)
    require_rating = Column(Boolean, default=True)
    require_comment = Column(Boolean, default=False)

    # Public display settings
    show_public_reviews = Column(Boolean, default=True)
    minimum_rating_for_public = Column(Integer, default=3)  # Only show ratings >= this value publicly

    # WhatsApp message template
    request_template = Column(Text, default="Hi {{clientName}}! We hope you enjoyed your {{serviceName}} appointment. We'd love to hear your feedback! Please rate your experience: {{feedbackLink}}")

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
