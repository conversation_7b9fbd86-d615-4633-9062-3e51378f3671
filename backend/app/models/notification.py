from sqlalchemy import <PERSON>umn, String, <PERSON>olean, Integer, ForeignKey, UUID, JSON
from uuid import uuid4
from app.database import Base

class NotificationPreference(Base):
    __tablename__ = "notification_preferences"

    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey("users.id"), nullable=False)
    event_type = Column(String, nullable=False)
    email_enabled = Column(Boolean, default=True)
    sms_enabled = Column(Boolean, default=False)
    push_enabled = Column(Boolean, default=False)
    reminder_minutes = Column(Integer, default=60)
    settings = Column(JSON, default=dict)