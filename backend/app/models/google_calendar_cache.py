from sqlalchemy import Column, String, DateTime, JSON, Foreign<PERSON>ey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from app.database import Base


class GoogleCalendarCache(Base):
    """
    Model for caching Google Calendar events to avoid frequent API calls
    """
    __tablename__ = "google_calendar_cache"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    event_id = Column(String, nullable=False, index=True)
    event_data = Column(JSON, nullable=False)
    start_time = Column(DateTime, nullable=False, index=True)
    end_time = Column(DateTime, nullable=False, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="google_calendar_cache")

    def __repr__(self):
        return f"<GoogleCalendarCache(id={self.id}, event_id={self.event_id})>"
