from sqlalchemy import Column, String, J<PERSON><PERSON>, DateTime, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUI<PERSON> as PSQL_UUID
from sqlalchemy.sql import func
from uuid import uuid4, UUID as PythonUUID
from app.database import Base
from pydantic import BaseModel, Field, ConfigDict, field_validator
from datetime import datetime
from typing import Optional, List, Dict, Any, Union, Generic, TypeVar
from uuid import UUID

class Client(Base):
    __tablename__ = "clients"

    id = Column(PSQL_UUID, primary_key=True, default=uuid4)
    user_id = Column(PSQL_UUID, ForeignKey("users.id"), nullable=False)  # Non-nullable for data integrity
    phone = Column(String)
    name = Column(String)
    email = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_appointment = Column(DateTime, nullable=True)
    next_appointment = Column(DateTime, nullable=True)
    tags = Column(JSON, default=[])
    notes = Column(String, default="")
    profile_image = Column(Text, nullable=True)
    stripe_customer_id = Column(String, nullable=True)

class ClientSchema(BaseModel):
    id: UUID
    user_id: UUID  # Required field - each client must belong to a user
    name: str
    email: str
    phone: str
    tags: List[str] = []
    notes: str = ""
    last_appointment: Optional[datetime] = None
    next_appointment: Optional[datetime] = None
    created_at: Optional[Union[datetime, str]] = None  # Accept both datetime and string, optional for updates
    profile_image: Optional[str] = None
    stripe_customer_id: Optional[str] = None

    @field_validator("id", "user_id", mode="before")
    def convert_uuid_to_str(cls, v):
        if isinstance(v, PythonUUID):
            return str(v)
        return v

    @field_validator("name", "email", "phone", "notes", mode="before")
    def convert_none_to_empty_string(cls, v):
        if v is None:
            return ""
        return v

    @field_validator("last_appointment", "next_appointment", mode="before")
    def format_dates(cls, v):
        if v:
            return v.isoformat()  # Convert datetime to ISO string
        return None  # Return None if the date is null

    model_config = ConfigDict(from_attributes=True)


class PaginatedClientResponse(BaseModel):
    """Paginated response model for clients"""
    items: List[ClientSchema]
    total: int
    page: int
    page_size: int
    total_pages: int