from sqlalchemy import Column, String, Text, DateTime, ForeignKey, UUID
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func
from uuid import uuid4
from app.database import Base

class AIConfiguration(Base):
    __tablename__ = "ai_configurations"

    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey("users.id"), nullable=False)
    system_prompt = Column(Text, nullable=False)
    tone = Column(String(50), default="professional")
    primary_language = Column(String(10), default="en")
    supported_languages = Column(JSONB, default=["en"])
    business_rules = Column(JSONB)
    response_templates = Column(JSONB)
    whatsapp_settings = Column(JSONB, default={
        # When enabled, AI will automatically respond to WhatsApp messages even when the user is logged out
        "enableAutoResponses": True,
        # Delay in milliseconds before sending the response (to make it seem more natural)
        "responseDelay": 2000,
        # Maximum length of AI responses
        "maxResponseLength": 500,
        # Whether to log detailed information about AI responses
        "enableDetailedLogging": True
    })
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class KnowledgeDocument(Base):
    __tablename__ = "knowledge_documents"

    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey("users.id"), nullable=False)
    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    embedding = Column(Text)  # Will be converted to pgvector in code
    document_metadata = Column(JSONB)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
