from sqlalchemy import Column, String, Boolean, JSON, DateTime, ForeignKey, UUID
from sqlalchemy.sql import func
from uuid import uuid4
from app.database import Base

class ExternalIntegration(Base):
    __tablename__ = "external_integrations"
    
    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey("users.id"), nullable=False)
    integration_type = Column(String, nullable=False)  # google_calendar, whatsapp, gmail
    credentials = Column(JSON, nullable=False)
    active = Column(Boolean, default=True)
    last_synced = Column(DateTime(timezone=True))