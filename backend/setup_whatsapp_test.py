"""
Setup WhatsApp test for real-world testing
This script will:
1. Create a test client if needed
2. Set up the Evolution API instance
3. Provide instructions for testing
"""

import sys
import os
import json
import requests
from uuid import UUID, uuid4
from datetime import datetime

# Add the parent directory to the path so we can import the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models.client import Client
from app.models.user import User
from app.models.service import Service
from app.config import settings

def setup_whatsapp_test():
    """Set up WhatsApp test for real-world testing"""
    print("\n=== Setting up WhatsApp Test ===\n")

    # Create a database session
    db = SessionLocal()

    try:
        # User ID for the test user
        user_id = "b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7"  # <EMAIL>
        user_uuid = UUID(user_id)

        # Get the user
        user = db.query(User).filter(User.id == user_uuid).first()
        if not user:
            print(f"User not found: {user_id}")
            return

        print(f"Setting up test for user: {user.email}")

        # Check if Evolution API is configured
        if not settings.EVOLUTION_API_URL or not settings.EVOLUTION_API_KEY:
            print("Error: Evolution API not configured. Please set EVOLUTION_API_URL and EVOLUTION_API_KEY in .env")
            return

        # Evolution API headers
        headers = {
            "Content-Type": "application/json",
            "apikey": settings.EVOLUTION_API_KEY
        }

        # Check if instance exists
        instance_name = user_id
        instance_check_url = f"{settings.EVOLUTION_API_URL}/instance/connectionState/{instance_name}"

        try:
            response = requests.get(instance_check_url, headers=headers)
            instance_exists = response.status_code == 200

            if instance_exists:
                state = response.json().get("state", "")
                print(f"WhatsApp instance exists with state: {state}")

                if state != "open":
                    print("Instance is not connected. Let's reconnect...")

                    # Delete the instance if it's in a bad state
                    if state in ["connecting", "close"]:
                        delete_url = f"{settings.EVOLUTION_API_URL}/instance/delete/{instance_name}"
                        delete_response = requests.delete(delete_url, headers=headers)
                        print(f"Deleted existing instance: {delete_response.status_code}")
                        instance_exists = False
            else:
                print("WhatsApp instance does not exist. Creating new instance...")
        except Exception as e:
            print(f"Error checking instance: {str(e)}")
            instance_exists = False

        # Create instance if it doesn't exist
        if not instance_exists:
            create_url = f"{settings.EVOLUTION_API_URL}/instance/create"
            create_data = {
                "instanceName": instance_name,
                "token": user_id,
                "qrcode": True,
                "webhook": f"{settings.API_URL}/whatsapp/webhook/{user_id}",
                "webhookUrl": f"{settings.API_URL}/whatsapp/webhook/{user_id}",
                "webhookEnabled": True
            }

            try:
                response = requests.post(create_url, headers=headers, json=create_data)
                if response.status_code == 201:
                    print("WhatsApp instance created successfully")
                else:
                    print(f"Error creating WhatsApp instance: {response.status_code} - {response.text}")
                    return
            except Exception as e:
                print(f"Error creating WhatsApp instance: {str(e)}")
                return

        # Check connection state again
        try:
            response = requests.get(instance_check_url, headers=headers)
            if response.status_code == 200:
                state = response.json().get("state", "")
                if state == "open":
                    print("\nWhatsApp is already connected! No QR code needed.")
                    print("Your WhatsApp integration is ready for testing.")
                else:
                    # Try to get QR code
                    qr_url = f"{settings.EVOLUTION_API_URL}/instance/qrcode/{instance_name}"
                    try:
                        response = requests.get(qr_url, headers=headers)
                        if response.status_code == 200:
                            qr_data = response.json()
                            if "qrcode" in qr_data:
                                print("\nScan this QR code with your WhatsApp to connect:")
                                print(f"QR Code URL: {qr_data.get('qrcode')}")
                            else:
                                print("\nNo QR code available. Try accessing the WhatsApp integration from the app's interface.")
                                print("Go to Settings > Integrations > WhatsApp to connect.")
                        else:
                            print("\nNo QR code available. Try accessing the WhatsApp integration from the app's interface.")
                            print("Go to Settings > Integrations > WhatsApp to connect.")
                    except Exception as e:
                        print(f"\nCouldn't get QR code: {str(e)}")
                        print("Try accessing the WhatsApp integration from the app's interface.")
                        print("Go to Settings > Integrations > WhatsApp to connect.")
            else:
                print(f"\nCouldn't check connection state: {response.status_code}")
                print("Try accessing the WhatsApp integration from the app's interface.")
                print("Go to Settings > Integrations > WhatsApp to connect.")
        except Exception as e:
            print(f"\nError checking connection: {str(e)}")
            print("Try accessing the WhatsApp integration from the app's interface.")
            print("Go to Settings > Integrations > WhatsApp to connect.")

        # Get services
        services = db.query(Service).filter(Service.user_id == user_uuid).all()
        if not services:
            print("No services found. Please add some services first.")
        else:
            print("\nAvailable services:")
            for service in services:
                print(f"- {service.name}: ${service.price} ({service.duration_minutes} minutes)")

        # Print test instructions
        print("\n=== Test Instructions ===")
        print("1. Make sure your WhatsApp is connected in the app (Settings > Integrations > WhatsApp)")
        print("2. Use a second phone to send a WhatsApp message to your primary phone")
        print("3. Try sending these test messages from the second phone:")
        print("   - 'Hi, what services do you offer?'")
        print("   - 'I'd like to book a haircut tomorrow at 2pm'")
        print("   - 'Yes, please book it for me'")
        print("   - 'Can you tell me your business hours?'")
        print("4. The AI should respond automatically to your second phone")
        print("5. Check the Messages page in the app to see the conversation")
        print("6. Check the Calendar page to see if appointments are created")
        print("\nTroubleshooting:")
        print("- If you don't receive responses, check the WhatsApp connection in Settings > Integrations")
        print("- Make sure your server is accessible from the internet for webhooks to work")
        print("- Check the server logs for any errors")
        print("\nGood luck with your testing!")

    except Exception as e:
        print(f"Error setting up WhatsApp test: {str(e)}")

    finally:
        db.close()

if __name__ == "__main__":
    setup_whatsapp_test()
