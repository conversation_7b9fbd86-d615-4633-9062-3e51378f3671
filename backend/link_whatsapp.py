"""
Link WhatsApp instance to user
This script creates an external integration record that links a user to a WhatsApp instance
"""

import os
import sys
import json
from uuid import UUID
from datetime import datetime

from app.database import SessionLocal
from app.models.integration import ExternalIntegration

def link_whatsapp(user_id: str, instance_id: str, internal_id: str):
    """Link WhatsApp instance to user"""
    print(f"Linking WhatsApp instance {instance_id} (internal ID: {internal_id}) to user {user_id}")

    # Create a database session
    db = SessionLocal()

    try:
        # Check if integration already exists
        existing = db.query(ExternalIntegration).filter(
            ExternalIntegration.user_id == UUID(user_id),
            ExternalIntegration.integration_type == "whatsapp"
        ).first()

        if existing:
            print(f"WhatsApp integration already exists for user {user_id}")
            print(f"Existing integration: {existing.id}, credentials: {existing.credentials}")

            # Update the existing integration
            existing.credentials = {
                "instance_id": instance_id,
                "internal_id": internal_id,
                "status": "connected"
            }
            existing.last_updated = datetime.utcnow()

            db.commit()
            print(f"Updated WhatsApp integration for user {user_id}")
            return

        # Create a new integration
        integration = ExternalIntegration(
            user_id=UUID(user_id),
            integration_type="whatsapp",
            credentials={
                "instance_id": instance_id,
                "internal_id": internal_id,
                "status": "connected"
            },
            active=True
        )

        db.add(integration)
        db.commit()

        print(f"Created WhatsApp integration for user {user_id}")

    except Exception as e:
        print(f"Error linking WhatsApp instance: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    if len(sys.argv) < 4:
        print("Usage: python link_whatsapp.py <user_id> <instance_id> <internal_id>")
        sys.exit(1)

    user_id = sys.argv[1]
    instance_id = sys.argv[2]
    internal_id = sys.argv[3]
    link_whatsapp(user_id, instance_id, internal_id)
