#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix indentation in the client creation block
"""

# Read the file
with open('/app/app/routes/whatsapp.py', 'r') as f:
    lines = f.readlines()

# Find the problematic section
start_line = None
for i, line in enumerate(lines):
    if "# Try to get contact info from WhatsApp" in line and line.startswith("                #"):
        start_line = i
        break

if start_line:
    print(f"Found problematic section starting at line {start_line + 1}")
    
    # Fix indentation from this point until we find the end of the if block
    for i in range(start_line, len(lines)):
        line = lines[i]
        
        # If line starts with 16 spaces, reduce to 12 spaces
        if line.startswith("                ") and not line.startswith("                    "):
            lines[i] = "            " + line[16:]
            print(f"Fixed line {i + 1}: {line.strip()}")
        
        # Stop when we reach the end of the if block
        if line.strip() and not line.startswith("                ") and not line.startswith("            ") and i > start_line + 5:
            break
    
    # Write the file back
    with open('/app/app/routes/whatsapp.py', 'w') as f:
        f.writelines(lines)
    
    print("Client block indentation fixed!")
else:
    print("Could not find the problematic section")
