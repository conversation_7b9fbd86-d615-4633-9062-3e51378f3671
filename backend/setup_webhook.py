"""
Set up webhook for WhatsApp instance
This script sets up a webhook for a WhatsApp instance in Evolution API
"""

import os
import sys
import json
import requests

from app.config import settings

def setup_webhook(instance_id: str):
    """Set up webhook for WhatsApp instance"""
    print(f"Setting up webhook for WhatsApp instance {instance_id}")

    # Set up Evolution API headers
    headers = {
        "Content-Type": "application/json",
        "apikey": settings.EVOLUTION_API_KEY
    }

    # Get the webhook URL
    webhook_url = f"{settings.BACKEND_URL}/whatsapp/webhook/{instance_id}"
    print(f"Webhook URL: {webhook_url}")

    # Set up the webhook
    webhook_response = requests.post(
        f"{settings.EVOLUTION_API_URL}/webhook/set/{instance_id}",
        headers=headers,
        json={
            "webhook": webhook_url,
            "webhook_by_events": True,
            "webhook_base64": False,
            "events": {
                "application_startup": True,
                "qrcode_updated": True,
                "connection_update": True,
                "messages_upsert": True,
                "messages_update": True,
                "send_message": True,
                "contacts_upsert": True,
                "contacts_update": True,
                "presence_update": True,
                "chats_upsert": True,
                "chats_update": True,
                "groups_upsert": True,
                "groups_update": True,
                "group_participants_update": True,
                "labels_edit": True,
                "call": True,
                "new_jwt_token": True
            }
        }
    )

    print(f"Webhook response: {webhook_response.status_code}")
    print(json.dumps(webhook_response.json(), indent=2))

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python setup_webhook.py <instance_id>")
        sys.exit(1)

    instance_id = sys.argv[1]
    setup_webhook(instance_id)
