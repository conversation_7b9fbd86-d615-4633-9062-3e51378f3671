"""
Test script for WhatsApp webhook integration
This script simulates a webhook event from Evolution API to test the AI response
"""

import os
import json
import logging
import asyncio
import requests
from datetime import datetime
from uuid import UUID, uuid4

from app.database import SessionLocal
from app.routes.whatsapp import process_new_messages
from app.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_whatsapp_webhook(user_id: str, phone: str, message_text: str):
    """Test the WhatsApp webhook processing"""
    logger.info(f"Testing WhatsApp webhook for user {user_id}")

    # Create a database session
    db = SessionLocal()

    try:
        # Create a simulated webhook payload
        webhook_data = {
            "event": "MESSAGES_UPSERT",
            "data": {
                "messages": [
                    {
                        "key": {
                            "remoteJid": f"{phone}@s.whatsapp.net",
                            "fromMe": False,
                            "id": f"test-{uuid4()}"
                        },
                        "message": {
                            "conversation": message_text
                        },
                        "messageTimestamp": int(datetime.now().timestamp()),
                        "pushName": "Test User"
                    }
                ]
            }
        }

        # Process the webhook data
        await process_new_messages(user_id, webhook_data, db)

        logger.info(f"Webhook processed successfully for message: {message_text}")
        return True

    except Exception as e:
        logger.error(f"Error testing WhatsApp webhook: {str(e)}")
        return False
    finally:
        db.close()

async def main():
    """Run the test"""
    # Use a valid user ID from the database
    user_id = "b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7"  # <EMAIL>

    # Use a test phone number
    client_phone = "1234567890"  # Without the + prefix

    # Test messages
    messages = [
        "Hello, I'd like to book an appointment",
        "I need a haircut tomorrow at 2pm",
        "Yes, please book it for me"
    ]

    for message in messages:
        logger.info(f"Sending test message: {message}")
        success = await test_whatsapp_webhook(user_id, client_phone, message)
        
        if success:
            logger.info(f"Message processed successfully: {message}")
        else:
            logger.error(f"Failed to process message: {message}")
        
        # Wait a moment before sending the next message
        await asyncio.sleep(2)

if __name__ == "__main__":
    asyncio.run(main())
