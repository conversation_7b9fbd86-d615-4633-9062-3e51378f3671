"""
Test Google Calendar Sync
This script will:
1. Find a user with Google Calendar integration
2. Find appointments that need syncing
3. Sync them with Google Calendar
"""

import sys
import os
import asyncio
from uuid import UUID

# Add the parent directory to the path so we can import the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models.appointment import Appointment, AppointmentStatus
from app.models.integration import ExternalIntegration
from app.services.google_calendar_service import get_google_calendar_service
from app.services.appointment_service import AppointmentService

async def test_google_calendar_sync():
    """Test Google Calendar sync"""
    print("\n=== Testing Google Calendar Sync ===\n")
    
    # Create a database session
    db = SessionLocal()
    
    try:
        # Find a user with Google Calendar integration
        integration = db.query(ExternalIntegration).filter(
            ExternalIntegration.integration_type == "google_calendar",
            ExternalIntegration.active == True
        ).first()
        
        if not integration:
            print("No active Google Calendar integration found. Please connect Google Calendar first.")
            return
        
        user_id = integration.user_id
        print(f"Found Google Calendar integration for user {user_id}")
        
        # Find appointments that need syncing
        appointments = db.query(Appointment).filter(
            Appointment.user_id == user_id,
            Appointment.status != AppointmentStatus.CANCELLED,
            (Appointment.synced_with_google == False) | 
            (Appointment.last_synced < Appointment.updated_at)
        ).all()
        
        print(f"Found {len(appointments)} appointments that need syncing")
        
        if not appointments:
            print("No appointments need syncing. Creating a test appointment...")
            
            # Find the most recent appointment to use as a template
            template = db.query(Appointment).filter(
                Appointment.user_id == user_id
            ).order_by(Appointment.created_at.desc()).first()
            
            if not template:
                print("No existing appointments found to use as a template.")
                return
            
            # Create a test appointment
            from datetime import datetime, timedelta
            import copy
            
            # Create a copy of the template appointment
            test_appointment = Appointment(
                user_id=template.user_id,
                client_id=template.client_id,
                service_id=template.service_id,
                start_time=datetime.now() + timedelta(days=1, hours=2),
                end_time=datetime.now() + timedelta(days=1, hours=3),
                status=AppointmentStatus.CONFIRMED,
                notes="Test appointment for Google Calendar sync",
                synced_with_google=False
            )
            
            db.add(test_appointment)
            db.commit()
            db.refresh(test_appointment)
            
            print(f"Created test appointment with ID {test_appointment.id}")
            appointments = [test_appointment]
        
        # Sync appointments with Google Calendar
        google_calendar_service = get_google_calendar_service(db)
        
        for appointment in appointments:
            print(f"\nSyncing appointment {appointment.id}...")
            result = await google_calendar_service.sync_appointment_to_google(appointment)
            
            if result["success"]:
                print(f"Successfully synced appointment to Google Calendar")
                print(f"Event ID: {result.get('event_id')}")
                print(f"HTML Link: {result.get('html_link')}")
            else:
                print(f"Failed to sync appointment: {result.get('message')}")
        
        # Test the sync all function
        print("\nTesting sync all function...")
        appointment_service = AppointmentService(db)
        result = await appointment_service.sync_with_google_calendar(user_id)
        
        print(f"Sync all result: {result}")
        
        print("\nGoogle Calendar sync test completed successfully!")
    
    except Exception as e:
        print(f"Error testing Google Calendar sync: {str(e)}")
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_google_calendar_sync())
