#!/usr/bin/env python3
"""
Debug script to check WhatsApp AI auto-response functionality
"""

import sys
import os
import requests
import json
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import get_db
from app.models.ai_configuration import AIConfiguration
from app.config import settings

def check_ai_configuration(user_id):
    """Check AI configuration for a user"""
    print("\n=== Checking AI Configuration for user {} ===".format(user_id))

    db = next(get_db())
    try:
        ai_config = db.query(AIConfiguration).filter(AIConfiguration.user_id == user_id).first()

        if not ai_config:
            print("❌ No AI configuration found for this user")
            return False

        print("✅ AI configuration found")
        print(f"System Prompt: {ai_config.system_prompt[:100]}...")
        print(f"Tone: {ai_config.tone}")
        print(f"Primary Language: {ai_config.primary_language}")
        print(f"WhatsApp Settings: {json.dumps(ai_config.whatsapp_settings, indent=2)}")

        if ai_config.whatsapp_settings and ai_config.whatsapp_settings.get("enableAutoResponses"):
            print("✅ Auto-responses are ENABLED")
            return True
        else:
            print("❌ Auto-responses are DISABLED")
            return False

    except Exception as e:
        print(f"❌ Error checking AI configuration: {str(e)}")
        return False
    finally:
        db.close()

def check_whatsapp_connection(user_id):
    """Check WhatsApp connection status"""
    print(f"\n=== Checking WhatsApp Connection for user {user_id} ===")

    try:
        headers = {
            "Content-Type": "application/json",
            "apikey": settings.EVOLUTION_API_KEY
        }

        # Check connection state
        response = requests.get(
            f"{settings.EVOLUTION_API_URL}/instance/connectionState/{user_id}",
            headers=headers
        )

        if response.status_code == 200:
            data = response.json()
            state = data.get('instance', {}).get('state')
            print(f"✅ Connection state: {state}")
            return state == 'open'
        else:
            print(f"❌ Failed to get connection state: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error checking WhatsApp connection: {str(e)}")
        return False

def check_webhook_setup(user_id):
    """Check webhook configuration"""
    print(f"\n=== Checking Webhook Setup for user {user_id} ===")

    try:
        headers = {
            "Content-Type": "application/json",
            "apikey": settings.EVOLUTION_API_KEY
        }

        # Get webhook configuration
        response = requests.get(
            f"{settings.EVOLUTION_API_URL}/webhook/find/{user_id}",
            headers=headers
        )

        if response.status_code == 200:
            webhook_data = response.json()
            print("✅ Webhook configuration found:")
            print(f"Enabled: {webhook_data.get('enabled')}")
            print(f"URL: {webhook_data.get('url')}")
            print(f"Events: {webhook_data.get('events')}")
            print(f"Webhook by Events: {webhook_data.get('webhookByEvents')}")

            expected_url = f"{settings.BACKEND_URL}/whatsapp/webhook/{user_id}"
            actual_url = webhook_data.get('url')

            if actual_url == expected_url:
                print("✅ Webhook URL is correct")
            else:
                print(f"❌ Webhook URL mismatch:")
                print(f"  Expected: {expected_url}")
                print(f"  Actual: {actual_url}")

            if webhook_data.get('enabled'):
                print("✅ Webhook is enabled")
            else:
                print("❌ Webhook is disabled")

            required_events = ["MESSAGES_UPSERT", "MESSAGES_UPDATE", "SEND_MESSAGE", "CONNECTION_UPDATE"]
            webhook_events = webhook_data.get('events', [])

            missing_events = [event for event in required_events if event not in webhook_events]
            if not missing_events:
                print("✅ All required events are configured")
            else:
                print(f"❌ Missing events: {missing_events}")

            return webhook_data.get('enabled') and not missing_events
        else:
            print(f"❌ Failed to get webhook configuration: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error checking webhook setup: {str(e)}")
        return False

def setup_webhook(user_id):
    """Set up webhook for the user"""
    print(f"\n=== Setting up Webhook for user {user_id} ===")

    try:
        headers = {
            "Content-Type": "application/json",
            "apikey": settings.EVOLUTION_API_KEY
        }

        webhook_url = f"{settings.BACKEND_URL}/whatsapp/webhook/{user_id}"

        webhook_config = {
            "enabled": True,
            "url": webhook_url,
            "webhookByEvents": True,
            "webhookBase64": True,
            "events": [
                "MESSAGES_UPSERT",
                "MESSAGES_UPDATE",
                "SEND_MESSAGE",
                "CONNECTION_UPDATE"
            ]
        }

        response = requests.post(
            f"{settings.EVOLUTION_API_URL}/webhook/set/{user_id}",
            headers=headers,
            json=webhook_config
        )

        if response.status_code in [200, 201]:
            print("✅ Webhook setup successful")
            print(f"Webhook URL: {webhook_url}")
            return True
        else:
            print(f"❌ Failed to setup webhook: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error setting up webhook: {str(e)}")
        return False

def test_ai_response(user_id):
    """Test AI response generation"""
    print(f"\n=== Testing AI Response Generation for user {user_id} ===")

    try:
        # Test the AI response endpoint
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {generate_test_token(user_id)}"
        }

        test_data = {
            "message": "Hello, I would like to book an appointment",
            "language": "en"
        }

        response = requests.post(
            f"{settings.BACKEND_URL}/ai/generate-response",
            headers=headers,
            json=test_data
        )

        if response.status_code == 200:
            ai_response = response.json()
            print("✅ AI response generation successful")
            print(f"Response: {ai_response.get('text', 'No text in response')}")
            return True
        else:
            print(f"❌ Failed to generate AI response: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error testing AI response: {str(e)}")
        return False

def generate_test_token(user_id):
    """Generate a test JWT token for API calls"""
    import jwt
    from datetime import datetime, timedelta

    payload = {
        'sub': '<EMAIL>',
        'id': user_id,
        'exp': datetime.utcnow() + timedelta(hours=1)
    }

    # Use a simple secret for testing
    return jwt.encode(payload, 'test-secret', algorithm='HS256')

def main():
    if len(sys.argv) < 2:
        print("Usage: python debug_whatsapp_ai.py <user_id>")
        print("Example: python debug_whatsapp_ai.py b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7")
        sys.exit(1)

    user_id = sys.argv[1]

    print(f"🔍 Debugging WhatsApp AI Auto-Response for user: {user_id}")
    print(f"Backend URL: {settings.BACKEND_URL}")
    print(f"Evolution API URL: {settings.EVOLUTION_API_URL}")
    print(f"Timestamp: {datetime.now()}")

    # Run all checks
    ai_config_ok = check_ai_configuration(user_id)
    whatsapp_connected = check_whatsapp_connection(user_id)
    webhook_ok = check_webhook_setup(user_id)

    # If webhook is not properly set up, try to fix it
    if not webhook_ok and whatsapp_connected:
        print("\n🔧 Attempting to fix webhook setup...")
        webhook_ok = setup_webhook(user_id)

    # Test AI response generation
    ai_response_ok = test_ai_response(user_id)

    # Summary
    print(f"\n{'='*50}")
    print("🔍 DIAGNOSIS SUMMARY")
    print(f"{'='*50}")
    print(f"AI Configuration: {'✅ OK' if ai_config_ok else '❌ ISSUE'}")
    print(f"WhatsApp Connection: {'✅ OK' if whatsapp_connected else '❌ ISSUE'}")
    print(f"Webhook Setup: {'✅ OK' if webhook_ok else '❌ ISSUE'}")
    print(f"AI Response Generation: {'✅ OK' if ai_response_ok else '❌ ISSUE'}")

    if all([ai_config_ok, whatsapp_connected, webhook_ok, ai_response_ok]):
        print("\n🎉 All systems appear to be working correctly!")
        print("If auto-responses still don't work, try sending a test message to your WhatsApp number.")
    else:
        print("\n⚠️  Issues detected. Please fix the problems above.")

        if not ai_config_ok:
            print("- Enable auto-responses in the AI Configuration page")
        if not whatsapp_connected:
            print("- Reconnect to WhatsApp using the QR code")
        if not webhook_ok:
            print("- Check your backend URL and Evolution API configuration")
        if not ai_response_ok:
            print("- Check your AI API keys and configuration")

if __name__ == "__main__":
    main()
