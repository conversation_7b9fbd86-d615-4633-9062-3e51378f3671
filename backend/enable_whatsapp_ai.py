"""
Enable WhatsApp AI auto-responses for a user
This script enables WhatsApp AI auto-responses for a specific user
"""

import os
import sys
import json
from uuid import UUID

from app.database import SessionLocal
from app.models.ai_configuration import AIConfiguration

def enable_whatsapp_ai(user_id: str):
    """Enable WhatsApp AI auto-responses for a user"""
    print(f"Enabling WhatsApp AI auto-responses for user {user_id}")
    
    # Create a database session
    db = SessionLocal()
    
    try:
        # Find the user's AI configuration
        ai_config = db.query(AIConfiguration).filter(AIConfiguration.user_id == UUID(user_id)).first()
        
        if not ai_config:
            print(f"No AI configuration found for user {user_id}, creating one")
            
            # Create a new AI configuration
            ai_config = AIConfiguration(
                user_id=UUID(user_id),
                system_prompt="You are an AI assistant for a business that provides various services to clients. You communicate with clients via WhatsApp and help manage their appointments.",
                tone="professional",
                primary_language="en",
                supported_languages=["en", "es", "ca"],
                whatsapp_settings={
                    "enableAutoResponses": True,
                    "responseDelay": 2000,
                    "maxResponseLength": 500,
                    "enableDetailedLogging": True
                }
            )
            
            db.add(ai_config)
            db.commit()
            print(f"Created new AI configuration for user {user_id} with auto-responses enabled")
        else:
            # Update the existing AI configuration
            whatsapp_settings = ai_config.whatsapp_settings or {}
            whatsapp_settings["enableAutoResponses"] = True
            
            ai_config.whatsapp_settings = whatsapp_settings
            db.commit()
            print(f"Updated AI configuration for user {user_id}, auto-responses enabled")
        
    except Exception as e:
        print(f"Error enabling WhatsApp AI auto-responses: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python enable_whatsapp_ai.py <user_id>")
        sys.exit(1)
    
    user_id = sys.argv[1]
    enable_whatsapp_ai(user_id)
