"""add stripe customer id to clients

Revision ID: add_stripe_customer_id_to_clients
Revises: add_stripe_fields_to_users
Create Date: 2023-05-16 11:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_stripe_customer_id'
down_revision = 'add_stripe_fields_to_users'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add stripe_customer_id column to clients table
    op.add_column('clients', sa.Column('stripe_customer_id', sa.String(), nullable=True))


def downgrade() -> None:
    # Drop stripe_customer_id column from clients table
    op.drop_column('clients', 'stripe_customer_id')
