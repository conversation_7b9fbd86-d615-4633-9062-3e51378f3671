"""add profile_image to clients

Revision ID: add_profile_image_to_clients
Revises: 74276504e775
Create Date: 2023-05-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_profile_image_to_clients'
down_revision = '74276504e775'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add profile_image column to clients table
    op.add_column('clients', sa.Column('profile_image', sa.Text(), nullable=True))


def downgrade() -> None:
    # Remove profile_image column from clients table
    op.drop_column('clients', 'profile_image')
