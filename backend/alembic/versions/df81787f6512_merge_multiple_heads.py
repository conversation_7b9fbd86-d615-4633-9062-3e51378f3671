"""merge_multiple_heads

Revision ID: df81787f6512
Revises: 1468a159dc49, a75f05e22bcb
Create Date: 2025-04-16 11:29:59.159396

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'df81787f6512'
down_revision: Union[str, None] = ('1468a159dc49', 'a75f05e22bcb')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
