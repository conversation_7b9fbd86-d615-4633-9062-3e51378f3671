"""empty message

Revision ID: a75f05e22bcb
Revises: 7f794aff07c6
Create Date: 2025-04-15 07:48:05.858536

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a75f05e22bcb'
down_revision: Union[str, None] = '7f794aff07c6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
