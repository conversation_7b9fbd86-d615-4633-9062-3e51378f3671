"""add address column

Revision ID: add_address_column
Revises:
Create Date: 2023-05-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_address_column'
down_revision = 'add_stripe_ids'
branch_labels = None
depends_on = None


def upgrade():
    # Add address column to users table
    op.add_column('users', sa.Column('address', sa.String(), nullable=True))

    # Copy address from settings to the new column
    op.execute("""
        UPDATE users
        SET address = settings->>'address'
        WHERE settings->>'address' IS NOT NULL
    """)


def downgrade():
    # Remove address column from users table
    op.drop_column('users', 'address')
