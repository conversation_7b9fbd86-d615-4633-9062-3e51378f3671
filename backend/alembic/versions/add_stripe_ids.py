"""Add Stripe IDs

Revision ID: add_stripe_ids
Revises: 
Create Date: 2023-05-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from uuid import uuid4

# revision identifiers, used by Alembic.
revision = 'add_stripe_ids'
down_revision = None  # Update this with the previous migration ID
branch_labels = None
depends_on = None


def upgrade():
    # Add stripe_product_id and stripe_annual_price_id columns to pricing_plans table
    op.add_column('pricing_plans', sa.Column('stripe_product_id', sa.String(), nullable=True))
    op.add_column('pricing_plans', sa.Column('stripe_annual_price_id', sa.String(), nullable=True))


def downgrade():
    # Remove the columns
    op.drop_column('pricing_plans', 'stripe_annual_price_id')
    op.drop_column('pricing_plans', 'stripe_product_id')
