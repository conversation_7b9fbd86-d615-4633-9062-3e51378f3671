"""enhance_appointment_model_with_recurrence

Revision ID: f9eaee2e60a9
Revises: 5fea268628bd
Create Date: 2025-04-16 12:31:45.236277

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = 'f9eaee2e60a9'
down_revision: Union[str, None] = '5fea268628bd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create RecurrenceFrequency enum type
    recurrence_frequency = sa.Enum('daily', 'weekly', 'biweekly', 'monthly', 'yearly', name='recurrencefrequency')
    recurrence_frequency.create(op.get_bind())

    # Add RESCHEDULED to AppointmentStatus enum
    op.execute("ALTER TYPE appointmentstatus ADD VALUE 'rescheduled' AFTER 'pending'")

    # Add Google Calendar integration fields
    op.add_column('appointments', sa.Column('google_event_id', sa.String(), nullable=True))
    op.add_column('appointments', sa.Column('synced_with_google', sa.Boolean(), server_default='false', nullable=False))
    op.add_column('appointments', sa.Column('last_synced', sa.DateTime(timezone=True), nullable=True))

    # Add recurrence fields
    op.add_column('appointments', sa.Column('is_recurring', sa.Boolean(), server_default='false', nullable=False))
    op.add_column('appointments', sa.Column('recurrence_frequency', sa.Enum('daily', 'weekly', 'biweekly', 'monthly', 'yearly', name='recurrencefrequency'), nullable=True))
    op.add_column('appointments', sa.Column('recurrence_interval', sa.Integer(), server_default='1', nullable=True))
    op.add_column('appointments', sa.Column('recurrence_end_date', sa.DateTime(timezone=True), nullable=True))
    op.add_column('appointments', sa.Column('recurrence_count', sa.Integer(), nullable=True))
    op.add_column('appointments', sa.Column('recurrence_days', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column('appointments', sa.Column('recurrence_parent_id', sa.UUID(), nullable=True))

    # Add conflict resolution fields
    op.add_column('appointments', sa.Column('has_conflict', sa.Boolean(), server_default='false', nullable=False))
    op.add_column('appointments', sa.Column('conflict_notes', sa.String(), nullable=True))

    # Add foreign key for recurrence_parent_id
    op.create_foreign_key('fk_appointment_recurrence_parent', 'appointments', 'appointments', ['recurrence_parent_id'], ['id'])


def downgrade() -> None:
    # Drop foreign key for recurrence_parent_id
    op.drop_constraint('fk_appointment_recurrence_parent', 'appointments', type_='foreignkey')

    # Drop conflict resolution fields
    op.drop_column('appointments', 'conflict_notes')
    op.drop_column('appointments', 'has_conflict')

    # Drop recurrence fields
    op.drop_column('appointments', 'recurrence_parent_id')
    op.drop_column('appointments', 'recurrence_days')
    op.drop_column('appointments', 'recurrence_count')
    op.drop_column('appointments', 'recurrence_end_date')
    op.drop_column('appointments', 'recurrence_interval')
    op.drop_column('appointments', 'recurrence_frequency')
    op.drop_column('appointments', 'is_recurring')

    # Drop Google Calendar integration fields
    op.drop_column('appointments', 'last_synced')
    op.drop_column('appointments', 'synced_with_google')
    op.drop_column('appointments', 'google_event_id')

    # Drop RecurrenceFrequency enum type
    sa.Enum(name='recurrencefrequency').drop(op.get_bind())

    # Note: We can't easily remove a value from an enum in PostgreSQL
    # The RESCHEDULED status will remain in the enum
