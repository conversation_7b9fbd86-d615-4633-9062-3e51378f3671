"""add source to messages

Revision ID: add_source_to_messages
Revises: 38d603ece296
Create Date: 2023-04-13 22:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_source_to_messages'
down_revision = '38d603ece296'
branch_labels = None
depends_on = None


def upgrade():
    # Add source column to messages table with default value 'app'
    op.add_column('messages', sa.Column('source', sa.Text(), nullable=True, server_default='app'))


def downgrade():
    # Remove source column from messages table
    op.drop_column('messages', 'source')
