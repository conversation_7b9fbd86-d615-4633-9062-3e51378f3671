"""add whatsapp settings to ai config

Revision ID: add_whatsapp_settings
Depends on: 5d65837cc15d
Create Date: 2023-05-30 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB


# revision identifiers, used by Alembic.
revision = 'add_whatsapp_settings'
down_revision = '5d65837cc15d'
branch_labels = None
depends_on = None


def upgrade():
    # Add whatsapp_settings column to ai_configurations table
    op.add_column('ai_configurations', sa.Column('whatsapp_settings', JSONB, nullable=True))
    
    # Set default values for existing rows
    op.execute("""
    UPDATE ai_configurations
    SET whatsapp_settings = '{"enableAutoResponses": true, "responseDelay": 2000, "maxResponseLength": 500, "enableDetailedLogging": true}'
    WHERE whatsapp_settings IS NULL
    """)


def downgrade():
    # Remove whatsapp_settings column
    op.drop_column('ai_configurations', 'whatsapp_settings')
