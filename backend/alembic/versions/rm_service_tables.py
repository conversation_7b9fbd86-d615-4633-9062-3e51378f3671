"""rm service tables

Revision ID: rm_service_tables
Revises: 5d65837cc15d
Create Date: 2025-05-15 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import text


# revision identifiers, used by Alembic.
revision: str = 'rm_service_tables'
down_revision: Union[str, None] = '5d65837cc15d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Check if there's any data in the tables before dropping them
    conn = op.get_bind()
    
    # Check service_fisioterapeuta
    result = conn.execute(text("SELECT COUNT(*) FROM service_fisioterapeuta"))
    fisio_count = result.scalar()
    
    # Check service_perruqueria
    result = conn.execute(text("SELECT COUNT(*) FROM service_perruqueria"))
    perruq_count = result.scalar()
    
    # Log the counts for reference
    print(f"Found {fisio_count} rows in service_fisioterapeuta")
    print(f"Found {perruq_count} rows in service_perruqueria")
    
    # Drop the tables
    op.drop_table('service_fisioterapeuta')
    op.drop_table('service_perruqueria')
    
    # Drop the enum type if it's not used elsewhere
    op.execute("DROP TYPE IF EXISTS therapytype")


def downgrade() -> None:
    # Recreate the TherapyType enum
    op.execute("CREATE TYPE therapytype AS ENUM ('postural', 'traumatology')")
    
    # Recreate the specialized tables
    op.create_table('service_fisioterapeuta',
        sa.Column('service_id', sa.UUID(), nullable=False),
        sa.Column('tipus_terapia', sa.Enum('POSTURAL', 'TRAUMATOLOGY', name='therapytype'), nullable=False),
        sa.Column('a_domicili', sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(['service_id'], ['services.id'], ),
        sa.PrimaryKeyConstraint('service_id')
    )
    
    op.create_table('service_perruqueria',
        sa.Column('service_id', sa.UUID(), nullable=False),
        sa.Column('usa_maquina', sa.Boolean(), nullable=True),
        sa.Column('tipus_tall', sa.String(), nullable=True),
        sa.ForeignKeyConstraint(['service_id'], ['services.id'], ),
        sa.PrimaryKeyConstraint('service_id')
    )
    
    # Note: This downgrade doesn't restore any data that was in the tables
    # If you need to restore data, you would need to have backed it up before dropping the tables
