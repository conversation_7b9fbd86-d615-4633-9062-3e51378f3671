"""add_2fa_fields_to_user

Revision ID: add_2fa_fields
Revises: 7f794aff07c6
Create Date: 2024-04-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_2fa_fields'
down_revision = '7f794aff07c6'
branch_labels = None
depends_on = None


def upgrade():
    # Add 2FA fields to the users table
    op.add_column('users', sa.Column('twofa_secret', sa.String(), nullable=True))
    op.add_column('users', sa.Column('twofa_enabled', sa.<PERSON>(), server_default='false', nullable=False))
    op.add_column('users', sa.Column('backup_codes', sa.JSON(), server_default='[]', nullable=True))


def downgrade():
    # Remove 2FA fields from the users table
    op.drop_column('users', 'backup_codes')
    op.drop_column('users', 'twofa_enabled')
    op.drop_column('users', 'twofa_secret')
