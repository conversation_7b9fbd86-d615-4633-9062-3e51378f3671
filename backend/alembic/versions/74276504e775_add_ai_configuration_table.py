"""add_ai_configuration_table

Revision ID: 74276504e775
Revises: a75f05e22bcb
Create Date: 2025-04-16 11:26:27.820175

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID, JSONB


# revision identifiers, used by Alembic.
revision: str = '74276504e775'
down_revision: Union[str, None] = 'c0f652bb8884'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create ai_configurations table
    op.create_table(
        'ai_configurations',
        sa.Column('id', UUID, primary_key=True, server_default=sa.text('uuid_generate_v4()')),
        sa.Column('user_id', UUID, sa.ForeignKey('users.id'), nullable=False),
        sa.Column('system_prompt', sa.Text, nullable=False),
        sa.Column('tone', sa.String(50), server_default='professional'),
        sa.Column('primary_language', sa.String(10), server_default='en'),
        sa.Column('supported_languages', JSONB, server_default=sa.text("'[\"en\"]'::jsonb")),
        sa.Column('business_rules', JSONB),
        sa.Column('response_templates', JSONB),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now()),
    )

    # Create knowledge_documents table
    op.create_table(
        'knowledge_documents',
        sa.Column('id', UUID, primary_key=True, server_default=sa.text('uuid_generate_v4()')),
        sa.Column('user_id', UUID, sa.ForeignKey('users.id'), nullable=False),
        sa.Column('title', sa.String(255), nullable=False),
        sa.Column('content', sa.Text, nullable=False),
        sa.Column('embedding', sa.LargeBinary),  # Will be converted to pgvector in code
        sa.Column('document_metadata', JSONB),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
    )


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_table('knowledge_documents')
    op.drop_table('ai_configurations')
