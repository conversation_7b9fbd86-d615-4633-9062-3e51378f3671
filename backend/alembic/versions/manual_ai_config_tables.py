"""manual_ai_config_tables

Revision ID: manual_ai_config_tables
Revises: c0f652bb8884
Create Date: 2025-04-16 12:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'manual_ai_config_tables'
down_revision: Union[str, None] = 'c0f652bb8884'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Tables were created manually via SQL
    pass


def downgrade() -> None:
    # Drop tables in reverse order
    op.execute("DROP TABLE IF EXISTS knowledge_documents")
    op.execute("DROP TABLE IF EXISTS ai_configurations")
