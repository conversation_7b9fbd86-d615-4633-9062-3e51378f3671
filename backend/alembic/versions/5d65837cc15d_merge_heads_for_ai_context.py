"""merge_heads_for_ai_context

Revision ID: 5d65837cc15d
Revises: df81787f6512, manual_ai_config_tables, new_ai_config_tables
Create Date: 2025-04-16 12:03:25.926166

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5d65837cc15d'
down_revision: Union[str, None] = ('df81787f6512', 'manual_ai_config_tables', 'new_ai_config_tables')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
