"""add stripe price id to pricing plans

Revision ID: add_stripe_price_id
Revises: add_stripe_customer_id
Create Date: 2023-05-17 11:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_stripe_price_id'
down_revision = 'add_stripe_customer_id'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add stripe_price_id column to pricing_plans table
    op.add_column('pricing_plans', sa.Column('stripe_price_id', sa.String(), nullable=True))


def downgrade() -> None:
    # Drop stripe_price_id column from pricing_plans table
    op.drop_column('pricing_plans', 'stripe_price_id')
