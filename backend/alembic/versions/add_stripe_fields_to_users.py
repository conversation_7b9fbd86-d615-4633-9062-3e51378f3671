"""add stripe fields to users

Revision ID: add_stripe_fields_to_users
Revises: add_profile_image_to_clients
Create Date: 2023-05-15 11:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_stripe_fields_to_users'
down_revision = 'add_profile_image_to_clients'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add stripe_customer_id column to users table
    op.add_column('users', sa.Column('stripe_customer_id', sa.String(), nullable=True))
    
    # Add plan_id column to users table
    op.add_column('users', sa.Column('plan_id', sa.UUID(), nullable=True))
    
    # Add foreign key constraint
    op.create_foreign_key(
        'fk_users_plan_id_pricing_plans',
        'users', 'pricing_plans',
        ['plan_id'], ['id']
    )


def downgrade() -> None:
    # Drop foreign key constraint
    op.drop_constraint('fk_users_plan_id_pricing_plans', 'users', type_='foreignkey')
    
    # Drop plan_id column from users table
    op.drop_column('users', 'plan_id')
    
    # Drop stripe_customer_id column from users table
    op.drop_column('users', 'stripe_customer_id')
