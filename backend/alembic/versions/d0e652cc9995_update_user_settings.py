"""update_user_settings

Revision ID: d0e652cc9995
Revises: c0f652bb8884
Create Date: 2023-04-09 18:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'd0e652cc9995'
down_revision = 'c0f652bb8884'
branch_labels = None
depends_on = None


def upgrade():
    # Make user_id nullable in clients table
    # op.alter_column('clients', 'user_id',
    #            existing_type=sa.UUID(),
    #            nullable=True)
    
    # Check if settings column already exists in users table
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    columns = [c['name'] for c in inspector.get_columns('users')]
    
    # Only add the column if it doesn't exist
    if 'settings' not in columns:
        op.add_column('users', sa.Column('settings', postgresql.JSON(astext_type=sa.Text()), nullable=True))


def downgrade():
    # We don't want to drop the settings column in downgrade
    # as it might contain important user data
    
    # Revert the change, making user_id non-nullable again
    # op.alter_column('clients', 'user_id',
    #            existing_type=sa.UUID(),
    #            nullable=False)
    pass
