"""enhance_ai_conversation_context

Revision ID: 5fea268628bd
Revises: 5d65837cc15d
Create Date: 2025-04-16 12:04:09.863055

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB


# revision identifiers, used by Alembic.
revision: str = '5fea268628bd'
down_revision: Union[str, None] = '5d65837cc15d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Enhance AIConversationContext table
    op.add_column('ai_conversation_contexts', sa.Column('conversation_language', sa.String(10), server_default='en'))
    op.add_column('ai_conversation_contexts', sa.Column('message_count', sa.Integer(), server_default='0'))
    op.add_column('ai_conversation_contexts', sa.Column('last_intent', sa.String(50), nullable=True))
    op.add_column('ai_conversation_contexts', sa.Column('has_booking_intent', sa.<PERSON>(), server_default='false'))
    op.add_column('ai_conversation_contexts', sa.Column('booking_stage', sa.String(50), nullable=True))
    op.add_column('ai_conversation_contexts', sa.Column('booking_data', JSONB, nullable=True))
    op.add_column('ai_conversation_contexts', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()))
    op.add_column('ai_conversation_contexts', sa.Column('last_message_at', sa.DateTime(timezone=True), nullable=True))

    # Alter context_data column to use JSONB
    op.alter_column('ai_conversation_contexts', 'context_data', type_=JSONB, existing_type=sa.JSON, postgresql_using='context_data::jsonb')

    # Create ConversationMessage table
    op.create_table(
        'conversation_messages',
        sa.Column('id', sa.UUID(), primary_key=True, server_default=sa.text('uuid_generate_v4()')),
        sa.Column('conversation_id', sa.UUID(), sa.ForeignKey('ai_conversation_contexts.id'), nullable=False),
        sa.Column('user_id', sa.UUID(), sa.ForeignKey('users.id'), nullable=True),
        sa.Column('client_id', sa.UUID(), sa.ForeignKey('clients.id'), nullable=True),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('role', sa.String(20), nullable=False),
        sa.Column('detected_language', sa.String(10), nullable=True),
        sa.Column('detected_intent', sa.String(50), nullable=True),
        sa.Column('message_metadata', JSONB, nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
    )


def downgrade() -> None:
    # Drop ConversationMessage table
    op.drop_table('conversation_messages')

    # Revert AIConversationContext table changes
    op.drop_column('ai_conversation_contexts', 'conversation_language')
    op.drop_column('ai_conversation_contexts', 'message_count')
    op.drop_column('ai_conversation_contexts', 'last_intent')
    op.drop_column('ai_conversation_contexts', 'has_booking_intent')
    op.drop_column('ai_conversation_contexts', 'booking_stage')
    op.drop_column('ai_conversation_contexts', 'booking_data')
    op.drop_column('ai_conversation_contexts', 'created_at')
    op.drop_column('ai_conversation_contexts', 'last_message_at')

    # Revert context_data column to JSON
    op.alter_column('ai_conversation_contexts', 'context_data', type_=sa.JSON, existing_type=JSONB, postgresql_using='context_data::json')
