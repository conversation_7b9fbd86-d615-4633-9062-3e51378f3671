"""merge_heads

Revision ID: 1468a159dc49
Revises: 74276504e775
Create Date: 2025-04-16 11:28:56.485491

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1468a159dc49'
down_revision: Union[str, None] = '74276504e775'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
