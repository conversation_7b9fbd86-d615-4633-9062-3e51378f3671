"""remove settings column

Revision ID: remove_settings_column
Revises: migrate_settings_to_columns
Create Date: 2023-05-17 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'remove_settings_column'
down_revision = 'migrate_settings_to_columns'
branch_labels = None
depends_on = None


def upgrade():
    # Remove the settings column
    op.drop_column('users', 'settings')


def downgrade():
    # Add the settings column back
    op.add_column('users', sa.Column('settings', postgresql.JSON(astext_type=sa.Text()), nullable=True))
