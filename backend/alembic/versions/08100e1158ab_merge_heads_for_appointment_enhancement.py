"""merge_heads_for_appointment_enhancement

Revision ID: 08100e1158ab
Revises: f9eaee2e60a9
Create Date: 2025-04-16 12:33:04.522605

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '08100e1158ab'
down_revision: Union[str, None] = 'f9eaee2e60a9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
