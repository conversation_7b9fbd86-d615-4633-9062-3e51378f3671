"""migrate settings to columns

Revision ID: migrate_settings_to_columns
Revises: add_address_column
Create Date: 2023-05-16 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'migrate_settings_to_columns'
down_revision = 'add_address_column'
branch_labels = None
depends_on = None


def upgrade():
    # Add columns for each setting
    op.add_column('users', sa.Column('phone', sa.String(), nullable=True))
    op.add_column('users', sa.Column('website', sa.String(), nullable=True))
    op.add_column('users', sa.Column('logo_url', sa.Text(), nullable=True))
    
    # Copy data from settings to the new columns
    op.execute("""
        UPDATE users 
        SET phone = settings->>'phone',
            website = settings->>'website',
            logo_url = settings->>'logo_url'
        WHERE settings IS NOT NULL
    """)


def downgrade():
    # Remove the columns
    op.drop_column('users', 'phone')
    op.drop_column('users', 'website')
    op.drop_column('users', 'logo_url')
