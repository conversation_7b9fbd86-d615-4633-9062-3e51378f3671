#!/usr/bin/env python
"""
Very simple test script for OpenAI API integration (Python 2.7 compatible)
"""

import os
import sys
import json
import requests

# Your API key (replace with your actual key)
api_key = "********************************************************************************************************************************************************************"

def test_chat_completion():
    """Test chat completion API using direct HTTP requests"""
    print("Testing chat completion API...")
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + api_key
    }
    
    data = {
        "model": "gpt-4o-mini",
        "messages": [
            {"role": "user", "content": "Write a haiku about AI"}
        ]
    }
    
    try:
        response = requests.post(
            "https://api.openai.com/v1/chat/completions",
            headers=headers,
            data=json.dumps(data)
        )
        
        if response.status_code != 200:
            print("\nChat completion test: FAILED")
            print("Status code: {}".format(response.status_code))
            print("Response: {}".format(response.text))
            return False
        
        result = response.json()
        
        print("\nResponse:")
        print(result["choices"][0]["message"]["content"])
        print("\nUsage:")
        print("Prompt tokens: {}".format(result["usage"]["prompt_tokens"]))
        print("Completion tokens: {}".format(result["usage"]["completion_tokens"]))
        print("Total tokens: {}".format(result["usage"]["total_tokens"]))
        
        print("\nChat completion test: SUCCESS")
        return True
    except Exception as e:
        print("\nChat completion test: FAILED")
        print("Error: {}".format(str(e)))
        return False

if __name__ == "__main__":
    print("Using OpenAI API key: {}...{}".format(api_key[:5], api_key[-4:]))
    
    chat_success = test_chat_completion()
    
    if chat_success:
        print("\nTest passed successfully!")
        sys.exit(0)
    else:
        print("\nTest failed. Please check the error messages above.")
        sys.exit(1)
