#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update invoice URLs in the database from Stripe.
This script should be run after adding the new columns to the database.
"""

import os
import sys
import stripe
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from uuid import UUID

# Add the app directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.config import settings
from app.models.invoice import Invoice

# Initialize Stripe with the API key
if not settings.STRIPE_SECRET_KEY:
    print("Stripe API key not configured. Please set STRIPE_SECRET_KEY in .env file.")
    sys.exit(1)

stripe.api_key = settings.STRIPE_SECRET_KEY

# Create database connection
engine = create_engine(settings.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

try:
    # Get all invoices with Stripe invoice IDs but no URLs
    invoices = db.query(Invoice).filter(
        Invoice.stripe_invoice_id.isnot(None),
        (Invoice.invoice_pdf_url.is_(None) | Invoice.hosted_invoice_url.is_(None))
    ).all()
    
    print(f"Found {len(invoices)} invoices to update")
    
    # Update each invoice
    for invoice in invoices:
        try:
            print(f"Updating invoice {invoice.id} with Stripe ID {invoice.stripe_invoice_id}")
            
            # Get the Stripe invoice
            stripe_invoice = stripe.Invoice.retrieve(invoice.stripe_invoice_id)
            
            # Update the invoice URLs
            invoice.invoice_pdf_url = stripe_invoice.invoice_pdf if hasattr(stripe_invoice, 'invoice_pdf') else None
            invoice.hosted_invoice_url = stripe_invoice.hosted_invoice_url if hasattr(stripe_invoice, 'hosted_invoice_url') else None
            
            print(f"  PDF URL: {invoice.invoice_pdf_url}")
            print(f"  Hosted URL: {invoice.hosted_invoice_url}")
            
            # Commit the changes
            db.commit()
            
        except Exception as e:
            print(f"Error updating invoice {invoice.id}: {str(e)}")
            db.rollback()
    
    print("Done updating invoices")
    
except Exception as e:
    print(f"Error: {str(e)}")
    db.rollback()
finally:
    db.close()
