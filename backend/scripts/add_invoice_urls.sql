-- Add invoice_pdf_url and hosted_invoice_url columns to invoices table
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS invoice_pdf_url VARCHAR;
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS hosted_invoice_url VARCHAR;

-- Update existing invoices with Stripe invoice IDs to fetch and store URLs
-- This would need to be run with a separate script that can interact with Stripe API
-- Example of how to update a specific invoice:
-- UPDATE invoices SET invoice_pdf_url = 'https://example.com/invoice.pdf', hosted_invoice_url = 'https://example.com/invoice' WHERE stripe_invoice_id = 'in_123456789';
