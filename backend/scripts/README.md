# Database Scripts

This directory contains scripts for database operations that are not handled by Alembic migrations.

## Invoice URL Scripts

### add_invoice_urls.sql

This SQL script adds the `invoice_pdf_url` and `hosted_invoice_url` columns to the `invoices` table. These columns are used to store the URLs for Stripe invoice PDFs and hosted invoice pages.

To run the script:

```bash
docker-compose exec postgres psql -U postgres -d fixmycal -f /app/scripts/add_invoice_urls.sql
```

### update_invoice_urls.py

This Python script updates existing invoices with URLs from Stripe. It retrieves all invoices with Stripe invoice IDs but no URLs, fetches the URLs from <PERSON><PERSON>, and updates the database.

To run the script:

```bash
docker-compose exec backend python /app/scripts/update_invoice_urls.py
```

## Usage Notes

- These scripts should be run in order: first `add_invoice_urls.sql`, then `update_invoice_urls.py`.
- The scripts require that <PERSON><PERSON> is properly configured with a valid API key.
- The scripts are idempotent and can be run multiple times without causing issues.
- The scripts will log their progress to the console.

## Troubleshooting

If you encounter issues:

1. Make sure <PERSON><PERSON> is properly configured with a valid API key.
2. Check that the database is accessible and the `invoices` table exists.
3. Check the logs for error messages.
4. If the script fails, you can run it again after fixing the issue.
