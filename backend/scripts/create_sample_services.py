from uuid import UUID
import sys
import os

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models.service import Service, ServiceType
from app.models.user import User

def create_sample_services():
    db = SessionLocal()
    
    try:
        # Get the first user
        user = db.query(User).first()
        if not user:
            print("No users found in the database. Please create a user first.")
            return
        
        # Create service types if they don't exist
        service_types = {
            "Haircut": db.query(ServiceType).filter(ServiceType.name == "Haircut").first(),
            "Massage": db.query(ServiceType).filter(ServiceType.name == "Massage").first(),
            "Consultation": db.query(ServiceType).filter(ServiceType.name == "Consultation").first(),
            "Physiotherapy": db.query(ServiceType).filter(ServiceType.name == "Physiotherapy").first()
        }
        
        for type_name, type_obj in service_types.items():
            if not type_obj:
                new_type = ServiceType(name=type_name)
                db.add(new_type)
                service_types[type_name] = new_type
        
        db.commit()
        
        # Refresh service types to get their IDs
        for type_name, type_obj in service_types.items():
            db.refresh(type_obj)
        
        # Create sample services
        services = [
            {
                "name": "Basic Haircut",
                "description": "A simple haircut with scissors and clippers",
                "price": 25.0,
                "duration_minutes": 30,
                "type_id": service_types["Haircut"].id,
                "user_id": user.id
            },
            {
                "name": "Premium Haircut",
                "description": "Haircut with wash, styling, and beard trim",
                "price": 45.0,
                "duration_minutes": 60,
                "type_id": service_types["Haircut"].id,
                "user_id": user.id
            },
            {
                "name": "Swedish Massage",
                "description": "Relaxing full-body massage",
                "price": 60.0,
                "duration_minutes": 60,
                "type_id": service_types["Massage"].id,
                "user_id": user.id
            },
            {
                "name": "Deep Tissue Massage",
                "description": "Therapeutic massage focusing on deep muscle layers",
                "price": 80.0,
                "duration_minutes": 90,
                "type_id": service_types["Massage"].id,
                "user_id": user.id
            },
            {
                "name": "Initial Consultation",
                "description": "First-time client consultation",
                "price": 40.0,
                "duration_minutes": 45,
                "type_id": service_types["Consultation"].id,
                "user_id": user.id
            },
            {
                "name": "Physiotherapy Session",
                "description": "Therapeutic exercises and manual therapy",
                "price": 70.0,
                "duration_minutes": 60,
                "type_id": service_types["Physiotherapy"].id,
                "user_id": user.id
            }
        ]
        
        # Add services to the database
        for service_data in services:
            # Check if service already exists
            existing_service = db.query(Service).filter(
                Service.name == service_data["name"],
                Service.user_id == service_data["user_id"]
            ).first()
            
            if not existing_service:
                new_service = Service(**service_data)
                db.add(new_service)
                print(f"Added service: {service_data['name']}")
            else:
                print(f"Service already exists: {service_data['name']}")
        
        db.commit()
        print("Sample services created successfully!")
        
    except Exception as e:
        db.rollback()
        print(f"Error creating sample services: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    create_sample_services()
