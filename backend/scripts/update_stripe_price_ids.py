#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update pricing plans with Stripe price IDs.
Run this script after setting up your products in Stripe.

Usage:
    docker-compose exec backend python scripts/update_stripe_price_ids.py
"""

import sys
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.pricing import PricingPlan
from app.config import settings

# Check if DATABASE_URL is set
if not settings.DATABASE_URL:
    print("Error: DATABASE_URL environment variable is not set.")
    sys.exit(1)

# Create database connection
engine = create_engine(settings.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

# Stripe price IDs for each plan
# Replace these with your actual Stripe price IDs
PRICE_IDS = {
    "Starter": "price_your_starter_price_id",
    "Professional": "price_your_professional_price_id",
    "Enterprise": "price_your_enterprise_price_id"
}

def update_price_ids():
    """Update pricing plans with Stripe price IDs."""
    try:
        # Get all pricing plans
        plans = db.query(PricingPlan).all()
        
        if not plans:
            print("No pricing plans found in the database.")
            return
        
        for plan in plans:
            if plan.name in PRICE_IDS:
                print(f"Updating {plan.name} plan with Stripe price ID: {PRICE_IDS[plan.name]}")
                plan.stripe_price_id = PRICE_IDS[plan.name]
            else:
                print(f"Warning: No Stripe price ID defined for plan: {plan.name}")
        
        # Commit changes
        db.commit()
        print("Successfully updated pricing plans with Stripe price IDs.")
    
    except Exception as e:
        db.rollback()
        print(f"Error updating pricing plans: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    # Ask for confirmation
    confirm = input("This will update pricing plans with Stripe price IDs. Continue? (y/n): ")
    if confirm.lower() == 'y':
        update_price_ids()
    else:
        print("Operation cancelled.")
