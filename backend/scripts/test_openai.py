#!/usr/bin/env python
"""
Test script for OpenAI API integration
"""

import os
import sys
from dotenv import load_dotenv
from openai import OpenAI

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables from .env file
load_dotenv()

# Get API key from environment
api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    print("Error: OPENAI_API_KEY environment variable not set")
    sys.exit(1)

# Initialize OpenAI client
client = OpenAI(api_key=api_key)

def test_chat_completion():
    """Test chat completion API"""
    print("Testing chat completion API...")

    try:
        completion = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "user", "content": "Write a haiku about AI"}
            ]
        )

        print("\nResponse:")
        print(completion.choices[0].message.content)
        print("\nUsage:")
        print("Prompt tokens: {}".format(completion.usage.prompt_tokens))
        print("Completion tokens: {}".format(completion.usage.completion_tokens))
        print("Total tokens: {}".format(completion.usage.total_tokens))

        print("\nChat completion test: SUCCESS")
        return True
    except Exception as e:
        print("\nChat completion test: FAILED")
        print("Error: {}".format(str(e)))
        return False

def test_embeddings():
    """Test embeddings API"""
    print("\nTesting embeddings API...")

    try:
        response = client.embeddings.create(
            model="text-embedding-3-small",
            input="Hello, world!"
        )

        embedding = response.data[0].embedding

        print("\nEmbedding dimension: {}".format(len(embedding)))
        print("First 5 values: {}".format(embedding[:5]))

        print("\nEmbeddings test: SUCCESS")
        return True
    except Exception as e:
        print("\nEmbeddings test: FAILED")
        print("Error: {}".format(str(e)))
        return False

if __name__ == "__main__":
    print("Using OpenAI API key: {}...{}".format(api_key[:5], api_key[-4:]))

    chat_success = test_chat_completion()
    embeddings_success = test_embeddings()

    if chat_success and embeddings_success:
        print("\nAll tests passed successfully!")
        sys.exit(0)
    else:
        print("\nSome tests failed. Please check the error messages above.")
        sys.exit(1)
