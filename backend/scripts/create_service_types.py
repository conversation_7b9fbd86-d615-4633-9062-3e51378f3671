#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create default service types in the database.
"""

import sys
import os
from uuid import uuid4

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models.service import ServiceType

def create_service_types():
    """Create default service types in the database."""
    db = SessionLocal()
    
    try:
        # Define default service types
        default_types = [
            "Haircut",
            "Hair Coloring",
            "Hair Styling",
            "Hair Treatment",
            "Massage",
            "Facial",
            "Manicure",
            "Pedicure",
            "Consultation",
            "Physiotherapy",
            "Other"
        ]
        
        # Check which types already exist
        existing_types = {type_obj.name for type_obj in db.query(ServiceType).all()}
        
        # Create types that don't exist yet
        types_to_create = []
        for type_name in default_types:
            if type_name not in existing_types:
                types_to_create.append(ServiceType(name=type_name))
        
        if types_to_create:
            db.add_all(types_to_create)
            db.commit()
            print(f"Created {len(types_to_create)} new service types: {', '.join(t.name for t in types_to_create)}")
        else:
            print("All default service types already exist.")
        
    except Exception as e:
        db.rollback()
        print(f"Error creating service types: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    create_service_types()
