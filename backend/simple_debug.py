#!/usr/bin/env python3
"""
Simple debug script to check WhatsApp AI auto-response functionality
"""

import sys
import os
import requests
import json

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import get_db
from app.models.ai_configuration import AIConfiguration
from app.config import settings

def main():
    if len(sys.argv) < 2:
        print("Usage: python simple_debug.py <user_id>")
        print("Example: python simple_debug.py b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7")
        sys.exit(1)

    user_id = sys.argv[1]

    print("=== Debugging WhatsApp AI Auto-Response ===")
    print("User ID: " + user_id)
    print("Backend URL: " + settings.BACKEND_URL)
    print("Evolution API URL: " + settings.EVOLUTION_API_URL)

    # Check AI Configuration
    print("\n1. Checking AI Configuration...")
    db = next(get_db())
    try:
        ai_config = db.query(AIConfiguration).filter(AIConfiguration.user_id == user_id).first()

        if not ai_config:
            print("ERROR: No AI configuration found for this user")
            return

        print("AI configuration found")
        print("WhatsApp Settings: " + json.dumps(ai_config.whatsapp_settings, indent=2))

        if ai_config.whatsapp_settings and ai_config.whatsapp_settings.get("enableAutoResponses"):
            print("SUCCESS: Auto-responses are ENABLED")
        else:
            print("ERROR: Auto-responses are DISABLED")

    except Exception as e:
        print("ERROR checking AI configuration: " + str(e))
    finally:
        db.close()

    # Check WhatsApp Connection
    print("\n2. Checking WhatsApp Connection...")
    try:
        headers = {
            "Content-Type": "application/json",
            "apikey": settings.EVOLUTION_API_KEY
        }

        response = requests.get(
            settings.EVOLUTION_API_URL + "/instance/connectionState/" + user_id,
            headers=headers
        )

        if response.status_code == 200:
            data = response.json()
            state = data.get('instance', {}).get('state')
            print("Connection state: " + str(state))
            if state == 'open':
                print("SUCCESS: WhatsApp is connected")
            else:
                print("ERROR: WhatsApp is not connected")
        else:
            print("ERROR: Failed to get connection state: " + str(response.status_code))

    except Exception as e:
        print("ERROR checking WhatsApp connection: " + str(e))

    # Check Webhook Setup
    print("\n3. Checking Webhook Setup...")
    try:
        response = requests.get(
            settings.EVOLUTION_API_URL + "/webhook/find/" + user_id,
            headers=headers
        )

        if response.status_code == 200:
            webhook_data = response.json()
            print("Webhook response: " + str(webhook_data))

            if webhook_data:
                print("Webhook configuration found:")
                print("Enabled: " + str(webhook_data.get('enabled')))
                print("URL: " + str(webhook_data.get('url')))
                print("Events: " + str(webhook_data.get('events')))
            else:
                print("ERROR: Webhook data is None or empty")

            expected_url = settings.BACKEND_URL + "/whatsapp/webhook/" + user_id
            actual_url = webhook_data.get('url')

            if actual_url == expected_url:
                print("SUCCESS: Webhook URL is correct")
            else:
                print("ERROR: Webhook URL mismatch")
                print("Expected: " + expected_url)
                print("Actual: " + str(actual_url))

            if webhook_data.get('enabled'):
                print("SUCCESS: Webhook is enabled")
            else:
                print("ERROR: Webhook is disabled")

        else:
            print("ERROR: Failed to get webhook configuration: " + str(response.status_code))
            print("Response: " + response.text)

    except Exception as e:
        print("ERROR checking webhook setup: " + str(e))

    print("\n=== Debug Complete ===")

if __name__ == "__main__":
    main()
