"""
Add test services to the database
"""

import sys
import os
from uuid import UUID
from datetime import datetime

# Add the parent directory to the path so we can import the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models.service import Service

def add_test_services():
    """Add test services to the database"""
    # Create a database session
    db = SessionLocal()
    
    try:
        # User ID for the test user
        user_id = UUID("b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7")  # <EMAIL>
        
        # Check if services already exist
        existing_services = db.query(Service).filter(Service.user_id == user_id).count()
        if existing_services > 0:
            print(f"User already has {existing_services} services. Skipping.")
            return
        
        # Define test services
        test_services = [
            {
                "name": "Haircut",
                "description": "Basic haircut service",
                "duration_minutes": 30,
                "price": 25.00,
                "color": "#FF5733"
            },
            {
                "name": "Haircut & Style",
                "description": "Haircut with styling",
                "duration_minutes": 45,
                "price": 35.00,
                "color": "#33FF57"
            },
            {
                "name": "Color Treatment",
                "description": "Hair coloring service",
                "duration_minutes": 90,
                "price": 65.00,
                "color": "#3357FF"
            },
            {
                "name": "Blowout",
                "description": "Hair blowout and styling",
                "duration_minutes": 30,
                "price": 30.00,
                "color": "#F3FF33"
            },
            {
                "name": "Deep Conditioning",
                "description": "Deep conditioning treatment",
                "duration_minutes": 45,
                "price": 40.00,
                "color": "#FF33F3"
            }
        ]
        
        # Add services to the database
        for service_data in test_services:
            service = Service(
                user_id=user_id,
                name=service_data["name"],
                description=service_data["description"],
                duration_minutes=service_data["duration_minutes"],
                price=service_data["price"],
                color=service_data["color"],
                created_at=datetime.now()
            )
            db.add(service)
        
        # Commit the changes
        db.commit()
        
        print(f"Added {len(test_services)} test services for user {user_id}")
    
    except Exception as e:
        db.rollback()
        print(f"Error adding test services: {str(e)}")
    
    finally:
        db.close()

if __name__ == "__main__":
    add_test_services()
