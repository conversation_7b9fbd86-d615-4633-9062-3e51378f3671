-- Create google_calendar_cache table
CREATE TABLE IF NOT EXISTS google_calendar_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    event_id VARCHAR NOT NULL,
    event_data JSONB NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS ix_google_calendar_cache_event_id ON google_calendar_cache(event_id);
CREATE INDEX IF NOT EXISTS ix_google_calendar_cache_start_time ON google_calendar_cache(start_time);
CREATE INDEX IF NOT EXISTS ix_google_calendar_cache_end_time ON google_calendar_cache(end_time);
