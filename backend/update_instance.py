"""
Update WhatsApp instance with webhook
This script updates a WhatsApp instance in Evolution API with a webhook
"""

import os
import sys
import json
import requests

from app.config import settings

def update_instance(instance_id: str):
    """Update WhatsApp instance with webhook"""
    print(f"Updating WhatsApp instance {instance_id} with webhook")
    
    # Set up Evolution API headers
    headers = {
        "Content-Type": "application/json",
        "apikey": settings.EVOLUTION_API_KEY
    }
    
    # Get the webhook URL
    webhook_url = f"{settings.BACKEND_URL}/whatsapp/webhook/{instance_id}"
    print(f"Webhook URL: {webhook_url}")
    
    # Update the instance
    update_response = requests.put(
        f"{settings.EVOLUTION_API_URL}/instance/update/{instance_id}",
        headers=headers,
        json={
            "instanceName": instance_id,
            "token": instance_id,
            "qrcode": True,
            "webhook": webhook_url,
            "webhookEvents": {
                "application_startup": True,
                "qrcode_updated": True,
                "connection_update": True,
                "messages_upsert": True,
                "messages_update": True,
                "send_message": True,
                "contacts_upsert": True,
                "contacts_update": True,
                "presence_update": True,
                "chats_upsert": True,
                "chats_update": True,
                "groups_upsert": True,
                "groups_update": True,
                "group_participants_update": True,
                "labels_edit": True,
                "call": True,
                "new_jwt_token": True
            }
        }
    )
    
    print(f"Update response: {update_response.status_code}")
    print(json.dumps(update_response.json(), indent=2))

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python update_instance.py <instance_id>")
        sys.exit(1)
    
    instance_id = sys.argv[1]
    update_instance(instance_id)
