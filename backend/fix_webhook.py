#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to fix WhatsApp webhook setup
"""

import sys
import os
import requests
import json

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config import settings

def setup_webhook(user_id):
    """Set up webhook for the user"""
    print("=== Setting up Webhook for user {} ===".format(user_id))

    try:
        headers = {
            "Content-Type": "application/json",
            "apikey": settings.EVOLUTION_API_KEY
        }

        webhook_url = settings.BACKEND_URL + "/whatsapp/webhook/" + user_id
        print("Setting webhook URL to: " + webhook_url)

        webhook_config = {
            "webhook": {
                "enabled": True,
                "url": webhook_url,
                "webhookByEvents": True,
                "webhookBase64": True,
                "events": [
                    "MESSAGES_UPSERT",
                    "MESSAGES_UPDATE",
                    "SEND_MESSAGE",
                    "CONNECTION_UPDATE"
                ]
            }
        }

        print("Webhook config: " + json.dumps(webhook_config, indent=2))

        response = requests.post(
            settings.EVOLUTION_API_URL + "/webhook/set/" + user_id,
            headers=headers,
            json=webhook_config
        )

        print("Response status: " + str(response.status_code))
        print("Response body: " + response.text)

        if response.status_code in [200, 201]:
            print("SUCCESS: Webhook setup successful")
            return True
        else:
            print("ERROR: Failed to setup webhook")
            return False

    except Exception as e:
        print("ERROR setting up webhook: " + str(e))
        return False

def verify_webhook(user_id):
    """Verify webhook setup"""
    print("\n=== Verifying Webhook Setup ===")

    try:
        headers = {
            "Content-Type": "application/json",
            "apikey": settings.EVOLUTION_API_KEY
        }

        response = requests.get(
            settings.EVOLUTION_API_URL + "/webhook/find/" + user_id,
            headers=headers
        )

        print("Verification response status: " + str(response.status_code))
        print("Verification response body: " + response.text)

        if response.status_code == 200:
            webhook_data = response.json()
            if webhook_data:
                print("SUCCESS: Webhook is now configured")
                print("Enabled: " + str(webhook_data.get('enabled')))
                print("URL: " + str(webhook_data.get('url')))
                print("Events: " + str(webhook_data.get('events')))
                return True
            else:
                print("ERROR: Webhook data is still None")
                return False
        else:
            print("ERROR: Failed to verify webhook")
            return False

    except Exception as e:
        print("ERROR verifying webhook: " + str(e))
        return False

def main():
    if len(sys.argv) < 2:
        print("Usage: python fix_webhook.py <user_id>")
        print("Example: python fix_webhook.py b42a17b9-f3aa-4ec4-98c2-2f814aafbaf7")
        sys.exit(1)

    user_id = sys.argv[1]

    print("=== Fixing WhatsApp Webhook ===")
    print("User ID: " + user_id)
    print("Backend URL: " + settings.BACKEND_URL)
    print("Evolution API URL: " + settings.EVOLUTION_API_URL)

    # Set up the webhook
    success = setup_webhook(user_id)

    if success:
        # Verify the setup
        verify_webhook(user_id)
        print("\n=== Webhook Fix Complete ===")
        print("Try sending a WhatsApp message now to test auto-responses!")
    else:
        print("\n=== Webhook Fix Failed ===")
        print("Please check your Evolution API configuration")

if __name__ == "__main__":
    main()
