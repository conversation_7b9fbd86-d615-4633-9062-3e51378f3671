"""
Set up WhatsApp connection for a user
This script creates a WhatsApp instance in Evolution API and sets up the webhook
"""

import os
import sys
import json
import requests
from uuid import UUID

from app.database import SessionLocal
from app.models.user import User
from app.config import settings

def setup_whatsapp(user_id: str):
    """Set up WhatsApp connection for a user"""
    print(f"Setting up WhatsApp connection for user {user_id}")
    
    # Create a database session
    db = SessionLocal()
    
    try:
        # Find the user
        user = db.query(User).filter(User.id == UUID(user_id)).first()
        
        if not user:
            print(f"User {user_id} not found")
            return
        
        print(f"Found user: {user.email}")
        
        # Set up Evolution API headers
        headers = {
            "Content-Type": "application/json",
            "apikey": settings.EVOLUTION_API_KEY
        }
        
        # Check if instance already exists
        instance_response = requests.get(
            f"{settings.EVOLUTION_API_URL}/instance/connectionState/{user_id}",
            headers=headers
        )
        
        if instance_response.status_code == 200 and instance_response.json().get("state") == "open":
            print(f"WhatsApp instance for user {user_id} is already connected")
            return
        
        # Delete instance if it exists but is not connected
        if instance_response.status_code == 200:
            print(f"Deleting existing WhatsApp instance for user {user_id}")
            delete_response = requests.delete(
                f"{settings.EVOLUTION_API_URL}/instance/delete/{user_id}",
                headers=headers
            )
            print(f"Delete response: {delete_response.status_code} - {delete_response.text}")
        
        # Create a new instance
        print(f"Creating new WhatsApp instance for user {user_id}")
        
        # Get the webhook URL
        webhook_url = f"{settings.API_URL}/whatsapp/webhook/{user_id}"
        print(f"Webhook URL: {webhook_url}")
        
        # Create the instance
        create_response = requests.post(
            f"{settings.EVOLUTION_API_URL}/instance/create",
            headers=headers,
            json={
                "instanceName": user_id,
                "token": user_id,
                "qrcode": True,
                "webhook": webhook_url,
                "webhookEvents": {
                    "application_startup": True,
                    "qrcode_updated": True,
                    "connection_update": True,
                    "messages_upsert": True,
                    "messages_update": True,
                    "send_message": True,
                    "contacts_upsert": True,
                    "contacts_update": True,
                    "presence_update": True,
                    "chats_upsert": True,
                    "chats_update": True,
                    "groups_upsert": True,
                    "groups_update": True,
                    "group_participants_update": True,
                    "labels_edit": True,
                    "call": True,
                    "new_jwt_token": True
                }
            }
        )
        
        print(f"Create response: {create_response.status_code}")
        print(json.dumps(create_response.json(), indent=2))
        
        # Get the QR code
        qr_response = requests.get(
            f"{settings.EVOLUTION_API_URL}/instance/qrcode/{user_id}",
            headers=headers
        )
        
        if qr_response.status_code == 200 and qr_response.json().get("qrcode"):
            print(f"QR code: {qr_response.json().get('qrcode')}")
        else:
            print(f"Failed to get QR code: {qr_response.status_code} - {qr_response.text}")
        
    except Exception as e:
        print(f"Error setting up WhatsApp connection: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python setup_whatsapp.py <user_id>")
        sys.exit(1)
    
    user_id = sys.argv[1]
    setup_whatsapp(user_id)
