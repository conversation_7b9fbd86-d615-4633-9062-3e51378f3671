#!/usr/bin/env python3
"""
Scrip<PERSON> to clean up the webhook function
"""

# Read the file
with open('/app/app/routes/whatsapp.py', 'r') as f:
    lines = f.readlines()

# Find the webhook function end
webhook_end = None
for i, line in enumerate(lines):
    if 'return {"success": False, "message": f"Error: {str(e)}"}' in line:
        webhook_end = i + 1
        break

# Find the proper process_new_messages function start
process_start = None
for i, line in enumerate(lines):
    if '"""Process new messages from WhatsApp webhook"""' in line:
        process_start = i - 1  # Include the function definition line
        break

if webhook_end and process_start:
    print(f"Webhook ends at line {webhook_end + 1}")
    print(f"Process function starts at line {process_start + 1}")
    
    # Remove everything between webhook_end and process_start
    new_lines = lines[:webhook_end] + ['\n\n'] + lines[process_start:]
    
    # Write the file back
    with open('/app/app/routes/whatsapp.py', 'w') as f:
        f.writelines(new_lines)
    
    print("Cleaned up the webhook function!")
else:
    print("Could not find the boundaries to clean up")
