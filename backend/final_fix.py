#!/usr/bin/env python3
"""
Final comprehensive fix for all indentation issues
"""

# Read the file
with open('/app/app/routes/whatsapp.py', 'r') as f:
    content = f.read()

# Replace the entire problematic function with a corrected version
old_function_start = "async def process_new_messages(user_id: str, webhook_data: dict, db: Session):"
new_function = '''async def process_new_messages(user_id: str, webhook_data: dict, db: Session):
    """Process new messages from WhatsApp webhook"""
    try:
        from app.models.message import Message
        from app.models.client import Client
        from app.models.ai_configuration import AIConfiguration
        from app.services.ai_assistant import AIAssistant
        from app.services.embedding_service import embedding_service
        from app.websocket_manager import manager
        from uuid import UUID, uuid4
        from datetime import datetime
        import json
        import requests
        import asyncio
        from app.config import settings

        EVOLUTION_HEADERS = {
            "Content-Type": "application/json",
            "apikey": settings.EVOLUTION_API_KEY
        }

        # Log the webhook data for debugging
        print(f"Processing webhook data for user {user_id}: {json.dumps(webhook_data, indent=2)[:500]}...")

        # Extract the message data from the webhook payload
        # Evolution API sends the message data directly in 'data', not in 'data.messages'
        message_data = webhook_data.get('data')
        if not message_data:
            print("No message data found in webhook")
            return

        print(f"Processing single message from webhook")

        # Process the single message
        # Skip messages sent by the business
        if message_data.get('key', {}).get('fromMe', False):
            print("Skipping message sent by business")
            return

        # Extract message ID to prevent duplicates
        message_id = message_data.get('key', {}).get('id', '')
        if not message_id:
            print("No message ID found")
            return

        # Check if this message has already been processed in the database
        # We'll use the message ID as a reference in the metadata
        try:
            # Use a safer approach to check for existing messages
            # Instead of using JSONB contains which might not work in all PostgreSQL setups
            existing_message = db.query(Message).filter(
                Message.user_id == UUID(user_id),
                Message.source == "whatsapp"
            ).all()

            # Manually check if any message has the same WhatsApp message ID
            existing_message = next((msg for msg in existing_message if
                                    msg.intent_data and
                                    isinstance(msg.intent_data, dict) and
                                    msg.intent_data.get("whatsapp_message_id") == message_id), None)
        except Exception as e:
            print(f"Error checking for existing message: {str(e)}")
            existing_message = None

        if existing_message:
            print(f"Message with ID {message_id} already exists in database, skipping")
            return

        # Extract the phone number (remove the @s.whatsapp.net suffix)
        remote_jid = message_data.get('key', {}).get('remoteJid', '')
        if not remote_jid or '@' not in remote_jid:
            print(f"Invalid remote JID: {remote_jid}")
            return

        phone = remote_jid.split('@')[0]

        # Extract the message content
        message_content = ''
        message_obj = message_data.get('message', {})

        # Handle different message types
        if 'conversation' in message_obj and message_obj['conversation']:
            message_content = message_obj['conversation']
        elif 'extendedTextMessage' in message_obj and message_obj['extendedTextMessage'].get('text'):
            message_content = message_obj['extendedTextMessage'].get('text', '')
        elif 'imageMessage' in message_obj:
            caption = message_obj['imageMessage'].get('caption', '')
            message_content = f"[Image sent]{' ' + caption if caption else ''}"
        elif 'videoMessage' in message_obj:
            caption = message_obj['videoMessage'].get('caption', '')
            message_content = f"[Video sent]{' ' + caption if caption else ''}"
        elif 'audioMessage' in message_obj:
            message_content = "[Audio message sent]"
        elif 'documentMessage' in message_obj:
            filename = message_obj['documentMessage'].get('fileName', 'document')
            message_content = f"[Document sent: {filename}]"
        elif 'locationMessage' in message_obj:
            lat = message_obj['locationMessage'].get('degreesLatitude', '')
            long = message_obj['locationMessage'].get('degreesLongitude', '')
            message_content = f"[Location sent: {lat},{long}]"
        elif 'contactMessage' in message_obj:
            name = message_obj['contactMessage'].get('displayName', 'contact')
            message_content = f"[Contact shared: {name}]"
        elif 'stickerMessage' in message_obj:
            message_content = "[Sticker sent]"
        else:
            # Try to extract any text content from unknown message types
            try:
                message_content = str(message_obj)
                if len(message_content) > 100:
                    message_content = "[Unsupported message type]"
            except:
                message_content = "[Unsupported message type]"

        # Skip non-text messages for now (like senderKeyDistributionMessage)
        if not message_content or message_content.startswith("[Unsupported"):
            print(f"Skipping non-text message: {list(message_obj.keys())}")
            return

        # Skip group messages for now
        if remote_jid.endswith('@g.us'):
            print(f"Skipping group message from: {remote_jid}")
            return

        if not phone or not message_content:
            print(f"Missing phone or message content: {message_data}")
            return

        # Find the user
        try:
            user_uuid = UUID(user_id)
        except ValueError:
            print(f"Invalid user ID: {user_id}")
            return

        # Find the client by phone number
        client = db.query(Client).filter(
            Client.user_id == user_uuid,
            Client.phone.like(f"%{phone}%")
        ).first()

        # If client doesn't exist, create a new one
        if not client:
            print(f"Client not found for phone {phone}, creating new client")

            # Try to get contact info from WhatsApp
            contact_name = "Unknown"
            try:
                contacts_response = requests.post(
                    f"{settings.EVOLUTION_API_URL}/chat/findContacts/{user_id}",
                    headers=EVOLUTION_HEADERS,
                    json={
                        "where": {
                            "id": remote_jid
                        }
                    }
                )

                if contacts_response.status_code == 200:
                    contacts_data = contacts_response.json()
                    if contacts_data and len(contacts_data) > 0:
                        contact_name = contacts_data[0].get('pushName', 'Unknown')
            except Exception as e:
                print(f"Error getting contact info: {str(e)}")

            # Create a new client
            client = Client(
                id=uuid4(),  # Generate a UUID for the client
                user_id=user_uuid,
                name=contact_name if contact_name != "Unknown" else f"WhatsApp Contact ({phone})",
                phone=phone,
                email="",
                tags=["whatsapp"],  # Use lowercase for consistency
                notes="Automatically created from WhatsApp message",
                created_at=datetime.now()
            )
            db.add(client)
            db.commit()
            db.refresh(client)

        # Generate embedding for the incoming message
        message_embedding = await embedding_service.get_embedding(message_content)

        # Save the incoming message
        timestamp = datetime.fromtimestamp(message_data.get('messageTimestamp', 0))

        # Create metadata with WhatsApp-specific information
        whatsapp_metadata = {
            "whatsapp_message_id": message_id,
            "whatsapp_chat_id": remote_jid,
            "whatsapp_message_type": next(iter(message_obj.keys())) if message_obj else "unknown",
            "whatsapp_timestamp": message_data.get('messageTimestamp', 0)
        }

        incoming_message = Message(
            user_id=user_uuid,
            client_id=client.id,
            content=message_content,
            is_from_business=False,  # From client
            sent_at=timestamp,
            embedding=message_embedding,
            source="whatsapp",  # This is a WhatsApp message
            intent_data=whatsapp_metadata  # Store WhatsApp metadata
        )
        db.add(incoming_message)
        db.commit()
        db.refresh(incoming_message)

        # Check if auto-responses are enabled for this user
        ai_config = db.query(AIConfiguration).filter(AIConfiguration.user_id == user_uuid).first()
        auto_responses_enabled = True  # Default to enabled

        if ai_config and ai_config.whatsapp_settings:
            auto_responses_enabled = ai_config.whatsapp_settings.get("enableAutoResponses", True)

        if not auto_responses_enabled:
            print(f"Auto-responses are disabled for user {user_id}, skipping AI processing")
            return

        # Process the message with AI
        ai_assistant = AIAssistant(db, user_id)
        response_text, action = await ai_assistant.process_message(str(client.id), message_content)

        # Generate embedding for the AI response
        response_embedding = await embedding_service.get_embedding(response_text)

        # Save the AI response
        # Create metadata for the response
        response_metadata = {
            "whatsapp_parent_message_id": message_id,
            "whatsapp_chat_id": remote_jid,
            "ai_generated": True,
            "response_timestamp": int(datetime.now().timestamp())
        }

        outgoing_message = Message(
            user_id=user_uuid,
            client_id=client.id,
            content=response_text,
            is_from_business=True,  # From business
            sent_at=datetime.now(),
            embedding=response_embedding,
            parent_id=incoming_message.id,  # Link to the incoming message
            source="whatsapp",  # This is a WhatsApp message, even though it's AI-generated
            intent_data=response_metadata  # Store metadata
        )
        db.add(outgoing_message)
        db.commit()

        # Send the AI response back via WhatsApp with retry mechanism
        max_retries = 3
        retry_delay = 2  # seconds
        success = False

        for retry in range(max_retries):
            try:
                send_response = requests.post(
                    f"{settings.EVOLUTION_API_URL}/message/sendText/{user_id}",
                    headers=EVOLUTION_HEADERS,
                    json={
                        "number": phone,
                        "text": response_text,
                        "delay": 1200,
                        "linkPreview": False
                    },
                    timeout=10  # Add timeout to prevent hanging
                )

                if send_response.status_code == 201 or send_response.status_code == 200:
                    print(f"AI response sent to WhatsApp: {send_response.status_code} - {send_response.text}")
                    success = True

                    # Update the outgoing message with the WhatsApp message ID if available
                    try:
                        response_data = send_response.json()
                        if response_data and 'key' in response_data and 'id' in response_data['key']:
                            whatsapp_message_id = response_data['key']['id']
                            outgoing_message.intent_data['whatsapp_message_id'] = whatsapp_message_id
                            db.commit()
                    except Exception as json_error:
                        print(f"Error extracting WhatsApp message ID: {str(json_error)}")

                    break
                else:
                    print(f"Failed to send WhatsApp message (attempt {retry+1}/{max_retries}): {send_response.status_code} - {send_response.text}")

                    # Wait before retrying
                    if retry < max_retries - 1:
                        await asyncio.sleep(retry_delay * (retry + 1))  # Exponential backoff
            except Exception as send_error:
                print(f"Error sending WhatsApp message (attempt {retry+1}/{max_retries}): {str(send_error)}")

                # Wait before retrying
                if retry < max_retries - 1:
                    await asyncio.sleep(retry_delay * (retry + 1))  # Exponential backoff

        if not success:
            print("Failed to send WhatsApp message after all retries")
            # Update the message to indicate failure
            outgoing_message.intent_data['send_failed'] = True
            db.commit()

        print(f"Successfully processed WhatsApp message from {phone}: {message_content[:50]}...")

    except Exception as e:
        print(f"Error processing new messages: {str(e)}")
        import traceback
        traceback.print_exc()'''

# Find the start and end of the function
start_pos = content.find(old_function_start)
if start_pos == -1:
    print("Could not find the function to replace")
    exit(1)

# Find the next function definition to know where this function ends
next_function_pos = content.find("\nasync def ", start_pos + 1)
if next_function_pos == -1:
    next_function_pos = content.find("\ndef ", start_pos + 1)
if next_function_pos == -1:
    next_function_pos = len(content)

# Replace the function
new_content = content[:start_pos] + new_function + content[next_function_pos:]

# Write the file back
with open('/app/app/routes/whatsapp.py', 'w') as f:
    f.write(new_content)

print("Function replaced successfully!")
