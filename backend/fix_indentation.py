#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix indentation in the webhook function
"""

# Read the file
with open('/app/app/routes/whatsapp.py', 'r') as f:
    lines = f.readlines()

# Find the start of the problematic section
start_line = None
for i, line in enumerate(lines):
    if "# Extract the message content" in line and line.startswith("            #"):
        start_line = i
        break

if start_line:
    print(f"Found problematic section starting at line {start_line + 1}")
    
    # Fix indentation from this point until we find the end of the function
    in_function = True
    for i in range(start_line, len(lines)):
        line = lines[i]
        
        # If line starts with 12 spaces, reduce to 8 spaces
        if line.startswith("            ") and not line.startswith("                "):
            lines[i] = "        " + line[12:]
            print(f"Fixed line {i + 1}: {line.strip()}")
        
        # Stop when we reach the end of the function (next function definition)
        if line.strip().startswith("async def ") or line.strip().startswith("def "):
            if i > start_line + 10:  # Make sure we're not at the current function
                break
    
    # Write the file back
    with open('/app/app/routes/whatsapp.py', 'w') as f:
        f.writelines(lines)
    
    print("Indentation fixed!")
else:
    print("Could not find the problematic section")
